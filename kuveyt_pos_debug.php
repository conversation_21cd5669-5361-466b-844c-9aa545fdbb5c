<?php

/**
 * KuveytTurk POS Debug Script
 * 
 * Bu script, KuveytTurk POS ile gelen request'leri debug etmek için kullan<PERSON>lır.
 */

echo "=== KuveytTurk POS Debug Tool ===\n\n";

// Test 1: AuthenticationResponse XML Parsing
echo "Test 1: AuthenticationResponse XML Parsing\n";
echo "-------------------------------------------\n";

$sampleAuthResponse = '<?xml version="1.0" encoding="utf-8"?>
<KuveytTurkVPosMessage>
    <ResponseCode>00</ResponseCode>
    <ResponseMessage>Kart doğrulandı.</ResponseMessage>
    <MDStatusCode>1</MDStatusCode>
    <MDStatusDescription>AUTHENTICATION_SUCCESSFUL</MDStatusDescription>
    <MerchantOrderId>P608</MerchantOrderId>
    <OrderId>283961617</OrderId>
    <Amount>1000</Amount>
    <InstallmentCount>0</InstallmentCount>
    <BusinessKey>202508149555000000000019243</BusinessKey>
</KuveytTurkVPosMessage>';

function parseKuveytPosAuthenticationResponse(string $xmlResponse): ?array
{
    try {
        if (empty(trim($xmlResponse))) {
            echo "✗ XML response boş\n";
            return null;
        }

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        
        if (!$dom->loadXML($xmlResponse)) {
            $xmlErrors = libxml_get_errors();
            echo "✗ XML parse edilemedi: " . print_r($xmlErrors, true) . "\n";
            libxml_clear_errors();
            return null;
        }

        // Extract key values from XML
        $responseCode = getXmlValue($dom, 'ResponseCode');
        $responseMessage = getXmlValue($dom, 'ResponseMessage');
        $mdStatusCode = getXmlValue($dom, 'MDStatusCode');
        $mdStatusDescription = getXmlValue($dom, 'MDStatusDescription');
        $merchantOrderId = getXmlValue($dom, 'MerchantOrderId');
        $orderId = getXmlValue($dom, 'OrderId');
        $amount = getXmlValue($dom, 'Amount');
        $installmentCount = getXmlValue($dom, 'InstallmentCount');
        $businessKey = getXmlValue($dom, 'BusinessKey');

        echo "✓ Response Code: $responseCode\n";
        echo "✓ Response Message: $responseMessage\n";
        echo "✓ MD Status: $mdStatusCode - $mdStatusDescription\n";
        echo "✓ Order ID: $orderId\n";
        echo "✓ Merchant Order ID: $merchantOrderId\n";
        echo "✓ Amount: $amount\n";
        echo "✓ Business Key: $businessKey\n";

        // Check if authentication was successful
        if ($responseCode === '00' && $mdStatusCode === '1' && $mdStatusDescription === 'AUTHENTICATION_SUCCESSFUL') {
            return [
                'status' => 'approved',
                'error_message' => '',
                'md_error_message' => '',
                'installment_count' => (int)$installmentCount ?: 0,
                'ref_ret_num' => $businessKey,
                'order_id' => $orderId,
                'merchant_order_id' => $merchantOrderId,
                'amount' => $amount,
                'response_code' => $responseCode,
                'response_message' => $responseMessage,
                'md_status' => $mdStatusCode,
                'md_status_description' => $mdStatusDescription,
            ];
        } else {
            return [
                'status' => 'declined',
                'error_message' => 'Payment declined by bank',
                'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                'installment_count' => 0,
                'ref_ret_num' => null,
                'response_code' => $responseCode,
                'response_message' => $responseMessage,
            ];
        }

    } catch (Exception $e) {
        echo "✗ Parse hatası: " . $e->getMessage() . "\n";
        return null;
    }
}

function getXmlValue(DOMDocument $dom, string $tagName): ?string
{
    try {
        $nodes = $dom->getElementsByTagName($tagName);
        if ($nodes && $nodes->length > 0) {
            $node = $nodes->item(0);
            if ($node && $node->textContent !== null) {
                return trim($node->textContent);
            }
        }
    } catch (Exception $e) {
        echo "Warning: Error extracting $tagName: " . $e->getMessage() . "\n";
    }
    return null;
}

$result = parseKuveytPosAuthenticationResponse($sampleAuthResponse);
if ($result && $result['status'] === 'approved') {
    echo "✓ AuthenticationResponse başarıyla parse edildi\n";
    echo "✓ Status: " . $result['status'] . "\n";
    echo "✓ Business Key: " . $result['ref_ret_num'] . "\n";
} else {
    echo "✗ AuthenticationResponse parse edilemedi\n";
}

echo "\n";

// Test 2: Request Data Simulation
echo "Test 2: Request Data Simulation\n";
echo "--------------------------------\n";

// Simulate different types of KuveytPos responses
$testCases = [
    'AuthenticationResponse' => [
        'AuthenticationResponse' => urlencode($sampleAuthResponse),
        'method' => 'POST'
    ],
    'Direct POST' => [
        'ResponseCode' => '00',
        'ResponseMessage' => 'İşlem başarılı',
        'MD' => '202508149555000000000019243',
        'MerchantOrderId' => 'P608',
        'OrderId' => '283961617',
        'Amount' => '1000',
        'InstallmentCount' => '0',
        'method' => 'POST'
    ],
    'Failed Response' => [
        'ResponseCode' => '99',
        'ResponseMessage' => 'İşlem başarısız',
        'method' => 'POST'
    ]
];

foreach ($testCases as $testName => $testData) {
    echo "\nTest Case: $testName\n";
    echo str_repeat('-', strlen($testName) + 11) . "\n";
    
    if (isset($testData['AuthenticationResponse'])) {
        echo "✓ AuthenticationResponse bulundu\n";
        $authResponse = urldecode($testData['AuthenticationResponse']);
        $parsed = parseKuveytPosAuthenticationResponse($authResponse);
        if ($parsed) {
            echo "✓ Parse başarılı: " . $parsed['status'] . "\n";
        }
    } elseif (isset($testData['ResponseCode'])) {
        echo "✓ Direct POST parametreleri bulundu\n";
        echo "✓ Response Code: " . $testData['ResponseCode'] . "\n";
        echo "✓ Response Message: " . $testData['ResponseMessage'] . "\n";
        
        if ($testData['ResponseCode'] === '00') {
            echo "✓ Başarılı response\n";
        } else {
            echo "✗ Başarısız response\n";
        }
    }
}

echo "\n=== Debug Tamamlandı ===\n";
echo "\nGerçek debug için:\n";
echo "1. storage/logs/laravel.log dosyasını takip edin\n";
echo "2. KuveytPos ile test ödemesi yapın\n";
echo "3. Aşağıdaki log mesajlarını arayın:\n";
echo "   - 'KuveytPos detected, using full manual processing'\n";
echo "   - 'KuveytPos full manual processing started'\n";
echo "   - 'Processing AuthenticationResponse'\n";
echo "   - 'Processing direct POST parameters'\n";
echo "\n4. Hata durumunda şu mesajları kontrol edin:\n";
echo "   - 'KuveytPos manual processing failed'\n";
echo "   - 'No recognizable response format found'\n";
echo "\n5. Başarılı durumda transaction tablosunda 'approved' status görmelisiniz\n";
