{"name": "nasir<PERSON>n/laravel-starter", "description": "A CMS like modular Laravel starter project.", "keywords": ["framework", "laravel", "cms", "starter", "admin", "admin dashboard", "laravel blog", "website"], "license": "GPL-3.0-or-later", "type": "project", "require": {"php": "^8.2", "arcanedev/log-viewer": "^11.0", "guzzlehttp/guzzle": "^7.9", "hashids/hashids": "^5.0", "intervention/image": "^2", "laracasts/flash": "^3.2", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.12", "laravel/tinker": "^2.9", "lavary/laravel-menu": "^1.8", "livewire/livewire": "^3.4", "mews/laravel-pos": "^1.0", "mews/pos": "^1.6", "nwidart/laravel-modules": "^10.0", "predis/predis": "^2.1", "spatie/laravel-activitylog": "^4.8", "spatie/laravel-backup": "^8.6", "spatie/laravel-html": "^3.11", "spatie/laravel-medialibrary": "^11.4", "spatie/laravel-pdf": "^1.5", "spatie/laravel-permission": "^6.4", "symfony/http-client": "^6.4", "unisharp/laravel-filemanager": "^2.9", "yajra/laravel-datatables": "^11.0", "yajra/laravel-datatables-oracle": "^11.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.12", "fakerphp/faker": "^1.23", "laravel/breeze": "^2.0", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^10.5", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "Mo<PERSON>les/"}, "classmap": ["app/Models"], "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "clear-all": ["composer <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "@php artisan clear-compiled", "@php artisan cache:clear", "@php artisan route:clear", "@php artisan view:clear", "@php artisan config:clear", "@php artisan cache:forget spatie.permission.cache"], "pint": ["pint"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}