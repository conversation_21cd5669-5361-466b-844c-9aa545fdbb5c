<div class="row mb-3">
    <div class="col-7">
        <div class="form-group">
            {{ html()->label('Başlık', 'name') }} {!! fielf_required("required") !!}
            {{ html()->text('name')->placeholder('Başlık giriniz')->class('form-control')->attributes(["required"]) }}
        </div>
    </div>

    <div class="col">
        <div class="form-group">
            {{ html()->label('URL', 'slug') }}
            {{ html()->text('slug')->placeholder('url')->class('form-control') }}
        </div>
    </div>

</div>
<div class="row mb-3">
    <div class="col-12">
        <div class="form-group">
            {{ html()->label('Özet metin', 'intro') }}
            {{ html()->textarea('intro')->placeholder('Özet metin giriniz')->class('form-control')->rows('2')}}
        </div>
    </div>
</div>
<div class="row mb-3">
    <div class="col-12">
        <div class="form-group">
            {{ html()->label('İçerik metni', 'content') }}
            {{ html()->textarea('content')->placeholder('İçerik metni giriniz')->class('form-control') }}
        </div>
    </div>
</div>
<div class="row mb-3">
    <div class="col-12">
        <div class="form-group">
            {{ html()->label('Gönderi resmi', 'featured_image') }} {!! fielf_required('required') !!}
            <div class="input-group mb-3">
                {{ html()->text('featured_image')->placeholder('İçerik resmini seç')->class('form-control')->attributes(["required", 'aria-label'=>'Image', 'aria-describedby'=>'button-image']) }}
                <div class="input-group-append">
                    <button class="btn btn-info" type="button" id="button-image" data-input="featured_image"><i class="fas fa-folder-open"></i> @lang('Browse')</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row row-cols-5 mb-3">
    <div class="col">
        <div class="form-group">
            {{ html()->label('Kategori', 'category_id') }}
            {{ html()->select('category_id', isset($article)?optional($article->category)->pluck('name', 'id'):'')->placeholder(__("Select an option"))->class('form-control select2-category') }}
        </div>
    </div>
    <div class=" col">
        <div class="form-group">
            <?php
            $select_options = [
                'Makale' => 'Makale',
                'Ozellik' => 'Özellik',
                'Haber' => 'Haber',
                'Popup' => 'Popup Duyuru',
            ];
            ?>
            {{ html()->label('İçerik Türü', 'type') }} {!! fielf_required('required') !!}
            {{ html()->select('type', $select_options)->placeholder(__("Select an option"))->class('form-select')->attributes(["required"]) }}
        </div>
    </div>
    <div class="col">
        <div class="form-group">
            <?php
            $select_options = [
                '1' => __('Yes'),
                '0' => __('No'),
            ];
            ?>
            {{ html()->label('Öne çıkan mı?', 'is_featured') }} {!! fielf_required('required') !!}
            {{ html()->select('is_featured', $select_options)->placeholder('Sseçiniz')->class('form-select')->attributes(["required"]) }}
        </div>
    </div>

    <div class="col">
        <div class="form-group">
            <?php
            $select_options = [
                '1' => __('Published'),
                '0' => __('Unpublished'),
                '2' => __('Draft')
            ];
            ?>
            {{ html()->label('Durum', 'status') }} {!! fielf_required('required') !!}
            {{ html()->select('status', $select_options)->placeholder('Durum seç')->class('form-select')->attributes(["required"]) }}
        </div>
    </div>
    <div class="col">
        <div class="form-group">
            {{ html()->label('Yayınlanma Tarihi', 'published_at') }} {!! fielf_required('required') !!}
            {{ html()->datetime('published_at')->placeholder('Yayın tarihi seç')->class('form-control')->attributes(["required"]) }}
        </div>
    </div>
</div>


<!-- Select2 Library -->
<x-library.select2 />

@push('after-styles')
<!-- File Manager -->
<link rel="stylesheet" href="{{ asset('vendor/file-manager/css/file-manager.css') }}">

<link href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-lite.min.css" rel="stylesheet">
<style>
    .note-editor.note-frame :after {
        display: none;
    }

    .note-editor .note-toolbar .note-dropdown-menu,
    .note-popover .popover-content .note-dropdown-menu {
        min-width: 180px;
    }
</style>
@endpush

@push ('after-scripts')
<script type="module" src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-lite.min.js"></script>
<script type="module">
    // Define function to open filemanager window
    var lfm = function(options, cb) {
        var route_prefix = (options && options.prefix) ? options.prefix : '/laravel-filemanager';
        window.open(route_prefix + '?type=' + options.type || 'file', 'FileManager', 'width=900,height=600');
        window.SetUrl = cb;
    };

    // Define LFM summernote button
    var LFMButton = function(context) {
        var ui = $.summernote.ui;
        var button = ui.button({
            contents: '<i class="note-icon-picture"></i> ',
            tooltip: 'Insert image with filemanager',
            click: function() {

                lfm({
                    type: 'image',
                    prefix: '/laravel-filemanager'
                }, function(lfmItems, path) {
                    lfmItems.forEach(function(lfmItem) {
                        context.invoke('insertImage', lfmItem.url);
                    });
                });

            }
        });
        return button.render();
    };

    $('#content').summernote({
        height: 120,
        toolbar: [
            ['style', ['style']],
            ['font', ['fontname', 'fontsize', 'bold', 'underline', 'clear']],
            ['color', ['color']],
            ['para', ['ul', 'ol', 'paragraph']],
            ['table', ['table']],
            ['insert', ['link', 'lfm', 'video']],
            ['view', ['codeview', 'undo', 'redo', 'help']],
        ],
        buttons: {
            lfm: LFMButton
        }
    });
</script>

<script type="module" src="{{ asset('vendor/laravel-filemanager/js/stand-alone-button.js') }}"></script>
<script type="module">
    $('#button-image').filemanager('image');
</script>

<script type="module">
    $(document).ready(function() {
        $(document).on('select2:open', () => {
            document.querySelector('.select2-search__field').focus();
            document.querySelector('.select2-container--open .select2-search__field').focus();
        });

        $('.select2-category').select2({
            theme: "bootstrap4",
            placeholder: '@lang("Select an option")',
            minimumInputLength: 2,
            allowClear: true,
            ajax: {
                url: '{{route("backend.categories.index_list")}}',
                dataType: 'json',
                data: function(params) {
                    return {
                        q: $.trim(params.term)
                    };
                },
                processResults: function(data) {
                    return {
                        results: data
                    };
                },
                cache: true
            }
        });

    });
</script>
@endpush