<?php

return [
    'name' => '<PERSON>si<PERSON>',
    'slug' => 'Slug',
    'created_by_alias' => '<PERSON><PERSON>',
    'intro' => 'Giri<PERSON>',
    'content' => 'İçerik',
    'featured_image' => 'Öne Çıkan Resim',
    'category_id' => 'Kategori',
    'type' => 'Tür',
    'is_featured' => 'Öne Çıkan mı',
    'tags' => 'Etiketler',
    'status' => 'Durum',
    'published_at' => 'Yayınlanma Tarihi',

    'meta_title' => 'Meta Başlık',
    'meta_keywords' => 'Meta Anahtar Kelimeler',
    'meta_description' => 'Meta Açıklama',
    'meta_og_image' => 'Meta Açık Grafik Resmi',
    'meta_og_url' => 'Meta Açık Grafik URL',
    'order' => 'Sıra',

];
