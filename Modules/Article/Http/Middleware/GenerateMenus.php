<?php

namespace Modules\Article\Http\Middleware;

use Closure;

class GenerateMenus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        \Menu::make('admin_sidebar', function ($menu) {
            // Articles Dropdown
            $articles_menu = $menu->add('<i class="nav-icon fas fa-file-alt"></i> '.__('Article'), [
                'class' => 'nav-group',
            ])
                ->data([
                    'order' => 81,
                    'activematches' => [
                        'admin/posts*',
                        'admin/categories*',
                    ],
                    'permission' => ['view_posts', 'view_categories'],
                ]);
            $articles_menu->link->attr([
                'class' => 'nav-link nav-group-toggle',
                'href' => '#',
            ]);

            // Submenu: Posts
            $articles_menu->add('<i class="nav-icon fas fa-file-alt"></i> '.__('Posts'), [
                'route' => 'backend.posts.index',
                'class' => 'nk-menu-item',
            ])
                ->data([
                    'order' => 82,
                    'activematches' => 'admin/posts*',
                    'permission' => ['edit_posts'],
                ])
                ->link->attr([
                    'class' => 'nav-link',
                ]);
        })->sortBy('order');

        return $next($request);
    }
}
