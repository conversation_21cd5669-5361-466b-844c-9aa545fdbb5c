{"name": "nasirkhan/comment-module", "description": "Comment module for <PERSON><PERSON> Starter, nasirkhan/laravel-starter. It can be used with any other Models. One to many polymorphic relation.", "type": "laravel-module", "version": "v3.0.0", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "extra": {"laravel": {"providers": ["Modules\\Comment\\Providers\\CommentServiceProvider"], "aliases": {}}}, "autoload": {"psr-4": {"Modules\\Comment\\": ""}}}