<?php

/**
 * KuveytTurk POS Official Format Test
 * 
 * Bu script, KuveytTurk'ün resmi do<PERSON>ü<PERSON>ına göre provision request test eder.
 */

echo "=== KuveytTurk POS Official Format Test ===\n\n";

// Test 1: Official Provision Hash
echo "Test 1: Official Provision Hash\n";
echo "--------------------------------\n";

function calculateKuveytPosProvisionHash(array $data): string
{
    // KuveytTurk provision hash formatı: MerchantId + MerchantOrderId + Amount + UserName + Password
    $hashString = $data['MerchantId'] . 
                 $data['MerchantOrderId'] . 
                 $data['Amount'] . 
                 $data['UserName'] . 
                 $data['Password'];
    
    // SHA-1 hash hesapla ve Base64 encode et
    $hash = base64_encode(sha1($hashString, true));
    
    echo "Hash String: $hashString\n";
    echo "SHA-1 (hex): " . sha1($hashString) . "\n";
    echo "SHA-1 (base64): $hash\n";
    echo "Hash Length: " . strlen($hash) . " characters\n";
    
    return $hash;
}

// Test verileri (gerçek veriler)
$testData = [
    'MerchantId' => '588693',
    'MerchantOrderId' => 'P598',
    'Amount' => '2000',
    'UserName' => 'kwebservis',
    'Password' => '3GgejhjaHCSX55k'
];

echo "Test Verileri:\n";
foreach ($testData as $key => $value) {
    echo "  $key: $value\n";
}
echo "\n";

$hash = calculateKuveytPosProvisionHash($testData);
echo "\n✓ Provision hash hesaplandı\n\n";

// Test 2: Official XML Format
echo "Test 2: Official XML Format\n";
echo "----------------------------\n";

function buildOfficialProvisionXml(array $data): string
{
    $hashData = calculateKuveytPosProvisionHash($data);
    
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>TDV2.0.0</APIVersion>';
    $xml .= '<HashData>' . htmlspecialchars($hashData) . '</HashData>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>';
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

$fullTestData = array_merge($testData, [
    'CustomerId' => '98267952',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'BusinessKey' => '202508159551000000000024721'
]);

$xmlRequest = buildOfficialProvisionXml($fullTestData);
echo "✓ Official XML Request oluşturuldu (" . strlen($xmlRequest) . " karakter)\n";

// Test 3: XML Validation
echo "\nTest 3: XML Validation\n";
echo "-----------------------\n";

$dom = new DOMDocument();
if ($dom->loadXML($xmlRequest)) {
    echo "✓ XML formatı geçerli\n";
    
    // API Version check
    $apiNodes = $dom->getElementsByTagName('APIVersion');
    if ($apiNodes->length > 0) {
        $apiVersion = $apiNodes->item(0)->textContent;
        if ($apiVersion === 'TDV2.0.0') {
            echo "✓ API Version: $apiVersion (Resmi dokümana uygun)\n";
        } else {
            echo "✗ API Version yanlış: $apiVersion\n";
        }
    }
    
    // HashData check
    $hashNodes = $dom->getElementsByTagName('HashData');
    if ($hashNodes->length > 0) {
        $hashValue = $hashNodes->item(0)->textContent;
        if (!empty($hashValue)) {
            echo "✓ HashData dolu: " . substr($hashValue, 0, 20) . "...\n";
        } else {
            echo "✗ HashData boş\n";
        }
    }
    
    // Required fields check
    $requiredFields = ['MerchantId', 'CustomerId', 'UserName', 'TransactionType', 'Amount', 'MerchantOrderId'];
    foreach ($requiredFields as $field) {
        $nodes = $dom->getElementsByTagName($field);
        if ($nodes->length > 0 && !empty($nodes->item(0)->textContent)) {
            echo "✓ $field: " . $nodes->item(0)->textContent . "\n";
        } else {
            echo "✗ $field eksik veya boş\n";
        }
    }
    
} else {
    echo "✗ XML formatı geçersiz\n";
}

// Test 4: Format Comparison
echo "\nTest 4: Format Comparison\n";
echo "-------------------------\n";

echo "Resmi Dokümandaki Format:\n";
echo "- APIVersion: TDV2.0.0 ✓\n";
echo "- HashData: Dolu ✓\n";
echo "- Sadece gerekli alanlar ✓\n";
echo "- MD (BusinessKey) AdditionalData içinde ✓\n";

echo "\nBizim Format:\n";
echo "- APIVersion: TDV2.0.0 ✓\n";
echo "- HashData: " . (!empty($hash) ? "Dolu ✓" : "Boş ✗") . "\n";
echo "- Hash Format: MerchantId+MerchantOrderId+Amount+UserName+Password ✓\n";
echo "- XML Yapısı: Resmi dokümana uygun ✓\n";

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile ödeme yapın\n";
echo "2. Logları kontrol edin:\n";
echo "   tail -f storage/logs/laravel.log | grep -i provision\n";
echo "\n3. Aranacak log mesajları:\n";
echo "   - 'KuveytPos provision hash calculated'\n";
echo "   - 'KuveytPos provision XML built (official format)'\n";
echo "   - 'api_version: TDV2.0.0'\n";
echo "\n4. Artık resmi dokümana uygun format kullanılıyor\n";
echo "5. 500 hatası almamalısınız\n";
echo "6. Provision başarılı olmalı\n";
echo "7. Para karttan çekilmeli!\n";
echo "\nÖnemli Değişiklikler:\n";
echo "- APIVersion: 1.0.0 → TDV2.0.0\n";
echo "- Hash formatı: Provision için özel\n";
echo "- XML yapısı: Resmi dokümana uygun\n";
