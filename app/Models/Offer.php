<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class Offer extends Model
{
    protected $fillable = ['id', 'address1', 'address2', 'address3', 'amt', 'amt_vat', 'amt_vat_tra', 'country_id', 'city_id', 'town_id', 'co_id', 'doc_date', 'doc_no', 'entity_id', 'doc_tra_id', 'cur_tra_id', 'sales_person_id', 'offer_status', 'create_user_id', 'branch_id', 'amt_receipt', 'amt_receipt_tra', 'amt_tra', 'payment_method_id', 'shipping_entity_id', 'cur_rate_tra', 'cur_rate_type_id', 'due_day', 'purchase_sales', 'gnl_note1', 'incoterms_id'];
    public $timestamps = false;
    protected $casts = [
        'doc_date' => 'date',
    ];


    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'create_user_id');
    }

    public function sales_person()
    {
        return $this->belongsTo(SalesPerson::class);
    }
}
