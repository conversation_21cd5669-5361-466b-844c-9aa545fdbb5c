<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Brand extends Model
{
    protected $fillable = ['id', 'description'];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = Brand::orderBy('description')->pluck('description as name', 'id');

        return $dropdown;
    }

    public function prices()
    {
        return $this->hasMany(PriceGroup::class, 'zz_brand_id');
    }
}
