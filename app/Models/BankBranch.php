<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class BankBranch extends Model
{
    protected $fillable = ['id', 'bank_id', 'description'];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = BankBranch::orderBy('description')->pluck('description as name', 'id');

        return $dropdown;
    }

    public function bank()
    {
        return $this->blongsTo(Bank::class);
    }
}
