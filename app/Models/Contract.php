<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class Contract extends Model
{
    protected $guarded = [];
    protected $casts = [
        'contract_start_date' => 'date',
        'contract_end_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];


    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function sales_person()
    {
        return $this->belongsTo(SalesPerson::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'cur_tra_id', 'id');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'form_contract_m_id', 'id');
    }

    public function priceList()
    {
        return $this->belongsToMany(PriceList::class);
    }
    public function ContractPriceList()
    {
        return $this->hasMany(ContractPriceList::class, 'contract_id', 'id');
    }
}
