<?php

namespace App\Models;
use DB;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{

    protected $fillable = ['id', 'item_code', 'item_name', 'unit_id', 'categories1_id', 'categories2_id', 'categories3_id', 'categories4_id', 'categories5_id', 'categories6_id', 'categories7_id', 'item_name2', 'ispassive', 'create_date', 'update_date', 'brand_id', 'density', 'density_unit_id', 'depth', 'height', 'metric_unit_id', 'net_weight', 'volume_unit_id', 'weight_unit_id', 'width', 'volume', 'default_tax_id'];
    const CREATED_AT = 'create_date';
    const UPDATED_AT = 'update_date';
    public $timestamps = false;


    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function metric_unit()
    {
        return $this->belongsTo(Unit::class, 'metric_unit_id', 'id');
    }
    public function volume_unit()
    {
        return $this->belongsTo(Unit::class, 'volume_unit_id', 'id');
    }
    public function density_unit()
    {
        return $this->belongsTo(Unit::class, 'density_unit_id', 'id');
    }

    public function category4()
    {
        return $this->belongsTo(Category::class, 'categories4_id', 'id');
    }

    public function category5()
    {
        return $this->belongsTo(Category::class, 'categories5_id', 'id');
    }

    public function category6()
    {
        return $this->belongsTo(Category::class, 'categories6_id', 'id');
    }

    public function category7()
    {
        return $this->belongsTo(Category::class, 'categories7_id', 'id');
    }

    public static function forDropdown($category_id)
    {

        $query = Product::where('categories1_id', $category_id);
        $units = $query->select(DB::raw('CONCAT(item_name, " (", item_code, ")") as name'), 'id')->get();
        $dropdown = $units->pluck('name', 'id');

        return $dropdown;

    }

}
