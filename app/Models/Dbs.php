<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class Dbs extends Model
{

    protected $fillable = ['id', 'co_id', 'entity_id', 'bank_id', 'doc_no', 'doc_date', 'due_day', 'due_date', 'amt', 'created_at', 'updated_at'];
    public $table = 'dbs';
    protected $casts = [
        'doc_date' => 'date',
        'due_date' => 'date',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function bank()
    {
        return $this->belongsTo(Bank::class);
    }
}
