<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Country extends Model
{
    protected $fillable = ['id', 'iso_country_code', 'country_name'];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = Country::orderBy('name')->pluck('country_name as name', 'id');

        return $dropdown;
    }

    public function cities()
    {
        return $this->hasMany(City::class);
    }
}
