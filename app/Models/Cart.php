<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    protected $guarded = ['id'];


    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function shipping_address()
    {
        return $this->belongsTo(Address::class);
    }

    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'co_id', 'id');
    }

    public function cart_items()
    {
        return $this->hasMany(CartItem::class, 'cart_id', 'id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'cur_tra_id', 'id');
    }
}
