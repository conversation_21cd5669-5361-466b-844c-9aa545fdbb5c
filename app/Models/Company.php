<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Company extends Model
{
    protected $fillable = ['id', 'co_code', 'address1', 'address2', 'address3', 'zip_code', 'city_id', 'town_id', 'country_id', 'logo', 'co_desc', 'co_short_desc', 'email', 'tel1', 'web_site', 'tax_no', 'tax_office_id', 'created_at', 'updated_at'];
    protected $casts = [
        'created_at',
        'updated_at'
    ];

    /**
     * Retrieves the associated User model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsToMany(User::class);
    }

    public function api_auth()
    {
        return $this->belongsToMany(ApiAuth::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function requests()
    {
        return $this->hasMany(Ad::class);
    }

    public function entities()
    {
        return $this->belongsToMany(Entity::class);
    }
    public function products()
    {
        return $this->belongsToMany(Product::class ,'co_id', 'item_id', 'id');
    }
}
