<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Bank extends Model
{
    protected $fillable = ['id', 'bank_code', 'bank_desc'];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = Bank::orderBy('bank_desc')->pluck('bank_desc as name', 'id');

        return $dropdown;
    }

    public function branches()
    {
        return $this->hasMany(BankBranch::class);
    }
}
