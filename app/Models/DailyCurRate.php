<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class DailyCurRate extends Model
{
    protected $fillable = ['id','cur_to_id','cur_from_id','cur_rate_type_id','cur_rate_tra', 'doc_date', 'created_at', 'updated_at'];

    protected $casts = [
        'doc_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function currencyFrom()
    {
        return $this->belongsTo(Currency::class, 'cur_from_id', 'id');
    }

    public function currencyTo()
    {
        return $this->belongsTo(Currency::class, 'cur_to_id', 'id');
    }

    public function rateType()
    {
        return $this->belongsTo(CurrencyRateType::class, 'cur_rate_type_id', 'id');
    }

    public function scopeLatestRate($query, $from, $to, $type)
    {
        return $query->where('cur_from_id', $from)
            ->where('cur_to_id', $to)
            ->where('cur_rate_type_id', $type)
            ->latest()
            ->first();
    }
}
