<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Branch extends Model
{
    protected $fillable = ['id', 'co_id', 'branch_code', 'branch_desc', 'branch_short_desc', 'tel1', 'email', 'ispassive', 'tax_office_id', 'tax_no', 'town_id', 'city_id', 'country_id'];
    public $timestamps = false;


    public function company()
    {
        return $this->belongsTo(Company::class, 'co_id', 'id');
    }
}
