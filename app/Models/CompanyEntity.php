<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanyEntity extends Model
{
    protected $guarded = [];
    protected $table = 'company_entity';

    public $timestamps = false;

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class, 'co_id', 'id');
    }

    public function sales_person()
    {
        return $this->belongsTo(SalesPerson::class);
    }
}
