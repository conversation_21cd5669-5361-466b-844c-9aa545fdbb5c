<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PriceGroup extends Model
{
    protected $guarded = [];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = PriceGroup::orderBy('description')->pluck('description as name', 'id');
        return $dropdown;
    }

    public function prices()
    {
        return $this->hasMany(Price::class, 'price_list_id');
    }
}
