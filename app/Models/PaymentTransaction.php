<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PaymentTransaction extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */

    use SoftDeletes;

    protected $guarded = ['id'];

    public function payment()
    {
        return $this->belongsTo(Payment::class);
    }
    public function posErrorCode()
    {
        return $this->belongsTo(PosErrorCode::class);
    }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }

    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }


}
