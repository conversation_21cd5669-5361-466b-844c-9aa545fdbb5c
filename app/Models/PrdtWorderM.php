<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PrdtWorderM extends Model
{
    protected $fillable = ['id', 'co_id', 'item_id', 'branch_id', 'worder_no', 'entity_id', 'wo_type_id', 'worder_type', 'qty', 'qty_man', 'qty_prm', 'qty_man_prm', 'qty_planned_rework', 'qty_wo_actual_rework', 'unit_id', 'open_close', 'worder_status', 'note_large', 'note_large2', 'source_app', 'source_m_id', 'source_d_id', 'prd_source_app' ,'create_date', 'update_date'];
    public $timestamps = false;
    protected $table = 'prdt_worder_m';

    const CREATED_AT = 'create_date';
    const UPDATED_AT = 'update_date';

    protected $casts = [
        'create_date' => 'datetime',
        'update_date' => 'datetime',
    ];


}
