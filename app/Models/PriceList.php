<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class PriceList extends Model
{
    protected $guarded = [];
    public $timestamps = false;
    public static function forDropdown()
    {
        $dropdown = PriceList::orderBy('description_1')->pluck('description_1 as name', 'id');

        return $dropdown;
    }

    public function prices()
    {
        return $this->hasMany(Price::class, 'price_list_m_id');
    }

    public function priceGroups()
    {
        return $this->belongsTo(PriceGroup::class, 'price_list_id');
    }

    public function contractPriceLists()
    {
        return $this->hasMany(ContractPriceList::class, 'price_list_id');
    }

    public function contract()
    {
        return $this->belongsToMany(Contract::class);
    }
    public function brand()
    {
        return $this->belongsTo(Brand::class,'zz_brand_id','id');
    }
}
