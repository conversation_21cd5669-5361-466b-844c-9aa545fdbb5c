<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Entity extends Model
{

    protected $fillable = ['id', 'entity_type', 'entity_code', 'address1', 'address2', 'address3', 'zip_code', 'town_id', 'city_id', 'country_id', 'entity_name', 'email','tel1', 'web_site', 'tax_no', 'tax_office_id', 'is_default','created_at', 'updated_at'];
    protected $casts = [
        'created_at',
        'updated_at'
    ];

    public function company()
    {
        return $this->belongsToMany(Company::class);
    }

    public function user()
    {
        return $this->belongsToMany(User::class);
    }
    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function contracts()
    {
        return $this->hasMany(Contract::class);
    }

    public function active_contracts()
    {
        return $this->hasMany(Contract::class)->where('ispassive', '0');
    }

    public function offers()
    {
        return $this->hasMany(Offer::class);
    }
    public function dbs()
    {
        return $this->hasMany(Dbs::class);
    }

    public function city()
    {
        return $this->belongsTo(City::class);
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function town()
    {
        return $this->belongsTo(Town::class);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function shipments()
    {
        return $this->hasMany(Shipment::class);
    }


    /**
     * Bu firmaya yetkili kullanıcılar
     */
    public function users()
    {
        return $this->belongsToMany(User::class, 'entity_user')
            ->withPivot('pivot_status', 'pivot_created_by', 'pivot_created_at');
    }

    /**
     * Bu firmanın aktif olduğu kullanıcılar
     */
    public function activeUsers()
    {
        return $this->hasMany(User::class, 'active_entity_id');
    }


    /**
     * Firmanın vergi dairesi
     */
    public function taxOffice()
    {
        return $this->belongsTo(TaxOffice::class, 'tax_office_id');
    }


    /**
     * Firmanın DBS kayıtları
     */
    public function dbsRecords()
    {
        return $this->hasMany(Dbs::class, 'entity_id');
    }

    /**
     * Firmanın çek kayıtları
     */
    public function chequeRecords()
    {
        return $this->hasMany(ChequeRecord::class, 'entity_id');
    }

    /**
     * Firmanın sepetleri
     */
    public function carts()
    {
        return $this->hasMany(Cart::class, 'entity_id');
    }



    /**
     * Firmanın fiyat listeleri
     */
    public function priceLists()
    {
        return $this->hasMany(PriceList::class, 'entity_id');
    }

    /**
     * Firmanın bağlı olduğu şirketler
     */
    public function companies()
    {
        return $this->belongsToMany(Company::class, 'company_entity')
            ->withPivot('sales_person_id', 'due_day', 'is_default');
    }

    /**
     * Tam adres formatında döndür
     */
    public function getFullAddressAttribute()
    {
        $address = $this->address1;

        if ($this->address2) {
            $address .= ', ' . $this->address2;
        }

        if ($this->address3) {
            $address .= ', ' . $this->address3;
        }

        $address .= ', ' . optional($this->town)->town_name;
        $address .= ', ' . optional($this->city)->city_name;
        $address .= ', ' . optional($this->country)->country_name;

        if ($this->zip_code) {
            $address .= ' - ' . $this->zip_code;
        }

        return $address;
    }

}
