<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class OrderD extends Model
{
    protected $table = 'orders_d';
    protected $guarded = [];
    public $timestamps = false;

    public function order()
    {
        return $this->belongsTo(Order::class, 'order_m_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'item_id', 'id');
    }
}
