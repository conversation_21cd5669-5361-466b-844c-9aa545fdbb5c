<?php

namespace App\Models;

use App\Models\Presenters\UserPresenter;
use App\Models\Traits\HasHashedMediaTrait;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Spatie\MediaLibrary\HasMedia;
use Spatie\Permission\Traits\HasRoles;
use App\Notifications\ResetPassword as ResetPasswordNotification;

class User extends Authenticatable implements HasMedia, MustVerifyEmail
{
    use HasFactory;
    use HasHashedMediaTrait;
    use HasRoles;
    use Notifiable;
    use SoftDeletes;
    use UserPresenter;

    protected $guarded = [
        'id',
        'updated_at',
        '_token',
        '_method',
        'password_confirmation',
    ];




    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password_changed_at' => 'datetime',
        'date_of_birth' => 'date',
        'status' => 'integer',
        'dark_mode' => 'boolean',
        'compact_mode' => 'boolean',
    ];

    /**
     * The attributes that should be hidden for arrays.
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * Retrieve the providers associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function providers()
    {
        return $this->hasMany('App\Models\UserProvider');
    }

    public function companies()
    {
        return $this->belongsToMany('App\Models\Company');
    }



    /**
     * Retrieves the profile of the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function profile()
    {
        return $this->hasOne('App\Models\UserProfile');
    }

    /**
     * Get the user profile associated with the user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function userprofile()
    {
        return $this->hasOne('App\Models\UserProfile');
    }

    /**
     * Get the list of users related to the current User.
     */
    public function getRolesListAttribute()
    {
        return array_map('intval', $this->roles->pluck('id')->toArray());
    }

    public function active_entity()
    {
        return $this->belongsTo('App\Models\Entity', 'active_entity_id');
    }

    public function entities()
    {
            return $this->belongsToMany('App\Models\Entity')->where('pivot_status',1)->withPivot('pivot_created_at', 'pivot_status', 'pivot_created_by');
    }
    public function all_entities()
    {
        return $this->belongsToMany('App\Models\Entity')->withPivot('pivot_created_at', 'pivot_status', 'pivot_created_by');
    }
    /**
     * Route notifications for the Slack channel.
     *
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return string
     */
    public function routeNotificationForSlack($notification)
    {
        return env('SLACK_NOTIFICATION_WEBHOOK');
    }

    /**
     * Boot the model.
     *
     * Register the model's event listeners.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();

        // create a event to happen on creating
        static::creating(function ($table) {
            $table->created_by = Auth::id();
        });

        // create a event to happen on updating
        static::updating(function ($table) {
            $table->updated_by = Auth::id();
        });

        // create a event to happen on saving
        static::saving(function ($table) {
            $table->updated_by = Auth::id();
        });

        // create a event to happen on deleting
        static::deleting(function ($table) {
            $table->deleted_by = Auth::id();
            $table->save();
        });
    }

    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPasswordNotification($token));
    }







    /**
     * Kullanıcının aktif firma ilişkisi
     */
    public function activeEntity()
    {
        return $this->belongsTo(Entity::class, 'active_entity_id');
    }


    /**
     * Kullanıcının oluşturduğu kayıtlar
     */
    public function createdRecords()
    {
        return $this->hasMany(User::class, 'created_by');
    }

    /**
     * Kullanıcının güncellediği kayıtlar
     */
    public function updatedRecords()
    {
        return $this->hasMany(User::class, 'updated_by');
    }

    /**
     * Kullanıcının sildiği kayıtlar
     */
    public function deletedRecords()
    {
        return $this->hasMany(User::class, 'deleted_by');
    }

    /**
     * Kullanıcının tamamladığı kayıtlar
     */
    public function completedRecords()
    {
        return $this->hasMany(ChequeRecord::class, 'completed_by');
    }

    /**
     * Kullanıcının satış temsilcisi olduğu kayıtlar
     */
    public function salesPerson()
    {
        return $this->hasOne(SalesPerson::class);
    }

    /**
     * Kullanıcının sepeti
     */
    public function carts()
    {
        return $this->hasMany(Cart::class);
    }

    /**
     * Kullanıcının ödemeleri
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Kullanıcının oluşturduğu duyurular
     */
    public function announcements()
    {
        return $this->hasMany(Announcement::class);
    }

    /**
     * Kullanıcının oluşturduğu olaylar
     */
    public function events()
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Kullanıcı durumunu kontrol et (aktif/pasif)
     */
    public function isActive()
    {
        return $this->status === 1;
    }

    /**
     * Kullanıcının email'inin doğrulanıp doğrulanmadığını kontrol et
     */
    public function isVerified()
    {
        return $this->email_verified_at !== null;
    }

    /**
     * Get the user's full name.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return "{$this->first_name} {$this->last_name}";
    }

}
