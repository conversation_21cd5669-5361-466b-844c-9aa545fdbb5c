<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Discount extends Model
{
    protected $fillable = ['id', 'description', 'disc_code', 'disc_rate', 'disc_calc_type', 'is_enabled', 'ispassive'];

    public $timestamps = false;

    public function contract()
    {
        return $this->belongsTo(Contract::class, 'form_contract_m_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Ad::class, 'item_id', 'id');
    }

    public function disc1()
    {
        return $this->belongsTo(Discount::class);
    }

}
