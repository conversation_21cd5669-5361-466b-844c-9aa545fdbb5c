<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    protected $guarded = [];

    protected $casts = [
        'doc_date' => 'date',
        'created_at',
        'updated_at',
'shipping_date' => 'date',
    ];
    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function order_d()
    {
        return $this->hasMany(OrderD::class , 'order_m_id', 'id');
    }
    public function payment_method()
    {
        return $this->belongsTo(PaymentMethod::class);
    }
    public function currency()
    {
        return $this->belongsTo(Currency::class, 'cur_tra_id', 'id');
    }

    public function contract()
    {
        return $this->belongsTo(Contract::class, 'form_contract_m_id', 'id');
    }
}
