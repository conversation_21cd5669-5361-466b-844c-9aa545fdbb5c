<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class City extends Model
{
    protected $fillable = ['id', 'country_id', 'city_name'];
    public $timestamps = false;
    public static function forDropdown($country_id)
    {
        $dropdown = City::where('country_id', $country_id)->pluck('city_name as name', 'id');

        return $dropdown;
    }

    public function country()
    {
        return $this->belongsTo(Country::class);
    }

    public function towns()
    {
        return $this->hasMany(Town::class);
    }

}
