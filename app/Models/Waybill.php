<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;


class Waybill extends Model
{
    protected $fillable = ['id', 'address1', 'address2', 'address3', 'amt', 'amt_vat', 'amt_vat_tra', 'country_id', 'city_id', 'town_id', 'co_id', 'doc_date', 'doc_no', 'invoice_status', 'entity_id', 'doc_tra_id', 'cur_tra_id', 'e_doc_no', 'uuid'];
    public $timestamps = false;
    protected $casts = [
        'doc_date' => 'date',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }

    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class);
    }
}
