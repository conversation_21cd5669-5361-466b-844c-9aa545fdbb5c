<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class ChequeRecords extends Model

{

    protected $guarded = ['id'];
    protected $casts = ['created_at' => 'datetime', 'updated_at' => 'datetime', 'cargo_at' => 'datetime'];

    public function entity()

    {
        return $this->belongsTo(\App\Entity::class);
    }

    public function user()
    {
        return $this->belongsTo(\App\User::class, 'user_id');
    }
    public function delivery_user()
    {
        return $this->belongsTo(\App\User::class, 'completed_by');
    }

    public function bank()
    {
        return $this->belongsTo(\App\Models\Bank::class);
    }

    public function currency()
    {
        return $this->belongsTo(\App\Currency::class);
    }

    public function media()
    {
        return $this->morphMany(\App\Media::class, 'model');
    }


}