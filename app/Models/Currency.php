<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Currency extends Model
{

    protected $fillable = ['id', 'cur_code', 'cur_symbol', 'description'];

    public $timestamps = false;

    public static function forDropdown($state_id)
    {
        $dropdown = Currency::pluck('name', 'id');

        return $dropdown;
    }

    public function rates()
    {
        return $this->hasMany(CurrencyExchangeRate::class);
    }

    public function rate()
    {
        return $this->hasOne(CurrencyExchangeRate::class)->latest();
    }

    public function buying_rate()
    {
        return $this->hasOne(DailyCurRate::class, 'cur_from_id')->where('cur_rate_type_id', 235)->orderByDesc('id');
    }

    public function buying_rate_11()
    {
        return $this->hasOne(DailyCurRate::class, 'cur_from_id')->where('cur_rate_type_id', 277)->orderByDesc('id');
    }

}
