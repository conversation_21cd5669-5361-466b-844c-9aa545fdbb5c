<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CartItem extends Model
{
    protected $guarded = ['id'];


    public function cart()
    {
        return $this->belongsTo(Cart::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'cur_tra_id', 'id');
    }

    public function parent_product()
    {
        return $this->belongsTo(Product::class, 'parent_id', 'id');
    }

}
