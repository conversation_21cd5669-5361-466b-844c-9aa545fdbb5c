<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Price extends Model
{
    public $timestamps = false;

// Mass assignment için
    protected $guarded = [];
    public function priceList()
    {
        return $this->belongsTo(PriceList::class, 'price_list_m_id', 'id');
    }

    public function product()
    {
        return $this->belongsTo(Product::class, 'item_id');
    }

    public function currency()
    {
        return $this->belongsTo(Currency::class, 'cur_tra_id');

    }

    public function unit()
    {
        return $this->hasOneThrough(Unit::class, Product::class, 'id', 'id', 'item_id', 'unit_id');

    }

    public function priceRule()
    {
        return $this->belongsTo(PriceRule::class ,'rule_id');
    }
}
