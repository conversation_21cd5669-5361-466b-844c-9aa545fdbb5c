<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    protected $guarded = [];

    protected $casts = [
        'created_at',
        'updated_at',
        'doc_date' => 'datetime:Y-m-d',
    ];


    public function order()
    {
        return $this->belongsTo(Order::class, 'source_m_id');
    }

    public function city()
    {
        return $this->belongsTo(City::class, 'shipping_city_id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'shipping_country_id');
    }
    public function town()
    {
        return $this->belongsTo(Town::class, 'shipping_town_id');
    }
    public function entity()
    {
        return $this->belongsTo(Entity::class);
    }
    public function shipping_entity()
    {
        return $this->belongsTo(Entity::class, 'shipping_entity_id');
    }
}
