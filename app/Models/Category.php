<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */


    protected $fillable = ['id' , 'categories_code', 'description', 'parent_cat_id', 'step', 'categories_page', 'group_name', 'zz_b2b'];

    /**
     * Retrieves the associated User model.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function children()
    {
        return $this->HasMany(Category::class, 'parent_cat_id', 'id');
    }
}
