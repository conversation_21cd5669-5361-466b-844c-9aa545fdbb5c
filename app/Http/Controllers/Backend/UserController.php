<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Mail\UserPasswordReset;
use App\Models\Company;
use App\Models\Entity;
use App\Models\User;
use Flash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;
use Spatie\Permission\Models\Role;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index( Request $request)
    {

        $status = $request->get('status', '3');
        $users = User::when($status !== '3', function ($query) use ($status) {
            return $query->where('status', $status);
        })->get();

        return view('backend.users.index', compact('users', 'status'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $roles = Role::where('name', '!=', 'super admin')->get();

        $entity_ids = DB::table('company_entity')->where('co_id', 2725)->pluck('entity_id')->toArray();
        $entities = Entity::whereIn('id', $entity_ids)->where('is_default',1)->get();

        return view('backend.users.create', compact('roles', 'entities'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $baseValidation = [
            'first_name' => 'required|string|max:191',
            'last_name' => 'required|string|max:191',
            'email' => 'required|string|email|max:191|unique:users',
            'mobile' => 'nullable|string|max:20',
            'password' => ['required', 'confirmed', Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()],
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|boolean',
            'role' => 'nullable|exists:roles,id',
            'entities' => 'nullable|array', // Making entities optional by default
        ];

        // Check if the selected role is "bayi"
        if ($request->has('role') && !empty($request->role)) {
            $role = Role::find($request->role);

            if ($role && strtolower($role->name) === 'bayi') {
                // If role is "bayi", make entities field required
                $baseValidation['entities'] = 'required|array|min:1';
            }
        }

        $request->validate($baseValidation);
        $active_entity_id = 1;
        try {
            if ($request->has('entities') && !empty($request->entities)) {
                $active_entity_id = $request->entities[0];
            }

            DB::beginTransaction();

            $user = new User();
            $user->name = $request->first_name . ' ' . $request->last_name;
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = $request->email;
            $user->mobile = $request->mobile;
            $user->username = Str::slug($request->first_name . '-' . $request->last_name) . '-' . random_int(100000, 999999);
            $user->password = Hash::make($request->password);
            $user->status = $request->status;
            $user->active_entity_id = $active_entity_id;

            // Avatar işlemi
            if ($request->hasFile('avatar')) {
                $avatarPath = $request->file('avatar')->store('users/avatars', 'public');
                $user->avatar = 'storage/' . $avatarPath;
            }

            $user->save();

            // Directly insert the role
            if ($request->has('role') && !empty($request->role)) {
                DB::table('model_has_roles')->insert([
                    'role_id' => $request->role,
                    'model_type' => User::class,
                    'model_id' => $user->id
                ]);

                // Clear the role cache
                app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
            }

            // Firmaları atama
            if ($request->has('entities') && !empty($request->entities)) {
                $user->entities()->sync($request->entities);
            }

            DB::commit();

            Flash::success('Kullanıcı başarıyla oluşturuldu.');

            return redirect()->route('backend.users.index');
        } catch (\Exception $e) {
            DB::rollBack();

            Flash::error('Kullanıcı oluşturulurken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }
    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = User::with(['roles', 'entities'])->findOrFail($id);

        return view('backend.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = User::with(['entities'])->findOrFail($id);
        $roles = Role::where('name', '!=', 'super admin')->get();


        // Directly query the role ID from the model_has_roles table to ensure accuracy
        $userRole = DB::table('model_has_roles')
            ->where('model_id', $user->id)
            ->where('model_type', User::class)
            ->value('role_id');

        $userEntities = $user->entities->pluck('id')->toArray();

        return view('backend.users.edit', compact('user', 'roles', 'userRole', 'userEntities'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $baseValidation = [
            'first_name' => 'required|string|max:191',
            'last_name' => 'required|string|max:191',
            'email' => 'required|string|email|max:191|unique:users,email,' . $user->id,
            'mobile' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'status' => 'required|boolean',
            'role' => 'nullable|exists:roles,id',
            'entities' => 'nullable|array', // Making entities optional by default
        ];

        $active_entity_id = 1;
        // Check if the selected role is "bayi"
        if ($request->has('role') && !empty($request->role)) {
            $role = Role::find($request->role);

            if ($role && strtolower($role->name) === 'bayi') {
                // If role is "bayi", make entities field required
                $baseValidation['entities'] = 'required|array|min:1';
            }
        }

        $request->validate($baseValidation);

        try {
            DB::beginTransaction();
            if ($request->has('entities') && !empty($request->entities)) {
                $active_entity_id = $request->entities[0];
            }

            $user->name = $request->first_name . ' ' . $request->last_name;
            $user->first_name = $request->first_name;
            $user->last_name = $request->last_name;
            $user->email = $request->email;
            $user->mobile = $request->mobile;
            $user->status = $request->status;
            $user->active_entity_id = $active_entity_id;

            // Avatar işlemi
            if ($request->hasFile('avatar')) {
                // Eski avatar varsa sil
                if ($user->avatar && $user->avatar != 'img/default-avatar.jpg') {
                    Storage::disk('public')->delete(str_replace('storage/', '', $user->avatar));
                }

                $avatarPath = $request->file('avatar')->store('users/avatars', 'public');
                $user->avatar = 'storage/' . $avatarPath;
            }

            $user->save();

            // Directly handle the role assignment in the database for reliability
            if ($request->has('role') && !empty($request->role)) {
                // First, remove all existing roles for this user
                DB::table('model_has_roles')
                    ->where('model_id', $user->id)
                    ->where('model_type', User::class)
                    ->delete();

                // Then insert the new role
                DB::table('model_has_roles')->insert([
                    'role_id' => $request->role,
                    'model_type' => User::class,
                    'model_id' => $user->id
                ]);

                // Clear the role cache for this user
                app()->make(\Spatie\Permission\PermissionRegistrar::class)->forgetCachedPermissions();
            }

            // Firmaları atama - only if provided
            if ($request->has('entities') && !empty($request->entities)) {
                $user->entities()->sync($request->entities);
            } else {
                // For non-bayi roles, empty array is acceptable (clear all entities)
                $user->entities()->sync([]);
            }

            DB::commit();

            Flash::success('Kullanıcı başarıyla güncellendi.');

            return redirect()->route('backend.users.index');
        } catch (\Exception $e) {
            DB::rollBack();

            Flash::error('Kullanıcı güncellenirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }



    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $user = User::findOrFail($id);

            // Avatar silme
            if ($user->avatar && $user->avatar != 'img/default-avatar.jpg') {
                Storage::disk('public')->delete(str_replace('storage/', '', $user->avatar));
            }

            // İlişkileri temizle
            $user->roles()->detach();
            $user->entities()->detach();

            $user->delete();

            Flash::success('Kullanıcı başarıyla silindi.');

            return redirect()->route('backend.users.index');
        } catch (\Exception $e) {
            Flash::error('Kullanıcı silinirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back();
        }
    }

    /**
     * Show the form for setting user password.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function showPasswordForm($id)
    {
        $user = User::findOrFail($id);

        return view('backend.users.password', compact('user'));
    }

    /**
     * Update user password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function updatePassword(Request $request, $id)
    {
        $user = User::findOrFail($id);

        $request->validate([
            'password' => ['required', 'confirmed', Password::min(8)
                ->letters()
                ->mixedCase()
                ->numbers()
                ->symbols()],
            'send_email' => 'nullable|boolean'
        ]);

        try {
            $user->password = Hash::make($request->password);
            $user->password_changed_at = now();
            $user->save();

            // Kullanıcıya e-posta gönder
            if ($request->has('send_email') && $request->send_email) {
                Mail::to($user->email)->send(new UserPasswordReset($user, $request->password));
            }

            Flash::success('Kullanıcı şifresi başarıyla güncellendi.');

            return redirect()->route('backend.users.index');
        } catch (\Exception $e) {
            Flash::error('Şifre güncellenirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back();
        }
    }

    /**
     * Send password reset email.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function sendPasswordReset($id)
    {
        try {
            $user = User::findOrFail($id);

            $newPassword = Str::random(10); // Rastgele şifre oluştur
            $user->password = Hash::make($newPassword);
            $user->password_changed_at = now();
            $user->save();

            // Kullanıcıya e-posta gönder
            Mail::to($user->email)->send(new UserPasswordReset($user, $newPassword));

            return response()->json([
                'success' => true,
                'message' => 'Şifre sıfırlama e-postası başarıyla gönderildi.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'E-posta gönderilirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Change user status.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function changeStatus(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);
            $status = $request->has('status') ? $request->status : ($user->status == 1 ? 0 : 1);

            $user->status = $status;
            $user->save();

            return response()->json([
                'success' => true,
                'message' => 'Kullanıcı durumu başarıyla güncellendi.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Durum güncellenirken bir hata oluştu: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get entities with AJAX.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function getEntities(Request $request)
    {
        $search = $request->input('q');
        $page = $request->input('page', 1);
        $perPage = 30;
        $entity_ids = DB::table('company_entity')->where('co_id', 2725)->pluck('entity_id')->toArray();
        $query = Entity::whereIn('id', $entity_ids)->where('is_default',1);

        if ($search) {
            $query->where(function($q) use ($search) {
                $q->where('entity_name', 'like', "%{$search}%")
                    ->orWhere('entity_code', 'like', "%{$search}%");
            });
        }

        $total = $query->count();
        $entities = $query->skip(($page - 1) * $perPage)
            ->take($perPage)
            ->get();

        $formattedEntities = $entities->map(function($company) {
            return [
                'id' => $company->id,
                'entity_name' => $company->entity_name,
                'entity_code' => $company->entity_code
            ];
        });

        return response()->json([
            'items' => $formattedEntities,
            'total_count' => $total
        ]);
    }

}