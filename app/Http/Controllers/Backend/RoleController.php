<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Flash;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        $roles = Role::with(['permissions', 'users'])->get();
        $permissions = Permission::with('roles')->get();

        return view('backend.users.roles.index', compact('roles', 'permissions'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $permissions = Permission::all();

        return view('backend.users.roles.create', compact('permissions'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:125|unique:roles,name',
            'permissions' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            $role = Role::create([
                'name' => $request->name,
                'guard_name' => 'web'
            ]);

            if ($request->has('permissions')) {
                $permissions = Permission::whereIn('id', $request->permissions)->get();
                $role->syncPermissions($permissions);
            }

            DB::commit();

            Flash::success('Rol başarıyla oluşturuldu.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            DB::rollBack();

            Flash::error('Rol oluşturulurken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $role = Role::findOrFail($id);
        $permissions = Permission::all();
        $rolePermissions = $role->permissions->pluck('id')->toArray();

        return view('backend.users.roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $role = Role::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:125|unique:roles,name,' . $role->id,
            'permissions' => 'nullable|array'
        ]);

        try {
            DB::beginTransaction();

            $role->name = $request->name;
            $role->save();

            if ($request->has('permissions')) {
                // Get actual Permission models instead of just IDs
                $permissions = Permission::whereIn('id', $request->permissions)->get();
                $role->syncPermissions($permissions);
            } else {
                $role->syncPermissions([]);
            }

            DB::commit();

            Flash::success('Rol başarıyla güncellendi.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            DB::rollBack();

            Flash::error('Rol güncellenirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $role = Role::findOrFail($id);

            // Rolün kullanımda olmadığını kontrol et
            if ($role->users->count() > 0) {
                Flash::error('Bu rol kullanıcılara atanmış durumda, önce kullanıcılardan rolü kaldırın.');
                return redirect()->back();
            }

            $role->permissions()->detach();
            $role->delete();

            Flash::success('Rol başarıyla silindi.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            Flash::error('Rol silinirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back();
        }
    }
}