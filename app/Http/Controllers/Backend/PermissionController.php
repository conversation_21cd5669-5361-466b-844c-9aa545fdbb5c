<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Flash;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;

class PermissionController extends Controller
{
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('backend.permissions.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:125|unique:permissions,name'
        ]);

        try {
            Permission::create([
                'name' => $request->name,
                'guard_name' => 'web'
            ]);

            Flash::success('İzin başarıyla oluşturuldu.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            Flash::error('İzin oluşturulurken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $permission = Permission::findOrFail($id);

        return view('backend.permissions.edit', compact('permission'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $permission = Permission::findOrFail($id);

        $request->validate([
            'name' => 'required|string|max:125|unique:permissions,name,' . $permission->id
        ]);

        try {
            $permission->name = $request->name;
            $permission->save();

            Flash::success('İzin başarıyla güncellendi.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            Flash::error('İzin güncellenirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $permission = Permission::findOrFail($id);

            // İznin kullanımda olmadığını kontrol et
            if ($permission->roles->count() > 0) {
                Flash::error('Bu izin rollere atanmış durumda, önce rollerden izni kaldırın.');
                return redirect()->back();
            }

            $permission->delete();

            Flash::success('İzin başarıyla silindi.');

            return redirect()->route('backend.roles.index');
        } catch (\Exception $e) {
            Flash::error('İzin silinirken bir hata oluştu: ' . $e->getMessage());

            return redirect()->back();
        }
    }
}