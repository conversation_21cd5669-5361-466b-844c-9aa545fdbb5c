<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Config;

class LanguageController extends Controller
{
    public function switch($language)
    {
        if (!in_array($language, ['en', 'tr', /* add other supported languages */])) {
            flash()->error(__('Geçersiz dil seçimi'))->important();
            return redirect()->back();
        }

        // Set language for current request
        App::setLocale($language);

        // Store language in session to persist across requests
        session()->put('locale', $language);

        // Set locale for date/time formatting
        setlocale(LC_TIME, $language);
        Carbon::setLocale($language);

        flash()->success(__('Dil değiştirildi:').' '.strtoupper($language))->important();

        return redirect()->back();
    }
}
