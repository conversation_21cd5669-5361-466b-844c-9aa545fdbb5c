<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\City;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Address;
use App\Models\Country;

use App\Models\Town;
use Illuminate\Http\Request;

class AddressController extends Controller
{

    public function index()
    {
        $addresses = Address::all();

        return view('frontend.address.index', compact('addresses'));

    }

    public function show($id)
    {
        $address = Address::find($id);

        return view('frontend.address.show', compact('address'));

    }

    public function create()
    {
        $countries = Country::forDropdown();

        return view('frontend.address.create', compact('countries'));

    }

    public function edit($id)
    {
        $address = Address::find($id);
        $countries = Country::forDropdown();

        return view('frontend.address.edit', compact('address', 'countries'));
    }

    public function store(Request $request)
    {
        $user = auth()->user();
        $request = $request->all();
        $request['user_id'] = $user->id;
        $request['entity_id'] = $user->active_entity_id;

        if(!is_numeric($request['town_id'])){
           return ['success' => false, 'message' => 'Lütfen ilçe seçiniz!'];
        }

        Address::create($request);

        $addresses = Address::where('entity_id', $request['entity_id'])->pluck('address1 as name', 'id');

        return ['success' => true, 'message' => 'Adres oluşturuldu!', 'addresses' => $addresses];
    }


}
