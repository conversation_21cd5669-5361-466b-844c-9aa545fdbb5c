<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;

use App\Models\Category;
use App\Models\Company;
use App\Models\Contract;
use App\Models\ItemUnit;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\OrderStatus;
use App\Models\Product;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;


class ProductController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {


        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        $products = Product::where('is_open_to_internet',1)->get();

        return view('frontend.product.index')->with(compact('products'));
    }

    public function show($id)
    {
        $order = Order::find($id);
        $data['order'] = $order;

    }

  public function production_query(Request $request)
    {
        $user = auth()->user();
        $density = $request->input('density', null);
        $width_height = $request->input('wh', null);
        $depth = $request->input('depth', null);
        $pid = $request->input('pid');

            $query = Product::query();
            if ($width_height>0) {
                $query->where('categories7_id', $width_height);
            }
            if ($density>0) {
                $query->where('categories5_id', $density);
            }
            if($depth>0){
                $query->where('categories6_id', $depth);
            }
            $products = $query->simplePaginate(7)->withQueryString();;
            $data['products'] = $products;

        $data['item_units'] = ItemUnit::where('item_id', $pid)->orderBy('line_no')->get();

        $data['densities'] = Category::where('zz_b2b',1)->whereIn('id', Product::distinct()->orderBy('categories5_id')->pluck('categories5_id'))->orderBy('sort')->orderBy('description')->select('id', 'description')->get();
        $data['depths'] = Category::where('zz_b2b',1)->whereIn('id', Product::distinct()->orderBy('categories6_id')->pluck('categories6_id'))->orderBy('sort')->orderBy('description')->select('id', 'description')->get();
        $data['whs'] = Category::where('zz_b2b',1)->whereIn('id', Product::distinct()->orderBy('categories7_id')->pluck('categories7_id'))->orderBy('sort')->orderBy('description')->select('id', 'description')->get();

            // $data['densities'] = Product::distinct()->leftJoin('product_branches', 'products.id', '=', 'product_branches.item_id')->orderBy('density')->pluck('density');
        $data['density_active'] = $density;
        $data['wh_active'] = $width_height;
        $data['depth_active'] = $depth;

        return view('frontend.product.production_query', $data);
    }

    public function production_query_store(Request $request)
    {
        $input = $request->all();
        $user = auth()->user();
 ;

        $emails = ['<EMAIL>'];
        $data = array('name'=>"AkdagTasyunuPortal", 'user' => $user, 'input' => $input);
        Mail::send(['html'=>'emails.production_query'], $data, function($message) use($emails) {
            $message->to($emails)->subject('Üretim Sorgusu ' );
            $message->from('<EMAIL>', env('APP_NAME', 'B2B Portal'));
        });

        return ['success' => 1, 'message' => 'Üretim sorgusu başarıyla kaydedildi.'];
    }


}
