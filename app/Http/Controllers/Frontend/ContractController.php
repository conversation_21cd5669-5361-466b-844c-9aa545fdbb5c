<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Company;
use App\Models\Entity;
use App\Models\Offer;
use App\Models\Ad;
use App\Models\AdDetail;
use App\Models\Order;
use App\Models\User;
use App\Models\Contract;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Number;

class ContractController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }


        if (request()->ajax()) {

            $contracts = Contract::select('e.entity_name', 'contracts.id', 'contracts.doc_no', 'contracts.description', 'contracts.due_day', 'contracts.note_large', 'contract_start_date', 'contract_end_date', 'contracts.amt', 'cur_code', 'remaining_amount','used_amount','shipping_amount', 'zz_ton_price', 'doc_description', 'is_passive')
                ->leftJoin('entities as e', 'contracts.entity_id', '=', 'e.id')
                ->leftJoin('currencies as gc', 'contracts.cur_tra_id', '=', 'gc.id')
                ->whereIn('entity_id', $entity_ids)
                ->where('request_status', 4)
               ;

            if (request()->has('entity_id')) {
                $entity_id = request()->get('entity_id');
                if (!empty($entity_id)) {
                    $contracts->where('contracts.entity_id', $entity_id);
                }
            }
            if (request()->has('doc_tra_id')) {
                $doc_tra_id = request()->get('doc_tra_id');
                if (!empty($doc_tra_id)) {
                    $contracts->where('contracts.doc_tra_id', $doc_tra_id);
                }
            }
            if (request()->has('ispassive')) {
                $contracts->where('ispassive', request()->get('ispassive'));
            }

            if (!empty(request()->start_date) && !empty(request()->end_date)) {
                $start = date('Y-m-d', strtotime(request()->start_date));
                $end = date('Y-m-d', strtotime(request()->end_date));
                $contracts->whereDate('contract_start_date', '>=',  $start)->whereDate('contract_end_date', '<=',  $end);
            }
            return Datatables::of($contracts)
                ->addColumn(
                    'action', function ($row) use($user) {
                    $action = '<ul class="nk-tb-actions gx-1">
                                                                    <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                    <li><a data-href="sozlesme/' . encode_id($row->id) . '?action=contract_details" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-focus"></em><span>Hızlı Bakış</span></a></li>
                                                                                    <li><a data-href="sozlesme/' . encode_id($row->id) . '?action=orders" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-repeat"></em><span>Sözleşme Siparişleri</span></a></li>
                                                                                    <li><a data-href="sozlesme/' . encode_id($row->id) . '?action=actions" data-container="#modal_container" class="btn-modal"><em class="icon ni ni-activity-round"></em><span>İşlemler</span></a></li>
                                                                                    ';
                    if($user->can('update_contract_end_date')) {
                        $action .= '
<li class="divider"></li><li><a data-href="sozlesme/' . encode_id($row->id) . '?action=update_end_date" data-container="#modal_container" class="btn-modal"><em class="icon ni ni-calendar-check"></em><span>Bitiş Tarihi Güncelle</span></a></li>';
                        if($row->is_passive == 1) {
                            $action .= '
 <li><a href="#" data-id="' . encode_id($row->id) . '" class="active-passive"><em class="icon ni ni-check-circle"></em><span>Aktif Yap</span></a></li>';
                        } else {
                            $action .= '
 <li><a href="#" data-id="' . encode_id($row->id) . '" class="active-passive"><em class="icon ni ni-cross-circle"></em><span>Pasif Yap</span></a></li>';
                        }
                                                                                    }
                                                                                 $action .= '</ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                    return $action;
                })
                ->editColumn('doc_no', function ($row) {
                    if (empty($row->description))
                        return '<a href="/sozlesme/'.encode_id($row->id).'">'.$row->doc_no.'</a>';
                    else
                        return $row->doc_no . ' <i class="text-info ni ni-info-fill" data-bs-toggle="tooltip" title="' . $row->description . '"></i>';
                })
                ->editColumn('entity_name', function ($row) {
                    return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="' . $row->entity_name . '">' . limit_words($row->entity_name, 2) . '</span>';
                })
                ->editColumn('contract_start_date', function ($row) {
                    return date('d.m.Y', strtotime($row->contract_start_date));
                })
                ->editColumn('contract_end_date', function ($row) {
                    if ($row->contract_end_date->isPast()) {
                        return '<span class="tb-sub text-warning"><em class="icon ni ni-clock"></em><span data-bs-toggle="tooltip" data-bs-placement="top" title="' . date('d.m.Y', strtotime($row->contract_end_date)) . '">' . $row->contract_end_date->diffForhumans() . '</span></span>';
                    } else {
                        return '<span class="tb-sub text-primary"><em class="icon ni ni-clock"></em><span data-bs-toggle="tooltip" data-bs-placement="top" title="' . date('d.m.Y', strtotime($row->contract_end_date)) . '">' . $row->contract_end_date->diffForhumans() . '</span></span>';
                    }
                })
                ->editColumn('amt', function ($row) {
                    return Number::currency($row->amt, $row->cur_code, 'tr');
                })
                ->editColumn('due_day', function ($row) {
                    return ($row->due_day > 0) ? $row->due_day . ' gün' : '-';
                })
                ->editColumn('doc_description', function ($row) {
                    return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="' . nl2br($row->note_large) . '">' . $row->doc_description . '</span>';
                })
                ->editColumn('zz_ton_price', function ($row) {
                    return Number::currency($row->zz_ton_price, $row->cur_code, 'tr');
                })
                ->editColumn('shipping_amount', function ($row) {
                    return '<span class="badge badge-dim bg-warning" data-bs-toggle="tooltip" data-bs-placement="top""><span>' . Number::currency($row->shipping_amount, 'try', 'tr') . '</span></span>';

                }) ->editColumn('remaining_amount', function ($row) {
                    return '<span class="badge badge-dim bg-success" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="Kullanılan toplam tutar: ' . Number::currency($row->remaining_amount, 'TRY', 'tr') . '"><em class="icon ni ni-wallet-alt"></em><span>' . Number::currency($row->remaining_amount, 'try', 'tr') . '</span></span>';
                })
                ->editColumn('status_color', function ($row) {
                    if ($row->is_passive == 1) {
                        return '#dedede'; // dark gray
                    }

                })
                ->setRowAttr([
                    'data-href' => function ($row) {
                        return action([\App\Http\Controllers\Frontend\ContractController::class, 'show'], [$row->id]);
                    },])
                ->rawColumns(['action', 'shipping_amount','remaining_amount', 'contract_end_date', 'doc_no', 'doc_description', 'entity_name'])
                ->removeColumn('note_large')
                ->make(true);
        }


        $entities = Entity::whereIn('id', $entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.contract.index')->with(compact('entities'));
    }

    public function show($id)
    {
        $user = auth()->user();
        $id = decode_id($id);

        $contract = Contract::find($id);
        $action = request()->action;

        if($user->id == 1) {
            $data['details'] = $details = uyumapi('SElECT * FROM fint_form_contract_d where form_contract_m_id = '.$id.' and source_app = 1000');
        }

        if ($action == 'contract_details' or $action == 'actions') {
            $sql = 'SELECT qty, amt, amt_tra, doc_date, item_name from fint_form_contract_d 
                                  left join invd_item on fint_form_contract_d.item_id = invd_item.item_id
         where form_contract_m_id = ' . $id . ' ORDER BY doc_date DESC';
            $data['contractDetails'] = uyumapi($sql);
        }
        if ($action == 'orders') {
            $orders = Order::where('form_contract_m_id', $id)->get();
            $data['orders'] = $orders;
        }


        $used_percent = 0;
        if ($contract->used_amount>0) {
            $used_percent = ($contract->used_amount/$contract->amt)*100;
            $used_percent = number_format($used_percent,1);
        }

        $data['contract_order_total'] = $contract->orders()->sum('amt');
        $data['contract_order_count'] = $contract->orders()->count();
        $data['last_order_date'] = $contract->orders()->orderByDesc('doc_date')->value('doc_date');

        $data['used_percent'] = $used_percent;
        $data['contract'] = $contract;
        $data['action'] = $action;

        if (empty($action)) {
            return view('frontend.contract.show', $data);
        } else {
            return view('frontend.contract.modal.' . $action, $data);
        }

    }

    public function edit()
    {
        $all = request()->all();

        return $all;

    }

    public function update()
    {
        $all = request()->all();
        $action = request()->action;

        if($action == 'update_contract_end_date')
    {
        $id = request()->contract_id;
        $contract_end_date = date('d.m.Y', strtotime(request()->contract_end_date));
        $sql_contract_end_date =  $contract_end_date ? "TO_DATE('$contract_end_date', 'dd.mm.yyyy')" : 'NULL';
        $contract = Contract::find($id);
        $contract->contract_end_date = $contract_end_date;

        try {
            uyumapi("UPDATE FINT_FORM_CONTRACT_M SET CONTRACT_END_DATE = $sql_contract_end_date WHERE FORM_CONTRACT_M_ID = " . $id);
            uyumapi("UPDATE FINT_FORM_CONTRACT_TERM SET END_DATE = $sql_contract_end_date WHERE FORM_CONTRACT_M_ID = ".$id);

        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => 'Sözleşme bitiş tarihi güncellenemedi.']);
        }

        $contract->save();

        return response()->json(['success' => true, 'message' => 'Sözleşme bitiş tarihi  '. $contract_end_date .' olarak güncellendi.']);
    }

        if($action == 'active_passive_contract')
        {
            $id = decode_id(request()->id);
            $contract = Contract::find($id);
            $is_passive = $contract->is_passive;
            $is_passive = $is_passive == 1 ? 0 : 1;
            $contract->is_passive = $is_passive;
            $contract->save();
            return response()->json(['success' => true, 'message' => 'Sözleşme '.($is_passive == 1 ? 'pasif' : 'aktif').' hale getirildi.']);
        }

        return $all;

    }



}
