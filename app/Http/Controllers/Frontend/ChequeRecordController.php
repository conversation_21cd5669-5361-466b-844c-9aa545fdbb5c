<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Ad;

use App\Category;
use App\Currency;
use App\Media;
use App\Models\Bank;
use App\Models\ChequeRecords;
use App\Contact;
use App\Notifications\SendMessage;
use App\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\DB;

class ChequeRecordController extends Controller
{


    public function index(Request $request)
    {
        $business_id = request()->session()->get('user.business_id');
        $auth_id = auth()->user()->id;

        $is_admin = false;
        $category = Category::where('slug', 'cekKayitlariYonetim')->first();
        $admins = $category->users()->pluck('id')->toArray();
        if (in_array($auth_id, $admins)) {
            $is_admin = true;
        }

        // $is_admin = $this->moduleUtil->is_admin(auth()->user(), $business_id);

        if (request()->ajax()) {

            // delete document
            if (request()->action == 'destroyMedia') {
                try {

                    $cheque_id = Media::where('business_id', $business_id)->where('id', request()->media_id)->value('model_id');
                    Media::deleteMedia($business_id, request()->media_id);

                    $output = [
                        'url' => action('App\Http\Controllers\Frontend\ChequeRecordController@edit', [$cheque_id]),
                        'success' => true,
                        'msg' => __("lang_v1.success")
                    ];
                } catch (\Exception $e) {
                    $output = ['success' => false,
                        'msg' => __('messages.something_went_wrong')
                    ];
                }

                return $output;
            }

            $cheques = ChequeRecords::where('business_id', $business_id);

            if ($is_admin) {
                if (!empty(request()->input('user_id'))) {
                    $cheques->where('user_id', request()->input('user_id'));
                }
            } else {
                $cheques->where('user_id', $auth_id);
            }
            if (!empty(request()->input('bank_id'))) {
                $cheques->where('bank_id', request()->input('bank_id'));
            }
            if (!empty(request()->input('cargo_at'))) {
                if (request()->input('cargo_at') == 'waiting') {
                    $cheques->whereNull('cargo_tracking_number');
                } elseif (request()->input('cargo_at') == 'gone') {
                    $cheques->whereNotNull('cargo_tracking_number');
                }
            }
            if (!empty(request()->start_date) && !empty(request()->end_date)) {
                $start = date('Y-m-d', strtotime(request()->start_date));
                $end = date('Y-m-d', strtotime(request()->end_date));
                $cheques->whereDate('cheque_records.due_date', '>=', $start)
                    ->whereDate('cheque_records.due_date', '<=', $end);
            }
            $cheques->get();

            return Datatables::of($cheques)
                ->addColumn(
                    'action',
                    function ($row) {
                        $html = ' <a class="btn btn-info btn-xs btn-modal" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@edit', [$row->id]) . '" data-container="#modal_container"><i class="fa fa-edit"></i></i> Düzenle</a>';
                        $html .= '<a href="#" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@show', [$row->id]) . '" class="btn btn-primary btn-xs btn-modal" data-container="#modal_container"><i class="fa fa-eye"></i> Göster</a>';
                        return $html . '</div>';
                    }
                )
                ->editColumn('user_id', function ($row) {
                    $user = User::select('first_name', 'last_name')->where('id', $row->user_id)->first();
                    return $user->first_name . ' ' . $user->last_name;
                })
                ->editColumn('amount', function ($row) {
                    $currency = Currency::where('id', $row->currency_id)->value('code');
                    return Number::currency($row->amount,$currency->cur_code,app()->getLocale());

                })
                ->editColumn('bank_id', function ($row) {
                    $bank_id = Bank::where('id', $row->bank_id)->value('name');
                    return $bank_id;
                })

                ->editColumn('cargo', function ($row) use ($is_admin) {
                    $html = '';
                    if (!empty($row->cargo_tracking_number)) {
                        if ($row->delivery_at) {
                            $html .= ' <a class="btn btn-default" disabled data-href="#"><i class="fa fa-check-double"></i></i> Teslim Alındı</a>';
                        } else {

                            $html .= ' <a class="btn btn-success btn-modal" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@edit', [$row->id]) . '?section=cargo" data-container="#modal_container"><i class="fa fa-envelope"></i></i> Kargolandı</a>';
                            if ($is_admin) {
                                $html .= ' <a class="btn btn-default btn-delivered" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@destroy', [$row->id]) . '"><i class="fa fa-check"></i></i> Teslim Aldım</a>';
                            }
                        }

                    } elseif (!empty($row->person_delivered)) {

                        if ($row->delivery_at) {
                            $html .= ' <a class="btn btn-default" disabled data-href="#"><i class="fa fa-check-double"></i></i> Teslim Alındı</a>';
                        } else {
                            $html .= ' <a class="btn btn-primary btn-modal" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@edit', [$row->id]) . '?section=cargo" data-container="#modal_container"><i class="fa fa-handshake"></i></i> Elden Teslim</a>';
                            if ($is_admin) {
                                $html .= ' <a class="btn btn-default btn-destroy-media" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@destroy', [$row->id]) . '?section=cargo" data-container="#modal_container"><i class="fa fa-check-double"></i></i> Teslim Aldım</a>';
                            }
                        }

                    } else {
                        $html .= ' <a class="btn btn-warning btn-modal" data-href="' . action('App\Http\Controllers\Frontend\ChequeRecordController@edit', [$row->id]) . '?section=cargo" data-container="#modal_container"><i class="fa fa-hourglass"></i></i> Beklemede</a>';
                    }
                    return $html;
                })
                ->rawColumns(['action', 'cargo'])
                ->removeColumn('business_id')
                ->make(true);
        }

        if ($is_admin) {
            $ids = ChequeRecords::distinct('user_id')->pluck('user_id');

            $all_users = User::select('id', DB::raw("CONCAT(COALESCE(surname, ''),' ',COALESCE(first_name, ''),' ',COALESCE(last_name,'')) as full_name"))->whereIn('id',$ids)->get();
            $users = $all_users->pluck('full_name', 'id');
        } else {
            $users = [$auth_id => 'Kendi Kayıtlarım'];
        }

        $load_modal = $request->input('load_modal', null);
        if ($load_modal) {
            $load_modal = action('App\Http\Controllers\Frontend\ChequeRecordController@create');
        }
        $banks = Bank::orderBy('name')->pluck('name', 'id');

        return view('cheque_records.index')->with(compact('users', 'is_admin', 'load_modal', 'banks'));


    }

    public function create()
    {
        $business_id = request()->session()->get('user.business_id');
        $banks = Bank::orderBy('bank_desc')->pluck('bank_desc as name', 'id');

        return view('frontend.cheque_records.create')->with(compact('banks'));

    }

    public function store(Request $request)
    {
        try {
            $input = request()->only(
                'customer_name',
                'amount',
                'currency_id',
                'date',
                'due_date',
                'description',
                'contract_no',
                'bank_id'
            );
            $business_id = request()->session()->get('user.business_id');

            $input['user_id'] = request()->session()->get('user.id');
            $input['business_id'] = $business_id;
            if (empty($input['date']) or empty($input['due_date'])) {
                return ['success' => 0,
                    'msg' => 'Çek tarihi ve vade tarihi alanları zorunludur'
                ];
            }
            $input['date'] = date('Y-m-d', strtotime($input['date']));
            $input['due_date'] = date('Y-m-d', strtotime($input['due_date']));

            $cheque = ChequeRecords::create($input);

            Media::uploadMedia($business_id, $cheque, request(), 'documents', false, 'cheque_document');

            $output = ['success' => 1,
                'msg' => 'Çek kaydı oluşturuldu'
            ];

        } catch (\Exception $e) {
            \Log::emergency("File:" . $e->getFile() . "Satır:" . $e->getLine() . "Mesaj:" . $e->getMessage());

            $output = ['success' => 0,
                'msg' => __('messages.something_went_wrong')
            ];
        }

        return $output;

    }

    public function edit($id)
    {
        $section = request()->get('section', 'edit');
        $cheque = ChequeRecords::find($id);
        if ($cheque->delivery_at) {
            $section = 'contract_no';
        }
        $auth_id = auth()->user()->id; $can_update = false;
        if ($auth_id === $cheque->user_id) {
            $can_update = true;
        }
        $banks = Bank::orderBy('name')->pluck('name', 'id');

        return view('cheque_records.modal.edit')->with(compact('cheque', 'banks', 'section', 'can_update'));
    }

    public function update($id)
    {
        try {
            $business_id = request()->session()->get('user.business_id');
            $cheque = ChequeRecords::where('business_id', $business_id)->where('id', $id)->first();

            if (request()->has('cargo')) {
                $input = request()->only(
                    'cargo_firm',
                    'cargo_tracking_number',
                    'person_delivered',
                    'cargo_at'
                );

                if (!empty($input['cargo_firm']) && !empty($input['person_delivered'])) {
                    return ['success' => 0,
                        'msg' => 'Çek teslim bilgisi olarak, kargo ile teslim veya elden teslim alanlarından sadece birini doldurunuz.'
                    ];
                }
                if (!empty($input['person_delivered']) && empty($input['cargo_at'])) {
                    return ['success' => 0,
                        'msg' => 'Elden teslim tarihi seçiniz.'
                    ];
                }
                if (!empty($input['cargo_firm']) && empty($input['cargo_tracking_number'])) {
                    return ['success' => 0,
                        'msg' => 'Kargo takip no giriniz.'
                    ];
                }


                if (!empty($input['cargo_at'])) {
                    $input['cargo_at'] = date('Y-m-d',strtotime($input['cargo_at']));
                } else {
                    $input['cargo_at'] = now();
                }

                $category = Category::where('business_id', $business_id)->where('slug', 'finansCekMailGrubu')->first();
                if ($category->users()->exists()) {
                    $message = [
                        'greeting' => 'Merhaba,',
                        'subject' => $cheque->customer_name . ' Çeki Teslim Bilgisi',
                        'body' => $cheque->customer_name . ' carisine ait ' . Number::currency($cheque->amount,$cheque->currency->code,app()->getLocale()) .' tutarlı çek için, ' . auth()->user()->user_full_name . ' tarafından teslim işlemi başlatıldı. Çeki teslim aldıktan sonra portal üzerinden "Teslim aldım" olarak işleyiniz. <br><br>',
                        'thanks' => ' ',
                        'actionText' => 'Çek Detayları',
                        'actionURL' => action('App\Http\Controllers\Frontend\ChequeRecordController@index'),
                    ];
                    Notification::send($category->users, new SendMessage($message));
                }
                $msg = 'Çek için teslim bilgisi güncellendi';
            } elseif (request()->has('only_contract_no')) {
                $input = request()->only(
                    'contract_no'
                );
            }
            else {
                $input = request()->only(
                    'customer_name',
                    'amount',
                    'currency_id',
                    'date',
                    'due_date',
                    'description',
                    'contract_no',
                    'bank_id'
                );
                $input['user_id'] = request()->session()->get('user.id');
                $input['business_id'] = $business_id;
                $input['date'] = date('Y-m-d', strtotime($input['date']));
                $input['due_date'] = date('Y-m-d', strtotime($input['due_date']));
                Media::uploadMedia($business_id, $cheque, request(), 'documents', false, 'cheque_document');
                $msg = 'Çek bilgisi güncellendi';
            }

            $cheque->update($input);


            $output = ['success' => 1,
                'msg' => $msg
            ];

        } catch (\Exception $e) {
            \Log::emergency("File:" . $e->getFile() . "Satır:" . $e->getLine() . "Mesaj:" . $e->getMessage());

            $output = ['success' => 0,
                'msg' => __('messages.something_went_wrong')
            ];
        }

        return $output;

    }

    public function show($id)
    {
        $cheque = ChequeRecords::find($id);

        return view('cheque_records.modal.show')->with(compact('cheque'));
    }


    public function destroy($id)
    {
        $business_id = request()->session()->get('user.business_id');
        $completed_by = request()->session()->get('user.id');

        if (request()->ajax()) {
            try {

                $cheque = ChequeRecords::where('business_id', $business_id)->where('id', $id)->first();
                $cheque->update(['delivery_at' => now(), 'completed_by' => $completed_by]);

                $user = User::find($cheque->user_id);
                $message = [
                    'greeting' => 'Merhaba,',
                    'subject' => $cheque->customer_name . ' Çeki Teslim Bilgisi',
                    'body' => date('d.m.Y', strtotime($cheque->cargo_at)) . ' tarihinde gönderdiğiniz '.$cheque->customer_name. ' carisine ait çek teslim alındı. <br><br>',
                    'thanks' => ' ',
                    'actionText' => 'Çek Detayları',
                    'actionURL' => action('App\Http\Controllers\Frontend\ChequeRecordController@index'),
                ];
                Notification::send($user, new SendMessage($message));

                $output = [
                    'success' => true,
                    'msg' => __('lang_v1.success')
                ];
            } catch (Exception $e) {
                \Log::emergency("File:" . $e->getFile() . "Satır:" . $e->getLine() . "Mesaj:" . $e->getMessage());

                $output = [
                    'success' => false,
                    'msg' => __('messages.something_went_wrong')
                ];
            }

            return $output;
        }
    }


}
