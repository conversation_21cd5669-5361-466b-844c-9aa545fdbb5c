<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\App;

class FinancialReportController extends Controller
{
    /**
     * Display payment methods analysis report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function paymentMethodsAnalysis(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $entityId = $request->input('entity_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get entities for the dropdown filter
        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Build the query for payment methods analysis
        $query = DB::table('payments')
            ->join('payment_transactions', 'payments.id', '=', 'payment_transactions.payment_id')
            ->join('payment_methods', 'payments.payment_type', '=', 'payment_methods.id')
            ->leftJoin('entities', 'payments.entity_id', '=', 'entities.id')
            ->select(
                'payment_methods.id as payment_method_id',
                'payment_methods.description as payment_method',
                DB::raw('COUNT(payment_transactions.id) as transaction_count'),
                DB::raw('SUM(payment_transactions.amount) as total_amount'),
                DB::raw('AVG(payment_transactions.amount) as average_amount')
            )
            ->whereYear('payments.created_at', $year);

        // Apply additional filters if specified
        if ($month) {
            $query->whereMonth('payments.created_at', $month);
        }

        if ($entityId) {
            $query->where('payments.entity_id', $entityId);
        }

        // Group and order the results
        $paymentData = $query
            ->groupBy('payment_methods.id', 'payment_methods.description')
            ->orderBy('total_amount', 'desc')
            ->get();

        // Calculate totals
        $total_count = $paymentData->sum('transaction_count');
        $total_amount = $paymentData->sum('total_amount');

        $totals = [
            'transaction_count' => $total_count,
            'total_amount' => $total_amount,
            'average_amount' => $total_count > 0 ? $total_amount / $total_count : 0
        ];

        // Get monthly trend data for chart
        $monthlyTrend = DB::table('payments')
            ->join('payment_transactions', 'payments.id', '=', 'payment_transactions.payment_id')
            ->select(
                DB::raw('MONTH(payments.created_at) as month'),
                DB::raw('SUM(payment_transactions.amount) as total_amount')
            )
            ->whereYear('payments.created_at', $year)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $monthlyTrendData = [];
        foreach ($monthlyTrend as $item) {
            $monthName = Carbon::create()->locale(App::getLocale())->month($item->month)->format('F');
            $monthlyTrendData[] = [
                'month' => $monthName,
                'total_amount' => $item->total_amount
            ];
        }

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Ödeme Metodları Analizi';
            $fileName = 'odeme_metodlari_analizi_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $paymentData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.payment-methods-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $paymentData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'payment'
                );
            }
        }

        return view('frontend.reports.payment-methods-analysis', compact(
            'paymentData',
            'totals',
            'years',
            'year',
            'month',
            'entities',
            'entityId',
            'monthlyTrendData'
        ));
    }

    /**
     * Display contract usage report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function contractUsageReport(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $entityId = $request->input('entity_id');
        $salesPersonId = $request->input('sales_person_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('contracts')
            ->selectRaw('YEAR(contract_start_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get entities for the dropdown filter
        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Get sales representatives for the dropdown filter
        $salesPeople = DB::table('sales_people')
            ->select('id', 'first_name', 'sales_person_code')
            ->where('ispassive', 0)
            ->orderBy('first_name')
            ->get();

        // Build the query for contract usage report
        $query = DB::table('contracts')
            ->join('entities', 'contracts.entity_id', '=', 'entities.id')
            ->leftJoin('sales_people', 'contracts.sales_person_id', '=', 'sales_people.id')
            ->select(
                'contracts.id',
                'contracts.doc_no',
                'contracts.entity_id',
                'entities.entity_name',
                'contracts.doc_date',
                'contracts.contract_start_date',
                'contracts.contract_end_date',
                'contracts.amt as total_amount',
                'contracts.used_amount',
                'contracts.remaining_amount',
                'sales_people.first_name as sales_person_name'
            )
            ->whereYear('contracts.contract_start_date', $year)
            ->orWhereYear('contracts.contract_end_date', $year);

        // Apply additional filters if specified
        if ($entityId) {
            $query->where('contracts.entity_id', $entityId);
        }

        if ($salesPersonId) {
            $query->where('contracts.sales_person_id', $salesPersonId);
        }

        // Order the results
        $contractData = $query
            ->orderBy('contracts.contract_end_date', 'desc')
            ->get();

        // Prepare data for charts and additional analysis
        $contractData->map(function ($contract) {
            // Calculate usage percentage
            $contract->usage_percentage = $contract->total_amount > 0
                ? round(($contract->used_amount / $contract->total_amount) * 100, 2)
                : 0;

            // Calculate days remaining
            $endDate = Carbon::parse($contract->contract_end_date);
            $today = Carbon::today();
            $contract->days_remaining = $today->lte($endDate) ? $today->diffInDays($endDate) : 0;

            // Calculate contract status
            if ($today->gt($endDate)) {
                $contract->status = 'Sona Erdi';
                $contract->status_class = 'danger';
            } elseif ($contract->usage_percentage >= 90) {
                $contract->status = 'Kritik';
                $contract->status_class = 'warning';
            } else {
                $contract->status = 'Aktif';
                $contract->status_class = 'success';
            }

            return $contract;
        });

        // Calculate totals
        $total_amount = $contractData->sum('total_amount');
        $used_amount = $contractData->sum('used_amount');
        $remaining_amount = $contractData->sum('remaining_amount');

        $totals = [
            'total_amount' => $total_amount,
            'used_amount' => $used_amount,
            'remaining_amount' => $remaining_amount,
            'average_usage' => $total_amount > 0
                ? round(($used_amount / $total_amount) * 100, 2)
                : 0
        ];

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Sözleşme Kullanım Raporu';
            $fileName = 'sozlesme_kullanim_raporu_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $contractData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.contract-usage-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $contractData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'contract'
                );
            }
        }

        return view('frontend.reports.contract-usage', compact(
            'contractData',
            'totals',
            'years',
            'year',
            'entities',
            'entityId',
            'salesPeople',
            'salesPersonId'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName, $view)
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType The type of report ('payment' or 'contract')
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName, $reportType)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'payment') {
            $sheet->mergeCells('A1:E1');

            // Set headers
            $sheet->setCellValue('A3', 'Ödeme Metodu');
            $sheet->setCellValue('B3', 'İşlem Sayısı');
            $sheet->setCellValue('C3', 'Toplam Tutar (₺)');
            $sheet->setCellValue('D3', 'Ortalama Tutar (₺)');
            $sheet->setCellValue('E3', 'Yüzde (%)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $percentage = $totals['total_amount'] > 0
                    ? round(($data->total_amount / $totals['total_amount']) * 100, 2)
                    : 0;

                $sheet->setCellValue('A' . $row, $data->payment_method);
                $sheet->setCellValue('B' . $row, $data->transaction_count);
                $sheet->setCellValue('C' . $row, $data->total_amount);
                $sheet->setCellValue('D' . $row, $data->average_amount);
                $sheet->setCellValue('E' . $row, $percentage);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->setCellValue('B' . $row, $totals['transaction_count']);
            $sheet->setCellValue('C' . $row, $totals['total_amount']);
            $sheet->setCellValue('D' . $row, $totals['average_amount']);
            $sheet->setCellValue('E' . $row, '100.00');

            // Format numbers
            $sheet->getStyle('C4:D' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');
            $sheet->getStyle('E4:E' . $row)->getNumberFormat()->setFormatCode('#,##0.00 %');

            // Auto-size columns
            foreach (range('A', 'E') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

        } elseif ($reportType === 'contract') {
            $sheet->mergeCells('A1:I1');

            // Set headers
            $sheet->setCellValue('A3', 'Sözleşme No');
            $sheet->setCellValue('B3', 'Müşteri');
            $sheet->setCellValue('C3', 'Başlangıç Tarihi');
            $sheet->setCellValue('D3', 'Bitiş Tarihi');
            $sheet->setCellValue('E3', 'Toplam Tutar (₺)');
            $sheet->setCellValue('F3', 'Kullanılan Tutar (₺)');
            $sheet->setCellValue('G3', 'Kalan Tutar (₺)');
            $sheet->setCellValue('H3', 'Kullanım Oranı (%)');
            $sheet->setCellValue('I3', 'Satış Temsilcisi');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data->doc_no);
                $sheet->setCellValue('B' . $row, $data->entity_name);
                $sheet->setCellValue('C' . $row, $data->contract_start_date);
                $sheet->setCellValue('D' . $row, $data->contract_end_date);
                $sheet->setCellValue('E' . $row, $data->total_amount);
                $sheet->setCellValue('F' . $row, $data->used_amount);
                $sheet->setCellValue('G' . $row, $data->remaining_amount);
                $sheet->setCellValue('H' . $row, $data->usage_percentage);
                $sheet->setCellValue('I' . $row, $data->sales_person_name);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->mergeCells('A' . $row . ':D' . $row);
            $sheet->setCellValue('E' . $row, $totals['total_amount']);
            $sheet->setCellValue('F' . $row, $totals['used_amount']);
            $sheet->setCellValue('G' . $row, $totals['remaining_amount']);
            $sheet->setCellValue('H' . $row, $totals['average_usage']);

            // Format date columns
            $sheet->getStyle('C4:D' . ($row - 1))->getNumberFormat()->setFormatCode('yyyy-mm-dd');

            // Format number columns
            $sheet->getStyle('E4:G' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');
            $sheet->getStyle('H4:H' . $row)->getNumberFormat()->setFormatCode('#,##0.00 %');

            // Auto-size columns
            foreach (range('A', 'I') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }

        // Style common elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:' . ($reportType === 'payment' ? 'E' : 'I') . '3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':' . ($reportType === 'payment' ? 'E' : 'I') . $row)->getFont()->setBold(true);

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}