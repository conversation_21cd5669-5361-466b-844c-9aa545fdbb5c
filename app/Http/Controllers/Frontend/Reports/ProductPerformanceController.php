<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\App;

class ProductPerformanceController extends Controller
{
    /**
     * Display the product performance report index page
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {

        return view('frontend.reports.product-performance.index');
    }

    /**
     * Display the top selling products report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function topSellingProducts(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = (int) $request->input('month');

        $categoryId = $request->input('category_id');
        $limit = $request->input('limit', 20);
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        $categories = DB::table('categories')
            ->select('id', 'description')
            ->where('is_active', 1)
            ->orderBy('description')
            ->get();

        // Build the query for top selling products
        $query = DB::table('orders_d')
            ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
            ->join('products', 'orders_d.item_id', '=', 'products.id')
            ->leftJoin('categories', 'products.categories1_id', '=', 'categories.id')
            ->leftJoin('brands', 'products.brand_id', '=', 'brands.id')
            ->select(
                'products.id',
                'products.item_code',
                'products.item_name',
                'categories.description as category_name',
                'brands.description as brand_name',
                DB::raw('SUM(orders_d.qty) as total_quantity'),
                DB::raw('SUM(orders_d.amt) as total_amount'),
                DB::raw('SUM(orders_d.amt_vat) as total_vat'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            )
            ->whereYear('orders.doc_date', $year);

        // Apply month filter if specified
        if ($month) {
            $query->whereMonth('orders.doc_date', $month);
        }

        // Apply category filter if specified
        if ($categoryId) {
            $query->where('products.categories1_id', $categoryId);
        }

        // Group and order the results
        $topSellingProducts = $query->groupBy(
            'products.id',
            'products.item_code',
            'products.item_name',
            'categories.description',
            'brands.description'
        )
            ->orderBy('total_amount', 'desc')
            ->limit($limit)
            ->get();

        // Calculate totals
        $totals = [
            'total_quantity' => $topSellingProducts->sum('total_quantity'),
            'total_amount' => $topSellingProducts->sum('total_amount'),
            'total_vat' => $topSellingProducts->sum('total_vat'),
            'order_count' => $topSellingProducts->sum('order_count')
        ];

        // Get monthly trend data for charts (if a specific product is selected)
        $monthlyTrendData = [];
        $selectedProductId = $request->input('product_id');

        if ($selectedProductId) {
            $monthlyTrend = DB::table('orders_d')
                ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                ->select(
                    DB::raw('MONTH(orders.doc_date) as month'),
                    DB::raw('SUM(orders_d.qty) as total_quantity'),
                    DB::raw('SUM(orders_d.amt) as total_amount')
                )
                ->where('orders_d.item_id', $selectedProductId)
                ->whereYear('orders.doc_date', $year)
                ->groupBy('month')
                ->orderBy('month')
                ->get();
            foreach ($monthlyTrend as $item) {

                $monthName = Carbon::create()->locale(App::getLocale())->month($item->month)->translatedFormat('F');$monthlyTrendData[] = [
                    'month' => $monthName,
                    'total_quantity' => $item->total_quantity,
                    'total_amount' => $item->total_amount
                ];
            }
        }

        // Handle exports if requested
        if ($export) {
            $title = 'En Çok Satan Ürünler - ' . $year;
            if ($month) {
                $title .= ' ' . Carbon::create()->month($month)->format('F');
            }

            $fileName = 'en_cok_satan_urunler_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $topSellingProducts->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.product-performance.exports.top-selling-products-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $topSellingProducts->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'top-selling'
                );
            }
        }

        return view('frontend.reports.product-performance.top-selling-products', compact(
            'topSellingProducts',
            'totals',
            'years',
            'year',
            'month',
            'categories',
            'categoryId',
            'limit',
            'selectedProductId',
            'monthlyTrendData'
        ));
    }

    /**
     * Display the category-based sales report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function categorySales(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $quarter = $request->input('quarter');
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Build the query for category sales
        $query = DB::table('orders_d')
            ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
            ->join('products', 'orders_d.item_id', '=', 'products.id')
            ->leftJoin('categories', 'products.categories1_id', '=', 'categories.id')
            ->select(
                'categories.id as category_id',
                'categories.description as category_name',
                DB::raw('COUNT(DISTINCT products.id) as product_count'),
                DB::raw('SUM(orders_d.qty) as total_quantity'),
                DB::raw('SUM(orders_d.amt) as total_amount'),
                DB::raw('SUM(orders_d.amt_vat) as total_vat'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            )
            ->whereYear('orders.doc_date', $year);

        // Apply quarter filter if specified
        if ($quarter) {
            $startMonth = ($quarter - 1) * 3 + 1;
            $endMonth = $quarter * 3;
            $query->whereRaw('MONTH(orders.doc_date) BETWEEN ? AND ?', [$startMonth, $endMonth]);
        }

        // Group and order the results
        $categorySales = $query->groupBy(
            'categories.id',
            'categories.description'
        )
            ->orderBy('total_amount', 'desc')
            ->get();

        // Calculate totals
        $totals = [
            'product_count' => $categorySales->sum('product_count'),
            'total_quantity' => $categorySales->sum('total_quantity'),
            'total_amount' => $categorySales->sum('total_amount'),
            'total_vat' => $categorySales->sum('total_vat'),
            'order_count' => $categorySales->sum('order_count')
        ];

        // Get quarterly trend data for charts (for all categories)
        $quarterlyTrendData = [];

        // Q1, Q2, Q3, Q4 trend data
        for ($q = 1; $q <= 4; $q++) {
            $startMonth = ($q - 1) * 3 + 1;
            $endMonth = $q * 3;

            $quarterSales = DB::table('orders_d')
                ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                ->join('products', 'orders_d.item_id', '=', 'products.id')
                ->whereYear('orders.doc_date', $year)
                ->whereRaw('MONTH(orders.doc_date) BETWEEN ? AND ?', [$startMonth, $endMonth])
                ->select(DB::raw('SUM(orders_d.amt) as total_amount'))
                ->first();

            $quarterlyTrendData[] = [
                'quarter' => 'Q' . $q,
                'total_amount' => $quarterSales->total_amount ?? 0
            ];
        }

        // Get month-by-month breakdown data for the selected category
        $monthlyBreakdown = [];
        $selectedCategoryId = $request->input('category_id');

        if ($selectedCategoryId) {
            for ($m = 1; $m <= 12; $m++) {
                $monthlySales = DB::table('orders_d')
                    ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                    ->join('products', 'orders_d.item_id', '=', 'products.id')
                    ->where('products.categories1_id', $selectedCategoryId)
                    ->whereYear('orders.doc_date', $year)
                    ->whereMonth('orders.doc_date', $m)
                    ->select(DB::raw('SUM(orders_d.amt) as total_amount'))
                    ->first();

                $monthlyBreakdown[] = [
                    'month' => Carbon::create()->month($m)->format('F'),
                    'total_amount' => $monthlySales->total_amount ?? 0
                ];
            }
        }

        // Handle exports if requested
        if ($export) {
            $title = 'Kategori Bazlı Satış Raporu - ' . $year;
            if ($quarter) {
                $title .= ' Q' . $quarter;
            }

            $fileName = 'kategori_bazli_satis_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $categorySales->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.product-performance.exports.category-sales-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $categorySales->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'category'
                );
            }
        }

        return view('frontend.reports.product-performance.category-sales', compact(
            'categorySales',
            'totals',
            'years',
            'year',
            'quarter',
            'quarterlyTrendData',
            'selectedCategoryId',
            'monthlyBreakdown'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName, $view)
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType The type of report ('top-selling' or 'category')
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName, $reportType)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'top-selling') {
            $sheet->mergeCells('A1:H1');

            // Set headers
            $sheet->setCellValue('A3', 'Ürün Kodu');
            $sheet->setCellValue('B3', 'Ürün Adı');
            $sheet->setCellValue('C3', 'Kategori');
            $sheet->setCellValue('D3', 'Marka');
            $sheet->setCellValue('E3', 'Sipariş Sayısı');
            $sheet->setCellValue('F3', 'Satış Miktarı');
            $sheet->setCellValue('G3', 'Satış Tutarı (₺)');
            $sheet->setCellValue('H3', 'KDV Tutarı (₺)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data->item_code);
                $sheet->setCellValue('B' . $row, $data->item_name);
                $sheet->setCellValue('C' . $row, $data->category_name ?? '-');
                $sheet->setCellValue('D' . $row, $data->brand_name ?? '-');
                $sheet->setCellValue('E' . $row, $data->order_count);
                $sheet->setCellValue('F' . $row, $data->total_quantity);
                $sheet->setCellValue('G' . $row, $data->total_amount);
                $sheet->setCellValue('H' . $row, $data->total_vat);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->mergeCells('A' . $row . ':D' . $row);
            $sheet->setCellValue('E' . $row, $totals['order_count']);
            $sheet->setCellValue('F' . $row, $totals['total_quantity']);
            $sheet->setCellValue('G' . $row, $totals['total_amount']);
            $sheet->setCellValue('H' . $row, $totals['total_vat']);

            // Format numbers
            $sheet->getStyle('F4:F' . $row)->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle('G4:H' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

            // Auto-size columns
            foreach (range('A', 'H') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        } elseif ($reportType === 'category') {
            $sheet->mergeCells('A1:G1');

            // Set headers
            $sheet->setCellValue('A3', 'Kategori');
            $sheet->setCellValue('B3', 'Ürün Sayısı');
            $sheet->setCellValue('C3', 'Sipariş Sayısı');
            $sheet->setCellValue('D3', 'Satış Miktarı');
            $sheet->setCellValue('E3', 'Satış Tutarı (₺)');
            $sheet->setCellValue('F3', 'KDV Tutarı (₺)');
            $sheet->setCellValue('G3', 'Toplam Tutar (₺)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data->category_name ?? 'Kategorisiz');
                $sheet->setCellValue('B' . $row, $data->product_count);
                $sheet->setCellValue('C' . $row, $data->order_count);
                $sheet->setCellValue('D' . $row, $data->total_quantity);
                $sheet->setCellValue('E' . $row, $data->total_amount);
                $sheet->setCellValue('F' . $row, $data->total_vat);
                $sheet->setCellValue('G' . $row, $data->total_amount + $data->total_vat);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->setCellValue('B' . $row, $totals['product_count']);
            $sheet->setCellValue('C' . $row, $totals['order_count']);
            $sheet->setCellValue('D' . $row, $totals['total_quantity']);
            $sheet->setCellValue('E' . $row, $totals['total_amount']);
            $sheet->setCellValue('F' . $row, $totals['total_vat']);
            $sheet->setCellValue('G' . $row, $totals['total_amount'] + $totals['total_vat']);

            // Format numbers
            $sheet->getStyle('D4:D' . $row)->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle('E4:G' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

            // Auto-size columns
            foreach (range('A', 'G') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }

        // Style common elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);

        if ($reportType === 'top-selling') {
            $sheet->getStyle('A3:H3')->getFont()->setBold(true);
            $sheet->getStyle('A' . $row . ':H' . $row)->getFont()->setBold(true);
        } else {
            $sheet->getStyle('A3:G3')->getFont()->setBold(true);
            $sheet->getStyle('A' . $row . ':G' . $row)->getFont()->setBold(true);
        }

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}