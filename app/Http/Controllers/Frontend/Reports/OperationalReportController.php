<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Spatie\LaravelPdf\Facades\Pdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\App;

class OperationalReportController extends Controller
{
    /**
     * Show the order status summary report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function orderStatusSummary(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $salesRepId = $request->input('sales_person_id');
        $entityId = $request->input('entity_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        $salesReps = DB::table('sales_people')
            ->select('id', 'first_name', 'sales_person_code')
            ->where('ispassive', 0)
            ->orderBy('first_name')
            ->get();

        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Build the query for order status summary
        $query = DB::table('orders')
            ->select(
                DB::raw('YEAR(doc_date) as year'),
                DB::raw('MONTH(doc_date) as month'),
                DB::raw('order_status as status'),
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(amt) as total_amount'),
                DB::raw('SUM(amt_vat) as total_vat')
            )
            ->whereYear('doc_date', $year);

        // Apply filters
        if ($month) {
            $query->whereMonth('doc_date', $month);
        }

        if ($salesRepId) {
            $query->where('sales_person_id', $salesRepId);
        }

        if ($entityId) {
            $query->where('entity_id', $entityId);
        }

        // Group and order the results
        $orderStatusData = $query
            ->groupBy('year', 'month', 'status')
            ->orderBy('year')
            ->orderBy('month')
            ->orderBy('status')
            ->get();

        // Transform the data for the view
        $monthlyData = [];
        $statusCounts = [
            0 => 0, // Bekleyen/Onay Bekliyor
            1 => 0, // Onaylandı
            2 => 0, // Kısmen Sevk Edildi
            3 => 0, // Tamamen Sevk Edildi
            4 => 0, // İptal Edildi
            5 => 0  // Reddedildi
        ];

        $statusLabels = [
            0 => 'Bekleyen',
            1 => 'Onaylandı',
            2 => 'Kısmen Sevk Edildi',
            3 => 'Tamamen Sevk Edildi',
            4 => 'İptal Edildi',
            5 => 'Reddedildi'
        ];

        $totalAmounts = [
            'order_count' => 0,
            'total_amount' => 0,
            'total_vat' => 0
        ];

        foreach ($orderStatusData as $item) {
            $monthKey = $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT);
            $monthName = Carbon::createFromDate($item->year, $item->month, 1)->locale(App::getLocale())->format('F Y');

            if (!isset($monthlyData[$monthKey])) {
                $monthlyData[$monthKey] = [
                    'month_name' => $monthName,
                    'statuses' => array_fill_keys(array_keys($statusLabels), 0),
                    'total_orders' => 0,
                    'total_amount' => 0,
                    'total_vat' => 0
                ];
            }

            $statusKey = isset($item->status) ? $item->status : 0;
            $monthlyData[$monthKey]['statuses'][$statusKey] = $item->order_count;
            $monthlyData[$monthKey]['total_orders'] += $item->order_count;
            $monthlyData[$monthKey]['total_amount'] += $item->total_amount;
            $monthlyData[$monthKey]['total_vat'] += $item->total_vat;

            $statusCounts[$statusKey] += $item->order_count;
            $totalAmounts['order_count'] += $item->order_count;
            $totalAmounts['total_amount'] += $item->total_amount;
            $totalAmounts['total_vat'] += $item->total_vat;
        }

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Sipariş Durumu Özeti';
            $fileName = 'siparis_durumu_ozeti_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $monthlyData,
                    $statusLabels,
                    $statusCounts,
                    $totalAmounts,
                    $title,
                    $fileName,
                    'frontend.reports.exports.order-status-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $monthlyData,
                    $statusLabels,
                    $statusCounts,
                    $totalAmounts,
                    $title,
                    $fileName,
                    'order-status'
                );
            }
        }

        return view('frontend.reports.order-status-summary', compact(
            'monthlyData',
            'statusLabels',
            'statusCounts',
            'totalAmounts',
            'years',
            'year',
            'month',
            'salesReps',
            'salesRepId',
            'entities',
            'entityId'
        ));
    }

    /**
     * Show the shipment performance report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function shipmentPerformance(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $transportTypeId = $request->input('transport_type_id');
        $entityId = $request->input('entity_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        $transportTypes = DB::table('transport_types')
            ->select('id', 'description')
            ->orderBy('description')
            ->get();

        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Build the query for shipment performance
        $query = DB::table('orders')
            ->join('shipments', 'orders.id', '=', 'shipments.source_m_id')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->leftJoin('transport_types', 'shipments.transport_type_id', '=', 'transport_types.id')
            ->select(
                'entities.entity_name',
                'transport_types.description as transport_type',
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                DB::raw('AVG(DATEDIFF(shipments.doc_date, orders.doc_date)) as avg_delivery_days'),
                DB::raw('SUM(CASE WHEN shipments.doc_date <= orders.shipping_date THEN 1 ELSE 0 END) as on_time_count'),
                DB::raw('SUM(CASE WHEN shipments.doc_date > orders.shipping_date THEN 1 ELSE 0 END) as late_count')
            )
            ->whereYear('orders.doc_date', $year);

        // Apply filters
        if ($month) {
            $query->whereMonth('orders.doc_date', $month);
        }

        if ($transportTypeId) {
            $query->where('shipments.transport_type_id', $transportTypeId);
        }

        if ($entityId) {
            $query->where('orders.entity_id', $entityId);
        }

        // Group and order the results
        $shipmentData = $query
            ->groupBy('entities.entity_name', 'transport_types.description')
            ->orderBy('entities.entity_name')
            ->get();

        // Calculate totals and percentages
        foreach ($shipmentData as $item) {
            $item->on_time_percentage = $item->order_count > 0
                ? round(($item->on_time_count / $item->order_count) * 100, 2)
                : 0;
            $item->late_percentage = $item->order_count > 0
                ? round(($item->late_count / $item->order_count) * 100, 2)
                : 0;
        }

        $totals = [
            'order_count' => $shipmentData->sum('order_count'),
            'on_time_count' => $shipmentData->sum('on_time_count'),
            'late_count' => $shipmentData->sum('late_count'),
            'avg_delivery_days' => $shipmentData->avg('avg_delivery_days') ?? 0
        ];

        $totals['on_time_percentage'] = $totals['order_count'] > 0
            ? round(($totals['on_time_count'] / $totals['order_count']) * 100, 2)
            : 0;
        $totals['late_percentage'] = $totals['order_count'] > 0
            ? round(($totals['late_count'] / $totals['order_count']) * 100, 2)
            : 0;

        // Get monthly trend data for charts
        $monthlyTrend = DB::table('orders')
            ->join('shipments', 'orders.id', '=', 'shipments.source_m_id')
            ->select(
                DB::raw('MONTH(orders.doc_date) as month'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                DB::raw('SUM(CASE WHEN shipments.doc_date <= orders.shipping_date THEN 1 ELSE 0 END) as on_time_count'),
                DB::raw('SUM(CASE WHEN shipments.doc_date > orders.shipping_date THEN 1 ELSE 0 END) as late_count')
            )
            ->whereYear('orders.doc_date', $year)
            ->when($transportTypeId, function ($query) use ($transportTypeId) {
                return $query->where('shipments.transport_type_id', $transportTypeId);
            })
            ->when($entityId, function ($query) use ($entityId) {
                return $query->where('orders.entity_id', $entityId);
            })
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        $monthlyPerformance = [];
        foreach ($monthlyTrend as $item) {
            $monthName = Carbon::create()->locale(App::getLocale())->month($item->month)->format('F');
            $onTimePercentage = $item->order_count > 0
                ? round(($item->on_time_count / $item->order_count) * 100, 2)
                : 0;

            $monthlyPerformance[] = [
                'month' => $monthName,
                'order_count' => $item->order_count,
                'on_time_count' => $item->on_time_count,
                'on_time_percentage' => $onTimePercentage,
                'late_count' => $item->late_count,
                'late_percentage' => 100 - $onTimePercentage
            ];
        }

        // Get transport type breakdown for chart
        $transportBreakdown = DB::table('orders')
            ->join('shipments', 'orders.id', '=', 'shipments.source_m_id')
            ->leftJoin('transport_types', 'shipments.transport_type_id', '=', 'transport_types.id')
            ->select(
                'transport_types.description',
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                DB::raw('SUM(CASE WHEN shipments.doc_date <= orders.shipping_date THEN 1 ELSE 0 END) as on_time_count')
            )
            ->whereYear('orders.doc_date', $year)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('orders.doc_date', $month);
            })
            ->when($entityId, function ($query) use ($entityId) {
                return $query->where('orders.entity_id', $entityId);
            })
            ->groupBy('transport_types.description')
            ->get();

        foreach ($transportBreakdown as $item) {
            $item->on_time_percentage = $item->order_count > 0
                ? round(($item->on_time_count / $item->order_count) * 100, 2)
                : 0;
        }

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Sevkiyat Performans Raporu';
            $fileName = 'sevkiyat_performans_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $shipmentData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.shipment-performance-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $shipmentData->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'shipment'
                );
            }
        }

        return view('frontend.reports.shipment-performance', compact(
            'shipmentData',
            'totals',
            'years',
            'year',
            'month',
            'transportTypes',
            'transportTypeId',
            'entities',
            'entityId',
            'monthlyPerformance',
            'transportBreakdown'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName, $view = 'frontend.reports.exports.order-status-pdf')
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType The type of report
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName, $reportType = 'order-status')
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'order-status') {
            $sheet->mergeCells('A1:G1');

            // Set headers
            $sheet->setCellValue('A3', 'Dönem');
            $sheet->setCellValue('B3', 'Bekleyen');
            $sheet->setCellValue('C3', 'Onaylandı');
            $sheet->setCellValue('D3', 'Kısmen Sevk');
            $sheet->setCellValue('E3', 'Tamamen Sevk');
            $sheet->setCellValue('F3', 'İptal');
            $sheet->setCellValue('G3', 'Reddedildi');
            $sheet->setCellValue('H3', 'Toplam Sipariş');
            $sheet->setCellValue('I3', 'Toplam Tutar (₺)');

            // Add data
            $row = 4;
            foreach ($reportData as $monthKey => $data) {
                $sheet->setCellValue('A' . $row, $data['month_name']);
                $sheet->setCellValue('B' . $row, $data['statuses'][0] ?? 0);
                $sheet->setCellValue('C' . $row, $data['statuses'][1] ?? 0);
                $sheet->setCellValue('D' . $row, $data['statuses'][2] ?? 0);
                $sheet->setCellValue('E' . $row, $data['statuses'][3] ?? 0);
                $sheet->setCellValue('F' . $row, $data['statuses'][4] ?? 0);
                $sheet->setCellValue('G' . $row, $data['statuses'][5] ?? 0);
                $sheet->setCellValue('H' . $row, $data['total_orders']);
                $sheet->setCellValue('I' . $row, $data['total_amount'] + $data['total_vat']);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->setCellValue('B' . $row, $totals[0] ?? 0);
            $sheet->setCellValue('C' . $row, $totals[1] ?? 0);
            $sheet->setCellValue('D' . $row, $totals[2] ?? 0);
            $sheet->setCellValue('E' . $row, $totals[3] ?? 0);
            $sheet->setCellValue('F' . $row, $totals[4] ?? 0);
            $sheet->setCellValue('G' . $row, $totals[5] ?? 0);
            $sheet->setCellValue('H' . $row, array_sum($totals));
            $sheet->setCellValue('I' . $row, $totals['total_amount'] + $totals['total_vat']);

            // Format numbers
            $sheet->getStyle('I4:I' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

            // Auto-size columns
            foreach (range('A', 'I') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

        } elseif ($reportType === 'shipment') {
            $sheet->mergeCells('A1:G1');

            // Set headers
            $sheet->setCellValue('A3', 'Müşteri');
            $sheet->setCellValue('B3', 'Taşıma Tipi');
            $sheet->setCellValue('C3', 'Sipariş Sayısı');
            $sheet->setCellValue('D3', 'Ort. Teslimat Süresi (Gün)');
            $sheet->setCellValue('E3', 'Zamanında Teslimat');
            $sheet->setCellValue('F3', 'Geç Teslimat');
            $sheet->setCellValue('G3', 'Zamanında Teslimat %');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data->entity_name);
                $sheet->setCellValue('B' . $row, $data->transport_type ?? 'Belirtilmemiş');
                $sheet->setCellValue('C' . $row, $data->order_count);
                $sheet->setCellValue('D' . $row, round($data->avg_delivery_days, 1));
                $sheet->setCellValue('E' . $row, $data->on_time_count);
                $sheet->setCellValue('F' . $row, $data->late_count);
                $sheet->setCellValue('G' . $row, $data->on_time_percentage . '%');
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->mergeCells('A' . $row . ':B' . $row);
            $sheet->setCellValue('C' . $row, $totals['order_count']);
            $sheet->setCellValue('D' . $row, round($totals['avg_delivery_days'], 1));
            $sheet->setCellValue('E' . $row, $totals['on_time_count']);
            $sheet->setCellValue('F' . $row, $totals['late_count']);
            $sheet->setCellValue('G' . $row, $totals['on_time_percentage'] . '%');

            // Auto-size columns
            foreach (range('A', 'G') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }

        // Style common elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:' . ($reportType === 'order-status' ? 'I' : 'G') . '3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':' . ($reportType === 'order-status' ? 'I' : 'G') . $row)->getFont()->setBold(true);

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}