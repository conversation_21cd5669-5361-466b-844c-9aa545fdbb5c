<?php

namespace App\Http\Controllers\Frontend\Reports;


use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Spatie\LaravelPdf\Facades\Pdf;


class ReportController extends Controller
{
    /**
     * Show the product sales analysis report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function productSalesReport(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $categoryId = $request->input('category_id');
        $brandId = $request->input('brand_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        $categories = DB::table('categories')
            ->select('id', 'description')
            ->where('is_active', 1)
            ->orderBy('description')
            ->get();

        $brands = DB::table('brands')
            ->select('id', 'description')
            ->orderBy('description')
            ->get();

        // Build the query for product sales
        $query = DB::table('orders_d')
            ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
            ->join('products', 'orders_d.item_id', '=', 'products.id')
            ->leftJoin('categories', 'products.categories1_id', '=', 'categories.id')
            ->leftJoin('brands', 'products.brand_id', '=', 'brands.id')
            ->select(
                'products.id',
                'products.item_code',
                'products.item_name',
                'categories.description as category_name',
                'brands.description as brand_name',
                DB::raw('SUM(orders_d.qty) as total_quantity'),
                DB::raw('SUM(orders_d.amt) as total_amount'),
                DB::raw('SUM(orders_d.amt_vat) as total_vat'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count')
            )
            ->whereYear('orders.doc_date', $year);

        // Apply filters
        if ($categoryId) {
            $query->where('products.categories1_id', $categoryId);
        }

        if ($brandId) {
            $query->where('products.brand_id', $brandId);
        }

        // Group and order the results
        $productSales = $query->groupBy(
            'products.id',
            'products.item_code',
            'products.item_name',
            'categories.description',
            'brands.description'
        )
            ->orderBy('total_amount', 'desc')
            ->get();

        // Calculate totals
        $totals = [
            'total_quantity' => $productSales->sum('total_quantity'),
            'total_amount' => $productSales->sum('total_amount'),
            'total_vat' => $productSales->sum('total_vat'),
            'order_count' => $productSales->max('order_count') // This is just an estimate
        ];

        // Category breakdown for chart
        $categoryBreakdown = DB::table('orders_d')
            ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
            ->join('products', 'orders_d.item_id', '=', 'products.id')
            ->leftJoin('categories', 'products.categories1_id', '=', 'categories.id')
            ->select(
                'categories.id',
                'categories.description as category_name',
                DB::raw('SUM(orders_d.amt) as total_amount')
            )
            ->whereYear('orders.doc_date', $year)
            ->groupBy('categories.id', 'categories.description')
            ->orderBy('total_amount', 'desc')
            ->get();

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Ürün Satış Analizi';
            $fileName = 'urun_satis_analizi_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $productSales->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.product-sales-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $productSales->toArray(),
                    $totals,
                    $title,
                    $fileName,
                    'product'
                );
            }
        }

        return view('frontend.reports.product-sales', compact(
            'productSales',
            'totals',
            'years',
            'year',
            'categories',
            'categoryId',
            'brands',
            'brandId',
            'categoryBreakdown'
        ));
    }

    /**
     * Show the sales report page or export to PDF/Excel.
     *
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function salesReport(Request $request)
    {
        // Check if export is requested
        $export = $request->input('export');

        // Default to current year if not specified
        $year = $request->input('year', date('Y'));
        $reportType = $request->input('report_type', 'monthly');
        $entityId = $request->input('entity_id');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get entities list for the dropdown
        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->take(1000)
            ->orderBy('entity_name')
            ->get();

        // Initialize data array
        $reportData = [];

        if ($reportType === 'monthly') {
            // Monthly sales for selected year
            $query = DB::table('orders')
                ->select(
                    DB::raw('MONTH(doc_date) as month'),
                    DB::raw('SUM(amt) as total_amount'),
                    DB::raw('SUM(amt_vat) as total_vat'),
                    DB::raw('COUNT(*) as order_count')
                )
                ->whereYear('doc_date', $year)
                ->groupBy('month')
                ->orderBy('month');

            // Add entity filter if specified
            if ($entityId) {
                $query->where('entity_id', $entityId);
            }

            $results = $query->get();

            // Format the data for the view
            foreach ($results as $row) {
                $month = Carbon::create()->month($row->month)->format('F');
                $reportData[] = [
                    'period' => $month,
                    'total_amount' => $row->total_amount,
                    'total_vat' => $row->total_vat,
                    'order_count' => $row->order_count,
                ];
            }
        } else {
            // Yearly sales summary
            $query = DB::table('orders')
                ->select(
                    DB::raw('YEAR(doc_date) as year'),
                    DB::raw('SUM(amt) as total_amount'),
                    DB::raw('SUM(amt_vat) as total_vat'),
                    DB::raw('COUNT(*) as order_count')
                )
                ->groupBy('year')
                ->orderBy('year', 'desc');

            // Add entity filter if specified
            if ($entityId) {
                $query->where('entity_id', $entityId);
            }

            $results = $query->get();

            // Format the data for the view
            foreach ($results as $row) {
                $reportData[] = [
                    'period' => $row->year,
                    'total_amount' => $row->total_amount,
                    'total_vat' => $row->total_vat,
                    'order_count' => $row->order_count,
                ];
            }
        }

        // Calculate totals
        $totals = [
            'total_amount' => array_sum(array_column($reportData, 'total_amount')),
            'total_vat' => array_sum(array_column($reportData, 'total_vat')),
            'order_count' => array_sum(array_column($reportData, 'order_count')),
        ];

        // Get top selling products if viewing monthly report
        $topProducts = [];
        $topCustomers = [];

        if ($reportType === 'monthly') {
            // Get top 5 selling products for the selected year/month
            $topProducts = DB::table('orders_d')
                ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                ->join('products', 'orders_d.item_id', '=', 'products.id')
                ->select(
                    'products.id',
                    'products.item_code',
                    'products.item_name',
                    DB::raw('SUM(orders_d.qty) as total_quantity'),
                    DB::raw('SUM(orders_d.amt) as total_amount')
                )
                ->whereYear('orders.doc_date', $year)
                ->when($entityId, function($query) use ($entityId) {
                    return $query->where('orders.entity_id', $entityId);
                })
                ->groupBy('products.id', 'products.item_code', 'products.item_name')
                ->orderBy('total_amount', 'desc')
                ->limit(5)
                ->get();

            // Get top 5 customers for the selected year/month
            $topCustomers = DB::table('orders')
                ->join('entities', 'orders.entity_id', '=', 'entities.id')
                ->select(
                    'entities.id',
                    'entities.entity_name',
                    DB::raw('COUNT(orders.id) as order_count'),
                    DB::raw('SUM(orders.amt) as total_amount')
                )
                ->whereYear('orders.doc_date', $year)
                ->groupBy('entities.id', 'entities.entity_name')
                ->orderBy('total_amount', 'desc')
                ->limit(5)
                ->get();
        }

        // Handle exports if requested
        if ($export) {
            $title = $reportType === 'monthly'
                ? $year . ' Yılı Aylık Satış Raporu'
                : 'Yıllık Satış Raporu';

            $fileName = 'satis_raporu_' . ($reportType === 'monthly' ? $year : 'yillik') . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf($reportData, $totals, $title, $fileName);
            } elseif ($export === 'excel') {
                return $this->exportToExcel($reportData, $totals, $title, $fileName);
            }
        }

        return view('frontend.reports.sales', compact(
            'reportData',
            'totals',
            'years',
            'year',
            'reportType',
            'entities',
            'entityId',
            'topProducts',
            'topCustomers'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName, $view = 'reports.exports.sales-pdf')
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType The type of report ('sales' or 'product')
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName, $reportType = 'sales')
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'sales') {
            $sheet->mergeCells('A1:E1');

            // Set headers
            $sheet->setCellValue('A3', 'Dönem');
            $sheet->setCellValue('B3', 'Sipariş Sayısı');
            $sheet->setCellValue('C3', 'Toplam Tutar (₺)');
            $sheet->setCellValue('D3', 'Toplam KDV (₺)');
            $sheet->setCellValue('E3', 'Genel Toplam (₺)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data['period']);
                $sheet->setCellValue('B' . $row, $data['order_count']);
                $sheet->setCellValue('C' . $row, $data['total_amount']);
                $sheet->setCellValue('D' . $row, $data['total_vat']);
                $sheet->setCellValue('E' . $row, $data['total_amount'] + $data['total_vat']);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->setCellValue('B' . $row, $totals['order_count']);
            $sheet->setCellValue('C' . $row, $totals['total_amount']);
            $sheet->setCellValue('D' . $row, $totals['total_vat']);
            $sheet->setCellValue('E' . $row, $totals['total_amount'] + $totals['total_vat']);

            // Format numbers
            $sheet->getStyle('C4:E' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

            // Auto-size columns
            foreach (range('A', 'E') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

        } elseif ($reportType === 'product') {
            $sheet->mergeCells('A1:H1');

            // Set headers
            $sheet->setCellValue('A3', 'Ürün Kodu');
            $sheet->setCellValue('B3', 'Ürün Adı');
            $sheet->setCellValue('C3', 'Kategori');
            $sheet->setCellValue('D3', 'Marka');
            $sheet->setCellValue('E3', 'Sipariş Sayısı');
            $sheet->setCellValue('F3', 'Satış Miktarı');
            $sheet->setCellValue('G3', 'Satış Tutarı (₺)');
            $sheet->setCellValue('H3', 'KDV Tutarı (₺)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data->item_code);
                $sheet->setCellValue('B' . $row, $data->item_name);
                $sheet->setCellValue('C' . $row, $data->category_name ?? '-');
                $sheet->setCellValue('D' . $row, $data->brand_name ?? '-');
                $sheet->setCellValue('E' . $row, $data->order_count);
                $sheet->setCellValue('F' . $row, $data->total_quantity);
                $sheet->setCellValue('G' . $row, $data->total_amount);
                $sheet->setCellValue('H' . $row, $data->total_vat);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->mergeCells('A' . $row . ':D' . $row);
            $sheet->setCellValue('E' . $row, $totals['order_count']);
            $sheet->setCellValue('F' . $row, $totals['total_quantity']);
            $sheet->setCellValue('G' . $row, $totals['total_amount']);
            $sheet->setCellValue('H' . $row, $totals['total_vat']);

            // Format numbers
            $sheet->getStyle('F4:F' . $row)->getNumberFormat()->setFormatCode('#,##0.00');
            $sheet->getStyle('G4:H' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

            // Auto-size columns
            foreach (range('A', 'H') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }

        // Style common elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:' . ($reportType === 'sales' ? 'E' : 'H') . '3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':' . ($reportType === 'sales' ? 'E' : 'H') . $row)->getFont()->setBold(true);

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}