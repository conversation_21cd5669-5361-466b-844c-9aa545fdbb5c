<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\App;

class SalesPerformanceController extends Controller
{
    /**
     * Display the monthly/yearly sales report page
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function monthlySales(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $entityId = $request->input('entity_id');
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get entities list for the dropdown
        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Initialize query
        $query = DB::table('orders')
            ->select(
                DB::raw('DATE_FORMAT(doc_date, "%Y-%m") as period'),
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(amt) as total_amount'),
                DB::raw('SUM(amt_vat) as total_vat')
            )
            ->whereYear('doc_date', $year);

        // Apply filters
        if ($month) {
            $query->whereMonth('doc_date', $month);
        }

        if ($entityId) {
            $query->where('entity_id', $entityId);
        }

        // Group and order the results
        $salesData = $query
            ->groupBy('period')
            ->orderBy('period')
            ->get();

        // Reformat the data for display
        $reportData = [];
        foreach ($salesData as $row) {
            $dateParts = explode('-', $row->period);
            $monthName = Carbon::createFromDate($dateParts[0], $dateParts[1], 1)->locale(App::getLocale())->format('F');

            $reportData[] = [
                'period' => $monthName . ' ' . $dateParts[0],
                'order_count' => $row->order_count,
                'total_amount' => $row->total_amount,
                'total_vat' => $row->total_vat,
                'total' => $row->total_amount + $row->total_vat
            ];
        }

        // Calculate totals
        $totals = [
            'order_count' => array_sum(array_column($reportData, 'order_count')),
            'total_amount' => array_sum(array_column($reportData, 'total_amount')),
            'total_vat' => array_sum(array_column($reportData, 'total_vat')),
            'total' => array_sum(array_column($reportData, 'total_amount')) + array_sum(array_column($reportData, 'total_vat'))
        ];

        // Get top 5 customers
        $topCustomers = DB::table('orders')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->select(
                'entities.id',
                'entities.entity_name',
                DB::raw('COUNT(orders.id) as order_count'),
                DB::raw('SUM(orders.amt) as total_amount')
            )
            ->whereYear('orders.doc_date', $year)
            ->when($month, function($query) use ($month) {
                return $query->whereMonth('orders.doc_date', $month);
            })
            ->groupBy('entities.id', 'entities.entity_name')
            ->orderBy('total_amount', 'desc')
            ->limit(5)
            ->get();

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Aylık Satış Performans Raporu';
            $fileName = 'aylik_satis_raporu_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf($reportData, $totals, $title, $fileName);
            } elseif ($export === 'excel') {
                return $this->exportToExcel($reportData, $totals, $title, $fileName);
            }
        }

        return view('frontend.reports.monthly-sales', compact(
            'reportData',
            'totals',
            'years',
            'year',
            'month',
            'entities',
            'entityId',
            'topCustomers'
        ));
    }

    /**
     * Display the yearly sales report page
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function yearlySales(Request $request)
    {
        // Get report parameters
        $startYear = $request->input('start_year', date('Y') - 3);
        $endYear = $request->input('end_year', date('Y'));
        $entityId = $request->input('entity_id');
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get entities list for the dropdown
        $entities = DB::table('entities')
            ->select('id', 'entity_name')
            ->orderBy('entity_name')
            ->take(1000)
            ->get();

        // Initialize query
        $query = DB::table('orders')
            ->select(
                DB::raw('YEAR(doc_date) as year'),
                DB::raw('COUNT(*) as order_count'),
                DB::raw('SUM(amt) as total_amount'),
                DB::raw('SUM(amt_vat) as total_vat')
            )
            ->whereRaw('YEAR(doc_date) BETWEEN ? AND ?', [$startYear, $endYear]);

        // Apply filters
        if ($entityId) {
            $query->where('entity_id', $entityId);
        }

        // Group and order the results
        $salesData = $query
            ->groupBy('year')
            ->orderBy('year')
            ->get();

        // Reformat the data for display
        $reportData = [];
        foreach ($salesData as $row) {
            $reportData[] = [
                'period' => $row->year,
                'order_count' => $row->order_count,
                'total_amount' => $row->total_amount,
                'total_vat' => $row->total_vat,
                'total' => $row->total_amount + $row->total_vat
            ];
        }

        // Calculate totals
        $totals = [
            'order_count' => array_sum(array_column($reportData, 'order_count')),
            'total_amount' => array_sum(array_column($reportData, 'total_amount')),
            'total_vat' => array_sum(array_column($reportData, 'total_vat')),
            'total' => array_sum(array_column($reportData, 'total_amount')) + array_sum(array_column($reportData, 'total_vat'))
        ];

        // Handle exports if requested
        if ($export) {
            $title = $startYear . '-' . $endYear . ' Yılları Arası Satış Performans Raporu';
            $fileName = 'yillik_satis_raporu_' . $startYear . '-' . $endYear . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf($reportData, $totals, $title, $fileName);
            } elseif ($export === 'excel') {
                return $this->exportToExcel($reportData, $totals, $title, $fileName);
            }
        }

        return view('frontend.reports.yearly-sales', compact(
            'reportData',
            'totals',
            'years',
            'startYear',
            'endYear',
            'entities',
            'entityId'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName)
    {
        return Pdf::view('frontend.reports.exports.sales-performance-pdf', [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:E1');

        // Set headers
        $sheet->setCellValue('A3', 'Dönem');
        $sheet->setCellValue('B3', 'Sipariş Sayısı');
        $sheet->setCellValue('C3', 'Satış Tutarı (₺)');
        $sheet->setCellValue('D3', 'KDV Tutarı (₺)');
        $sheet->setCellValue('E3', 'Genel Toplam (₺)');

        // Add data
        $row = 4;
        foreach ($reportData as $data) {
            $sheet->setCellValue('A' . $row, $data['period']);
            $sheet->setCellValue('B' . $row, $data['order_count']);
            $sheet->setCellValue('C' . $row, $data['total_amount']);
            $sheet->setCellValue('D' . $row, $data['total_vat']);
            $sheet->setCellValue('E' . $row, $data['total']);
            $row++;
        }

        // Add totals
        $sheet->setCellValue('A' . $row, 'TOPLAM');
        $sheet->setCellValue('B' . $row, $totals['order_count']);
        $sheet->setCellValue('C' . $row, $totals['total_amount']);
        $sheet->setCellValue('D' . $row, $totals['total_vat']);
        $sheet->setCellValue('E' . $row, $totals['total']);

        // Format numbers
        $sheet->getStyle('C4:E' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

        // Style elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:E3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':E' . $row)->getFont()->setBold(true);

        // Auto-size columns
        foreach (range('A', 'E') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}