<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Spatie\LaravelPdf\Facades\Pdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class CustomerAnalysisController extends Controller
{
    /**
     * Display the most valuable customers report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function valuableCustomers(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $period = $request->input('period', 'yearly');
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Initialize query to get most valuable customers
        $query = DB::table('orders')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->leftJoin('sales_people', 'orders.sales_person_id', '=', 'sales_people.id');

        if ($period === 'yearly') {
            $query->whereYear('orders.doc_date', $year);
            $periodText = $year . ' Yılı';
        } else {
            // If period is all-time
            $periodText = 'Tüm Zamanlar';
        }

        // Group by entity and get total stats
        $customers = $query->select(
            'entities.id',
            'entities.entity_code',
            'entities.entity_name',
            'sales_people.first_name as sales_rep_name',
            DB::raw('COUNT(orders.id) as order_count'),
            DB::raw('SUM(orders.amt) as total_amount'),
            DB::raw('SUM(orders.amt_vat) as total_vat'),
            DB::raw('MIN(orders.doc_date) as first_order_date'),
            DB::raw('MAX(orders.doc_date) as last_order_date'),
            DB::raw('DATEDIFF(MAX(orders.doc_date), MIN(orders.doc_date)) as days_between_first_last')
        )
            ->groupBy('entities.id', 'entities.entity_code', 'entities.entity_name', 'sales_people.first_name')
            ->orderBy('total_amount', 'desc')
            ->get();

        // Calculate totals
        $totals = [
            'order_count' => $customers->sum('order_count'),
            'total_amount' => $customers->sum('total_amount'),
            'total_vat' => $customers->sum('total_vat')
        ];

        // Add rank and percentage of total for each customer
        $totalAmount = $totals['total_amount'];
        $customerData = [];
        $cumulativePercent = 0;

        foreach ($customers as $index => $customer) {
            $percentOfTotal = ($totalAmount > 0) ? ($customer->total_amount / $totalAmount) * 100 : 0;
            $cumulativePercent += $percentOfTotal;

            $customerData[] = [
                'rank' => $index + 1,
                'id' => $customer->id,
                'entity_code' => $customer->entity_code,
                'entity_name' => $customer->entity_name,
                'sales_rep_name' => $customer->sales_rep_name,
                'order_count' => $customer->order_count,
                'total_amount' => $customer->total_amount,
                'total_vat' => $customer->total_vat,
                'first_order_date' => $customer->first_order_date,
                'last_order_date' => $customer->last_order_date,
                'days_between_first_last' => $customer->days_between_first_last,
                'percent_of_total' => $percentOfTotal,
                'cumulative_percent' => $cumulativePercent
            ];
        }

        // Handle exports if requested
        if ($export) {
            $title = 'En Değerli Müşteriler Raporu - ' . $periodText;
            $fileName = 'en_degerli_musteriler_' . strtolower(str_replace(' ', '_', $periodText)) . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $customerData,
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.valuable-customers-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $customerData,
                    $totals,
                    $title,
                    $fileName,
                    'valuable-customers'
                );
            }
        }

        return view('frontend.reports.valuable-customers', compact(
            'customerData',
            'totals',
            'years',
            'year',
            'period',
            'periodText'
        ));
    }

    /**
     * Display the customer purchase frequency report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function purchaseFrequency(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $period = $request->input('period', 'yearly');
        $minOrders = $request->input('min_orders', 2);
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Initialize query to get customer purchase frequency
        $query = DB::table('orders')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->leftJoin('sales_people', 'orders.sales_person_id', '=', 'sales_people.id');

        if ($period === 'yearly') {
            $query->whereYear('orders.doc_date', $year);
            $periodText = $year . ' Yılı';
        } else {
            // If period is all-time
            $periodText = 'Tüm Zamanlar';
        }

        // First, get basic order counts and date ranges
        $baseData = $query->select(
            'entities.id',
            'entities.entity_code',
            'entities.entity_name',
            'sales_people.first_name as sales_rep_name',
            DB::raw('COUNT(orders.id) as order_count'),
            DB::raw('SUM(orders.amt) as total_amount'),
            DB::raw('MIN(orders.doc_date) as first_order_date'),
            DB::raw('MAX(orders.doc_date) as last_order_date'),
            DB::raw('DATEDIFF(MAX(orders.doc_date), MIN(orders.doc_date)) as days_between_first_last')
        )
            ->groupBy('entities.id', 'entities.entity_code', 'entities.entity_name', 'sales_people.first_name')
            ->having('order_count', '>=', $minOrders)
            ->orderBy('order_count', 'desc')
            ->get();

        // Calculate frequency metrics for each customer
        $frequencyData = [];
        $now = Carbon::now();

        foreach ($baseData as $customer) {
            $firstOrderDate = Carbon::parse($customer->first_order_date);
            $lastOrderDate = Carbon::parse($customer->last_order_date);

            // Calculate days between orders
            $daysActive = max(1, $customer->days_between_first_last);
            $averageDaysBetweenOrders = ($customer->order_count > 1) ? round($daysActive / ($customer->order_count - 1)) : 0;

            // Calculate orders per month
            $monthsActive = max(1, ceil($daysActive / 30));
            $ordersPerMonth = round(($customer->order_count / $monthsActive), 2);

            // Calculate days since last order
            $daysSinceLastOrder = $lastOrderDate->diffInDays($now);

            $frequencyData[] = [
                'id' => $customer->id,
                'entity_code' => $customer->entity_code,
                'entity_name' => $customer->entity_name,
                'sales_rep_name' => $customer->sales_rep_name,
                'order_count' => $customer->order_count,
                'total_amount' => $customer->total_amount,
                'first_order_date' => $customer->first_order_date,
                'last_order_date' => $customer->last_order_date,
                'days_between_first_last' => $daysActive,
                'avg_days_between_orders' => $averageDaysBetweenOrders,
                'orders_per_month' => $ordersPerMonth,
                'days_since_last_order' => $daysSinceLastOrder
            ];
        }

        // Sort by average days between orders
        usort($frequencyData, function($a, $b) {
            return $a['avg_days_between_orders'] - $b['avg_days_between_orders'];
        });

        // Calculate totals
        $totals = [
            'customer_count' => count($frequencyData),
            'order_count' => array_sum(array_column($frequencyData, 'order_count')),
            'total_amount' => array_sum(array_column($frequencyData, 'total_amount'))
        ];

        // Handle exports if requested
        if ($export) {
            $title = 'Müşteri Satınalma Sıklığı Raporu - ' . $periodText;
            $fileName = 'musteri_satinalma_sikligi_' . strtolower(str_replace(' ', '_', $periodText)) . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $frequencyData,
                    $totals,
                    $title,
                    $fileName,
                    'frontend.reports.exports.purchase-frequency-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $frequencyData,
                    $totals,
                    $title,
                    $fileName,
                    'purchase-frequency'
                );
            }
        }

        // Group customers by frequency category
        $frequencyGroups = [
            'daily' => [],
            'weekly' => [],
            'monthly' => [],
            'quarterly' => [],
            'semiannual' => [],
            'annual' => [],
            'irregular' => []
        ];

        foreach ($frequencyData as $customer) {
            $avgDays = $customer['avg_days_between_orders'];

            if ($avgDays <= 7) {
                $frequencyGroups['daily'][] = $customer;
            } elseif ($avgDays <= 30) {
                $frequencyGroups['weekly'][] = $customer;
            } elseif ($avgDays <= 90) {
                $frequencyGroups['monthly'][] = $customer;
            } elseif ($avgDays <= 180) {
                $frequencyGroups['quarterly'][] = $customer;
            } elseif ($avgDays <= 365) {
                $frequencyGroups['semiannual'][] = $customer;
            } elseif ($avgDays <= 730) {
                $frequencyGroups['annual'][] = $customer;
            } else {
                $frequencyGroups['irregular'][] = $customer;
            }
        }

        return view('frontend.reports.purchase-frequency', compact(
            'frequencyData',
            'frequencyGroups',
            'totals',
            'years',
            'year',
            'period',
            'periodText',
            'minOrders'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName, $view)
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType The type of report
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName, $reportType)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'valuable-customers') {
            $sheet->mergeCells('A1:I1');

            // Set headers
            $sheet->setCellValue('A3', 'Sıra');
            $sheet->setCellValue('B3', 'Müşteri Kodu');
            $sheet->setCellValue('C3', 'Müşteri Adı');
            $sheet->setCellValue('D3', 'Satış Temsilcisi');
            $sheet->setCellValue('E3', 'Sipariş Sayısı');
            $sheet->setCellValue('F3', 'Toplam Tutar (₺)');
            $sheet->setCellValue('G3', 'Toplam KDV (₺)');
            $sheet->setCellValue('H3', 'Toplam Yüzdesi (%)');
            $sheet->setCellValue('I3', 'Kümülatif Yüzde (%)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data['rank']);
                $sheet->setCellValue('B' . $row, $data['entity_code']);
                $sheet->setCellValue('C' . $row, $data['entity_name']);
                $sheet->setCellValue('D' . $row, $data['sales_rep_name'] ?? '-');
                $sheet->setCellValue('E' . $row, $data['order_count']);
                $sheet->setCellValue('F' . $row, $data['total_amount']);
                $sheet->setCellValue('G' . $row, $data['total_vat']);
                $sheet->setCellValue('H' . $row, round($data['percent_of_total'], 2));
                $sheet->setCellValue('I' . $row, round($data['cumulative_percent'], 2));
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM');
            $sheet->mergeCells('A' . $row . ':D' . $row);
            $sheet->setCellValue('E' . $row, $totals['order_count']);
            $sheet->setCellValue('F' . $row, $totals['total_amount']);
            $sheet->setCellValue('G' . $row, $totals['total_vat']);
            $sheet->setCellValue('H' . $row, '100.00');
            $sheet->setCellValue('I' . $row, '100.00');

            // Format numbers
            $sheet->getStyle('F4:G' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');
            $sheet->getStyle('H4:I' . $row)->getNumberFormat()->setFormatCode('#,##0.00%');

            // Auto-size columns
            foreach (range('A', 'I') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }

        } elseif ($reportType === 'purchase-frequency') {
            $sheet->mergeCells('A1:J1');

            // Set headers
            $sheet->setCellValue('A3', 'Müşteri Kodu');
            $sheet->setCellValue('B3', 'Müşteri Adı');
            $sheet->setCellValue('C3', 'Satış Temsilcisi');
            $sheet->setCellValue('D3', 'Sipariş Sayısı');
            $sheet->setCellValue('E3', 'Toplam Tutar (₺)');
            $sheet->setCellValue('F3', 'İlk Sipariş Tarihi');
            $sheet->setCellValue('G3', 'Son Sipariş Tarihi');
            $sheet->setCellValue('H3', 'Ort. Sipariş Aralığı (Gün)');
            $sheet->setCellValue('I3', 'Aylık Sipariş Sayısı');
            $sheet->setCellValue('J3', 'Son Siparişten Beri (Gün)');

            // Add data
            $row = 4;
            foreach ($reportData as $data) {
                $sheet->setCellValue('A' . $row, $data['entity_code']);
                $sheet->setCellValue('B' . $row, $data['entity_name']);
                $sheet->setCellValue('C' . $row, $data['sales_rep_name'] ?? '-');
                $sheet->setCellValue('D' . $row, $data['order_count']);
                $sheet->setCellValue('E' . $row, $data['total_amount']);
                $sheet->setCellValue('F' . $row, $data['first_order_date']);
                $sheet->setCellValue('G' . $row, $data['last_order_date']);
                $sheet->setCellValue('H' . $row, $data['avg_days_between_orders']);
                $sheet->setCellValue('I' . $row, $data['orders_per_month']);
                $sheet->setCellValue('J' . $row, $data['days_since_last_order']);
                $row++;
            }

            // Add totals
            $sheet->setCellValue('A' . $row, 'TOPLAM (' . $totals['customer_count'] . ' Müşteri)');
            $sheet->mergeCells('A' . $row . ':C' . $row);
            $sheet->setCellValue('D' . $row, $totals['order_count']);
            $sheet->setCellValue('E' . $row, $totals['total_amount']);

            // Format numbers
            $sheet->getStyle('E4:E' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');
            $sheet->getStyle('I4:I' . $row)->getNumberFormat()->setFormatCode('#,##0.00');

            // Auto-size columns
            foreach (range('A', 'J') as $col) {
                $sheet->getColumnDimension($col)->setAutoSize(true);
            }
        }

        // Style common elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:' . ($reportType === 'valuable-customers' ? 'I' : 'J') . '3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':' . ($reportType === 'valuable-customers' ? 'I' : 'E') . $row)->getFont()->setBold(true);

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}