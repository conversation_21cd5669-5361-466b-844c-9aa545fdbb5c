<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Facades\App;

class DashboardReportController extends Controller
{
    /**
     * Display the dashboard KPI overview page
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        return view('frontend.reports.index');
    }

    /**
     * Display general performance indicators
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function generalPerformance(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $salesRepId = $request->input('sales_person_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get sales representatives list for the dropdown
        $salesReps = DB::table('sales_people')
            ->select('id', 'first_name', 'sales_person_code')
            ->where('ispassive', 0)
            ->orderBy('first_name')
            ->get();

        // Get monthly sales data
        $monthlySalesQuery = DB::table('orders')
            ->select(
                DB::raw('MONTH(doc_date) as month'),
                DB::raw('SUM(amt) as total_amount'),
                DB::raw('COUNT(*) as order_count')
            )
            ->whereYear('doc_date', $year)
            ->groupBy('month')
            ->orderBy('month');

        if ($salesRepId) {
            $monthlySalesQuery->where('sales_person_id', $salesRepId);
        }

        $monthlySales = $monthlySalesQuery->get();

        // Format monthly data for charts
        $monthLabels = [];
        $salesData = [];
        $orderCountData = [];

        for ($i = 1; $i <= 12; $i++) {
            $monthName = Carbon::create()->locale(App::getLocale())->month($i)->format('F');
            $monthLabels[] = $monthName;

            $monthlySale = $monthlySales->firstWhere('month', $i);
            $salesData[] = $monthlySale ? $monthlySale->total_amount : 0;
            $orderCountData[] = $monthlySale ? $monthlySale->order_count : 0;
        }

        // Get top 5 customers
        $topCustomers = DB::table('orders')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->select(
                'entities.id',
                'entities.entity_name',
                DB::raw('SUM(orders.amt) as total_amount'),
                DB::raw('COUNT(orders.id) as order_count')
            )
            ->whereYear('orders.doc_date', $year)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('orders.doc_date', $month);
            })
            ->when($salesRepId, function ($query) use ($salesRepId) {
                return $query->where('orders.sales_person_id', $salesRepId);
            })
            ->groupBy('entities.id', 'entities.entity_name')
            ->orderBy('total_amount', 'desc')
            ->limit(5)
            ->get();

        // Get top 5 products
        $topProducts = DB::table('orders_d')
            ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
            ->join('products', 'orders_d.item_id', '=', 'products.id')
            ->select(
                'products.id',
                'products.item_code',
                'products.item_name',
                DB::raw('SUM(orders_d.qty) as total_quantity'),
                DB::raw('SUM(orders_d.amt) as total_amount')
            )
            ->whereYear('orders.doc_date', $year)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('orders.doc_date', $month);
            })
            ->when($salesRepId, function ($query) use ($salesRepId) {
                return $query->where('orders.sales_person_id', $salesRepId);
            })
            ->groupBy('products.id', 'products.item_code', 'products.item_name')
            ->orderBy('total_amount', 'desc')
            ->limit(5)
            ->get();

        // Calculate year over year growth
        $thisYearSales = DB::table('orders')
            ->whereYear('doc_date', $year)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('doc_date', $month);
            })
            ->when($salesRepId, function ($query) use ($salesRepId) {
                return $query->where('sales_person_id', $salesRepId);
            })
            ->sum('amt');

        $lastYearSales = DB::table('orders')
            ->whereYear('doc_date', $year - 1)
            ->when($month, function ($query) use ($month) {
                return $query->whereMonth('doc_date', $month);
            })
            ->when($salesRepId, function ($query) use ($salesRepId) {
                return $query->where('sales_person_id', $salesRepId);
            })
            ->sum('amt');

        $yearOverYearGrowth = $lastYearSales > 0
            ? (($thisYearSales - $lastYearSales) / $lastYearSales) * 100
            : 0;

        // Calculate KPIs
        $kpis = [
            'total_sales' => $thisYearSales,
            'total_orders' => DB::table('orders')
                ->whereYear('doc_date', $year)
                ->when($month, function ($query) use ($month) {
                    return $query->whereMonth('doc_date', $month);
                })
                ->when($salesRepId, function ($query) use ($salesRepId) {
                    return $query->where('sales_person_id', $salesRepId);
                })
                ->count(),
            'average_order_value' => DB::table('orders')
                    ->whereYear('doc_date', $year)
                    ->when($month, function ($query) use ($month) {
                        return $query->whereMonth('doc_date', $month);
                    })
                    ->when($salesRepId, function ($query) use ($salesRepId) {
                        return $query->where('sales_person_id', $salesRepId);
                    })
                    ->avg('amt') ?? 0,
            'unique_customers' => DB::table('orders')
                ->whereYear('doc_date', $year)
                ->when($month, function ($query) use ($month) {
                    return $query->whereMonth('doc_date', $month);
                })
                ->when($salesRepId, function ($query) use ($salesRepId) {
                    return $query->where('sales_person_id', $salesRepId);
                })
                ->distinct('entity_id')
                ->count('entity_id'),
            'yoy_growth' => $yearOverYearGrowth
        ];

        // Handle exports if requested
        if ($export) {
            $title = 'Genel Performans Göstergeleri - ' . $year;
            $fileName = 'genel_performans_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    [
                        'kpis' => $kpis,
                        'topCustomers' => $topCustomers,
                        'topProducts' => $topProducts,
                        'monthlySales' => $monthlySales
                    ],
                    $title,
                    $fileName,
                    'frontend.reports.exports.general-performance-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    [
                        'kpis' => $kpis,
                        'topCustomers' => $topCustomers,
                        'topProducts' => $topProducts,
                        'monthlySales' => $monthlySales
                    ],
                    $title,
                    $fileName,
                    'general-performance'
                );
            }
        }

        return view('frontend.reports.general-performance', compact(
            'years',
            'year',
            'month',
            'salesReps',
            'salesRepId',
            'kpis',
            'topCustomers',
            'topProducts',
            'monthLabels',
            'salesData',
            'orderCountData'
        ));
    }

    /**
     * Display sales conversion report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function salesConversion(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $salesRepId = $request->input('sales_person_id');
        $export = $request->input('export');

        // Get filter options
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get sales representatives list for the dropdown
        $salesReps = DB::table('sales_people')
            ->select('id', 'first_name', 'sales_person_code')
            ->where('ispassive', 0)
            ->orderBy('first_name')
            ->get();

        // Get offers data (we assume these are potentials sales opportunities)
        $offersQuery = DB::table('offers')
            ->select(
                DB::raw('COUNT(id) as offer_count'),
                DB::raw('SUM(amt) as offer_amount')
            )
            ->whereYear('doc_date', $year);

        if ($month) {
            $offersQuery->whereMonth('doc_date', $month);
        }

        if ($salesRepId) {
            // Assuming offers have a sales_person_id field
            $offersQuery->where('sales_person_id', $salesRepId);
        }

        $offers = $offersQuery->first();

        // Get converted orders data
        $ordersQuery = DB::table('orders')
            ->select(
                DB::raw('COUNT(id) as order_count'),
                DB::raw('SUM(amt) as order_amount')
            )
            ->whereYear('doc_date', $year);

        if ($month) {
            $ordersQuery->whereMonth('doc_date', $month);
        }

        if ($salesRepId) {
            $ordersQuery->where('sales_person_id', $salesRepId);
        }

        $orders = $ordersQuery->first();

        // Calculate conversion rates
        $conversionRate = $offers && $offers->offer_count > 0
            ? ($orders->order_count / $offers->offer_count) * 100
            : 0;

        $valueConversionRate = $offers && $offers->offer_amount > 0
            ? ($orders->order_amount / $offers->offer_amount) * 100
            : 0;

        // Get monthly conversion data
        $monthlyData = [];

        for ($i = 1; $i <= 12; $i++) {
            $monthOffers = DB::table('offers')
                ->select(
                    DB::raw('COUNT(id) as offer_count'),
                    DB::raw('SUM(amt) as offer_amount')
                )
                ->whereYear('doc_date', $year)
                ->whereMonth('doc_date', $i)
                ->when($salesRepId, function ($query) use ($salesRepId) {
                    // Assuming offers have a sales_person_id field
                    return $query->where('sales_person_id', $salesRepId);
                })
                ->first();

            $monthOrders = DB::table('orders')
                ->select(
                    DB::raw('COUNT(id) as order_count'),
                    DB::raw('SUM(amt) as order_amount')
                )
                ->whereYear('doc_date', $year)
                ->whereMonth('doc_date', $i)
                ->when($salesRepId, function ($query) use ($salesRepId) {
                    return $query->where('sales_person_id', $salesRepId);
                })
                ->first();

            $monthConversion = $monthOffers && $monthOffers->offer_count > 0
                ? ($monthOrders->order_count / $monthOffers->offer_count) * 100
                : 0;

            $monthValueConversion = $monthOffers && $monthOffers->offer_amount > 0
                ? ($monthOrders->order_amount / $monthOffers->offer_amount) * 100
                : 0;

            $monthlyData[] = [
                'month' => Carbon::create()->month($i)->format('F'),
                'offer_count' => $monthOffers ? $monthOffers->offer_count : 0,
                'offer_amount' => $monthOffers ? $monthOffers->offer_amount : 0,
                'order_count' => $monthOrders ? $monthOrders->order_count : 0,
                'order_amount' => $monthOrders ? $monthOrders->order_amount : 0,
                'conversion_rate' => round($monthConversion, 2),
                'value_conversion_rate' => round($monthValueConversion, 2)
            ];
        }

        // Get sales rep performance data
        $salesRepPerformance = [];

        if (empty($salesRepId)) {
            $salesRepPerformance = DB::table('orders')
                ->join('sales_people', 'orders.sales_person_id', '=', 'sales_people.id')
                ->leftJoin('offers', function ($join) use ($year, $month) {
                    $join->on('orders.sales_person_id', '=', 'offers.sales_person_id')
                        ->whereYear('offers.doc_date', $year)
                        ->when($month, function ($query) use ($month) {
                            return $query->whereMonth('offers.doc_date', $month);
                        });
                })
                ->select(
                    'sales_people.id',
                    'sales_people.first_name',
                    DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                    DB::raw('SUM(orders.amt) as order_amount'),
                    DB::raw('COUNT(DISTINCT offers.id) as offer_count'),
                    DB::raw('SUM(offers.amt) as offer_amount')
                )
                ->whereYear('orders.doc_date', $year)
                ->when($month, function ($query) use ($month) {
                    return $query->whereMonth('orders.doc_date', $month);
                })
                ->groupBy('sales_people.id', 'sales_people.first_name')
                ->get()
                ->map(function ($item) {
                    $convRate = $item->offer_count > 0
                        ? ($item->order_count / $item->offer_count) * 100
                        : 0;

                    $valueConvRate = $item->offer_amount > 0
                        ? ($item->order_amount / $item->offer_amount) * 100
                        : 0;

                    return [
                        'id' => $item->id,
                        'name' => $item->first_name,
                        'order_count' => $item->order_count,
                        'order_amount' => $item->order_amount,
                        'offer_count' => $item->offer_count,
                        'offer_amount' => $item->offer_amount,
                        'conversion_rate' => round($convRate, 2),
                        'value_conversion_rate' => round($valueConvRate, 2)
                    ];
                })
                ->toArray();
        }

        // Summary data
        $summary = [
            'offer_count' => $offers ? $offers->offer_count : 0,
            'offer_amount' => $offers ? $offers->offer_amount : 0,
            'order_count' => $orders ? $orders->order_count : 0,
            'order_amount' => $orders ? $orders->order_amount : 0,
            'conversion_rate' => round($conversionRate, 2),
            'value_conversion_rate' => round($valueConversionRate, 2)
        ];

        // Handle exports if requested
        if ($export) {
            $title = 'Satış Dönüşüm Oranı Raporu - ' . $year;
            $fileName = 'satis_donusum_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    [
                        'summary' => $summary,
                        'monthlyData' => $monthlyData,
                        'salesRepPerformance' => $salesRepPerformance
                    ],
                    $title,
                    $fileName,
                    'frontend.reports.exports.sales-conversion-pdf'
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    [
                        'summary' => $summary,
                        'monthlyData' => $monthlyData,
                        'salesRepPerformance' => $salesRepPerformance
                    ],
                    $title,
                    $fileName,
                    'sales-conversion'
                );
            }
        }

        return view('frontend.reports.sales-conversion', compact(
            'years',
            'year',
            'month',
            'salesReps',
            'salesRepId',
            'summary',
            'monthlyData',
            'salesRepPerformance'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $view The view template to use
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $title, $fileName, $view)
    {
        return Pdf::view($view, [
            'reportData' => $reportData,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param string $title Report title
     * @param string $fileName Output filename
     * @param string $reportType Type of report
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $title, $fileName, $reportType)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);

        if ($reportType === 'general-performance') {
            $sheet->mergeCells('A1:F1');

            // KPIs Section
            $sheet->setCellValue('A3', 'KPI Özeti');
            $sheet->setCellValue('A4', 'Toplam Satış');
            $sheet->setCellValue('B4', $reportData['kpis']['total_sales']);
            $sheet->setCellValue('A5', 'Toplam Sipariş');
            $sheet->setCellValue('B5', $reportData['kpis']['total_orders']);
            $sheet->setCellValue('A6', 'Ortalama Sipariş Değeri');
            $sheet->setCellValue('B6', $reportData['kpis']['average_order_value']);
            $sheet->setCellValue('A7', 'Benzersiz Müşteri Sayısı');
            $sheet->setCellValue('B7', $reportData['kpis']['unique_customers']);
            $sheet->setCellValue('A8', 'Yıllık Büyüme (%)');
            $sheet->setCellValue('B8', $reportData['kpis']['yoy_growth']);

            // Top Customers Section
            $sheet->setCellValue('A10', 'En Çok Alım Yapan Müşteriler');
            $sheet->setCellValue('A11', 'Müşteri Adı');
            $sheet->setCellValue('B11', 'Sipariş Sayısı');
            $sheet->setCellValue('C11', 'Toplam Tutar');

            $row = 12;
            foreach ($reportData['topCustomers'] as $customer) {
                $sheet->setCellValue('A' . $row, $customer->entity_name);
                $sheet->setCellValue('B' . $row, $customer->order_count);
                $sheet->setCellValue('C' . $row, $customer->total_amount);
                $row++;
            }

            // Top Products Section
            $sheet->setCellValue('A' . ($row + 1), 'En Çok Satan Ürünler');
            $sheet->setCellValue('A' . ($row + 2), 'Ürün Kodu');
            $sheet->setCellValue('B' . ($row + 2), 'Ürün Adı');
            $sheet->setCellValue('C' . ($row + 2), 'Satış Miktarı');
            $sheet->setCellValue('D' . ($row + 2), 'Toplam Tutar');

            $row = $row + 3;
            foreach ($reportData['topProducts'] as $product) {
                $sheet->setCellValue('A' . $row, $product->item_code);
                $sheet->setCellValue('B' . $row, $product->item_name);
                $sheet->setCellValue('C' . $row, $product->total_quantity);
                $sheet->setCellValue('D' . $row, $product->total_amount);
                $row++;
            }

            // Monthly Sales Section
            $sheet->setCellValue('A' . ($row + 1), 'Aylık Satış Verileri');
            $sheet->setCellValue('A' . ($row + 2), 'Ay');
            $sheet->setCellValue('B' . ($row + 2), 'Sipariş Sayısı');
            $sheet->setCellValue('C' . ($row + 2), 'Toplam Tutar');

            $row = $row + 3;
            foreach ($reportData['monthlySales'] as $monthlySale) {
                $monthName = Carbon::create()->locale(App::getLocale())->month($monthlySale->month)->format('F');
                $sheet->setCellValue('A' . $row, $monthName);
                $sheet->setCellValue('B' . $row, $monthlySale->order_count);
                $sheet->setCellValue('C' . $row, $monthlySale->total_amount);
                $row++;
            }

        } elseif ($reportType === 'sales-conversion') {
            $sheet->mergeCells('A1:G1');

            // Summary Section
            $sheet->setCellValue('A3', 'Özet');
            $sheet->setCellValue('A4', 'Teklif Sayısı');
            $sheet->setCellValue('B4', $reportData['summary']['offer_count']);
            $sheet->setCellValue('A5', 'Teklif Tutarı');
            $sheet->setCellValue('B5', $reportData['summary']['offer_amount']);
            $sheet->setCellValue('A6', 'Sipariş Sayısı');
            $sheet->setCellValue('B6', $reportData['summary']['order_count']);
            $sheet->setCellValue('A7', 'Sipariş Tutarı');
            $sheet->setCellValue('B7', $reportData['summary']['order_amount']);
            $sheet->setCellValue('A8', 'Dönüşüm Oranı (%)');
            $sheet->setCellValue('B8', $reportData['summary']['conversion_rate']);
            $sheet->setCellValue('A9', 'Değer Dönüşüm Oranı (%)');
            $sheet->setCellValue('B9', $reportData['summary']['value_conversion_rate']);

            // Monthly Data Section
            $sheet->setCellValue('A11', 'Aylık Dönüşüm Verileri');
            $sheet->setCellValue('A12', 'Ay');
            $sheet->setCellValue('B12', 'Teklif Sayısı');
            $sheet->setCellValue('C12', 'Teklif Tutarı');
            $sheet->setCellValue('D12', 'Sipariş Sayısı');
            $sheet->setCellValue('E12', 'Sipariş Tutarı');
            $sheet->setCellValue('F12', 'Dönüşüm Oranı (%)');
            $sheet->setCellValue('G12', 'Değer Dönüşüm Oranı (%)');

            $row = 13;
            foreach ($reportData['monthlyData'] as $monthData) {
                $sheet->setCellValue('A' . $row, $monthData['month']);
                $sheet->setCellValue('B' . $row, $monthData['offer_count']);
                $sheet->setCellValue('C' . $row, $monthData['offer_amount']);
                $sheet->setCellValue('D' . $row, $monthData['order_count']);
                $sheet->setCellValue('E' . $row, $monthData['order_amount']);
                $sheet->setCellValue('F' . $row, $monthData['conversion_rate']);
                $sheet->setCellValue('G' . $row, $monthData['value_conversion_rate']);
                $row++;
            }

            if (!empty($reportData['salesRepPerformance'])) {
                // Sales Rep Performance Section
                $sheet->setCellValue('A' . ($row + 1), 'Satış Temsilcisi Performansı');
                $sheet->setCellValue('A' . ($row + 2), 'Satış Temsilcisi');
                $sheet->setCellValue('B' . ($row + 2), 'Teklif Sayısı');
                $sheet->setCellValue('C' . ($row + 2), 'Teklif Tutarı');
                $sheet->setCellValue('D' . ($row + 2), 'Sipariş Sayısı');
                $sheet->setCellValue('E' . ($row + 2), 'Sipariş Tutarı');
                $sheet->setCellValue('F' . ($row + 2), 'Dönüşüm Oranı (%)');
                $sheet->setCellValue('G' . ($row + 2), 'Değer Dönüşüm Oranı (%)');

                $row = $row + 3;
                foreach ($reportData['salesRepPerformance'] as $salesRep) {
                    $sheet->setCellValue('A' . $row, $salesRep['name']);
                    $sheet->setCellValue('B' . $row, $salesRep['offer_count']);
                    $sheet->setCellValue('C' . $row, $salesRep['offer_amount']);
                    $sheet->setCellValue('D' . $row, $salesRep['order_count']);
                    $sheet->setCellValue('E' . $row, $salesRep['order_amount']);
                    $sheet->setCellValue('F' . $row, $salesRep['conversion_rate']);
                    $sheet->setCellValue('G' . $row, $salesRep['value_conversion_rate']);
                    $row++;
                }
            }
        }

        // Format numbers for monetary values
        $sheet->getStyle('B4:G' . $row)->getNumberFormat()->setFormatCode('#,##0.00');

        // Format percentages
        $sheet->getStyle('F13:G' . $row)->getNumberFormat()->setFormatCode('0.00"%"');

        if ($reportType === 'general-performance') {
            $sheet->getStyle('B8')->getNumberFormat()->setFormatCode('0.00"%"');
        } else {
            $sheet->getStyle('B8:B9')->getNumberFormat()->setFormatCode('0.00"%"');
        }

        // Auto-size columns
        foreach (range('A', 'G') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Style elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3')->getFont()->setBold(true);
        $sheet->getStyle('A11')->getFont()->setBold(true);
        $sheet->getStyle('A12:G12')->getFont()->setBold(true);

        if ($reportType === 'general-performance') {
            $sheet->getStyle('A10')->getFont()->setBold(true);
            $sheet->getStyle('A11:C11')->getFont()->setBold(true);
            $sheet->getStyle('A' . ($row - count($reportData['topProducts']) - 1))->getFont()->setBold(true);
            $sheet->getStyle('A' . ($row - count($reportData['topProducts']) - 1) . ':D' . ($row - count($reportData['topProducts']) - 1))->getFont()->setBold(true);
        } elseif ($reportType === 'sales-conversion' && !empty($reportData['salesRepPerformance'])) {
            $sheet->getStyle('A' . ($row - count($reportData['salesRepPerformance']) - 1))->getFont()->setBold(true);
            $sheet->getStyle('A' . ($row - count($reportData['salesRepPerformance']) - 1) . ':G' . ($row - count($reportData['salesRepPerformance']) - 1))->getFont()->setBold(true);
        }
        $sheet->getStyle('A' . $row . ':G' . $row)->getFont()->setBold(true);
        $sheet->getStyle('A' . ($row + 1))->getFont()->setBold(true);
        $sheet->getStyle('A' . ($row + 2) . ':G' . ($row + 2))->getFont()->setBold(true);
        $sheet->getStyle('A' . ($row + 3) . ':G' . $row)->getFont()->setBold(true);
            // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}