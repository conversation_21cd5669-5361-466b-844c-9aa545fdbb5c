<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Spatie\LaravelPdf\Facades\Pdf;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Support\Facades\App;

class SalesRepPerformanceController extends Controller
{
    /**
     * Display the sales representative performance report
     *
     * @param Request $request
     * @return \Illuminate\View\View|\Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function index(Request $request)
    {
        // Get report parameters
        $year = $request->input('year', date('Y'));
        $month = $request->input('month');
        $salesRepId = $request->input('sales_person_id');
        $export = $request->input('export');

        // Get years list for the dropdown
        $years = DB::table('orders')
            ->selectRaw('YEAR(doc_date) as year')
            ->groupBy('year')
            ->orderBy('year', 'desc')
            ->pluck('year')
            ->toArray();

        // Get sales representatives list for the dropdown
        $salesReps = DB::table('sales_people')
            ->select('id', 'first_name', 'sales_person_code')
            ->where('ispassive', 0)
            ->orderBy('first_name')
            ->get();

        // Build the query for sales rep performance
        $query = DB::table('orders')
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->join('sales_people', 'orders.sales_person_id', '=', 'sales_people.id')
            ->select(
                'sales_people.id as sales_rep_id',
                'sales_people.first_name as sales_rep_name',
                'sales_people.sales_person_code',
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
                DB::raw('COUNT(DISTINCT orders.entity_id) as customer_count'),
                DB::raw('SUM(orders.amt) as total_sales'),
                DB::raw('SUM(orders.amt_vat) as total_vat')
            )
            ->whereYear('orders.doc_date', $year);

        // Apply month filter if specified
        if ($month) {
            $query->whereMonth('orders.doc_date', $month);
        }

        // Apply sales rep filter if specified
        if ($salesRepId) {
            $query->where('orders.sales_person_id', $salesRepId);
        }

        // Group and order the results
        $performanceData = $query
            ->groupBy('sales_people.id', 'sales_people.first_name', 'sales_people.sales_person_code')
            ->orderBy('total_sales', 'desc')
            ->get();

        // Calculate totals
        $totals = [
            'order_count' => $performanceData->sum('order_count'),
            'customer_count' => $performanceData->sum('customer_count'),
            'total_sales' => $performanceData->sum('total_sales'),
            'total_vat' => $performanceData->sum('total_vat')
        ];

        // Get monthly trend data for charts
        $monthlyTrendData = [];

        if ($salesRepId) {
            $monthlyTrend = DB::table('orders')
                ->select(
                    DB::raw('MONTH(doc_date) as month'),
                    DB::raw('COUNT(*) as order_count'),
                    DB::raw('SUM(amt) as total_sales')
                )
                ->where('sales_person_id', $salesRepId)
                ->whereYear('doc_date', $year)
                ->groupBy('month')
                ->orderBy('month')
                ->get();

            foreach ($monthlyTrend as $item) {
                $monthName = Carbon::create()->locale(App::getLocale())->month($item->month)->format('F');
                $monthlyTrendData[] = [
                    'month' => $monthName,
                    'order_count' => $item->order_count,
                    'total_sales' => $item->total_sales
                ];
            }
        }

        // Get top 5 customers for the selected sales rep
        $topCustomers = [];

        if ($salesRepId) {
            $topCustomers = DB::table('orders')
                ->join('entities', 'orders.entity_id', '=', 'entities.id')
                ->select(
                    'entities.id',
                    'entities.entity_name',
                    DB::raw('COUNT(*) as order_count'),
                    DB::raw('SUM(orders.amt) as total_amount')
                )
                ->where('orders.sales_person_id', $salesRepId)
                ->whereYear('orders.doc_date', $year)
                ->when($month, function ($query) use ($month) {
                    return $query->whereMonth('orders.doc_date', $month);
                })
                ->groupBy('entities.id', 'entities.entity_name')
                ->orderBy('total_amount', 'desc')
                ->limit(5)
                ->get();
        }

        // Handle exports if requested
        if ($export) {
            $title = $year . ' Yılı Satış Temsilcisi Performans Raporu';
            $fileName = 'satis_temsilcisi_performans_' . $year . '_' . date('Y-m-d') . '.' . $export;

            if ($export === 'pdf') {
                return $this->exportToPdf(
                    $performanceData->toArray(),
                    $totals,
                    $title,
                    $fileName
                );
            } elseif ($export === 'excel') {
                return $this->exportToExcel(
                    $performanceData->toArray(),
                    $totals,
                    $title,
                    $fileName
                );
            }
        }

        return view('frontend.reports.sales-rep-performance', compact(
            'performanceData',
            'totals',
            'years',
            'year',
            'month',
            'salesReps',
            'salesRepId',
            'monthlyTrendData',
            'topCustomers'
        ));
    }

    /**
     * Export report data to PDF
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToPdf($reportData, $totals, $title, $fileName)
    {
        return Pdf::view('frontend.reports.exports.sales-rep-performance-pdf', [
            'reportData' => $reportData,
            'totals' => $totals,
            'title' => $title
        ])->download($fileName);
    }

    /**
     * Export report data to Excel
     *
     * @param array $reportData The report data to export
     * @param array $totals The report totals
     * @param string $title Report title
     * @param string $fileName Output filename
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    private function exportToExcel($reportData, $totals, $title, $fileName)
    {
        // Create new Spreadsheet object
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set title
        $sheet->setCellValue('A1', $title);
        $sheet->mergeCells('A1:F1');

        // Set headers
        $sheet->setCellValue('A3', 'Satış Temsilcisi');
        $sheet->setCellValue('B3', 'Temsilci Kodu');
        $sheet->setCellValue('C3', 'Sipariş Sayısı');
        $sheet->setCellValue('D3', 'Müşteri Sayısı');
        $sheet->setCellValue('E3', 'Toplam Satış (₺)');
        $sheet->setCellValue('F3', 'Toplam KDV (₺)');

        // Add data
        $row = 4;
        foreach ($reportData as $data) {
            $sheet->setCellValue('A' . $row, $data->sales_rep_name);
            $sheet->setCellValue('B' . $row, $data->sales_person_code);
            $sheet->setCellValue('C' . $row, $data->order_count);
            $sheet->setCellValue('D' . $row, $data->customer_count);
            $sheet->setCellValue('E' . $row, $data->total_sales);
            $sheet->setCellValue('F' . $row, $data->total_vat);
            $row++;
        }

        // Add totals
        $sheet->setCellValue('A' . $row, 'TOPLAM');
        $sheet->mergeCells('A' . $row . ':B' . $row);
        $sheet->setCellValue('C' . $row, $totals['order_count']);
        $sheet->setCellValue('D' . $row, $totals['customer_count']);
        $sheet->setCellValue('E' . $row, $totals['total_sales']);
        $sheet->setCellValue('F' . $row, $totals['total_vat']);

        // Format numbers
        $sheet->getStyle('E4:F' . $row)->getNumberFormat()->setFormatCode('#,##0.00 ₺');

        // Auto-size columns
        foreach (range('A', 'F') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Style elements
        $sheet->getStyle('A1')->getFont()->setBold(true)->setSize(14);
        $sheet->getStyle('A3:F3')->getFont()->setBold(true);
        $sheet->getStyle('A' . $row . ':F' . $row)->getFont()->setBold(true);

        // Create the Excel file
        $writer = new Xlsx($spreadsheet);
        $path = storage_path('app/public/' . $fileName);
        $writer->save($path);

        return response()->download($path)->deleteFileAfterSend(true);
    }
}