<?php

namespace App\Http\Controllers\Frontend\Reports;

use App\Http\Controllers\Controller;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use App\Facades\AI;
use App\Models\Entity;
use App\Models\Order;
use App\Models\Product;
use App\Models\Contract;
use App\Models\Invoice;

class AIController extends Controller
{
    /**
     * AI sorgu formunu göster
     */
    public function index()
    {
        return view('frontend.reports.ai.query-form');
    }

    /**
     * AI sorgusunu işle ve sonuç döndür - İki aşamalı süreç
     */
    public function processQuery(Request $request)
    {
        // Form verilerini doğrula
        $request->validate([
            'query' => 'required|string|max:1000',
        ]);

        $query = $request->input('query');
        $userId = auth()->id();

        try {
            // Ajax isteği mi kontrol et
            $isAjax = $request->ajax() || $request->wantsJson();

            // İki aşamalı sorgu işleme metodunu kullan
            $response = AI::twoStageQuery($query);

            return $response;
            // Sorguyu logla
            $this->logQuery($userId, $query, strlen($response ?? ''));

            // Yanıt boş ise hata mesajı döndür
            if (empty($response)) {
                \Log::error('AI yanıtı boş', ['query' => $query]);
                $htmlResponse = '<p>Üzgünüm, yanıt alınamadı.</p>';

                // Ajax ise JSON yanıt, değilse doğrudan HTML
                return $isAjax ?
                    response()->json(['html_response' => $htmlResponse, 'from_cache' => false]) :
                    $htmlResponse;
            }

            // Ajax istekleri için JSON, normal form submitleri için HTML yanıt
            if ($isAjax) {
                return response()->json([
                    'html_response' => $response,
                    'from_cache' => Cache::has('ai_two_stage_query_' . md5($query))
                ]);
            } else {
                // Doğrudan HTML yanıt döndür
                return $response;
            }

        } catch (\Exception $e) {
            \Log::error('AI sorgu hatası', [
                'error_message' => $e->getMessage(),
                'error_trace' => $e->getTraceAsString(),
                'query' => $query
            ]);

            $errorMessage = '<p>Üzgünüm, yanıt alınamadı: ' . $e->getMessage() . '</p>';

            // Ajax istekleri için JSON, normal form submitleri için HTML yanıt
            return $isAjax ?
                response()->json(['html_response' => $errorMessage, 'from_cache' => false], 500) :
                $errorMessage;
        }
    }

    /**
     * SQL sorgusunu temizle
     */
    private function cleanSqlQuery($sql)
    {
        // ```sql ve ``` gibi kod blokları varsa kaldır
        $sql = preg_replace('/```sql|```/', '', $sql);
        return trim($sql);
    }

    /**
     * SQL sorgusunu çalıştır
     */
    private function executeSqlQuery($sql)
    {
        // Güvenlik için SELECT sorgularını kabul et
        if (!preg_match('/^\s*SELECT/i', $sql)) {
            throw new \Exception('Sadece SELECT sorguları kabul edilir');
        }

        // SQL injection koruması için bir kara liste kontrol et
        $blacklist = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE'];
        foreach ($blacklist as $term) {
            if (stripos($sql, $term) !== false) {
                throw new \Exception('Güvenlik ihlali: İzin verilmeyen SQL ifadesi');
            }
        }

        // Sorguyu çalıştır
        try {
            $results = DB::select($sql);
            return collect($results);
        } catch (\Exception $e) {
            \Log::error('SQL çalıştırma hatası', [
                'sql' => $sql,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Veritabanı şeması bilgilerini al
     */
    private function getDatabaseSchema()
    {
        // Tabloları al
        $tables = DB::select('SHOW TABLES');
        $schema = [];

        // PostgreSQL için: $tables = DB::select("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname != 'pg_catalog' AND schemaname != 'information_schema'");

        foreach ($tables as $table) {
            $tableName = reset($table); // İlk değeri al (tablo adı)

            // Her tablonun sütun bilgilerini al
            $columns = DB::select("DESCRIBE {$tableName}");
            // PostgreSQL için: $columns = DB::select("SELECT column_name, data_type FROM information_schema.columns WHERE table_name = '{$tableName}'");

            $schema[$tableName] = [];

            foreach ($columns as $column) {
                $schema[$tableName][] = [
                    'column' => $column->Field, // PostgreSQL için: $column->column_name
                    'type' => $column->Type,    // PostgreSQL için: $column->data_type
                    'key' => $column->Key      // PostgreSQL için ayrı bir sorgu gerekebilir
                ];
            }
        }

        return $schema;
    }

    /**
     * SQL sorgusu oluşturmak için prompt hazırla
     */
    private function prepareSqlPrompt($query, $schema)
    {
        $prompt = "### Veritabanı Şeması:\n\n";

        foreach ($schema as $table => $columns) {
            $prompt .= "Tablo: {$table}\n";
            $prompt .= "Sütunlar:\n";

            foreach ($columns as $column) {
                $prompt .= "- {$column['column']} ({$column['type']})";

                if (!empty($column['key'])) {
                    $prompt .= " [" . $column['key'] . "]";
                }

                $prompt .= "\n";
            }

            $prompt .= "\n";
        }

        $prompt .= "### Önemli İlişkiler ve Bilgiler:\n";
        $prompt .= "- products tablosunda ürün bilgileri bulunur\n";
        $prompt .= "- orders tablosu siparişlerin ana bilgilerini içerir\n";
        $prompt .= "- orders_d tablosu sipariş detaylarını içerir (order_m_id ile orders tablosuna bağlanır)\n";
        $prompt .= "- entities tablosu müşteri bilgilerini içerir\n";
        $prompt .= "- contracts tablosu sözleşme bilgilerini içerir\n";
        $prompt .= "- invoices tablosu fatura bilgilerini içerir\n";
        $prompt .= "- payments tablosu ödeme bilgilerini içerir\n\n";

        $prompt .= "### Kullanıcı Sorgusu:\n";
        $prompt .= "{$query}\n\n";

        $prompt .= "### Talimatlar:\n";
        $prompt .= "Yukarıdaki kullanıcı sorgusunu analiz ederek, gerekli verileri çekmek için bir SQL sorgusu oluştur.\n";
        $prompt .= "Sadece SQL sorgusunu döndür, açıklama ekleme.\n";
        $prompt .= "Mümkünse ilişkili tabloları JOIN kullanarak bağla.\n";
        $prompt .= "Eğer sorgu belirli bir zaman aralığı belirtiyorsa (son 7 gün, son 30 gün vb.) bunu WHERE koşulunda belirt.\n";
        $prompt .= "Sıralama, gruplama veya toplam hesaplamaları gerektiğinde kullan.\n";
        $prompt .= "Sorguda tarih alanları için MySQL tarih formatını kullan (YYYY-MM-DD).\n";

        return $prompt;
    }

    /**
     * Sorgudan anahtar kelimeleri çıkar
     */
    private function extractKeywords($query)
    {
        // Temel anahtar kelime çıkarma
        $lowercaseQuery = mb_strtolower($query, 'UTF-8');

        $keywords = [
            'ürün' => strpos($lowercaseQuery, 'ürün') !== false || strpos($lowercaseQuery, 'ürünler') !== false,
            'sipariş' => strpos($lowercaseQuery, 'sipariş') !== false || strpos($lowercaseQuery, 'siparişler') !== false,
            'müşteri' => strpos($lowercaseQuery, 'müşteri') !== false || strpos($lowercaseQuery, 'müşteriler') !== false,
            'satış' => strpos($lowercaseQuery, 'satış') !== false || strpos($lowercaseQuery, 'satışlar') !== false,
            'fatura' => strpos($lowercaseQuery, 'fatura') !== false || strpos($lowercaseQuery, 'faturalar') !== false,
            'sözleşme' => strpos($lowercaseQuery, 'sözleşme') !== false || strpos($lowercaseQuery, 'anlaşma') !== false,
            'tarih' => preg_match('/\b\d{2}[-.\/]\d{2}[-.\/]\d{4}\b|\b\d{4}[-.\/]\d{2}[-.\/]\d{2}\b/', $query) > 0,
            'tutar' => strpos($lowercaseQuery, 'tutar') !== false || strpos($lowercaseQuery, 'fiyat') !== false,
            'analiz' => strpos($lowercaseQuery, 'analiz') !== false || strpos($lowercaseQuery, 'rapor') !== false,
            'stok' => strpos($lowercaseQuery, 'stok') !== false || strpos($lowercaseQuery, 'envanter') !== false,
            'son' => strpos($lowercaseQuery, 'son') !== false,
            'en çok' => strpos($lowercaseQuery, 'en çok') !== false || strpos($lowercaseQuery, 'en fazla') !== false,
            'en az' => strpos($lowercaseQuery, 'en az') !== false || strpos($lowercaseQuery, 'en düşük') !== false,
            'listele' => strpos($lowercaseQuery, 'listele') !== false || strpos($lowercaseQuery, 'göster') !== false,
            'karşılaştır' => strpos($lowercaseQuery, 'karşılaştır') !== false || strpos($lowercaseQuery, 'kıyasla') !== false,
        ];

        // Zaman dilimi ifadelerini tanıma
        $timePatterns = [
            'son 7 gün' => preg_match('/son\s+7\s+gün|\bgeçen\s+hafta\b/i', $query),
            'son 30 gün' => preg_match('/son\s+30\s+gün|\bgeçen\s+ay\b|\bbu\s+ay\b/i', $query),
            'son 90 gün' => preg_match('/son\s+90\s+gün|\bson\s+3\s+ay\b|\büç\s+ay\b/i', $query),
            'son 365 gün' => preg_match('/son\s+365\s+gün|\bson\s+1\s+yıl\b|\bgeçen\s+yıl\b/i', $query),
        ];

        return array_merge($keywords, $timePatterns);
    }

    /**
     * Veritabanından ilgili verileri çek
     */
    private function fetchRelevantData($query, $keywords)
    {
        $data = [];

        // Ürün bilgileri
        if ($keywords['ürün'] || $keywords['stok']) {
            $products = DB::table('products')
                ->leftJoin('units', 'products.unit_id', '=', 'units.id')
                ->leftJoin('brands', 'products.brand_id', '=', 'brands.id')
                ->select('products.id', 'products.item_code', 'products.item_name',
                    'units.unit_name', 'brands.description as brand_name',
                    'products.density', 'products.net_weight')
                ->where('products.ispassive', '!=', 1)
                ->limit(100)
                ->get();

            $data['products'] = $products;
        }

        // Belirli bir tarih aralığı için filtre belirle
        $dateFilter = now()->subDays(30); // varsayılan son 30 gün

        if ($keywords['son 7 gün']) {
            $dateFilter = now()->subDays(7);
        } elseif ($keywords['son 90 gün']) {
            $dateFilter = now()->subDays(90);
        } elseif ($keywords['son 365 gün']) {
            $dateFilter = now()->subDays(365);
        }

        // Sipariş bilgileri
        if ($keywords['sipariş'] || $keywords['satış']) {
            $orders = DB::table('orders')
                ->join('entities', 'orders.entity_id', '=', 'entities.id')
                ->leftJoin('companies', 'orders.co_id', '=', 'companies.id')
                ->select('orders.id', 'orders.doc_no', 'orders.doc_date',
                    'entities.entity_name', 'orders.amt', 'orders.amt_vat')
                ->when($keywords['son'], function($query) use ($dateFilter) {
                    return $query->where('orders.doc_date', '>=', $dateFilter);
                })
                ->orderBy('orders.doc_date', 'desc')
                ->limit(50)
                ->get();

            // Son dönem sipariş detayları
            $recentOrderDetails = DB::table('orders_d')
                ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                ->join('products', 'orders_d.item_id', '=', 'products.id')
                ->select('orders.doc_no', 'orders.doc_date', 'products.item_name',
                    'orders_d.qty', 'orders_d.unit_price', 'orders_d.amt')
                ->when($keywords['son'], function($query) use ($dateFilter) {
                    return $query->where('orders.doc_date', '>=', $dateFilter);
                })
                ->orderBy('orders.doc_date', 'desc')
                ->limit(100)
                ->get();

            $data['orders'] = $orders;
            $data['recent_order_details'] = $recentOrderDetails;
        }

        // En çok satılan ürünler
        if ($keywords['en çok'] && ($keywords['ürün'] || $keywords['satış'])) {
            $topSellingProducts = DB::table('orders_d')
                ->join('orders', 'orders_d.order_m_id', '=', 'orders.id')
                ->join('products', 'orders_d.item_id', '=', 'products.id')
                ->select('products.item_name', 'products.item_code',
                    DB::raw('SUM(orders_d.qty) as total_quantity'),
                    DB::raw('SUM(orders_d.amt) as total_amount'),
                    DB::raw('COUNT(DISTINCT orders.id) as order_count'))
                ->when($keywords['son'], function($query) use ($dateFilter) {
                    return $query->where('orders.doc_date', '>=', $dateFilter);
                })
                ->groupBy('products.id', 'products.item_name', 'products.item_code')
                ->orderBy('total_quantity', 'desc')
                ->limit(20)
                ->get();

            $data['top_selling_products'] = $topSellingProducts;
        }

        // En çok alım yapan müşteriler
        if ($keywords['en çok'] && $keywords['müşteri']) {
            $topCustomers = DB::table('orders')
                ->join('entities', 'orders.entity_id', '=', 'entities.id')
                ->select('entities.entity_name', 'entities.entity_code',
                    DB::raw('COUNT(orders.id) as order_count'),
                    DB::raw('SUM(orders.amt) as total_amount'))
                ->when($keywords['son'], function($query) use ($dateFilter) {
                    return $query->where('orders.doc_date', '>=', $dateFilter);
                })
                ->groupBy('entities.id', 'entities.entity_name', 'entities.entity_code')
                ->orderBy('total_amount', 'desc')
                ->limit(20)
                ->get();

            $data['top_customers'] = $topCustomers;
        }

        // Diğer veri çekme metodları...

        return $data;
    }

    /**
     * Veri setini AI'ya gönderilecek metin bağlamına dönüştür
     */
    private function prepareContextFromData($data)
    {
        // Eğer data koleksiyon ise (SQL sorgusu sonucu)
        if ($data instanceof \Illuminate\Support\Collection) {
            if ($data->isEmpty()) {
                return "Verilen sorgu için veritabanında eşleşen veri bulunamadı.";
            }

            $context = "Veritabanı sorgu sonuçları:\n\n";

            // İlk 50 satırı ekle
            $limitedData = $data->take(50);

            // Tablo başlıklarını al (ilk satırın anahtarları)
            if ($limitedData->isNotEmpty()) {
                $firstItem = $limitedData->first();
                if (is_object($firstItem)) {
                    $headers = array_keys(get_object_vars($firstItem));
                } else if (is_array($firstItem)) {
                    $headers = array_keys($firstItem);
                } else {
                    $headers = ['value'];
                }

                $context .= "Sütunlar: " . implode(", ", $headers) . "\n\n";

                // Verileri satır satır ekle
                foreach ($limitedData as $index => $row) {
                    $context .= "Satır " . ($index + 1) . ":\n";

                    if (is_object($row)) {
                        foreach (get_object_vars($row) as $key => $value) {
                            $context .= "- $key: " . $this->formatValue($value) . "\n";
                        }
                    } else if (is_array($row)) {
                        foreach ($row as $key => $value) {
                            $context .= "- $key: " . $this->formatValue($value) . "\n";
                        }
                    } else {
                        $context .= "- value: " . $this->formatValue($row) . "\n";
                    }

                    $context .= "\n";
                }

                // Eğer veri seti büyükse, bunun bir özeti olduğunu belirt
                if ($data->count() > 50) {
                    $context .= "Not: Toplam " . $data->count() . " satır veri bulunmaktadır, yukarıda ilk 50 satır gösterilmiştir.\n";
                }
            }

            return $context;
        }

        // Eğer data bir diziyse (eski fetching metodu)
        $context = "Sistem tarih: " . now()->format('d.m.Y') . "\n\n";

        // Özet istatistikleri
        if (isset($data['summary'])) {
            $context .= "Özet Bilgiler:\n";
            $context .= "- Toplam Ürün Sayısı: " . number_format($data['summary']['total_products'], 0, ',', '.') . "\n";
            $context .= "- Toplam Müşteri Sayısı: " . number_format($data['summary']['total_customers'], 0, ',', '.') . "\n";
            $context .= "- Bu Ayki Sipariş Sayısı: " . number_format($data['summary']['total_orders_this_month'], 0, ',', '.') . "\n";
            $context .= "- Bu Ayki Toplam Satış: " . number_format($data['summary']['total_sales_this_month'], 2, ',', '.') . " TL\n";
            $context .= "- Aktif Sözleşme Sayısı: " . number_format($data['summary']['active_contracts'], 0, ',', '.') . "\n\n";
        }

        // En çok satılan ürünler
        if (isset($data['top_selling_products']) && $data['top_selling_products']->count() > 0) {
            $context .= "En Çok Satan Ürünler:\n";
            foreach ($data['top_selling_products'] as $index => $product) {
                if ($index < 10) {
                    $context .= "- " . $product->item_name . " (" . $product->item_code . ")";
                    $context .= ", Satış Miktarı: " . number_format($product->total_quantity, 2, ',', '.');
                    $context .= ", Toplam Tutar: " . number_format($product->total_amount, 2, ',', '.') . " TL\n";
                }
            }
            $context .= "\n";
        }

        // En çok alım yapan müşteriler
        if (isset($data['top_customers']) && $data['top_customers']->count() > 0) {
            $context .= "En Çok Alım Yapan Müşteriler:\n";
            foreach ($data['top_customers'] as $index => $customer) {
                if ($index < 10) {
                    $context .= "- " . $customer->entity_name;
                    $context .= ", Sipariş Sayısı: " . $customer->order_count;
                    $context .= ", Toplam Tutar: " . number_format($customer->total_amount, 2, ',', '.') . " TL\n";
                }
            }
            $context .= "\n";
        }

        // Diğer bağlamlar...

        return $context;
    }

    /**
     * Değerleri formatla
     */
    private function formatValue($value)
    {
        if (is_null($value)) {
            return 'NULL';
        } else if (is_bool($value)) {
            return $value ? 'true' : 'false';
        } else if (is_array($value) || is_object($value)) {
            return json_encode($value, JSON_UNESCAPED_UNICODE);
        } else {
            return (string) $value;
        }
    }

    /**
     * Kullanıcı sorgularını kaydet
     */
    private function logQuery($userId, $query, $responseLength)
    {
        // Sorguları kaydetmek için activity_log tablosunu kullan
        DB::table('activity_log')->insert([
            'log_name' => 'ai_query',
            'description' => $query,
            'subject_type' => 'App\Models\User',
            'event' => 'ai_query',
            'subject_id' => $userId,
            'properties' => json_encode([
                'query_length' => strlen($query),
                'response_length' => $responseLength,
                'user_ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
            ]),
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}