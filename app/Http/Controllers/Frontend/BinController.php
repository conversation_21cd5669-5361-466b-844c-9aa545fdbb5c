<?php
namespace App\Http\Controllers\Frontend;


use App\Services\BkmBinService;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;

class BinController extends Controller
{
    private BkmBinService $bkmBinService;

    public function __construct(BkmBinService $bkmBinService)
    {
        $this->bkmBinService = $bkmBinService;
    }

    public function query(): JsonResponse
    {
        try {
            $result = $this->bkmBinService->queryBin();
            return response()->json($result);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
