<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Mail;
use Validator;
use Carbon\Carbon;
use App\Models\Announcement;
use App\Models\Page;
use App\Models\User;


class PageController extends Controller
{
    public function about()
    {
        $data['hasAside'] = true;
        return view('frontend.pages.about', $data);
    }

    public function contact()
    {
        $data['hasAside'] = true;
        return view('frontend.pages.contact', $data);
    }

    public function help()
    {
        $data['hasAside'] = true;
        return view('frontend.pages.help', $data);
    }

    public function privacy()
    {
        $data['hasAside'] = true;
        return view('frontend.pages.privacy', $data);
    }

    public function terms()
    {
        $data['hasAside'] = true;
        return view('frontend.pages.terms', $data);
    }

    public function announcements()
    {

        $user = auth()->user();

        $today = Carbon::today();

        if ($user->can('create_announcement')) {
            $announcements = Announcement::latest()->simplePaginate(15);
        } else {

            $announcements = Announcement::whereDate('start_date', '<=', $today)->whereDate('end_date', '>=', $today)->latest()->simplePaginate(15);


        }
        $data['announcements'] = $announcements;


        return view('frontend.pages.announcements', $data);
    }


    public function storeAnnouncement(Request $request)
    {

        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'start_date' => 'required|date|before_or_equal:end_date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $id = $request->id;
        if ($id) {
            $announcement = Announcement::find($id);
            $announcement->update([
                'content' => $request->content,
                'title' => $request->title,
                'start_date' => $request->start_date,
                'end_date' => $request->end_date,
            ]);
            flash('Duyuru başarıyla güncellendi.')->success();
            return redirect()->route('announcements')->with('success', 'Duyuru başarıyla güncellendi.');
        }

        Announcement::create([
            'user_id' => auth()->id(),
            'title' => $request->title,
            'content' => $request->content,
            'start_date' => $request->start_date,
            'end_date' => $request->end_date,
        ]);
        flash('Mesajınız başarıyla gönderildi.')->success();
        return redirect()->route('announcements')->with('success', 'Duyuru başarıyla eklendi.');
    }

    public function page($slug)
    {
        $delete_media = request('media_id');
        $page = Page::where('slug', $slug)->first();

        $data['page'] = $page;

        if ($delete_media) {

            $media = $page->getMedia('page')->whereIn('id', $delete_media);
            $media->each(function ($item) {
                $item->delete();
            });
            return redirect()->route('page', $page->slug);
        }

        return view('frontend.pages.page', $data);
    }

    public function pageUpdate()
    {
        $input = request()->only('content', 'title', 'slug');
        $page = Page::where('slug', request('slug'))->first();
        if ($page) {
            $page->update($input);
        } else {
            $page = Page::create($input);
        }

        if (request()->hasFile('media')) {
            foreach (request()->file('media') as $file) {
                $page->addMedia($file)->toMediaCollection('page');
            }
        }


        return redirect()->route('page', $page->slug);
    }


    public function contact_post(Request $request)
    {
        $data['hasAside'] = true;
        $user = auth()->user();

        $request->validate([
            'email' => 'required|email',
            'subject' => 'required',
            'message' => 'required',
        ]);

        Mail::send('emails.contact_form',
            array(
                'name' => $user->name,
                'email' => $request->get('email'),
                'user_message' => $request->get('message')
            ), function ($message) use ($request) {
                $message->from('<EMAIL>');
                $message->to('<EMAIL>', 'B2B Portal')->subject($request->get('subject') ?? 'B2B Portalı İletişim Formu');
            });


        flash('Mesajınız başarıyla gönderildi.')->success();

        return view('frontend.pages.contact', $data);
    }


}
