<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Contract;
use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use App\Models\DailyCurRate;
use App\Models\LetterCredit;
use App\Models\Offer;
use App\Models\Order;
use App\Models\Company;
use App\Models\Shipment;
use DB;
use Carbon\Carbon;
use Modules\Article\Models\Post;
use Illuminate\Support\Facades\Cookie;

class HomeController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        if(!$user) return redirect()->route('home');

        return view('frontend.index');
    }

    /**
     * Privacy Policy Page.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function home()
    {
        $user = auth()->user();
        $active_day = request('day', 30); $days = [7,15,30,60,90,180,365];
        if(!in_array($active_day, $days) ) {
            $active_day = 30;
        }
        // Check if the 'popup' cookie exists, if not, create it with a 1-week expiration
        if (!Cookie::get('popup')) {
            Cookie::queue('popup', 'shown', 5); // 10080 minutes = 1 week
        }

        $currencies = Currency::whereNotNull('is_active')->get();
        $currency_last_update_at = DailyCurRate::latest()->value('updated_at');
        $offers = Offer::select('id', 'doc_date', 'doc_no', 'amt')->where('entity_id', $user->active_entity_id)->orderByDesc('id')->take(10)->get();
        $orders = Order::where('entity_id', $user->active_entity_id)->orderByDesc('id')->take(10)->get();
        $letter_credit = LetterCredit::select('id', 'letter_credit_no','due_date', 'amt')->where('entity_id', $user->active_entity_id)->first();
        $contracts = Contract::where('entity_id', $user->active_entity_id)->where('ispassive',0)->where('request_status', 4)->orderByDesc('remaining_amount')->take(4)->get();
        $data['contracts_end_date'] = Contract::where('entity_id', $user->active_entity_id)->whereDate('contract_end_date', '>', date('Y-m-d'))->where('request_status', 4)->orderBy('contract_end_date', 'asc')->where('ispassive',0)->take(4)->get();

        $last30DayOrders = DB::table('orders')
            ->select(DB::raw('DATE(doc_date) as date'), DB::raw('SUM(amt) as total_amt'))
            ->where('entity_id', $user->active_entity_id)
            ->whereDate('doc_date', '>=', Carbon::now()->subDays($active_day))
            ->groupBy(DB::raw('DATE(doc_date)'))
            ->get();
        $last30DayOrdersCount = DB::table('orders')
            ->select('id')
            ->where('entity_id', $user->active_entity_id)
            ->whereDate('doc_date', '>=', Carbon::now()->subDays($active_day))
            ->count();
// Son 30 günün tarihlerini içeren bir dizi oluştur
        $dates = [];
        for ($i = 0; $i < $active_day; $i++) {
            $dates[Carbon::now()->subDays($i)->format('Y-m-d')] = 0;
        }
// Sorgu sonucunda dönen siparişlerin tarihlerini diziye ekle
        foreach ($last30DayOrders as $order) {
            $dates[$order->date] = $order->total_amt;
        }

// Diziyi tarihe göre sırala
        ksort($dates);
        $last30labels = [];
        $last30data = [];
        foreach ($dates as $date => $total_amt) {
            $last30labels[] = Carbon::parse($date)->format('d'); // Tarihi 'd' formatına dönüştürür (sadece gün)
            $last30data[] = $total_amt;
        }

        if ($letter_credit) {
            $now = Carbon::now();
            $letter_credit_due_day_count = $letter_credit->due_date->diffInDays($now);
            $data['letter_credit_due_day_count'] = number_format($letter_credit_due_day_count);

        }
        $shipments = Shipment::select('doc_no', 'order_doc_no', 'id', 'doc_date', 'created_at', 'source_m_id')->where('entity_id', $user->active_entity_id)->latest()->take(3)->get();


        $order_m_ids = Order::where('entity_id', $user->active_entity_id)->whereDate('doc_date', Carbon::today()->subDays(2))->pluck('id');
        $data['todayTotal'] = DB::table('orders_d')
            ->select(DB::raw('SUM(qty) as qty'), DB::raw('SUM(zz_qty2) as quantity_pallet'), DB::raw('SUM(amt_tra) as amt_tra'))
            ->whereIn('order_m_id', $order_m_ids)
            ->first();

        $data['last30labels'] = json_encode($last30labels);
        $data['last30data'] = json_encode($last30data);
        $data['last30DayOrdersTotal'] = $last30DayOrders->sum('total_amt');
        $data['last30DayOrdersCount'] = $last30DayOrdersCount;
        $data['active_day'] = $active_day;

        $data['currencies'] = $currencies;
        $data['currency_last_update_at'] = $currency_last_update_at;
        $data['shipments'] = $shipments;
        $data['offers'] = $offers;
        $data['orders'] = $orders;
        $data['contracts'] = $contracts;
        $data['letter_credit'] = $letter_credit;
        $entity_ids = $user->entities->pluck('id')->toArray();

        $popup_excluded_entities = setting('popup_excluded_entities', '');
        $popup_closed_entity_ids = $popup_excluded_entities ? explode(',', $popup_excluded_entities) : [];
        $popup_closed_entity_ids = array_map('intval', $popup_closed_entity_ids);
        
        if (!in_array($user->active_entity_id, $popup_closed_entity_ids)) {
            $data['popup'] = Post::where('type', 'popup')->orderByDesc('id')->first();
        }

        return view('frontend.home', $data);
    }

    public function contactInfos()
    {
        $companies = Company::where('id', 2725)->get();

        return view('frontend.pages.contact_infos')->with('companies', $companies);
    }

}
