<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\CartItem;
use App\Models\City;
use App\Models\Contract;
use App\Models\ContractDiscount;
use App\Models\DailyCurRate;
use App\Models\Dbs;
use App\Models\Entity;
use App\Models\Price;
use App\Models\PriceList;
use App\Models\PriceRule;
use App\Models\Product;
use App\Models\Setting;
use App\Models\Tax;
use Yajra\DataTables\Facades\DataTables;
use DB;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Town;
use Carbon\Carbon;
use App\Models\Order;
use Illuminate\Support\Number;
use App\Models\Category;
use App\Models\Address;
use Illuminate\Support\Facades\Artisan;
use App\Models\BinNumber;
use App\Models\Payment;
use App\Models\ItemUnit;


class AjaxController extends Controller
{


    public function get_installment()
    {
        $card_number = request()->card_number;
        $contract_id = request()->contract_id;
        $payment_id = request()->payment_id;
        $amount = request()->amount;

        // Remove any thousand separators and replace comma with dot for decimal
        // $amount = str_replace(['.', ','], ['', '.'], $amount);

        $amount = formatNumber($amount);

        $user = auth()->user();

        $amt_total = (float)$amount; // Cast to float to ensure numeric operations work
        if ($payment_id) {
            $payment = Payment::where('id', $payment_id)->first();
            if ($amount > $payment->remaining_amount) {
                return ['success' => false, 'message' => 'Ödenecek tutar kalan tutardan büyük olamaz. Kalan toplam tutar: ' . Number::currency($payment->remaining_amount, 'TRY', 'tr'), 'remaining_amount' => number_format($payment->remaining_amount, 2)];
            } else {
                if (empty(request()->card_number)) {
                    return ['success' => false, 'message' => 'Kart numarası giriniz!', 'remaining_amount' => number_format($amount, 2)];
                }
            }
        }

        if ($amt_total > 0) {
            $cart_total_display = Number::currency($amt_total, 'TRY', 'tr');
            $cart_total = $amt_total; // Keep numeric value for calculations
            if (strlen($card_number) < 6) {
                return ['success' => false, 'message' => 'Kart numarası giriniz!'];
            }
        } else {
            $cart_total_display = Number::currency(0, 'TRY', 'tr');
            $cart_total = 0;
        }

        $binNumber = str_replace(' ', '', $card_number);
        $binNumber = substr($binNumber, 0, 6);

        if ($binNumber == null) {
            return ' <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>
                                                                                        <span class="placeholder col-12 placeholder-lg"></span>';
        }

        $bin = BinNumber::where('bin', $binNumber)->first();
        $taksit_sayisi = 0;
        $ek_taksit_sayisi = 0;

        if ($bin) {

            $selectedPos_active = Setting::where('name', $bin->pos_name)->value('val');
            if ($selectedPos_active == 0) {
                $taksit_sayisi = 1;
                $ek_taksit_sayisi = 0;
            } else {
                $bin_name = 'taksit_sayisi_' . $bin->bank . '_' . $bin->card_type;
                $taksit_sayisi = Setting::where('name', $bin_name)->value('val');
                $ek_taksit_sayisi = Setting::where('name', 'ek_' . $bin_name)->value('val');
            }

        }


        $html = '  <ul class="custom-control-group custom-control-vertical custom-control-stacked w-100 mb-1">
                                                                                        <li>
                                                                                            <div class="custom-control custom-radio custom-control-pro no-control">
                                                                                                <input type="radio"
                                                                                                       class="custom-control-input"
                                                                                                       name="installment"
                                                                                                       required
                                                                                                         value="1"
                                                                                                       id="btnRadioNc1"><label
                                                                                                        class="custom-control-label"
                                                                                                        for="btnRadioNc1">TEK ÇEKİM - ' . $cart_total . '</label></div>
                                                                                        </li>';
        for ($i = 2; $i <= $taksit_sayisi; $i++) {
            $html .= '<li>
                         <div class="custom-control custom-radio custom-control-pro no-control">
                           <input type="radio" class="custom-control-input" name="installment" value="' . $i . '" required id="btnRadioNc' . $i . '">
                                          <label class="custom-control-label" for="btnRadioNc' . $i . '">' . $i . ' TAKSİT - ' . $i . ' X ' . number_format($amt_total / $i, 2) . ' = ' . $cart_total;
            if ($ek_taksit_sayisi > 0) {
                $html .= ' ( +' . $ek_taksit_sayisi . ' EK TAKSİT )';
            }

            $html .= '</label>
                                          </div>
                  </li>';
        }

        $html .= '</ul>';
        return ['success' => true, 'message' => '', 'html' => $html, 'amount' => $amount, 'cart_total' => $cart_total_display];

    }

    public function get_products()
    {
        $category_id = request()->get('category_id');
        $request_density = request()->get('density');
        $contract_id = request()->get('contract_id');
        $request_width = request()->get('width');
        $request_height = request()->get('height');
        $request_depth = request()->get('depth');

        $products = Product::select('id as value', 'item_name as text')->where('is_open_to_internet', 1);

        if ($contract_id == 0) {
            $latest_price_list_id = PriceList::where('ispassive', 0)->where('co_id', 2725)->where('is_default', 1)->orderByDesc('id')->value('id');
            $product_ids = Price::where('price_list_m_id', $latest_price_list_id)->pluck('item_id');
            $products = $products->whereIn('id', $product_ids);
        } else {
            $price_list_id = DB::table('contract_price_list')->where('contract_id', $contract_id)->value('price_list_id');

            if ($price_list_id) {
                $product_ids = Price::where('price_list_m_id', $price_list_id)->pluck('item_id');
                $products = $products->whereIn('id', $product_ids);
            }
        }

        if ($category_id !== 'all') {
            $products = $products->where('categories2_id', $category_id);
        }

        if ($request_density > 0) {
            $products = $products->where('categories5_id', $request_density);
        }
        if ($request_width > 0) {
            $products = $products->where('width', $request_width);
        }
        if ($request_height > 0) {
            $products = $products->where('height', $request_height);
        }
        if ($request_depth > 0) {
            $products = $products->where('depth', $request_depth);
        }

        $depth_categories = Category::where('step', 6)->where('zz_b2b', 1)->pluck('id');
        $density_category_ids = Product::select('categories5_id')->distinct()->groupBy('categories5_id')->where('is_open_to_internet', 1)->whereIn('id', $products->pluck('value'))->get();
        $density_categories = Category::select('id', 'categories_code')->whereIn('id', $density_category_ids)->where('step', 5)->where('zz_b2b', 1)->get();
        $depths = Product::select('depth as value', 'depth as text')->where('is_open_to_internet', 1)->whereIn('categories6_id', $depth_categories)->whereIn('id', $products->pluck('value'))->groupBy('depth')->pluck('value', 'text');

        $widths = Product::select('width as value', 'width as text')->where('is_open_to_internet', 1)->whereIn('id', $products->pluck('value'))->groupBy('width')->get()->pluck('value', 'text');
        $heights = Product::select('height as value', 'height as text')->where('is_open_to_internet', 1)->whereIn('id', $products->pluck('value'))->groupBy('height')->pluck('value', 'text');

        $density_select = '<option value="0">Tümü</option>';
        foreach ($density_categories as $density) {
            if ($request_density == $density->id) {
                $density_select .= '<option value="' . $density->id . '" selected>' . $density->categories_code . '</option>';
            } else {
                $density_select .= '<option value="' . $density->id . '">' . $density->categories_code . '</option>';
            }
        }


        $width_select = '<option value="0">Tümü</option>';
        foreach ($widths as $width) {
            if ($request_width === $width) {
                $width_select .= '<option value="' . $width . '" selected>' . $width * 1000 . ' mm</option>';
            } else {
                $width_select .= '<option value="' . $width . '">' . $width * 1000 . ' mm</option>';
            }
        }

        $height_select = '<option value="0">Tümü</option>';
        foreach ($heights as $height) {
            if ($request_height === $height) {
                $height_select .= '<option value="' . $height . '" selected>' . $height * 1000 . ' mm</option>';
            } else {
                $height_select .= '<option value="' . $height . '">' . $height * 1000 . ' mm</option>';
            }
        }

        $depth_select = '<option value="0">Tümü</option>';
        foreach ($depths as $depth) {
            if ($request_depth === $depth) {
                $depth_select .= '<option value="' . $depth . '" selected>' . $depth * 100 . ' cm</option>';
            } else {
                $depth_select .= '<option value="' . $depth . '">' . $depth * 100 . ' cm</option>';
            }
        }

        return ['success' => true, 'products' => $products->pluck('value', 'text'), 'densities' => $density_select, 'widths' => $width_select, 'heights' => $height_select, 'depths' => $depth_select];
    }

    public function get_product()
    {
        $product_id = request()->get('product_id');
        if (!is_numeric($product_id)) {
            return ['success' => false, 'message' => 'Ürün seçiniz!'];
        }

        $product = Product::where('id', $product_id)->first();

        if ($product->density_unit_id) {
            $product_density = $product->density . ' ' . $product->density_unit->unit_code;
        } else {
            $product_density = '-';
        }

        $html = '<table class="table table-stripped">
                        <thead>
                                                                    <tr>
                                                                        
                                                                        <th>YOĞUNLUK</th>
                                                                        <th>KALINLIK</th>
                                                                        <th>EN/BOY</th>
                                                                        <th>KAPLAMA</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    <tr>
                                                                        
                                                                        <td>' . $product_density . '</td>
                                                                        <td>' . $product->depth * 1000 . ' MM</td>
                                                                        <td>' . $product->height * 1000 . 'X' . $product->width * 1000 . ' MM</td>
                                                                        <td>' . $product->category4->description . '</td>
                                                                    </tr>

                                                                    </tbody>
                                                                </table>';
        return ['html' => $html, 'success' => true];
    }

    public function get_product_price(Request $request)
    {
        $user = auth()->user();
        $is_unit_pallet = true;
        $is_unit_truck = false;
        $is_unit_ton = false;
        $product_id = request()->get('product_id');
        $quantity_truck = request()->get('quantity_truck');
        $quantity_truck = str_replace(',', '.', $quantity_truck);
        $quantity_pallet = request()->get('quantity_pallet');
        $quantity_pallet = str_replace(',', '.', $quantity_pallet);
        $qty = request()->get('qty');
        $qty = str_replace(',', '.', $qty);
        $contract_id = request()->get('contract_id');
        $quantity_ton = 0;
        $today = Carbon::today();

        if (!$product_id) {
            return ['success' => false, 'message' => 'Ürün seçiniz!'];
        }
        try {
            //  $unit_rates = uyumapi('SELECT * from invd_item_unit WHERE item_id = ' . $product_id);
            $unit_rates = ItemUnit::where('item_id', $product_id)->get();
            if ($unit_rates) {
                foreach ($unit_rates as $unit_rate) {
                    if (isset($unit_rate['unit2_id'])) {
                        if ($quantity_pallet > 0) {
                            if ($unit_rate['unit2_id'] == 252) { // metrekare
                                $qty = $quantity_pallet * $unit_rate['rate'];
                                $is_unit_pallet = true;
                            }
                        } elseif ($quantity_truck > 0) {
                            if ($unit_rate['unit2_id'] == 270) {
                                $qty = $quantity_truck * $unit_rate['rate'];
                                $is_unit_truck = true;
                            }
                        }
                    }
                }

                if ($quantity_truck > 0 and $is_unit_truck === false) {
                    return ['success' => false, 'message' => 'Tır bilgisi bulunamadı! Diğer birim giriniz.'];
                }
                if ($quantity_pallet > 0 and $is_unit_pallet === false) {
                    return ['success' => false, 'message' => 'Palet bilgisi bulunamadı!'];
                }

                foreach ($unit_rates as $unit_rate) {
                    if (isset($unit_rate['unit2_id'])) {
                        if ($unit_rate['unit2_id'] == 165) { // KG
                            $quantity_ton = ($qty * $unit_rate['rate2']) / 1000;
                        } elseif ($unit_rate['unit2_id'] == 252) { // pallet
                            $quantity_pallet = $qty / $unit_rate['rate'];
                        } elseif ($unit_rate['unit2_id'] == 270) {   // tır
                            $quantity_truck = $qty / $unit_rate['rate'];
                        }
                    }
                }
            } else {
                return ['success' => false, 'message' => 'Ürün fiyat bilgisi bulunamadı!'];
            }
        } catch (\Exception $e) {
            return ['success' => false, 'message' => 'Birim bilgisi alınamadı!'];
        }

        $product = Product::find($product_id);
        // 165:KG // 193:metrekare // 252:palet // 270:tır
        if (is_numeric($quantity_truck) && floor($quantity_truck) != $quantity_truck) {
            if ($user->active_entity->order_quantity) {
                return ['success' => false, 'message' => 'Tır miktarı tam sayı olmalıdır!'];
            }
        }

        if ($quantity_ton == 0) {
            return ['success' => false, 'message' => 'Ürün fiyatı bulunamadı!'];
        }
        $contract = Contract::where('id', $contract_id)->first();
        $zz_ton_price = null;
        if ($contract) {
            $zz_ton_price = $contract->zz_ton_price;
            $price_list = $contract->priceList->first();
        } else {
            $price_list = PriceList::where('is_default', 1)->first();
        }

        $covering_fee = 0;
        $covering_unit_price = 0;
        if (in_array($product->categories4_id, [2181, 2182, 2183])) { //kaplama türü alüminyum folyo veya siyah cam tülü
            if ($product->categories4_id == 2181) {
                $covering_price = Price::select('unit_price_tra', 'cur_tra_id')->where('item_id', 124901)->where('price_list_m_id', 951)->first();
            } elseif ($product->categories4_id == 2182) {
                $covering_price = Price::select('unit_price_tra', 'cur_tra_id')->where('item_id', 122739)->where('price_list_m_id', 951)->first();
            }
            if ($covering_price) {
                $cur_rate = DailyCurRate::where('cur_from_id', $covering_price->cur_tra_id)->where('cur_rate_type_id', 277)->orderByDesc('id')->value('cur_rate_tra');
                $covering_unit_price = $covering_price->unit_price_tra * $cur_rate;
                $covering_fee = $qty * $covering_unit_price;
            }
        }

        if ($price_list) {
            if ($zz_ton_price == null) {
                $zz_ton_price = $price_list->zz_ton_price;
            }
            $product_price = $price_list->prices->where('item_id', $product_id)->first();
            if ($product_price) {
                $unit_price_tra = $product_price->unit_price_tra;
            } else {
                $unit_price_tra = ((($quantity_ton / $qty) / 1000) * $zz_ton_price) * 1000;
            }

            $contract_discount = ContractDiscount::where('form_contract_m_id', $contract_id)->where('item_id', $product_id)->where('disc1_id', '>', 0)->first();
            if ($contract_discount) {
                $unit_price_tra = $unit_price_tra - ($unit_price_tra * $contract_discount->discount1->disc_rate / 100);
            }

        } else {
            return ['success' => false, 'message' => 'Fiyat listesi bulunamadı!'];
        }

        $tax_rate = Tax::where('id', $product->default_tax_id)->value('tax_rate');
        if (!$tax_rate) {
            $tax_rate = 0;
        }

        $view_data['tax_id'] = $product->default_tax_id;
        $view_data['ton_price'] = $zz_ton_price;
        $view_data['price_list_id'] = $price_list->id ?? null;
        $view_data['unit_price_tra'] = number_format($unit_price_tra, 2, ',', ''); // 12,000.11 sorun oluyor
        //$view_data['amount_total'] = $zz_ton_price * $quantity_ton;
        $view_data['amount_total'] = $unit_price_tra * $qty;
        $view_data['amount_vat'] = $view_data['amount_total'] * $tax_rate / 100;
        $view_data['amount_grand_total'] = $view_data['amount_total'] + $view_data['amount_vat'];
        $view_data['quantity_truck'] = $quantity_truck;
        $view_data['quantity_pallet'] = $quantity_pallet;
        $view_data['covering_fee'] = $covering_fee;
        $view_data['covering_unit_price'] = $covering_unit_price;
        $view_data['qty'] = $qty;
        $html = view('frontend.ajax.product_price', $view_data)->render();

        return ['success' => true, 'message' => '', 'html' => $html, 'covering_fee' => $covering_fee, 'unit_price_tra' => $unit_price_tra, 'amount_total' => $view_data['amount_total'], 'amount_vat' => $view_data['amount_vat'], 'amount_grand_total' => $view_data['amount_grand_total']];
    }

    public function get_cart_content()
    {
        $user = auth()->user();
        $contract_id = request()->contract_id;
        $contract = Contract::where('id', $contract_id)->first();
        $cart = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();

        if ($cart) {

            $cart_total = Number::currency($cart->amt + $cart->amt_vat, 'TRY', 'tr');
            $view_data['cart'] = $cart;

            return [
                'success' => true,
                'html' => view('frontend.ajax.cart_content', $view_data)->render(),
                'cart_count' => $cart->item_count,
                'cart_total' => $cart_total,
                'amount' => $cart->amt + $cart->amt_vat,
                'cart_id' => $cart->id,
                'shipping_address_id' => $cart->shipping_address_id,
                'country_id' => $cart->country_id,
                'city_id' => $cart->city_id,
                'town_id' => $cart->town_id,
                'incoterms_id' => $cart->incoterms_id,
                'shipping_date' => $cart->shipping_date,
                'notes' => $cart->notes,
            ];
        } else {
            return ['success' => false, 'cart_count' => 0, 'cart_total' => 0, 'html' => view('frontend.ajax.cart_content')->render(), 'message' => 'Sepetinizde ürün bulunmamaktadır!'];
        }


    }

    public function post_cart_content()
    {
        $user = auth()->user();
        $contract_id = request()->contract_id;
        $input = request()->only('city_id', 'town_id', 'country_id', 'shipping_date', 'notes', 'incoterms_id');
        if (empty($input['incoterms_id'])) {
            $input['incoterms_id'] = 37; // ECW
        }
//        if (!is_numeric($input['shipping_address_id'])) {
//            return ['success' =>   false, 'message' => 'Teslimat adresi seçiniz!'];
//        }


//       / return $input['shipping_date'];
        $input['shipping_date'] = date('Y-m-d', strtotime($input['shipping_date']));

        $cart = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();

        $cart->update($input);
    }

    public function get_contract_summary()
    {
        $user = auth()->user();
        $contract_id = request()->contract_id;
        $payment_tabs = ['eft'];
        $cart_count_msg = 'Sepet işlemleri';
        $cart_items_count = 0;
        $shipping_address_id = 0;
        $incoterms_id = 0;
        $shipping_date = '';
        $notes = '';
        if (!is_numeric($contract_id)) {
            return ['success' => false, 'message' => 'Sözleşme seçiniz!'];
        }
        $cart = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();

        if ($cart) {
            $contract = Contract::find($contract_id);

            if (!empty($contract->zz_ton_price) && !empty($cart->zz_ton_price)) {
                // sepetteki ton fiyatı ile sözleşme ton fiyatı farklıysa
                if ($contract->zz_ton_price != $cart->zz_ton_price) {
                    $cart->cart_items()->delete();
                    $cart->amt = 0;
                    $cart->amt_receipt = 0;
                    $cart->amt_receipt_tra = 0;
                    $cart->amt_tra = 0;
                    $cart->amt_vat = 0;
                    $cart->amt_vat_tra = 0;
                    $cart->item_count = 0;
                    $cart->qty = 0;
                    $cart->quantity_pallet = 0;
                    $cart->zz_ton_price = $contract->zz_ton_price;
                    $cart->save();
                    return [
                        'success' => true,
                        'message' => 'Ton fiyatı güncellendi.'
                    ];
                }
            }


            $cart_items_count = $cart->item_count;
            $shipping_address_id = $cart->shipping_address_id;
            $country_id = $cart->country_id;
            $city_id = $cart->city_id;
            $town_id = $cart->town_id;
            $incoterms_id = $cart->incoterms_id;
            $shipping_date = $cart->shipping_date;
            $notes = $cart->notes;
            $cart_total_amount = (($cart->amt + $cart->amt_vat) > 0) ? Number::currency($cart->amt + $cart->amt_vat, 'TRY', 'tr') : 'Ödeme işlemleri';
            $cart_total = number_format($cart->amt + $cart->amt_vat, 2);
            $cart_id = $cart->id;
            if ($cart->item_count > 0) {
                $cart_count_msg = $cart->item_count . ' ürün sepette';
            }
            $cart_amount = $cart->amt + $cart->amt_vat;
        } else {
            $cart_total_amount = 'Ödeme işlemleri';
            $cart_total = 0;
            $cart_amount = 0;
            $cart_id = 0;
        }

        if ($contract_id == 0) {
            array_push($payment_tabs, 'cc');
            return [
                'success' => true,
                'message' => 'Sözleşmesiz alım seçildi!',
                'doc_no' => 'Sözleşmesiz Alım',
                'cart_total_amount' => $cart_total_amount,
                'cart_total' => $cart_total,
                'amount' => $cart_amount,
                'cart_id' => $cart_id,
                'cart_count_msg' => $cart_count_msg,
                'payment_tabs' => $payment_tabs,
                'html' => '<p>Sözleşmesiz alım seçildi.</p>',
                'cart_items_count' => $cart_items_count,
                'shipping_address_id' => $shipping_address_id,
                'country_id' => $country_id,
                'city_id' => $city_id,
                'town_id' => $town_id,
                'incoterms_id' => $incoterms_id,
                'shipping_date' => $shipping_date,
                'notes' => $notes,
            ];
        }

        $contract = Contract::where('id', $contract_id)->firstOrFail();
        if ($contract->doc_description == 'ÇEK SÖZLEŞMESİ') {
            array_push($payment_tabs, 'cek');
        }
        if ($contract->doc_description == 'DBS SÖZLEŞMESİ') {
            array_push($payment_tabs, 'dbs');
        }
        if ($contract->doc_description == 'K.KARTI SÖZLEŞMESİ') {
            array_push($payment_tabs, 'cc');
        }

        if ($cart) {
            $cart_total_amount = (($cart->amt + $cart->amt_vat) > 0) ? Number::currency($cart->amt + $cart->amt_vat, 'TRY', 'tr') : 'Ödeme işlemleri';
            $cart_total = number_format($cart->amt + $cart->amt_vat, 2);
            if ($cart->item_count > 0) {
                $cart_count_msg = $cart->item_count . ' ürün sepette';
            } else {
                $cart_count_msg = 'Sepet işlemleri';
            }
        } else {
            $cart_total_amount = 'Ödeme işlemleri';
            $cart_total = 0;
            $cart_items_count = 0;
            $cart_count_msg = 'Sepet işlemleri';
            $shipping_address_id = '';
            $incoterms_id = '';
            $shipping_date = '';
            $notes = '';
        }

        $used_percent = 0;
        if ($contract->used_amount > 0) {
            $used_percent = ($contract->used_amount / $contract->amt) * 100;
            $used_percent = number_format($used_percent, 1);
        }

        $order_count = $contract->orders->count();
        $remaining_order_count = '';
        if ($order_count > 2) {
            $remaining_order_count = ' <li>
                        <div class="user-avatar bg-light sm"><span>+' . ($order_count - 2) . '</span></div>
                    </li>';
        }

        $html = '<div class="card card-bordered">
    <div class="card-inner">
        <div class="project">
            <div class="project-head">
                <a href="#" class="project-title">
                    <div class="user-avatar sq bg-purple"><span>SZ</span></div>
                    <div class="project-info">
                        <h6 class="title">' . $contract->doc_no . '</h6>
                        <span class="sub-text">Başlangıç: ' . date('d.m.Y', strtotime($contract->contract_start_date)) . ' - Bitiş: ' . date('d.m.Y', strtotime($contract->contract_end_date)) . '</span>
                    </div>
                </a>
                <div class="drodown">
                    <a href="#" class="dropdown-toggle btn btn-sm btn-icon btn-trigger mt-n1 me-n1" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                    <div class="dropdown-menu dropdown-menu-end">
                        <ul class="link-list-opt no-bdr">
                            <li><a href="#"><em class="icon ni ni-eye"></em><span>Sözleşmeyi Gör</span></a></li>
                            <li><a href="#"><em class="icon ni ni-cart"></em><span>Siparişleri Gör</span></a></li>
                            <li><a href="#"><em class="icon ni ni-check-round-cut"></em><span>Kalan Tutar</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="project-details">
           
            </div>
            <div class="project-progress">
                <div class="project-progress-details">
                    <div class="project-progress-task">
                        <em class="icon ni ni-check-round-cut"></em>
                        <span>Toplam: ' . Number::currency($contract->amt, $contract->currency->cur_code, app()->getLocale()) . ' / Kullanılan: ' . Number::currency($contract->used_amount ?? 0, $contract->currency->cur_code, app()->getLocale()) . ' / Kalan: ' . Number::currency($contract->amt - $contract->used_amount, $contract->currency->cur_code, app()->getLocale()) . ' </span>
                    </div>
                    <div class="project-progress-percent">' . $used_percent . '%</div>
                </div>
                <div class="progress progress-pill progress-md bg-light">
                    <div class="progress-bar" data-progress="' . $used_percent . '"></div>
                </div>
            </div>
            <div class="project-meta">
                <ul class="project-users g-1">';
        foreach ($contract->orders()->take(2)->get() as $order) {
            $html .= '<li>
                        <div class="badge badge-dim sm bg-primary"><span>' . $order->doc_no . '</span></div>
                    </li>';
        };
        $html .= $remaining_order_count . '
                </ul>
                <span class="badge badge-dim bg-success"><em class="icon ni ni-clock"></em><span>' . $contract->contract_end_date->diffForHumans() . '</span></span>
            </div>
        </div>
    </div>
</div>';

        return [
            'success' => true,
            'html' => $html,
            'payment_tabs' => $payment_tabs,
            'doc_no' => $contract->doc_no,
            'endDate' => date('d.m.Y', strtotime($contract->contract_end_date)),
            'cart_total_amount' => $cart_total_amount,
            'cart_count_msg' => $cart_count_msg,
            'cart_items_count' => $cart_items_count,
            'cart_total' => $cart_total,
            'amount' => $cart_amount ?? 0,
            'cart_id' => $cart_id ?? 0,
            'shipping_address_id' => $shipping_address_id,
            'incoterms_id' => $incoterms_id,
            'shipping_date' => $shipping_date,
            'notes' => $notes,
            'remaining_amount' => $contract->remaining_amount ?? 0,
        ];

    }

    public function get_cities()
    {
        $country_id = request()->get('country_id');
        $cities = City::forDropdown($country_id);

        return $cities;
    }

    public function get_dbs()
    {
        $user = auth()->user();
        $entity = Entity::findOrFail($user->active_entity_id);
        $entity_dbs = Dbs::where('entity_id', $entity->id)->where('remaining_amount', '>', 0)->where('amt', '>', 0)->get();

        if (count($entity_dbs) == 0) {
            return ['success' => false, 'message' => 'DBS bulunamadı!', 'html' => 'Ödeme için kullanabileceğiniz Doğrudan Borçlandırma Sistemi kaydı bulunamadı.'];
        }

        $html = '<table class="table">
                                                                                <thead>
                                                                                <tr>
                                                                                    <th>Belge No</th>
                                                                                    <th>Banka</th>
                                                                                    <th>Belge Tarihi</th>
                                                                                    <th>Vade Tarihi</th>
                                                                                    <th>Tutar</th>
                                                                                    <th>Kalan Limit</th>
                                                                                    <th></th>
                                                                                </tr>
                                                                                </thead>
                                                                                <tbody>';

        foreach ($entity_dbs as $dbs) {
            $html .= ' <tr>
                                                                                    <td>' . $dbs->doc_no . '</td>
                                                                                    <td>' . $dbs->bank->bank_desc . '</td>
                                                                                    <td>' . date('d.m.Y', strtotime($dbs->doc_date)) . '</td>
                                                                                    <td>' . date('d.m.Y', strtotime($dbs->due_date)) . '</td>
                                                                                    <td>' . Number::currency($dbs->amt, 'TRY', 'tr') . '</td>
                                                                                    <td>' . Number::currency($dbs->remaining_amount, 'TRY', 'tr') . '</td>
                                                                                    <td><span class="approve-sales-tooltip" data-bs-placement="left" tabindex="0" data-bs-toggle="tooltip" title="Ödeme için Mesafeli Satış Sözleşmesi\'ni onaylayınız">
 <button type="button" data-href="/pay-with-dbs?id=' . $dbs->id . '" data-container="#modal_container" class="btn-modal btn btn-sm btn-success pay-with-dbs" disabled="">Öde</button>
</span>
</td>
                                                                                </tr>
    ';
        }

        $html .= '</tbody>
                                                                            </table>';

        return ['success' => true, 'html' => $html];

    }

    public function get_towns()
    {
        $city_id = request()->get('city_id');
        $towns = Town::forDropdown($city_id);

        return $towns;
    }

    public function pay_with_dbs()
    {
        $user = auth()->user();

        $id = request()->id;
        $dbs = Dbs::find($id);
        $contract_id = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->whereNull('ordered_at')->value('contract_id');
        $contract = Contract::find($contract_id);
        $amt = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->whereNull('ordered_at')->sum('amt');
        $amt_vat = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->whereNull('ordered_at')->sum('amt_vat');
        $amt_total = $amt + $amt_vat;

        $data['amt_total'] = $amt_total;

        $data['contract'] = $contract;
        $data['dbs'] = $dbs;

        return view('frontend.payment.modal.pay_with_dbs', $data);
    }

    public function pay_with_eft()
    {
        $contract_id = request()->contract_id;
        $eft_amt = request()->eft_amt;
        $user = auth()->user();
        $cart = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();

        if ($cart) {
            return post_order_to_uyum($cart);
        }
        return ['success' => true, 'message' => 'Ödeme işlemi başarılı!'];
    }

    public function pay_with_contract()
    {
        $contract_id = request()->contract_id;
        $user = auth()->user();

        $cart = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();

        if ($cart) {
            // Sözleşme kalan tutarı kontrolü
            $contract = Contract::find($contract_id);
            if ($contract) {
                $cartTotal = ($cart->amt + $cart->amt_vat) ?? 0;
//                if ($cartTotal > $contract->remaining_amount) {
//                    return [
//                        'success' => false,
//                        'message' => 'Sepet toplam tutarı (' . number_format($cartTotal, 2) . ' ₺) sözleşme kalan tutarından (' . number_format($contract->remaining_amount, 2) . ' ₺) fazla olamaz!'
//                    ];
//                }
            }

//            if ($user->id == 1) {
//                return post_order_to_uyum($cart, true);
//            }
            return post_order_to_uyum($cart);
        }
        return ['success' => true, 'message' => 'Sipiş işlemi başarılı!'];
    }

    public function get_shipping_price()
    {
        $address_id = request()->address_id;
        $address = Address::find($address_id);

        $rule_id = PriceRule::where('rule_name', $address->city->city_name)->orderByDesc('id')->value('id');
        $price = Price::select('unit_price_tra')->where('rule_id', $rule_id)->where('price_list_m_id', 943)->first();

        if ($price) {
            return ['success' => true, 'value' => Number::currency($price->unit_price_tra, 'TRY', 'tr')];
        } else {
            return ['success' => true, 'message' => 'Nakliye fiyatı bulunamadı!', 'value' => 0];
        }
    }

    public function delete_cart_alert()
    {
        $user = auth()->user();
        $item_id = request()->item_id;
        $cart_id = request()->id;

        if ($cart_id) {
            $cart = Cart::find($cart_id);
        } else {
            $cart_item = CartItem::where('id', $item_id)->first();
            $cart = Cart::find($cart_item->cart_id);
            $data['cart_item'] = $cart_item;
        }
        if ($cart->user_id != $user->id) {
            return ['success' => false, 'message' => 'Bu işlemi yapmaya yetkiniz yok!'];
        }

        $data['cart'] = $cart;
        return view('frontend.cart.alert.delete_cart', $data);
    }

    public function delete_cart()
    {
        $contract_id = request()->contract_id;
        $item_id = request()->item_id;
        $cart_id = request()->cart_id;
        $user = auth()->user();

        if ($cart_id) {
            $cart = Cart::findOrFail($cart_id);
        } else {
            $cart_item = CartItem::where('id', $item_id)->firstOrFail();
            $cart = Cart::findOrFail($cart_item->cart_id);
        }
        if ($cart->user_id != $user->id) {
            return ['success' => false, 'message' => 'Bu işlemi yapmaya yetkiniz yok!'];
        }

        if ($item_id > 0) {
            $cart = Cart::find($cart_item->cart_id);
            if ($cart->user_id != $user->id) return ['success' => false, 'message' => 'Bu işlemi yapmaya yetkiniz yok!'];
            $cart_item = CartItem::where('id', $item_id)->first();
            $covering_item = CartItem::where('cart_id', $cart->id)->where('parent_id', $cart_item->product_id)->first();
            $contract = Contract::where('id', $contract_id)->first();
            $cart_item->delete();
            if ($covering_item) {
                $covering_item->delete();
            }
            $cart->item_count = $cart->cart_items->count();
            $cart->amt = $cart->cart_items->sum('amt');
            $cart->qty = $cart->cart_items->sum('qty');
            $cart->quantity_pallet = $cart->cart_items->sum('quantity_pallet');
            $cart->amt_tra = $cart->cart_items->sum('amt_tra');
            $cart->amt_vat = $cart->cart_items->sum('amt_vat');
            $cart->amt_vat_tra = $cart->cart_items->sum('amt_vat_tra');
            $cart->amt_receipt_tra = $cart->cart_items->sum('amt_tra') + $cart->cart_items->sum('amt_vat_tra');
            $cart->amt_receipt = $cart->cart_items->sum('amt') + $cart->cart_items->sum('amt_vat');
            $cart->save();

            if ($cart) {
                $cart_total_amount = (($cart->amt + $cart->amt_vat) > 0) ? Number::currency($cart->amt + $cart->amt_vat, 'TRY', 'tr') : 'Ödeme işlemleri';
                $cart_total = number_format($cart->amt + $cart->amt_vat, 2);
                if ($cart->item_count > 0) {
                    $cart_count_msg = $cart->item_count . ' ürün sepette';
                } else {
                    $cart_count_msg = 'Sepet işlemleri';
                }
                $cart_items_count = $cart->item_count;
                $shipping_address_id = $cart->shipping_address_id;
                $incoterms_id = $cart->incoterms_id;
                $shipping_date = $cart->shipping_date;
                $notes = $cart->notes;
            } else {
                $cart_total_amount = 'Ödeme işlemleri';
                $cart_total = 0;
                $cart_items_count = 0;
                $cart_count_msg = 'Sepet işlemleri';
                $shipping_address_id = '';
                $incoterms_id = '';
                $shipping_date = '';
                $notes = '';
            }
            $view_data['cart'] = $cart;
            if ($contract) {
                $view_data['contract'] = $contract;
            }

            return [
                'success' => true,
                'html' => view('frontend.ajax.cart_content', $view_data)->render(),
                //'doc_no' => $contract->doc_no,
                // 'endDate' => date('d.m.Y', strtotime($contract->contract_end_date)),
                'cart_total_amount' => $cart_total_amount,
                'cart_count_msg' => $cart_count_msg,
                'cart_items_count' => $cart_items_count,
                'cart_total' => $cart_total,
                'amount' => $cart->amt + $cart->amt_vat,
                'cart_id' => $cart->id,
                'shipping_address_id' => $shipping_address_id,
                'incoterms_id' => $incoterms_id,
                'shipping_date' => $shipping_date,
                'notes' => $notes,
                'message' => 'Ürün sepetten silindi!',
            ];

        } else {

            $cart->cart_items()->delete();
            return ['success' => true,
                'message' => 'Sepetteki tüm ürünler silindi!',
                'html' => view('frontend.ajax.cart_content')->render(),
                'cart_count_msg' => 'Sepet işlemleri',
                'cart_total_amount' => 'Ödeme işlemleri',
                'cart_count' => 0
            ];
        }

//            $output['cart_count_msg'] = $cart->item_count . ' ürün sepette';
//            $output['cart_count_msg'] = 'Sepet işlemleri';


    }

    public function upload(Request $request)
    {
        $user = auth()->user();
        if ($request->hasFile('file')) {
            $file = $request->file('file');

            // Optionally, validate the file
            $validatedData = $request->validate([
                'file' => 'required|image|max:10240', // for example, only images, max 10MB
            ]);

            // Handle Avatar upload
            if ($request->hasFile('file')) {
                if ($user->getMedia('user')->first()) {
                    $user->getMedia('user')->first()->delete();
                }

                $media = $user->addMedia($request->file('file'))->toMediaCollection('user');

                $user->avatar = $media->getUrl();

                $user->save();
            }


            return response()->json(['success' => true, 'message' => 'Profil resmi güncellendi.', 'imagePath' => $user->avatar, 'url' => $user->avatar]);

        }

        return response()->json(['success' => false, 'message' => 'Dosya yüklenemedi.']);
    }

    public function entity_update_status()
    {
        $entity_id = request()->entity_id;
        $user_id = request()->user_id;
        $status = request()->status;
        $user = auth()->user();
        $entities = request()->entities;
        $action = request()->action;

        if ($status == 'remove_permission') {
            if ($user->can('edit_users')) {
                try {
                    DB::table('entity_user')->where('user_id', $user_id)->where('entity_id', $entity_id)->delete();
                    return ['success' => true, 'message' => 'Kullanıcı başarıyla silindi!'];
                } catch (Exception $e) {
                    return ['success' => false, 'message' => 'Kullanıcı silinirken bir hata oluştu!'];
                }
            }
        }

        if ($action == 'entity_user') {
            if ($user->can('edit_users')) {
                try {
                    if ($entities) {
                        $theuser = User::findOrfail($user_id);
                        $theuser->entities()->attach($entities);
                        return ['success' => true, 'message' => 'Kullanıcı başarıyla eklendi!'];
                    }
                    if ($status == 'delete') {
                        DB::table('entity_user')->where('user_id', $user_id)->where('entity_id', $entity_id)->delete();
                        return ['success' => true, 'message' => 'Kullanıcı başarıyla silindi!'];
                    }
                    if ($entity_id) {
                        $permission = DB::table('entity_user')->where('user_id', $user_id)->where('entity_id', $entity_id)->first();
                        if ($permission) {
                            if ($permission->pivot_status == 0) {
                                DB::table('entity_user')->where('user_id', $user_id)->where('entity_id', $entity_id)->update(['pivot_status' => 1, 'pivot_created_by' => $user_id]);
                                return ['success' => true, 'message' => 'Yetki bilgisi başarıyla eklendi!'];
                            } else {
                                DB::table('entity_user')->where('user_id', $user_id)->where('entity_user.entity_id', $entity_id)->update(['pivot_status' => 0, 'pivot_created_by' => $user_id]);
                                return ['success' => true, 'message' => 'Yetki bilgisi başarıyla çıkarıldı!'];
                            }
                        } else {
                            DB::table('entity_user')->insert(['user_id' => $user_id, 'entity_id' => $entity_id, 'pivot_status' => 1]);
                            return ['success' => true, 'message' => 'Firmalar başarıyla eklendi!'];
                        }
                    }
                } catch (Exception $e) {
                    return ['success' => false, 'message' => 'Firmalar eklenirken bir hata oluştu!'];
                }

            } else {
                return ['success' => false, 'message' => 'Bu işlem için yetkiniz yok!'];
            }
        } elseif ($action == 'order_quantity') {

            $entity = Entity::find($entity_id);
            $value = ($entity->order_quantity) ? null : 1;
            $entity->order_quantity = $value;
            $entity->save();

            if ($value) {
                return ['success' => true, 'message' => $entity->entity_name . ' serbest sipariş verebilir!'];

            } else {
                return ['success' => false, 'message' => $entity->entity_name . ' serbest sipariş veremez!'];
            }

        }


    }

    public function order_search()
    {
        $search = request()->q;
        $user = auth()->user();

        //  $results = Order::select('id', 'doc_no as text')->where('doc_no', 'like', '%' . $search . '%')->paginate(10);


        $results = Order::select('orders.id', DB::raw("CONCAT(orders.doc_no, ' - ', entities.entity_name) as text"))
            ->join('entities', 'orders.entity_id', '=', 'entities.id')
            ->where('order_status', 2)
            ->where('orders.doc_no', 'like', '%' . $search . '%');

        if ($user->hasRole('bayi')) {
            $results = $results->where('orders.entity_id', $user->active_entity_id);
        }
        $results = $results->paginate(10);

        if ($results->total() == 0) {
            return ['items' => [], 'total_count' => 0];
        } else {
            return ['items' => $results->items(), 'total_count' => $results->total()];
        }

    }

    public function entity_search()
    {
        $search = request()->q;

        $results = Entity::whereNotNull('is_default')
            ->select('id', DB::raw("LEFT(entity_name, 33) as text"))
            ->where('entity_name', 'like', '%' . $search . '%')
            ->paginate(10);

        if ($results->total() == 0) {
            return ['items' => [], 'total_count' => 0];
        } else {
            return ['items' => $results->items(), 'total_count' => $results->total()];
        }

    }

    public function run_artisan()
    {


        Artisan::call(request()->command);
        return ['success' => true, 'message' => 'Veri güncelleme tamamlandı!', 'output' => shell_exec('php artisan ' . request()->command)];
    }

    public function dark_mode()
    {
        $user = auth()->user();

        if ($user->dark_mode == 1) {
            $user->dark_mode = null;
        } else {
            $user->dark_mode = 1;
        }
        $user->save();
    }

    public function compact_mode()
    {
        $user = auth()->user();
        if ($user->compact_mode == 1) {
            $user->compact_mode = null;
        } else {
            $user->compact_mode = 1;
        }
        $user->save();
    }

    private function calculateCoveringFee($product, $qty)
    {
        $covering_fee = 0;
        $covering_unit_price = 0;
        if (in_array($product->categories4_id, [2181, 2182, 2183])) { //kaplama türü alüminyum folyo veya siyah cam tülü
            if ($product->categories4_id == 2181) {
                $covering_price = Price::select('unit_price_tra', 'cur_tra_id')->where('item_id', 124901)->first();
            } else if ($product->categories4_id == 2182) {
                $covering_price = Price::select('unit_price_tra', 'cur_tra_id')->where('item_id', 122739)->first();
            }
            if ($covering_price) {
                $cur_rate = DailyCurRate::where('cur_from_id', $covering_price->cur_tra_id)->where('cur_rate_type_id', 277)->orderByDesc('id')->value('cur_rate_tra');
                $covering_unit_price = $covering_price->unit_price_tra * $cur_rate;
                $covering_fee = $qty * $covering_unit_price;
            }
        }
        return ['covering_fee' => $covering_fee, 'covering_unit_price' => $covering_unit_price];
    }


}
