<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Entity;

class EntityController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $type = request()->get('type');
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }

        $entities = Entity::whereIn('id', $entity_ids)->select('id', 'address1', 'address2', 'tel1', 'entity_name', 'entity_code', 'city_id');

        if ($type) {
            $entities->where('entity_type', $type);
        }

        $entities = $entities->paginate(20);

        $data['type'] = $type;
        $data['entities'] = $entities;

        return view('frontend.entity.index', $data);
    }

    public function balance()
    {
        $user = Auth()->user();
        $entity = Entity::find($user->active_entity_id);


        $data['entity'] = $entity;

        return view('frontend.entity.balance', $data);
    }

    public function ekstre()
    {
        $user = Auth()->user();
        $user_entity_ids = $user->entities->pluck('id')->toArray();
        $entities = Entity::select('id', 'address1', 'address2', 'tel1', 'entity_name', 'entity_code', 'city_id')
            ->whereIn('id', $user_entity_ids)
            ->paginate();

        $data['entities'] = $entities;

        return view('frontend.entity.ekstre', $data);
    }


    public function change_entity()
    {
        $user = Auth()->user();
        $data['active_entity_id'] = $user->active_entity_id;

      //  if($user->hasRole('administrator') || $user->hasRole('super admin') || str_contains($user->email,'akdagtasyunu.com')){
        if($user->hasRole('administrator') || $user->hasRole('super admin')){
            $data['entities'] = [];
        }else{
            $data['entities'] = $user->entities()->select('entity_name', 'id')->get();
        }

        return view('frontend.entity.change_entity', $data);
    }

    public function change_entity_update()
    {
        $user = Auth()->user();
        $active_entity_id = request()->input('active_entity_id');
        if ($active_entity_id == '') {
            return [
                'success' => 0,
                'msg' => 'Aktif cari seçiniz.'
            ];
        }if ($active_entity_id == $user->active_entity_id) {
            return [
                'success' => 0,
                'msg' => 'Aktif cari zaten seçili.'
            ];
        }

        $active_entity = Entity::find($active_entity_id);
        try {
            $user->update(['active_entity_id' => $active_entity_id]);
            $output = ['success' => 1,
                'entity_name' => limit_words($active_entity->entity_name, 3),
                'message' => 'Aktif cari: ' . $active_entity->entity_name . ' olarak güncellendi.'
            ];

        } catch (\Exception $e) {
            \Log::emergency("File:" . $e->getFile() . "Satır:" . $e->getLine() . "Mesaj:" . $e->getMessage());

            $output = [
                'success' => 0,
                'msg' => 'Bir hata oluştu. Lütfen tekrar deneyin.'
            ];
        }

        if (str_contains(url()->previous(), 'siparis/olustur') or str_contains(url()->previous(), 'panel')) {

            $output['redirect'] = url()->previous();
            $output['message'] .= '<br><br><span class="text-warning">Aktif cari değiştiği için bu sayfa yenilenecek...</span>';

        }
        return $output;

    }

}
