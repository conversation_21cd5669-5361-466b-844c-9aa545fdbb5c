<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Entity;
use App\Models\Offer;
use App\Models\Ad;
use App\Models\AdDetail;
use App\Models\User;
use Illuminate\Http\Request;

class SupportController extends Controller
{

    public function index()
    {
        $data['requests'] = '$requests';

        return view('frontend.support.tickets', $data);

    }


    public function manage_team()
    {
        $data['requests'] = '$requests';

        return view('frontend.support.manage_team', $data);

    }

    public function help()
    {
        $data['requests'] = '$requests';

        return view('frontend.support.help', $data);

    }

}
