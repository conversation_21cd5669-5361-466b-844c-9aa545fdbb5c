<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\LetterCredit;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Entity;
use App\Models\Order;
use Illuminate\Support\Number;



use App\Models\User;
use Illuminate\Http\Request;

class LetterCreditController extends Controller
{


    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }

            if (request()->ajax()) {

                $letter_credits = LetterCredit::select('e.entity_name','letter_credits.id', 'letter_credits.letter_credit_no', 'letter_credits.due_date', 'letter_credits.amt', 'letter_credits.note1', 'letter_credits.note2', 'letter_credit_status', 'letter_credits.doc_date', 'co.co_desc', 'cu.cur_code', 'e.entity_name', 'b.bank_desc', 'bb.description as branch_name')
                   ->whereIn('letter_credits.entity_id', $entity_ids)
                    ->leftJoin('entities as e', 'letter_credits.entity_id', '=', 'e.id')
                    ->leftJoin('currencies as cu', 'letter_credits.cur_tra_id', '=', 'cu.id')
                    ->leftJoin('banks as b', 'letter_credits.bank_id', '=', 'b.id')
                    ->leftJoin('bank_branches as bb', 'letter_credits.bank_branch_id', '=', 'bb.id')
                    ->leftJoin('companies as co', 'letter_credits.co_id', '=', 'co.id');

                if (request()->has('entity_id')) {
                    $entity_id = request()->get('entity_id');
                    if (!empty($entity_id)) {
                        $letter_credits->where('letter_credits.entity_id', $entity_id);
                    }
                }
                if (!empty(request()->get('status'))) {
                    $letter_credits->where('letter_credit_status', request()->get('status'));
                }
                if (!empty(request()->start_date) && !empty(request()->end_date)) {
                    $start = date('Y-m-d', strtotime(request()->start_date));
                    $end = date('Y-m-d', strtotime(request()->end_date));
                    $letter_credits->whereDate('doc_date', '>=', $start)
                        ->whereDate('doc_date', '<=', $end);
                }
                return Datatables::of($letter_credits)
                    ->addColumn(
                        'action', function ($row) {
                        $action = '<ul class="nk-tb-actions gx-1">
                           
                                                                    <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                    <li><a data-href="sozlesme/'.$row->id.'?action=contract_details" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-focus"></em><span>Hızlı Bakış</span></a></li>
                                                                                    <li><a data-href="sozlesme/'.$row->id.'?action=last" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-repeat"></em><span>Son Hareket</span></a></li>
                                                                                   
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                        return $action;
                    }
                    )

                    ->editColumn('letter_credit_no', function ($row) {
                        return '<a href=""><b>'.$row->letter_credit_no.'</b></a>';
                    })
                    ->editColumn('bank_desc', function ($row) {
                        return $row->bank_desc . '<br><em>' . mb_convert_case($row->branch_name, MB_CASE_TITLE, "UTF-8") . ' Şubesi</em>';
                    })
                    ->editColumn('letter_credit_status', function ($row) {
                        if($row->letter_credit_status == 1)
                            return '<span class="badge badge-dot bg-success">AKTİF</span>';
                        else
                            return '<span class="badge badge-dot bg-danger">PASİF</span>';

                    })
                    ->editColumn('amt', function ($row) {
                        return Number::currency($row->amt, $row->cur_code, 'tr');
                    })
                    ->editColumn('due_date', function ($row) {
                        return date('d.m.Y', strtotime($row->due_date));
                    })

                    ->setRowAttr([
                        'data-href' => function ($row) {
                            return  action([\App\Http\Controllers\Frontend\LetterCreditController::class, 'show'], [$row->id]);
                        }, ])
                    ->rawColumns(['action', 'letter_credit_no', 'doc_no', 'amt', 'bank_desc', 'letter_credit_status'])
                    ->removeColumn('branch_name')
                    ->make(true);
            }


        //$entity_ids = Contract::groupBy('entity_id')->pluck('entity_id')->where('ispassive',0)->toArray();


        $data['entities'] = Entity::whereIn('id' ,$entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.letter_credit.index', $data);
    }

    public function show($id)
    {
        $order = Order::find($id);
        $orderDetails = uyumapi("SELECT amt, amt_vat, qty, shipping_date, unit_price, line_no, item_name 
       FROM psmt_order_d
       LEFT JOIN invd_item ON psmt_order_d.item_id = invd_item.item_id
       where order_m_id = " .$order->id ." ORDER BY line_no ASC"
        );

        $data['order'] = $order;
        $data['orderDetails'] = $orderDetails;

        return view('frontend.order.modal.show', $data);

    }


    public function stockCards()
    {
        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        $items = uyumapi('SELECT item_id, item_name, item_code from INVD_ITEM  order by item_id desc limit 1000');

        return view('frontend.order.stock_cards')->with(compact('items'));
    }





}
