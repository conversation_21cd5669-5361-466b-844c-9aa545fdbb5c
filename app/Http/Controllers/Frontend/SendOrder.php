<?php

use App\Models\Order;

$cart = Order::where('entity_id', 1050373)->latest()->first();
$orderNo = 'B2B-'.date('Y') . $cart->order_no;
$array = [
    "value" => [
        "entityId" => $cart->entity_id,
        "entityCode" => "",
        "isApproveByMaster" => true,
        "formContractMId" => 0,
        "orderStatus" => "Açık",
        "entityRequestDate" => $cart->created_at,
        "docTraId" => 0,
        "docTraCode" => "",
        "amtVatTra" => $cart->amt_vat_tra,
        "amtVat" => $cart->amt_vat,
        "amtTra" => $cart->amt_tra,
        "amt" => $cart->amt,
        "amtReceiptTra" => $cart->amt_receipt_tra,
        "amtReceipt" => $cart->amt_receipt,
        "amtRoundTra" => 0,
        "amtRound" => 0,
        "curTra" => $cart->cur_tra,
        "curCode" => $cart->currency->cur_code,
        "curId" => 0,
        "curRateTypeId" => 0,
        "curRateTypeCode" => "",
        "dueDate" => "",
        "dueDay" => 0,
        "shippingDate" => "",
        "deliveryDate" => "",
        "salesPersonCode" => "",
        "salesPersonId" => $cart->sales_person_id,
        "paymentPlanMId" => 0,
        "paymentPlanCode" => "",
        "transportTypeCode" => "",
        "transportTypeId" => $cart->transport_type_id,
        "transporterCode" => "",
        "transporterId" => 0,
        "incotermsId" => 0,
        "incotermsName" => "",
        "paymentMethodId" => 0,
        "paymentMethodCode" => "",
        "gnlNote1" => "",
        "gnlNote2" => "",
        "gnlNote3" => "",
        "gnlNote4" => "",
        "gnlNote5" => "",
        "gnlNote6" => "",
        "gnlNote7" => "",
        "gnlNote8" => "",
        "gnlNote9" => "",
        "gnlNote10" => "",
        "address1" => $cart->address->address1,
        "address2" => $cart->address->address2,
        "address3" => $cart->address->address3,
        "cityId" => $cart->address->city_id,
        "cityName" => "",
        "townName" => "",
        "townId" => $cart->address->town_id,
        "countyId" => $cart->address->county_id,
        "shippingAddress1" => $cart->shipping_address->shipping_address1,
        "shippingAddress2" => $cart->shipping_address->shipping_address2,
        "shippingAddress3" => $cart->shipping_address->shipping_address3,
        "shippingCityId" => $cart->shipping_address->city_id,
        "shippingTownId" => $cart->shipping_address->town_id,
        "shippingCountyId" => $cart->shipping_address->country_id,
        "shippingEntityCode" => "",
        "shippingEntityId" => 0,
        "addressTypeCode" => "",
       // "isShippingAddressType" => true,
        "webAddress" => "",
        "firstName" => "",
        "familyName" => "",
        "email" => "",
        //"isDocDifferentCur" => true,
        "zipCode" => $cart->address->zip_code,
        "shippingZipCode" => $cart->shipping_address->zip_code,
        "vehicleCode" => "",
        "vehicleId" => 0,
        "driverIdentifyNo" => "",
        "driverGsmNo" => "",
        "shippingDesc1" => "",
        "transportEquipment" => "",
        "driverFamilyName" => "",
        "licencePlate" => "",
        //"isTCMB" => true,
        //"isLocalCurAction" => true,
        "shippingFirstname" => "",
        "shippingFamilyname" => "",
        "shippingTel1" => "",
        "shippingTel2" => "",
        "coId" => $cart->co_id,
        "coCode" => $cart->company->co_code,
        "branchId" => $cart->branch_id,
        "branchCode" => "",
        "docDate" => $cart->doc_date,
        "docNo" => $orderNo,
        "docNumberDId" => 0,
        "catCode1Id" => 0,
        "catCode1" => "",
        "catCode2Id" => 0,
        "catCode2" => "",
        "sourceApp" => "SatışSiparişi",
        "sourceApp2" => "",
        "sourceApp3" => "",
        "sourceMId" => 0,
        "sourceDId" => 0,
        "controller" => [
            "register1" => "",
            "register2" => "",
            "register3" => "",
            "register4" => "",
            "generalNote1" => "",
            "generalNote2" => "",
            "generalNote3" => "",
            "generalNote4" => "",
            "generalNote5" => "",
            "generalNote6" => "",
            "generalNote7" => "",
            "generalNote8" => "",
            "generalNote9" => "",
            "generalNote10" => "",
            "noteLarge" => ""
        ],
        "note1" => "",
        "note2" => "",
        "note3" => "",
        "createUserId" => 0,
        "currencyOption" => "Belge_Kuru",
        "entityOrderNo" => $orderNo,
        "entityOrderDate" => $cart->created_at,
        "disc0Id" => 0,
        "discCode0" => "",
        "disc0Rate" => 0,
        "amtDisc0Tra" => 0,
        "discCalcType0" => "Oran",
        "dynamicFields" => [],
        //"isWarning" => true,
        "promptValues" => ""
    ],
    "pageIndex" => 0,
    "pageSize" => 0,
    "totalPages" => 0,
    "totalCount" => 0
];

$array['value']['details'] = [
    [
        "colorId" => 0,
        "colorCode" => "",
        "packageTypeCode" => "",
        "packageTypeId" => 0,
        "itemId" => 0,
        "expenseId" => 0,
        "deliveryDate" => "",
        "shippingDate" => "",
        "entityRequestDate" => "",
        "entityItemId" => 0,
        "entityItemCode" => "",
        "lineType" => "S",
        "dcardId" => 0,
        "dcardCode" => "",
        "unitId" => $cart->product->unit_id,
        "unitCode" => $cart->product->unit->unit_code,
        "qty" => $cart->qty,
        "qtyPrm" => 0,
        "qtyFreePrm" => 0,
        "qtyFreeSec" => 0,
        "dueDay" => 0,
        "campaignId" => 0,
        "unitPriceTra" => $cart->unit_price_tra,
        "unitPrice" => $cart->unit_price,
        "vatStatus" => "Hariç",
        "vatId" => 0,
        "vatCode" => "",
        "vatRate" => 0,
        "amtVat" => 0,
        "abtActCode" => "",
        "abtActId" => 0,
        "abtBudgetId" => 0,
        "abtBudgetCode" => "",
        "otvCode" => "",
        "oivCode" => "",
        "vatDiscCode" => "",
        "priceListCode" => $priceListCode,
        "priceListId" => 0,
        "priceListDId" => 0,
        "whouseId" => 0,
        "whouseCode" => "",
        "abtBudgetD2Id" => 0,
        "disc1Id" => 0,
        "disc1Code" => "",
        "disc1Rate" => 0,
        "amtDisc1" => 0,
        "amtDisc1Tra" => 0,
        "disc2Id" => 0,
        "disc2Code" => "",
        "disc2Rate" => 0,
        "amtDisc2" => 0,
        "amtDisc2Tra" => 0,
        "disc3Id" => 0,
        "disc3Code" => "",
        "disc3Rate" => 0,
        "amtDisc3" => 0,
        "amtDisc3Tra" => 0,
        "amtDisc" => 0,
        "amtWithDisc" => 0,
        "amt" => $cart->amt,
        "amtTra" => $cart->amt_tra,
        "formContractMId" => $cart->contract_id,
        "formContractCode" => "",
        "itemAttribute1Id" => 0,
        "itemAttributeCode1" => "",
        "itemAttribute2Id" => 0,
        "itemAttributeCode2" => "",
        "itemAttribute3Id" => 0,
        "itemAttributeCode3" => "",
        "itemGnlAttribute1Id" => 0,
        "itemGnlAttributeCode1" => "",
        "itemGnlAttribute2Id" => 0,
        "itemGnlAttributeCode2" => "",
        "itemGnlAttribute3Id" => 0,
        "itemGnlAttributeCode3" => "",
        "discCalcType1" => 0,
        "discCalcType2" => 0,
        "discCalcType3" => 0,
        "referanceDocNo" => "",
        "lotId" => 0,
        "lotCode" => "",
        "qualityId" => 0,
        "qualityCode" => "",
        "barcode" => "",
        "itemNameManual" => "",
        "salesPersonId" => $cart->sales_person_id,
        "isItemAttribute" => true,
        "registerId" => 0,
        "registerFullName" => "",
        "promptValues" => "",
        "lineNo" => 0,
        "curTraId" => $cart->cur_tra_id,
        "curCode" => "",
        "curRateTypeId" => 235, // ihrackayitli ise 0
        "curRateTypeCode" => "",
        "curRateTra" => $cart->cur_rate_tra,
        "note1" => "",
        "note2" => "",
        "note3" => "",
        "noteLarge" => "",
        "catCode1Id" => 0,
        "catCode1" => "",
        "catCode2Id" => 0,
        "catCode2" => "",
        "sourceMId" => 0,
        "sourceDId" => 0,
        "sourceD3Id" => 0,
        "sourceOrderMId" => 0,
        "sourceOrderDId" => 0,
        "costCenterId" => 0,
        "costCenterCode" => "",
        "projectMId" => 0,
        "projectCode" => "",
        "gainLossTypeId" => 0,
        "gainLossTypeCode" => "",
        "analysisCode" => "",
        "analysisId" => 0,
        "taxTemplateName" => "",
        "taxTemplateMId" => 0,
        "plusMinus" => "Borç",
        "contactName" => "",
        "contactId" => 0,
        "sourceApp" => "SatışSiparişi",
        "sourceApp2" => "",
        "sourceApp3" => "",
        "dynamicFields" => []
    ]
];