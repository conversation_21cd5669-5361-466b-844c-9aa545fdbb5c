<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Company;
use App\Models\Entity;
use App\Models\Product;
use App\Models\Ad;
use App\Models\AdDetail;
use App\Models\Order;
use App\Models\PriceList;
use App\Models\Price;
use App\Models\Contract;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Number;

class PriceController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        // $entity_ids = $user->entities->pluck('id')->toArray();
        $entity_ids = Contract::where('entity_id', $user->active_entity_id)->pluck('entity_id')->toArray();
        $contractId = $user->active_entity->contract_id;
        if (!empty(request()->get('contract_id'))) {
            $contractId = request()->get('contract_id');
        }

        if (request()->ajax()) {
            if ($contractId == null || $contractId == 0) {
                $prices = PriceList::with(['prices.product.unit'])
                    ->where('is_default', 1)
                    ->latest()
                    ->get()
                    ->map(function ($priceList) {
                        return $priceList->prices->map(function ($price) {
                            // Only include products where is_open_to_internet is 1
                            if (optional($price->product)->is_open_to_internet == 1) {
                                return [
                                    'id' => $price->id,
                                    'price_list_description' => $price->priceList->description_1,
                                    'item_code' => optional($price->product)->item_code,
                                    'product_name' => optional($price->product)->item_name,
                                    'unit_price' => $price->unit_price_tra,
                                    'unit_name' => optional(optional($price->product)->unit)->unit_code,
                                    'cur_code' => $price->currency->cur_code,
                                    'zz_ton_price' => $price->priceList->zz_ton_price,
                                ];
                            }
                            return null;
                        })->filter(); // Filter out null values
                    });

                $prices = $prices[0];
            } else {
                $contractPrices = Contract::with(['contractPriceList.priceList.prices.product.unit'])
                    ->where('contracts.id', $contractId)
                    ->whereHas('contractPriceList.priceList', function ($query) {
                        $query->where('ispassive', 0); // Assuming you want to filter out passive price lists
                    })
                    ->get()
                    ->map(function ($contract) {
                        return $contract->contractPriceList->flatMap(function ($contractPriceList) {
                            return $contractPriceList->priceList->prices->map(function ($price) {
                                // Only include products where is_open_to_internet is 1
                                if (optional($price->product)->is_open_to_internet == 1) {
                                    return [
                                        'id' => $price->id,
                                        'price_list_description' => $price->priceList->description_1,
                                        'item_code' => $price->product->item_code,
                                        'product_name' => $price->product->item_name,
                                        'unit_price' => $price->unit_price_tra,
                                        'unit_name' => $price->product->unit->unit_code,
                                        'cur_code' => $price->currency->cur_code,
                                        'zz_ton_price' => $price->priceList->zz_ton_price,
                                    ];
                                }
                                return null;
                            })->filter(); // Filter out null values
                        });
                    });
                $prices = $contractPrices[0];
            }

            return Datatables::of($prices)
                ->addColumn(
                    'action', function ($row) {
                    $action = '<ul class="nk-tb-actions gx-1">
                                                                    <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                    <li><a data-href="sozlesme/' . $row['id'] . '?action=contract_details" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-focus"></em><span>Hızlı Bakış</span></a></li>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                    return $action;
                })

                ->editColumn('unit_price', function ($row) {
                    return Number::currency($row['unit_price'], $row['cur_code'], 'tr');
                })
                ->editColumn('product_name', function ($row) {
                    return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="' . nl2br($row['price_list_description']) . '">' . $row['product_name'] . '</span>';
                })
                ->editColumn('zz_ton_price', function ($row) {
                    return Number::currency($row['zz_ton_price'], $row['cur_code'], 'tr');
                })
                ->rawColumns(['action', 'end_date', 'product_name'])
                ->make(true);
        }

        $contracts = Contract::whereIn('entity_id', $entity_ids)->where('contract_end_date', '>', date('Y-m-d'))->where('ispassive',0)->where('request_status',4)->pluck('doc_no as name', 'id');

        return view('frontend.price.index')->with(compact('contracts'));
    }

    public function shipping_price()
    {
        $user = auth()->user();
        // Filter prices to only include products with is_open_to_internet = 1
        $prices = Price::where('price_list_m_id', 943)
            ->whereHas('product', function($query) {
                $query->where('is_open_to_internet', 1);
            })
            ->get();

        return view('frontend.price.shipping_price', compact('prices'));
    }
}