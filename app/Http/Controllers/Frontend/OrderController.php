<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Cart;
use App\Models\Category;
use App\Models\Contract;
use App\Models\Country;
use App\Models\Entity;
use App\Models\PriceList;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderD;
use App\Models\Setting;
use Illuminate\Support\Number;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Mail;
use App\Models\User;
use App\Models\City;
use App\Models\Town;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }
        if (request()->ajax()) {

            $orders = Order::select('orders.id', 'orders.doc_no', 'orders.doc_date', 'orders.form_contract_m_id', 'orders.amt_vat', 'orders.order_status', 'co.co_desc', 'orders.amt', 'ct.doc_no as contract_no', 'ct.note_large as note_large', 'cur_code', 'pm.description as payment_method', 'e.entity_name')
                ->leftJoin('entities as e', 'orders.entity_id', '=', 'e.id')
                ->leftJoin('companies as co', 'orders.co_id', '=', 'co.id')
                ->leftJoin('contracts as ct', 'orders.form_contract_m_id', '=', 'ct.id')
                ->leftJoin('currencies as cu', 'orders.cur_tra_id', '=', 'cu.id')
                ->rightJoin('payment_methods as pm', 'orders.payment_method_id', '=', 'pm.id')
                ->whereIn('orders.entity_id', $entity_ids);

            $start = date('Y-m-d', strtotime(request()->start_date));
            $end = date('Y-m-d', strtotime(request()->end_date));
            if (!empty(request()->get('entity_id'))) {
                $orders->where('orders.entity_id', request()->get('entity_id'));
            }
            if (!empty(request()->has('contract_status'))) {
                if (request()->contracted === 'contracted') {
                    $orders->whereNotNull('orders.form_contract_m_id');
                }
                if (request()->contracted === 'no-contract') {
                    $orders->whereNull('orders.form_contract_m_id');
                }

            }
            if (!empty(request()->order_status)) {
                $orders->where('order_status', request()->order_status);
            }
            if (!empty($start) && !empty($end)) {
                $orders->whereDate('orders.doc_date', '>=', $start)->whereDate('orders.doc_date', '<=', $end);
            }

            return Datatables::of($orders)
                ->addColumn(
                    'action', function ($row) {
                    $action = '<ul class="nk-tb-actions gx-1">
                                                               <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                    <li><a data-href="siparis/' . encode_id($row->id) . '" data-container="#modal_container" class="btn-modal" ><em class="icon ni ni-focus"></em><span>Hızlı Bakış</span></a></li>
                                                                                      <li><a data-href="/sozlesme/'.encode_id($row->form_contract_m_id).'?action=contract_details" data-container="#modal_container" class="btn-modal"><em class="icon ni ni-text-rich"></em><span>Sözleşme Bilgisi</span></a></li>';
                    if($row->order_status > 0)
                        $action .= '  <li><a data-href="siparis/'.encode_id($row->id).'?action=cancel_request" data-container="#modal_container" class="btn-modal"><em class="icon ni ni-cross-c"></em><span>İptal Talebi Oluştur</span></a></li>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>';
                    $action .= ' </ul>';
                    return $action;
                })
                ->editColumn('doc_no', function ($row) {
                    return $row->doc_no;
                })
                ->editColumn('entity_name', function ($row) {
                    return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="' . $row->entity_name . '">' . limit_words($row->entity_name, 3) . '</span>';
                })
                ->editColumn('doc_date', function ($row) {
                    return date('d.m.Y', strtotime($row->doc_date));
                })
                ->editColumn('contract_no', function ($row) {
                    return '<button class="btn" data-bs-toggle="tooltip" data-bs-title="' . $row->note_large . '">' . $row->contract_no . '</button>';
                })
                ->editColumn('amt', function ($row) {
                    return Number::currency($row->amt+$row->amt_vat, $row->cur_code, app()->getLocale());

                })
                ->editColumn('status_color', function ($row) {
                    if ($row->order_status == 1) {
                        return '#eeeeee'; // dark gray
                    }

                })
                ->rawColumns(['action', 'doc_date', 'doc_no', 'contract_no', 'entity_name'])
                ->removeColumn('note_large')
                ->make(true);
        }


        $data['entities'] = Entity::whereIn('id', $entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.order.index', $data);
    }

    public function shipment_report()
    {
        $user = auth()->user();
//        if (str_contains($user->email, 'akdagtasyunu.com')) {
//            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
//        } else {
//            $entity_ids = $user->entities->pluck('id')->toArray();
//        }
        $entity_ids = $user->entities->pluck('id')->toArray();
        if (request()->ajax()) {

            $orders = Order::select('orders.id', 'orders.doc_no', 'orders.doc_date', 'orders.form_contract_m_id', 'orders.amt_vat', 'orders.order_status', 'co.co_desc', 'orders.amt', 'ct.doc_no as contract_no', 'ct.note_large as note_large', 'cur_code', 'orders.note3', 'e.entity_name')
                ->leftJoin('entities as e', 'orders.entity_id', '=', 'e.id')
                ->leftJoin('companies as co', 'orders.co_id', '=', 'co.id')
                ->leftJoin('contracts as ct', 'orders.form_contract_m_id', '=', 'ct.id')
                ->leftJoin('currencies as cu', 'orders.cur_tra_id', '=', 'cu.id')
                ->whereIn('orders.entity_id', $entity_ids)
                ->where('orders.order_status', 1);

            if (request()->has('entity_id')) {
                $entity_id = request()->get('entity_id');
                if (!empty($entity_id)) {
                    $orders->where('orders.entity_id', $entity_id);
                }
            }

            if (!empty(request()->start_date) && !empty(request()->end_date)) {
                $start = date('Y-m-d', strtotime(request()->start_date));
                $end = date('Y-m-d', strtotime(request()->end_date));
                $orders->whereDate('orders.doc_date', '>=', $start)
                    ->whereDate('orders.doc_date', '<=', $end);
            }

            return Datatables::of($orders)
                ->editColumn('doc_no', function ($row) {
                    return $row->doc_no;
                })
                ->editColumn('entity_name', function ($row) {
                    return '<span data-bs-toggle="tooltip" data-bs-placement="top" title="' . $row->entity_name . '">' . limit_words($row->entity_name, 2) . '</span>';
                })
                ->editColumn('doc_date', function ($row) {
                    return date('d.m.Y', strtotime($row->doc_date));
                })
                ->editColumn('contract_no', function ($row) {
                    return '<button class="btn" data-bs-toggle="tooltip" data-bs-title="' . $row->note_large . '">' . $row->contract_no . '</button>';
                })
                ->editColumn('qty', function ($row) {
                    return number_format($row->order_d()->sum('qty'), 2, ',', '.');
                })
                ->editColumn('remaining_amount', function ($row) {
                    $remaining_amount = $row->order_d()->sum('qty') - $row->order_d()->sum('qty_shipping');
                    if($remaining_amount < 0){
                        $remaining_amount = 0;
                    }
                    return number_format($remaining_amount, 2, ',', '.');
                })
                ->editColumn('zz_qty2', function ($row) {
                    $toplam =  $row->order_d()->sum('zz_qty2');
                    $sevkedilen = $row->order_d()->sum('qty_shipping');
                    $kalan = $toplam-($sevkedilen/34.56);
                    if($kalan < 0){
                        $kalan = 0;
                    }
                    return 'Kalan: ' . number_format(($kalan), 2, ',', '.') . ' <br> Toplam: ' . number_format($toplam, 2, ',', '.');
                })
                ->editColumn('qty_shipping', function ($row) {
                    return number_format($row->order_d()->sum('qty_shipping'), 2, ',', '.');
                })
                ->editColumn('stock_number', function ($row) {
                    $stock_number = $row->order_d()->first();
                    if($stock_number){
                        return $stock_number->stock_number;
                    }

                })
                ->rawColumns(['doc_date', 'doc_no', 'contract_no', 'entity_name', 'zz_qty2'])
                ->make(true);
        }
//        if($user->hasRole('administrator') or $user->hasRole('süper admin') or str_contains($user->email,'akdagtasyunu.com')){
//            $data['entities'] = Entity::orderBy('entity_name')->whereNotNull('is_default')->pluck('entity_name as name', 'id');
//
//        }else{
//            $data['entities'] = Entity::whereIn('id', $entity_ids)->whereNotNull('is_default')->orderBy('entity_name')->pluck('entity_name as name', 'id');
//        }
        $data['entities'] = Entity::whereIn('id', $entity_ids)->whereNotNull('is_default')->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.order.shipment_report', $data);
    }

    public function show($id)
    {
        $id = decode_id($id);
        $action = request()->action;
        $order = Order::findOrFail($id);
        $data['order'] = $order;

        if ($action == 'cancel_request') {
            $data['order'] = $order;
            $data['cancel_reasons'] = config('laravel-pos.cancel_reasons');
            return view('frontend.order.modal.cancel_request', $data);
        } else {
            // Replace uyumapi call with direct database query
            $orderDetails = DB::table('orders_d')
                ->select('orders_d.line_no', 'orders_d.id as order_d_id', 'orders_d.doc_date',
                    'orders_d.amt', 'orders_d.amt_vat',  'qty_shipping', 'orders_d.amt_tra as amt_disc1', 'orders_d.amt_tra',
                    'products.item_name', 'orders_d.unit_price', 'orders_d.zz_qty2')
                ->leftJoin('products', 'orders_d.item_id', '=', 'products.id')
                ->where('orders_d.order_m_id', $order->id)
                ->orderBy('orders_d.line_no', 'ASC')
                ->get();

            $data['orderDetails'] = $orderDetails;
        }

        return view('frontend.order.modal.show', $data);
    }

    public function detail($order_id)
    {
        $order = Order::select('doc_no')->where('id', $order_id)->first();

        // Replace uyumapi call with direct database query
        $order_details = DB::table('orders_d')
            ->select('orders_d.line_no', 'orders_d.id as order_d_id', 'orders_d.doc_date',
                'orders_d.amt', 'orders_d.item_id', 'orders_d.amt_tra as amt_disc1', 'orders_d.amt_tra',
                'products.item_name', 'orders_d.unit_price', 'orders_d.qty', 'orders_d.qty_shipping', 'orders_d.zz_qty2')
            ->leftJoin('products', 'orders_d.item_id', '=', 'products.id')
            ->where('orders_d.order_m_id', $order_id)
            ->orderBy('orders_d.line_no', 'ASC')
            ->get();

        $data['order_details'] = $order_details;
        $data['order'] = $order;

        return view('frontend.order.details', $data);
    }

    public function create()
    {
        $user= auth()->user();
        $user_id = $user->id;

        $data['categories'] = Category::where('step', 1)->where('is_active', 1)->orderBy('description')->get();

        $data['items'] = Product::latest()->limit(100)->get();

        return view('frontend.order.create', $data);
    }

    public function create_wizard()
    {
        $user = auth()->user();
        $action = request()->action;
        if ($action == 'sepet') {
            $data['user_cart'] = Cart::with('cart_items')->where('user_id',$user->id)->where('entity_id', $user->active_entity_id)->orderByDesc('id')->whereNull('ordered_at')->first();
        }

        $data['user'] = $user;
        $data['general_price_list'] = Setting::where('name', 'general_price_list')->value('val');
        $data['order_with_contract'] = Setting::where('name', 'order_with_contract')->value('val');
        $data['warehouse_delivery_only'] = Setting::where('name', 'warehouse_delivery_only')->value('val');

        $data['activePosServices'] = Setting::where('type', 'pos')->where('val', 1)->pluck('name')->toArray();

        $data['countries'] = Country::orderBy('country_name')->get();
        $data['cities'] = City::orderBy('city_name')->where('country_id', 103)->where('is_active', 1)->get();

        $contract_id = request()->contract_id;
        if($contract_id){
            $data['cart'] = Cart::where('user_id', $user->id)->where('entity_id', $user->active_entity_id)->where('contract_id', $contract_id)->whereNull('ordered_at')->first();
        }

        $data['latest_price_list'] = PriceList::where('ispassive', 0)->where('co_id', 2725)->where('is_default', 1)->orderByDesc('id')->value('description_1');

        $data['items'] = Product::latest()->limit(100)->get();
        $data['categories'] = Category::select('id', 'description')->where('parent_cat_id', 2173)->where('zz_b2b',1)->orderBy('description')->get();
        $data['contracts'] = Contract::where('entity_id', $user->active_entity_id)->where('ispassive', 0)->where('is_passive', 0)->where('request_status', 4)->orderByDesc('contract_end_date')->take(20)->get();
        $data['allPosServices'] = config('laravel-pos.banks');

        return view('frontend.order.create_wizard', $data);
    }

    public function stockCards()
    {
        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        try {
            // Replace uyumapi call with direct database query
            $items = Product::select('id as item_id', 'item_name', 'item_code')
                ->orderByDesc('id')
                ->limit(1000)
                ->get();
        } catch (\Exception $e) {
            $items = [];
        }

        return view('frontend.order.stock_cards')->with(compact('items'));
    }

    public function store()
    {
        $request = request()->tax_no;
        $output = ['success' => true,
            'msg' => ''
        ];
        return $output;
    }

    public function report()
    {
        $user = auth()->user();
        $activeEntityId = $user->active_entity_id;

        $orders = Order::select('orders.id', 'orders.doc_no', 'orders.doc_date', 'orders.form_contract_m_id', 'orders.amt_vat', 'orders.amt', 'ct.doc_no as contract_no', 'ct.note_large as note_large', 'cur_code', 'pm.description as payment_method')
            ->leftJoin('contracts as ct', 'orders.form_contract_m_id', '=', 'ct.id')
            ->leftJoin('currencies as cu', 'orders.cur_tra_id', '=', 'cu.id')
            ->rightJoin('payment_methods as pm', 'orders.payment_method_id', '=', 'pm.id')
            ->where('orders.entity_id', $activeEntityId)
            ->orderBy('orders.doc_date', 'desc')
            ->take(10)
            ->get();

        $threeMonthsAgo = Carbon::now()->subMonths(3);
        $contract_orders = Order::select('contracts.doc_description', DB::raw('COUNT(*) as total_orders'), DB::raw('COUNT(*) * 100.0 / (SELECT COUNT(*) FROM orders WHERE entity_id = ? AND doc_date >= ?) as percentage'))
            ->join('contracts', 'orders.form_contract_m_id', '=', 'contracts.id')
            ->where('orders.entity_id', $activeEntityId)
            ->where('orders.doc_date', '>=', $threeMonthsAgo)
            ->groupBy('contracts.doc_description')
            ->setBindings([$activeEntityId, $threeMonthsAgo, $activeEntityId, $threeMonthsAgo]) // Binding parameters for the subquery
            ->get();

        $active_contract_amt_sum = Contract::where('entity_id', $user->active_entity_id)->where('ispassive',0)->where('request_status', 4)->sum('amt');
        $active_contract_used_amount_sum = Contract::where('entity_id', $user->active_entity_id)->where('ispassive',0)->where('request_status', 4)->sum('used_amount');

        $colors = ["#33d895", "#f9db7b", "#ff80b7", "#a224a6", "#A65224FF", "#A224A6FF"];
        $labels = $contract_orders->pluck('doc_description');
        $label_data = $contract_orders->pluck('total_orders');

        return view('frontend.order.report')->with(compact('contract_orders', 'orders', 'labels' ,'label_data', 'colors', 'active_contract_amt_sum', 'active_contract_used_amount_sum'));
    }

    public function update()
    {
        $order_d_id = request()->order_d_id;
        $order_d = OrderD::findOrFail($order_d_id);
        $order = $order_d->order;
        $pallet_quantity = request()->pallet_quantity;
        $cancel_reason = request()->cancel_reason;

        $messageContent = $order->entity->entity_name . ' cari firmasına ait ' . date('d.m.y', strtotime($order->doc_date)) . ' tarihli siparişi olan, ' . $order->doc_no . ' numaralı siparişe ait ' . $order_d->product->item_name . ' levhası üründen ' . $pallet_quantity . ' palet siparişini ' . $cancel_reason . ' nedeniyle iptal talebinde bulunmaktadır.';

        $emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];

        if ($emails) {
            $data = array('name'=>"Akdağ Taşyünü B2B Portal", 'messageContent' => $messageContent);
            dispatch(function () use ($emails, $data, $order) {
                Mail::send(['html'=>'emails.mail'], $data, function($message) use($emails, $order) {
                    $message->to($emails)->subject('Sipariş İptal Talebi: ' . $order->doc_no);
                    $message->from('<EMAIL>', env('APP_NAME', 'B2B Portal'));
                });
            })->onQueue('emails');
        }

        return ['success' => true, 'message' => $order->doc_no . ' nolu sipariş için iptal talebi başarıyla oluşturuldu.'];
    }
}