<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\Contract;
use App\Models\Offer;
use App\Models\Shipment;

class CalendarController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        $events = Event::all();
        $orders = Order::select('id','doc_date','doc_no', 'amt', 'cur_tra_id')->where('entity_id', $user->active_entity_id)->latest()->take(10)->get();
        $contracts = Contract::select('id','doc_no','doc_date','doc_description','contract_end_date','contract_start_date','doc_description')->where('entity_id', $user->active_entity_id)->where('ispassive',0)->where('contract_end_date', '>', date('Y-m-d'))->latest()->take(10)->get();
        $offers = Offer::select('id','doc_date','doc_no')->where('entity_id', $user->active_entity_id)->orderByDesc('id')->take(10)->get();
        $shipments = Shipment::select('id','doc_date','order_doc_no')->where('entity_id', $user->active_entity_id)->orderByDesc('id')->take(10)->get();

        $data['events'] = $events;
        $data['orders'] = $orders;
        $data['contracts'] = $contracts;
        $data['offers'] = $offers;
        $data['shipments'] = $shipments;

        return view('frontend.calendar.index', $data);
    }

    public function store()
    {

        $action = request()->action;
        $user = auth()->user();
        $request = request()->all();
        $request['user_id'] = $user->id;
        $request['entity_id'] = $user->active_entity_id;
        unset($request['_token']);
        if ($request['id']) {
            $id = $request['id'];
            if ($action == 'delete') {
                $event = Event::where('id', $id)->where('user_id', $user->id)->first();
                if(!$event) return ['success' => false, 'message' => 'Takvim kaydı bulunamadı!'];
                $event->delete();
                return ['success' => true, 'message' => 'Takvim kaydı silindi!'];
            }
            unset($request['id']);
            $event = Event::where('id', $id)->where('user_id', $user->id)->first();
            if (!$event) return ['success' => false, 'message' => 'Takvim kaydı bulunamadı!'];
            $event->update($request);
            $output = ['success' => true, 'message' => 'Takvim güncelelndi!'];
        } else {
            Event::create($request);
            $output = ['success' => true, 'message' => 'Takvime yeni kayıt eklendi!'];

        }

        return $output;
    }
}
