<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\BinNumber;
use App\Models\Cart;
use App\Models\Entity;
use App\Models\PaymentTransaction;
use Illuminate\Container\Container;
use Illuminate\Http\Request;
use Mews\Pos\Entity\Card\CreditCardInterface;
use Mews\Pos\Exceptions\CardTypeNotSupportedException;
use Mews\Pos\Exceptions\CardTypeRequiredException;
use Mews\Pos\Exceptions\HashMismatchException;
use Mews\Pos\Factory\CreditCardFactory;
use Mews\Pos\Gateways\PayFlexV4Pos;
use Mews\Pos\PosInterface;
use App\Models\Payment;
use App\Models\Setting;
use App\Mail\NewPaymentMail;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Number;
use GuzzleHttp\Client;

class PosPaymentController extends Controller
{
    private string $paymentModel = PosInterface::MODEL_3D_SECURE;

    public function __construct(
        private PosInterface $pos,
        private Container    $container,

    )
    {
    }

    public function make_payment(Container $container)
    {

        $payment_id = request()->get('id');
        if ($payment_id) {
            $payment = Payment::find($payment_id);
            $data['payment'] = $payment;
            $amount = $payment->amount;

            if ($payment) {
                if ($payment->paymentTransactions->count() > 0) {
                    $last_transaction = PaymentTransaction::where('payment_id', $payment_id)->whereNotNull('md_error_message')->orderByDesc('id')->first();
                    if ($last_transaction) $data['md_error_message'] = $last_transaction->md_error_message;
                }
            }
        }

        if (empty($amount)) {
            $amount = request()->get('amount');
        }

        $user = auth()->user();

        $data['user'] = $user;

        $data['payment_form_active'] = true;
        $data['new_payment'] = true;
        $data['amount'] = $amount;

        return view('frontend.payment.credit_card', $data);
    }

    private function posService(string $bank): PosInterface
    {
        return $this->container->get('laravel-pos:gateway:' . $bank);
    }

    private function getPosService(string $bank): PosInterface
    {
        return $this->container->get('laravel-pos:gateway:' . $bank);
    }

    /**
     * route: /payment/3d/form
     * Kullanicidan kredi kart bilgileri alip buraya POST ediyoruz
     */
    public function form(Request $request)
    {
        $user = auth()->user();
        $session = $request->getSession();
        $default_pos = Setting::where('name', 'default_pos')->value('val');
        $selectedPos = request()->get('bank');
        $installment = $request->get('installment') ?? 1;

        // tek çekim seçtiyse varsayılan postan odmee alinacak
        if($installment == 1) {
            $selectedPos = $default_pos;
        }
        
        // taksit seçtiyse
        if($installment > 1) {
            if (!empty($request->get('number'))) {
                $bin_number = substr(str_replace(' ', '', $request->get('number')), 0, 6);
                if ($bin_number) {
                    $selectedPos = BinNumber::where('bin', $bin_number)->value('pos_name');
                    $selectedPos_active = Setting::where('name', $selectedPos)->value('val');
                    // kartın posu aktif değilse varsayılan posa yönlendir
                    if ($selectedPos_active == 0) {
                        $selectedPos = null;
                    }
                }
            }
        }

        $payment_id = request()->get('payment_id', null);
        $cart_id = request()->get('cart_id', null);
        $amount = request()->get('amount');
        $amount = str_replace('.', '', $amount);
        $amount = str_replace(',', '.', $amount);

        if (empty($selectedPos)) {
            $selectedPos = $default_pos;
        }

        $payment = Payment::find($payment_id);

        // serbest ödeme için payment_id zorunlu
        if (!$payment) {
            $payment = Payment::create([
                'user_id' => $user->id,
                'entity_id' => $user->active_entity_id,
                'amount' => $amount,
                'card_holder' => request()->get('name'),
                'card_number' => ccMasking(request()->get('number')),
                'remaining_amount' => $amount,
            ]);
            $payment_id = $payment->id;
        } else {
            $payment->update([
                'card_holder' => request()->get('name'),
                'card_number' => ccMasking(request()->get('number')),
            ]);
        }
        $txnCode = '';
        if ($selectedPos == 'akbank') {
            $txnCode = '3000';
        }

        $this->pos = $this->posService($selectedPos);
        $session->set('selected_pos', $selectedPos);

        $transaction = $request->get('tx', PosInterface::TX_TYPE_PAY_AUTH);
        $amount = number_format((float)$amount, 2, '.', '');
        $callbackUrl = url("/payment/3d/response");
        $order = $this->createNewOrder(
            $this->paymentModel,
            $callbackUrl,
            $request->getClientIp(),
            $request->get('currency', PosInterface::CURRENCY_TRY),
            $txnCode,
            $request->get('installment'),
            $payment_id,
            $cart_id,
            $amount,
        );
        $session->set('order', $order);

        $card = $this->createCard($this->pos, $request->request->all());

        /**
         * PayFlex'te provizyonu (odemeyi) tamamlamak icin tekrar kredi kart bilgileri isteniyor,
         * bu yuzden kart bilgileri kaydediyoruz
         */
        if ($this->pos::class === PayFlexV4Pos::class) {
            $session->set('card', $request->request->all());
        }
        $session->set('tx', $transaction);

        try {
            $formData = $this->pos->get3DFormData($order, $this->paymentModel, $transaction, $card);

        } catch (\Throwable $e) {
            Log::error($e);
            return redirect()->route('odeme.index')->withErrors(['error' => 'Payment processing error: ' . $e->getMessage()]);
        }

        return view('redirect-form', [
            'formData' => $formData,
        ]);
    }

    /**
     * route: /payment/3d/response
     * Kullanici bankadan geri buraya redirect edilir.
     * Bu route icin CSRF disable edilmesi gerekiyor.
     */
    public function response(Request $request)
    {
        $session = $request->getSession();
        $this->pos = $this->posService($session->get('selected_pos'));
        $transaction = $session->get('tx', PosInterface::TX_TYPE_PAY_AUTH);

        // Log the incoming request from bank
        Log::info('Bank response received', [
            'method' => $request->getMethod(),
            'pos_class' => get_class($this->pos),
            'all_data' => $request->all(),
            'raw_post' => $request->getContent(),
            'headers' => $request->headers->all()
        ]);
        
        // Special handling for KuveytPos - Tamamen manuel işlem
        if ($session->get('selected_pos') === 'kuveytpos') {
            Log::info('KuveytPos detected, using full manual processing');

            // KuveytPos için tamamen manuel işlem
            $kuveytResponse = $this->processKuveytPosManually($request, $session);
            if ($kuveytResponse) {
                $response = $kuveytResponse;
                $session->set('last_response', $response);
                $order = $session->get('order');
                goto handle_payment_result;
            } else {
                Log::error('KuveytPos manual processing failed completely');
                $response = [
                    'status' => 'error',
                    'error_message' => 'Payment processing failed',
                    'md_error_message' => 'Ödeme işlemi başarısız oldu. Lütfen tekrar deneyiniz.',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
                $session->set('last_response', $response);
                $order = $session->get('order');
                goto handle_payment_result;
            }
        }

        // bankadan POST veya GET ile veri gelmesi gerekiyor
        if (($request->getMethod() !== 'POST')
            // PayFlex-CP GET request ile cevapliyor
            && ($request->getMethod() === 'GET' && ($this->pos::class !== \Mews\Pos\Gateways\PayFlexCPV4Pos::class || [] === $request->query->all()))
        ) {
            Log::warning('Invalid request method or empty data from bank');
            return redirect('/');
        }

        $card = null;
        $card_number = null;
        $card_holder = null;
        $entity_id = null;
        $phone_number = null;
        
        $order = $session->get('order');
        if (!$order) {
            throw new \Exception('Sipariş bulunamadı, session sıfırlanmış olabilir.');
        }

        $response = [
            'status' => 'error',
            'error_message' => '',
            'md_error_message' => '',
            'installment_count' => 0,
            'ref_ret_num' => null,
        ];

        try {
            if ($this->pos::class === \Mews\Pos\Gateways\PayFlexV4Pos::class) {
                $savedCard = $session->get('card');
                $card = $this->createCard($this->pos, $savedCard);
            }

            if($session->get('selected_pos') == 'akbank'){
                // İkinci istek - Asıl ödeme işlemi
                $order = array_merge($order, [
                    'txnCode' => '1000'
                ]);
            }

            // Log request data before making the call
            Log::info('Payment request', [
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'payment_model' => $this->paymentModel,
                'transaction_type' => $transaction,
                'pos_class' => get_class($this->pos),
                'selected_pos' => $session->get('selected_pos'),
                'request_data' => $request->all()
            ]);

            // KuveytPos için özel kontrol
            if ($session->get('selected_pos') === 'kuveytpos') {
                // KuveytPos'tan gelen yanıtı kontrol et
                if (!$request->has('AuthenticationResponse') &&
                    !$request->has('MD') &&
                    !$request->has('MerchantOrderId')) {
                    Log::warning('KuveytPos: Required parameters missing from bank response', [
                        'available_params' => array_keys($request->all())
                    ]);

                    $response = [
                        'status' => 'error',
                        'error_message' => 'Invalid response from bank',
                        'md_error_message' => 'Bankadan geçersiz yanıt alındı. Lütfen tekrar deneyiniz.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                    goto handle_payment_result;
                }
            }

            try {
                // KuveytPos için özel işlem
                if ($session->get('selected_pos') === 'kuveytpos') {
                    Log::info('KuveytPos payment request with POS library', [
                        'order' => $order,
                        'transaction' => $transaction,
                        'request_data' => $request->all()
                    ]);

                    // KuveytPos için özel try-catch
                    try {
                        $this->pos->payment($this->paymentModel, $order, $transaction, $card);
                        $response = $this->pos->getResponse();

                        Log::info('KuveytPos payment completed successfully', [
                            'response' => $response,
                            'is_success' => $this->pos->isSuccess()
                        ]);

                    } catch (\Error $kuveytError) {
                        Log::error('KuveytPos specific error caught', [
                            'error_message' => $kuveytError->getMessage(),
                            'error_file' => $kuveytError->getFile(),
                            'error_line' => $kuveytError->getLine(),
                            'request_data' => $request->all()
                        ]);

                        // KuveytPos XML parsing hatası - manuel olarak işle
                        if (strpos($kuveytError->getMessage(), 'attributes') !== false ||
                            strpos($kuveytError->getMessage(), 'Attempt to read property') !== false) {

                            Log::info('KuveytPos XML error detected, attempting manual processing');

                            // Manuel olarak response oluştur
                            $response = $this->handleKuveytPosManualResponse($request, $order);
                            if (!$response) {
                                throw $kuveytError; // Manuel işlem de başarısız olursa orijinal hatayı fırlat
                            }
                        } else {
                            throw $kuveytError;
                        }
                    }
                } else {
                    // Diğer POS'lar için normal işlem
                    $this->pos->payment($this->paymentModel, $order, $transaction, $card);
                    $response = $this->pos->getResponse();
                }

                Log::info('Payment response received', [
                    'pos_type' => $session->get('selected_pos'),
                    'response_type' => gettype($response),
                    'response_is_null' => is_null($response),
                    'response_keys' => is_array($response) ? array_keys($response) : 'not_array',
                    'response_data' => $response,
                    'is_success' => method_exists($this->pos, 'isSuccess') ? $this->pos->isSuccess() : false
                ]);

            } catch (\Error $e) {
                Log::error('KuveytPos payment processing failed with fatal error', [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);

                // KuveytPos serializer hatası için özel mesaj
                if (strpos($e->getMessage(), 'attributes') !== false ||
                    strpos($e->getMessage(), 'Attempt to read property') !== false) {
                    Log::error('KuveytPos XML parsing failed - likely payment declined or invalid response', [
                        'raw_request_data' => $request->all(),
                        'raw_request_content' => $request->getContent(),
                        'selected_pos' => $session->get('selected_pos')
                    ]);

                    // Ödeme başarısız olduğu için error response oluştur
                    $response = [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => 'Ödeme bankaca reddedildi veya geçersiz yanıt alındı. Kart bilgilerinizi kontrol ederek tekrar deneyiniz veya farklı bir kart kullanınız.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                } else {
                    // Diğer hatalar için genel error response
                    $response = [
                        'status' => 'error',
                        'error_message' => 'Payment processing error',
                        'md_error_message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                }
            } catch (\Exception $e) {
                Log::error('KuveytPos payment processing failed with exception', [
                    'error_message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]);

                $response = [
                    'status' => 'error',
                    'error_message' => 'Payment processing error',
                    'md_error_message' => 'Ödeme işlemi sırasında bir hata oluştu: ' . $e->getMessage(),
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
            }
            
            // Null check for response and ensure required keys exist
            if (!$response) {
                $response = [
                    'status' => 'error',
                    'error_message' => 'No response received from payment gateway',
                    'md_error_message' => 'Gateway returned null response',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ];
            } else {
                // Ensure required keys exist with default values
                $response = array_merge([
                    'status' => 'error',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                ], $response);
            }
            
            $session->set('last_response', $response);
        } catch (HashMismatchException $e) {
            Log::error($e->getMessage());
            $response = array_merge($response, [
                'error_message' => 'Hash mismatch error occurred',
                'md_error_message' => $e->getMessage(),
            ]);
        } catch (\Exception|\Error $e) {
            Log::error($e->getMessage());
            $response = array_merge($response, [
                'error_message' => 'Payment processing error',
                'md_error_message' => $e->getMessage(),
            ]);
        }

        handle_payment_result:
        $payment_id = null;
        $cart_id = null;
        if (str_starts_with($order['id'], 'P')) {
            $payment_id = (int)substr($order['id'], 1);
            $payment = Payment::find($payment_id);
            if ($payment) {
                $entity_id = $payment->entity_id;
            }
        }

        if (str_starts_with($order['id'], 'C')) {
            $cart_id = (int)substr($order['id'], 1);
            $cart = Cart::find($cart_id);
            if ($cart) {
                $entity_id = $cart->entity_id;
            }
        }

        if ($payment) {
            $card_number = $payment->card_number;
            $card_holder = $payment->card_holder;
            $phone_number = $payment->phone_number;
        }

        PaymentTransaction::create([
            'payment_id' => $payment_id ?? null,
            'cart_id' => $cart_id ?? null,
            'entity_id' => $entity_id ?? null,
            'order_id' => $order['id'],
            'amount' => $order['amount'],
            'status' => $response['status'] ?? 'error',
            'md_error_message' => $response['md_error_message'] ?? '',
            'pos_name' => $session->get('selected_pos'),
            'card_holder' => $card_holder,
            'card_number' => $card_number,
            'provision_number' => $response['ref_ret_num'] ?? null,
            'installment_count' => $response['installment_count'] ?? 0,
            'response' => json_encode($response),
        ]);

        // Ödeme başarı kontrolü
        $isPaymentSuccessful = false;

        if ($session->get('selected_pos') === 'kuveytpos') {
            // KuveytPos için özel başarı kontrolü
            $posIsSuccess = false;
            try {
                $posIsSuccess = $this->pos->isSuccess();
            } catch (\Exception $e) {
                Log::warning('KuveytPos isSuccess() method failed', ['error' => $e->getMessage()]);
                $posIsSuccess = false;
            }

            $isPaymentSuccessful = (isset($response['status']) && $response['status'] === 'approved') ||
                                   $posIsSuccess ||
                                   (isset($response['response_code']) && $response['response_code'] === '00');

            Log::info('KuveytPos payment success check', [
                'response_status' => $response['status'] ?? 'not_set',
                'pos_is_success' => $posIsSuccess,
                'response_code' => $response['response_code'] ?? 'not_set',
                'final_success' => $isPaymentSuccessful,
                'response_data' => $response
            ]);
        } else {
            // Diğer POS'lar için normal kontrol
            $isPaymentSuccessful = $this->pos->isSuccess() || (isset($response['status']) && $response['status'] === 'approved');
        }
        
        if ($isPaymentSuccessful) {
            if ($phone_number) {
                try {
                    $sms_message = 'Sayın '.$card_holder.', '.$card_number.' nolu kartınızdan bayi olan ticari borcunuza karşılık AKDAĞ YALITIM A.Ş\'ye '.Number::currency($payment->amount, 'TRY', 'tr').' ödeme yaptınız. Yaptığınız ödemenin karşılığını almadıysanız ya da ödemeyi onaylamıyorsanız bankanız ile irtibata geçerek işlemi iptal ediniz, aksi halde herhangi bir talepte bulunamayacağınızı ve şirketimizi sorumlu tutamayacağınızı bildiririz.';
                    $client = new Client();
                    $request_data = [
                        'dil' => 'tr',
                        'msgheader' => 'AKDAGYALITM',
                        'usercode' => '**********',
                        'password' => '92@BB6D',
                        'gsmno' => $phone_number,
                        'message' => $sms_message
                    ];
                    $client->get('https://api.netgsm.com.tr/sms/send/get/?' . http_build_query($request_data));
                } catch (\Exception $e) {
                    // sms gonderilemedi
                }
            }

//            $user = auth()->user();
//            $entity = Entity::find($user->active_entity_id);
//            $input = [
//                'entity_name' => $entity->entity_name,
//                'pos_name' => $session->get('selected_pos'),
//                'amount' => $order['amount'],
//                'card_holder' => $card_holder,
//                'installment_count' => $response['installment_count'],
//                'card_number' => $card_number,
//                'created_at' => now(),
//            ];
//            $emails = ['<EMAIL>'];
//            $data = [
//                'name' => "Akdag Tasyunu Portal",
//                'user' => $user,
//                'input' => $input
//            ];
//            Mail::to($emails)->queue(new NewPaymentMail($data));

            // odeme basarili, siparis durumunu guncelle
            if ($payment_id) {
                $totalPaid = PaymentTransaction::where('payment_id', $payment_id)->where('status', 'approved')->sum('amount');
                $payment = Payment::find($payment_id);
                $payment->remaining_amount = $payment->amount - $totalPaid;
                $payment->last_payment_date = date('Y-m-d');
                $payment->save();
                return redirect()->route('payment.show', ['id' => encode_id($payment_id)]);
            }

            if ($cart_id) {
                $cart = Cart::find($cart_id);
                if ($cart) {
                    return post_order_to_uyum($cart);
                }
                return redirect()->route('order.create_wizard', ['action' => 'odendi', 'id' => encode_id($cart_id)]);
            }
        }

        if ($payment_id) {
            return redirect()->route('payment.show', ['id' => encode_id($payment_id)])
                ->withErrors($response['error_message'] ?? 'Payment failed');
        }

        if ($cart_id) {
            return redirect()->route('order.create_wizard', ['action' => 'sepet'])
                ->withErrors($response['error_message'] ?? 'Payment failed');
        }

        return redirect()->route('odeme.index')->withErrors($response['error_message'] ?? 'Payment failed');
    }

    private function createNewOrder(
        string $paymentModel,
        string $callbackUrl,
        string $ip,
        string $currency,
        string $txnCode,
        ?int   $installment = 0,
        ?int   $payment_id = 0,
        ?int   $cart_id = 0,
        ?float $amount = 0.0,
        string $lang = PosInterface::LANG_TR

    ): array
    {
        if ($payment_id > 0) {
            $orderId = 'P' . $payment_id;
        } elseif ($cart_id > 0) {
            $orderId = 'C' . $cart_id;
        } else {
            $orderId = date('Ymd') . strtoupper(substr(uniqid(sha1(time())), 0, 4));
        }

        $order = [
            'id' => $orderId,
            'amount' => $amount,
            'currency' => $currency,
            'installment' => $installment,
            'txnCode' => $txnCode,
            'ip' => filter_var($ip, \FILTER_VALIDATE_IP, \FILTER_FLAG_IPV4) ? $ip : '127.0.0.1',
        ];

        if (in_array($paymentModel, [
            PosInterface::MODEL_3D_SECURE,
            PosInterface::MODEL_3D_PAY,
            PosInterface::MODEL_3D_HOST,
            PosInterface::MODEL_3D_PAY_HOSTING,
        ], true)) {
            $order['success_url'] = $callbackUrl;
            $order['fail_url'] = $callbackUrl;
        }

        if ($lang) {
            //lang degeri verilmezse account (EstPosAccount) dili kullanilacak
            $order['lang'] = $lang;
        }

        return $order;
    }

    private function createCard(PosInterface $pos, array $card): CreditCardInterface
    {
        try {
            // Get card type from form or detect it
            $providedType = $card['type'] ?? null;
            $detectedType = $this->detectCardType($card['number']);
            
            // Normalize the card type - convert form values to gateway-expected values
            $cardType = $this->normalizeCardType($providedType) ?? $detectedType;
            
            // Debug logging
            Log::info('Card type detection', [
                'card_number_prefix' => substr($card['number'], 0, 4),
                'provided_card_type' => $providedType,
                'detected_card_type' => $detectedType,
                'final_card_type' => $cardType,
                'pos_class' => get_class($pos),
                'supported_card_types' => array_keys($pos->getCardTypeMapping())
            ]);
            
            return CreditCardFactory::createForGateway(
                $pos,
                $card['number'],
                $card['year'],
                $card['month'],
                $card['cvv'],
                $card['name'],
                $cardType
            );
        } catch (CardTypeRequiredException|CardTypeNotSupportedException $e) {
            Log::error('Card type error', [
                'final_card_type' => $cardType ?? 'unknown',
                'card_number_prefix' => substr($card['number'], 0, 4),
                'supported_types' => array_keys($pos->getCardTypeMapping()),
                'error' => $e->getMessage()
            ]);
            throw $e;
        } catch (\LogicException $e) {
            Log::error($e);
            throw $e;
        }
    }

    private function normalizeCardType(?string $cardType): ?string
    {
        if (!$cardType) {
            return null;
        }

        // Convert form values to gateway-expected values
        $typeMapping = [
            'MasterCard' => 'master',
            'Visa' => 'visa',
            'Troy' => 'troy',
            'Amex' => null, // Not supported by KuveytPos
            'American Express' => null, // Not supported
        ];

        return $typeMapping[$cardType] ?? strtolower($cardType);
    }

    private function detectCardType(string $cardNumber): string
    {
        // Remove spaces and non-numeric characters
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        
        Log::info('Card type detection input', ['card_prefix' => substr($cardNumber, 0, 6)]);
        
        // Troy cards (9528, 6542, 6552, 65xx ranges)
        if (preg_match('/^9528/', $cardNumber) || 
            preg_match('/^6542/', $cardNumber) || 
            preg_match('/^6552/', $cardNumber)) {
            Log::info('Detected Troy card');
            return 'troy'; // Exact match for supported types
        }
        
        // Visa cards (4xxx)
        if (preg_match('/^4/', $cardNumber)) {
            Log::info('Detected Visa card');
            return 'visa'; // Exact match for supported types
        }
        
        // MasterCard (5xxx, 2xxx ranges)
        if (preg_match('/^5[1-5]/', $cardNumber) || 
            preg_match('/^2[2-7]/', $cardNumber)) {
            Log::info('Detected MasterCard card');
            return 'master'; // Exact match for supported types
        }
        
        // American Express is not supported by KuveytPos, default to visa
        // Default to Visa if cannot detect or card type not supported
        Log::info('Defaulting to Visa card type');
        return 'visa';
    }

    private function parseKuveytPosAuthenticationResponse(string $xmlResponse, Request $request): ?array
    {
        try {
            // XML response'un boş olup olmadığını kontrol et
            if (empty(trim($xmlResponse))) {
                Log::error('KuveytPos XML response is empty');
                return null;
            }

            $dom = new \DOMDocument();
            // XML parsing hatalarını bastır
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlResponse)) {
                $xmlErrors = libxml_get_errors();
                Log::error('Failed to parse KuveytPos XML response', [
                    'xml_errors' => $xmlErrors,
                    'xml_content' => $xmlResponse
                ]);
                libxml_clear_errors();
                return null;
            }

            // Extract key values from XML
            $responseCode = $this->getXmlValue($dom, 'ResponseCode');
            $responseMessage = $this->getXmlValue($dom, 'ResponseMessage');
            $mdStatusCode = $this->getXmlValue($dom, 'MDStatusCode');
            $mdStatusDescription = $this->getXmlValue($dom, 'MDStatusDescription');
            $merchantOrderId = $this->getXmlValue($dom, 'MerchantOrderId');
            $orderId = $this->getXmlValue($dom, 'OrderId');
            $amount = $this->getXmlValue($dom, 'Amount');
            $installmentCount = $this->getXmlValue($dom, 'InstallmentCount');
            $businessKey = $this->getXmlValue($dom, 'BusinessKey');

            Log::info('KuveytPos XML parsed values', [
                'response_code' => $responseCode,
                'response_message' => $responseMessage,
                'md_status_code' => $mdStatusCode,
                'md_status_description' => $mdStatusDescription,
                'merchant_order_id' => $merchantOrderId,
                'order_id' => $orderId,
                'amount' => $amount,
                'installment_count' => $installmentCount,
                'business_key' => $businessKey
            ]);

            // Check if authentication was successful
            if ($responseCode === '00' && $mdStatusCode === '1' && $mdStatusDescription === 'AUTHENTICATION_SUCCESSFUL') {
                return [
                    'status' => 'approved',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => (int)$installmentCount ?: 0,
                    'ref_ret_num' => $businessKey,
                    'order_id' => $orderId,
                    'merchant_order_id' => $merchantOrderId,
                    'amount' => $amount,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                    'md_status' => $mdStatusCode,
                    'md_status_description' => $mdStatusDescription,
                ];
            } else {
                return [
                    'status' => 'declined',
                    'error_message' => 'Payment declined by bank',
                    'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error parsing KuveytPos AuthenticationResponse', [
                'error' => $e->getMessage(),
                'xml' => $xmlResponse
            ]);
            return null;
        }
    }

    private function getXmlValue(\DOMDocument $dom, string $tagName): ?string
    {
        try {
            $nodes = $dom->getElementsByTagName($tagName);
            if ($nodes && $nodes->length > 0) {
                $node = $nodes->item(0);
                if ($node && $node->textContent !== null) {
                    return trim($node->textContent);
                }
            }
        } catch (\Exception $e) {
            Log::warning('Error extracting XML value', [
                'tag_name' => $tagName,
                'error' => $e->getMessage()
            ]);
        }
        return null;
    }

    /**
     * KuveytPos için tamamen manuel işlem
     */
    private function processKuveytPosManually(Request $request, $session): ?array
    {
        try {
            Log::info('KuveytPos full manual processing started', [
                'request_method' => $request->getMethod(),
                'request_data' => $request->all(),
                'request_content' => $request->getContent()
            ]);

            $order = $session->get('order');
            if (!$order) {
                Log::error('Order not found in session');
                return null;
            }

            // AuthenticationResponse varsa işle
            if ($request->has('AuthenticationResponse')) {
                Log::info('Processing AuthenticationResponse');

                $authResponse = urldecode($request->get('AuthenticationResponse'));
                $parsedAuth = $this->parseKuveytPosAuthenticationResponse($authResponse, $request);

                if ($parsedAuth && $parsedAuth['status'] === 'approved') {
                    Log::info('3D authentication successful, sending provision request');
                    return $this->makeKuveytPosProvisionRequest($parsedAuth, $order, $request);
                } else {
                    Log::warning('3D authentication failed', ['parsed_auth' => $parsedAuth]);
                    return [
                        'status' => 'declined',
                        'error_message' => 'Authentication failed',
                        'md_error_message' => '3D Secure doğrulama başarısız oldu.',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                    ];
                }
            }

            // Direkt POST parametreleri varsa işle
            $responseCode = $request->get('ResponseCode');
            $responseMessage = $request->get('ResponseMessage');
            $md = $request->get('MD');
            $merchantOrderId = $request->get('MerchantOrderId');
            $orderId = $request->get('OrderId');
            $amount = $request->get('Amount');

            if ($responseCode && $merchantOrderId) {
                Log::info('Processing direct POST parameters', [
                    'response_code' => $responseCode,
                    'merchant_order_id' => $merchantOrderId
                ]);

                if ($responseCode === '00') {
                    return [
                        'status' => 'approved',
                        'error_message' => '',
                        'md_error_message' => '',
                        'installment_count' => (int)$request->get('InstallmentCount', 0),
                        'ref_ret_num' => $md,
                        'order_id' => $orderId,
                        'merchant_order_id' => $merchantOrderId,
                        'amount' => $amount ?: $order['amount'],
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                } else {
                    return [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                }
            }

            Log::warning('KuveytPos: No recognizable response format found');
            return null;

        } catch (\Exception $e) {
            Log::error('KuveytPos manual processing failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos XML hatası durumunda manuel response işleme
     */
    private function handleKuveytPosManualResponse(Request $request, array $order): ?array
    {
        try {
            Log::info('KuveytPos manual response processing started', [
                'request_data' => $request->all(),
                'order' => $order
            ]);

            // Request'ten gerekli verileri al
            $md = $request->get('MD');
            $merchantOrderId = $request->get('MerchantOrderId');
            $orderId = $request->get('OrderId');
            $amount = $request->get('Amount');
            $responseCode = $request->get('ResponseCode');
            $responseMessage = $request->get('ResponseMessage');
            $installmentCount = $request->get('InstallmentCount', 0);

            // AuthenticationResponse varsa onu da kontrol et
            if ($request->has('AuthenticationResponse')) {
                $authResponse = urldecode($request->get('AuthenticationResponse'));
                $parsedAuth = $this->parseKuveytPosAuthenticationResponse($authResponse, $request);

                if ($parsedAuth && $parsedAuth['status'] === 'approved') {
                    // 3D doğrulama başarılı, şimdi provision isteği gönder
                    Log::info('KuveytPos manual: 3D auth successful, sending provision request');
                    return $this->makeKuveytPosProvisionRequest($parsedAuth, $order, $request);
                }
            }

            // Direkt response parametreleri varsa bunları kullan
            if ($responseCode && $merchantOrderId) {
                Log::info('KuveytPos manual: Processing direct response parameters', [
                    'response_code' => $responseCode,
                    'merchant_order_id' => $merchantOrderId,
                    'md' => $md
                ]);

                if ($responseCode === '00') {
                    // Başarılı response
                    return [
                        'status' => 'approved',
                        'error_message' => '',
                        'md_error_message' => '',
                        'installment_count' => (int)$installmentCount,
                        'ref_ret_num' => $md,
                        'order_id' => $orderId,
                        'merchant_order_id' => $merchantOrderId,
                        'amount' => $amount,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                } else {
                    // Başarısız response
                    return [
                        'status' => 'declined',
                        'error_message' => 'Payment declined by bank',
                        'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                        'installment_count' => 0,
                        'ref_ret_num' => null,
                        'response_code' => $responseCode,
                        'response_message' => $responseMessage,
                    ];
                }
            }

            Log::warning('KuveytPos manual: Insufficient data for manual processing');
            return null;

        } catch (\Exception $e) {
            Log::error('KuveytPos manual response processing failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos için provision (ikinci istek) gönder
     */
    private function makeKuveytPosProvisionRequest(array $authResponse, array $order, Request $request): ?array
    {
        try {
            Log::info('KuveytPos provision request starting', [
                'order_id' => $order['id'],
                'amount' => $order['amount'],
                'auth_response' => $authResponse
            ]);

            // KuveytPos provision için gerekli parametreler
            $provisionData = [
                'MerchantId' => config('laravel-pos.banks.kuveytpos.credentials.merchant_id'),
                'CustomerId' => config('laravel-pos.banks.kuveytpos.credentials.terminal_id'),
                'UserName' => config('laravel-pos.banks.kuveytpos.credentials.user_name'),
                'Password' => config('laravel-pos.banks.kuveytpos.credentials.enc_key'),
                'OrderId' => $authResponse['order_id'] ?? $order['id'],
                'MerchantOrderId' => $authResponse['merchant_order_id'] ?? $order['id'],
                'Amount' => $authResponse['amount'] ?? $order['amount'], // String olarak kullan
                'InstallmentCount' => $authResponse['installment_count'] ?? 0,
                'BusinessKey' => $authResponse['ref_ret_num'],
                'TransactionType' => 'Sale', // veya 'Auth' sonra 'Capture'
            ];

            Log::info('KuveytPos provision data prepared', [
                'provision_data' => $provisionData,
                'auth_response_amount' => $authResponse['amount'] ?? 'not_set',
                'order_amount' => $order['amount'] ?? 'not_set'
            ]);

            // XML request oluştur
            $xmlRequest = $this->buildKuveytPosProvisionXml($provisionData);

            Log::info('KuveytPos provision XML request', ['xml' => $xmlRequest]);

            // HTTP client ile provision endpoint'ine istek gönder
            $client = new \GuzzleHttp\Client();
            $provisionUrl = config('laravel-pos.banks.kuveytpos.gateway_endpoints.query_api');

            Log::info('KuveytPos sending provision request', [
                'url' => $provisionUrl,
                'xml_request' => $xmlRequest
            ]);

            $response = $client->post($provisionUrl, [
                'headers' => [
                    'Content-Type' => 'application/xml',
                    'Accept' => 'application/xml',
                ],
                'body' => $xmlRequest,
                'timeout' => 30,
                'verify' => false, // SSL doğrulamasını devre dışı bırak
            ]);

            $responseBody = $response->getBody()->getContents();
            Log::info('KuveytPos provision response received', [
                'status_code' => $response->getStatusCode(),
                'headers' => $response->getHeaders(),
                'response' => $responseBody
            ]);

            // XML response'u parse et
            return $this->parseKuveytPosProvisionResponse($responseBody);

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error('KuveytPos provision HTTP request failed', [
                'error' => $e->getMessage(),
                'request' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : 'No response',
                'status_code' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : 'No status'
            ]);
            return null;
        } catch (\Exception $e) {
            Log::error('KuveytPos provision request failed', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    /**
     * KuveytPos provision XML request oluştur (Resmi dokümana göre)
     */
    private function buildKuveytPosProvisionXml(array $data): string
    {
        // KuveytTurk resmi dokümanına göre hash hesapla
        $hashData = $this->calculateKuveytPosProvisionHash($data);

        // KuveytTurk resmi provision formatı
        $xml = '<?xml version="1.0" encoding="utf-8"?>';
        $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
        $xml .= '<APIVersion>TDV2.0.0</APIVersion>'; // Resmi dokümandaki versiyon
        $xml .= '<HashData>' . htmlspecialchars($hashData) . '</HashData>';
        $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
        $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
        $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
        $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
        $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
        $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
        $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
        $xml .= '<TransactionSecurity>3</TransactionSecurity>'; // 3D Secure

        // MD (BusinessKey) bilgisini ekle - Resmi dokümandaki format
        $xml .= '<KuveytTurkVPosAdditionalData>';
        $xml .= '<AdditionalData>';
        $xml .= '<Key>MD</Key>';
        $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
        $xml .= '</AdditionalData>';
        $xml .= '</KuveytTurkVPosAdditionalData>';

        $xml .= '</KuveytTurkVPosMessage>';

        Log::info('KuveytPos provision XML built (official format)', [
            'xml_length' => strlen($xml),
            'api_version' => 'TDV2.0.0',
            'merchant_id' => $data['MerchantId'],
            'customer_id' => $data['CustomerId'],
            'amount' => $data['Amount'],
            'business_key' => $data['BusinessKey'],
            'hash_data' => $hashData
        ]);

        return $xml;
    }

    /**
     * KuveytPos provision için hash hesapla (Resmi dokümana göre)
     */
    private function calculateKuveytPosProvisionHash(array $data): string
    {
        // KuveytTurk provision hash formatı: MerchantId + MerchantOrderId + Amount + UserName + Password
        $hashString = $data['MerchantId'] .
                     $data['MerchantOrderId'] .
                     $data['Amount'] .
                     $data['UserName'] .
                     $data['Password'];

        // SHA-1 hash hesapla ve Base64 encode et
        $hash = base64_encode(sha1($hashString, true));

        Log::info('KuveytPos provision hash calculated', [
            'hash_string' => $hashString,
            'hash' => $hash,
            'hash_length' => strlen($hash)
        ]);

        return $hash;
    }

    /**
     * KuveytPos için hash hesapla (Eski metod - kullanılmıyor)
     */
    private function calculateKuveytPosHash(array $data): string
    {
        // KuveytTurk provision için farklı hash formatları dene

        // Format 1: Standart format
        $hashString1 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      '' . // OkUrl (boş)
                      '' . // FailUrl (boş)
                      $data['UserName'] .
                      $data['Password'];

        // Format 2: Sadece temel alanlar
        $hashString2 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      $data['UserName'] .
                      $data['Password'];

        // Format 3: BusinessKey dahil
        $hashString3 = $data['MerchantId'] .
                      $data['MerchantOrderId'] .
                      $data['Amount'] .
                      $data['BusinessKey'] .
                      $data['UserName'] .
                      $data['Password'];

        // Farklı hash algoritmaları dene
        $hash1 = base64_encode(sha1($hashString1, true));
        $hash2 = base64_encode(sha1($hashString2, true));
        $hash3 = base64_encode(sha1($hashString3, true));
        $hash4 = base64_encode(hash('sha256', $hashString1, true));
        $hash5 = sha1($hashString1); // Hex format

        Log::info('KuveytPos hash calculations', [
            'hash_string_1' => $hashString1,
            'hash_string_2' => $hashString2,
            'hash_string_3' => $hashString3,
            'sha1_base64_1' => $hash1,
            'sha1_base64_2' => $hash2,
            'sha1_base64_3' => $hash3,
            'sha256_base64' => $hash4,
            'sha1_hex' => $hash5
        ]);

        // İlk olarak standart formatı dene
        return $hash1;
    }

    /**
     * KuveytPos provision response'u parse et
     */
    private function parseKuveytPosProvisionResponse(string $xmlResponse): ?array
    {
        try {
            if (empty(trim($xmlResponse))) {
                Log::error('KuveytPos provision response is empty');
                return null;
            }

            $dom = new \DOMDocument();
            libxml_use_internal_errors(true);

            if (!$dom->loadXML($xmlResponse)) {
                $xmlErrors = libxml_get_errors();
                Log::error('Failed to parse KuveytPos provision response', [
                    'xml_errors' => $xmlErrors,
                    'xml_content' => $xmlResponse
                ]);
                libxml_clear_errors();
                return null;
            }

            // Provision response'dan değerleri çıkar
            $responseCode = $this->getXmlValue($dom, 'ResponseCode');
            $responseMessage = $this->getXmlValue($dom, 'ResponseMessage');
            $orderId = $this->getXmlValue($dom, 'OrderId');
            $merchantOrderId = $this->getXmlValue($dom, 'MerchantOrderId');
            $amount = $this->getXmlValue($dom, 'Amount');
            $provisionNumber = $this->getXmlValue($dom, 'ProvisionNumber');
            $rrn = $this->getXmlValue($dom, 'RRN');
            $stan = $this->getXmlValue($dom, 'Stan');
            $installmentCount = $this->getXmlValue($dom, 'InstallmentCount');

            Log::info('KuveytPos provision response parsed', [
                'response_code' => $responseCode,
                'response_message' => $responseMessage,
                'order_id' => $orderId,
                'merchant_order_id' => $merchantOrderId,
                'amount' => $amount,
                'provision_number' => $provisionNumber,
                'rrn' => $rrn,
                'stan' => $stan,
                'installment_count' => $installmentCount
            ]);

            // Başarılı provision kontrolü
            if ($responseCode === '00') {
                Log::info('KuveytPos provision successful', [
                    'response_code' => $responseCode,
                    'provision_number' => $provisionNumber,
                    'rrn' => $rrn
                ]);

                return [
                    'status' => 'approved',
                    'error_message' => '',
                    'md_error_message' => '',
                    'installment_count' => (int)$installmentCount ?: 0,
                    'ref_ret_num' => $provisionNumber ?: $rrn,
                    'order_id' => $orderId,
                    'merchant_order_id' => $merchantOrderId,
                    'amount' => $amount,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                    'provision_number' => $provisionNumber,
                    'rrn' => $rrn,
                    'stan' => $stan,
                ];
            } else {
                Log::warning('KuveytPos provision failed', [
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage
                ]);

                return [
                    'status' => 'declined',
                    'error_message' => 'Payment declined by bank',
                    'md_error_message' => $responseMessage . ' (Code: ' . $responseCode . ')',
                    'installment_count' => 0,
                    'ref_ret_num' => null,
                    'response_code' => $responseCode,
                    'response_message' => $responseMessage,
                ];
            }

        } catch (\Exception $e) {
            Log::error('Error parsing KuveytPos provision response', [
                'error' => $e->getMessage(),
                'xml' => $xmlResponse
            ]);
            return null;
        }
    }

}