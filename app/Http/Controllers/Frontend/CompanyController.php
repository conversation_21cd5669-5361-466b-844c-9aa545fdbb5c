<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Company;
use App\Models\Ad;

class CompanyController extends Controller
{
    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $data['companies'] = Company::leftJoin('cities','cities.id','=','companies.city_id')->select('companies.id','companies.address1','companies.phone','co_desc', 'co_short_desc','cities.name as city_name')->simplePaginate(20);

        return view('frontend.companies', $data);
    }


}
