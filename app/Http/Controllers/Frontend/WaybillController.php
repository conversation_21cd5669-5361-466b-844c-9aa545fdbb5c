<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Contract;
use App\Models\Entity;
use App\Models\Product;
use App\Models\Order;
use App\Models\Waybill;
use Illuminate\Support\Number;

use App\Models\User;
use Illuminate\Http\Request;

class WaybillController extends Controller
{

    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
//        if (str_contains($user->email, 'akdagtasyunu.com')) {
//            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
//        } else {
//            $entity_ids = $user->entities->pluck('id')->toArray();
//        }
        $entity_ids = $user->entities->pluck('id')->toArray();

            if (request()->ajax()) {

                $waybill = Waybill::select('e.entity_name','waybills.id', 'waybills.doc_no','waybills.id', 'waybills.amt', 'waybills.uuid', 'waybills.amt_vat', 'waybills.e_doc_no', 'waybills.doc_date', 'co.co_code',  'co.co_desc', 'cur_code', 'e.entity_name')
                  ->where('co_id',2725)
                    ->whereIn('entity_id', $entity_ids)
                    ->leftJoin('entities as e', 'waybills.entity_id', '=', 'e.id')
                    ->leftJoin('currencies as cu', 'waybills.cur_tra_id', '=', 'cu.id')
                    ->leftJoin('companies as co', 'waybills.co_id', '=', 'co.id');

                if (request()->has('entity_id')) {
                    $entity_id = request()->get('entity_id');
                    if (!empty($entity_id)) {
                        $waybill->where('waybills.entity_id', $entity_id);
                    }
                }

                if (request()->has('entity_id')) {
                    $entity_id = request()->get('entity_id');
                    if (!empty($entity_id)) {
                        $waybill->where('waybills.entity_id', $entity_id);
                    }
                }
                if (request()->has('status')) {
                    $status = request()->get('status');
                    $end = date('Y-m-d', time());
                    if ($status == 'active') {
                        $waybill->whereDate('doc_date', '>=', $end);
                    } else if ($status == 'passive') {
                        $waybill->whereDate('doc_date', '<', $end);
                    }
                }
                if (!empty(request()->start_date) && !empty(request()->end_date)) {
                    $start = date('Y-m-d', strtotime(request()->start_date));
                    $end = date('Y-m-d', strtotime(request()->end_date));
                    $waybill->whereDate('doc_date', '>=', $start)
                        ->whereDate('doc_date', '<=', $end);
                }
                return Datatables::of($waybill)
                    ->addColumn(
                        'action', function ($row) {
                        $action = '<ul class="nk-tb-actions gx-1">
                           
                                                                    <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                 <li><a href="https://portal.uyumsoft.com.tr/Genel/Irsaliye/'.$row->uuid.'" target="_blank"><em class="icon ni ni-file-pdf"></em><span>İrsaliye Görüntüle</span></a></li>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                        return $action;

                    } )
                    ->editColumn('doc_no', function ($row) {
                        if(empty($row->description))
                            return $row->doc_no;
                        else
                            return $row->doc_no . ' <i class="text-info ni ni-info-fill" data-bs-toggle="tooltip" title="'.$row->description.'"></i>';
                    })
                    ->editColumn('entity_name', function ($row) {
                        return limit_words($row->entity_name, 3);
                    })
                    ->editColumn('doc_date', function ($row) {
                        return date('d.m.Y',strtotime($row->doc_date));
                    })
                    ->editColumn('co_desc', function ($row) {
                        return '<button class="btn" data-bs-toggle="tooltip" data-bs-title="'.$row->co_desc.'">'.$row->co_code.'</button>';
                    })
                    ->editColumn('amt', function ($row) {
                        return '<b>'.Number::currency($row->amt,$row->cur_code,'tr').'</b>';
                    })
                    ->editColumn('amt_vat', function ($row) {
                        return '<b>'.Number::currency($row->amt_vat,$row->cur_code,'tr').'</b>';
                    })
                    ->setRowAttr([
                        'data-href' => function ($row) {
                            return  action([\App\Http\Controllers\Frontend\WaybillController::class, 'show'], [$row->id]);
                        }, ])
                    ->rawColumns(['action', 'co_desc', 'doc_no','amt', 'amt_vat'])
                    ->removeColumn('co_code','uuid')
                    ->make(true);
            }

//        if($user->hasRole('administrator') or $user->hasRole('süper admin') or str_contains($user->email,'akdagtasyunu.com')){
//            $data['entities'] = Entity::orderBy('entity_name')->whereNotNull('is_default')->pluck('entity_name as name', 'id');
//
//        }else{
//            $data['entities'] = Entity::whereIn('id', $entity_ids)->whereNotNull('is_default')->orderBy('entity_name')->pluck('entity_name as name', 'id');
//        }
        $data['entities'] = Entity::whereIn('id', $entity_ids)->whereNotNull('is_default')->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.waybill.index', $data);
    }

    public function show($id)
    {
        $order = Order::find($id);
        $orderDetails = uyumapi("SELECT amt, amt_vat, qty, shipping_date, unit_price, line_no, item_name 
       FROM psmt_order_d
       LEFT JOIN invd_item ON psmt_order_d.item_id = invd_item.item_id
       where order_m_id = " .$order->id ." ORDER BY line_no ASC"
        );

        $data['order'] = $order;
        $data['orderDetails'] = $orderDetails;

        return view('frontend.order.modal.show', $data);

    }

    public function stockCards()
    {
        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        $items = uyumapi('SELECT item_id, item_name, item_code from INVD_ITEM  order by item_id desc limit 1000');

        return view('frontend.order.stock_cards')->with(compact('items'));
    }





}
