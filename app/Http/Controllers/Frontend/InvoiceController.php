<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\LetterCredit;
use Yajra\DataTables\Facades\DataTables;
use App\Models\Entity;
use App\Models\Order;
use App\Models\Invoice;
use Illuminate\Support\Number;

use App\Models\User;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{

    /**
     * Retrieves the view for the index page of the frontend.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $user = auth()->user();
        if (str_contains($user->email, 'akdagtasyunu.com')) {
            $entity_ids = Entity::whereNotNull('is_default')->pluck('id')->toArray();
        } else {
            $entity_ids = $user->entities->pluck('id')->toArray();
        }
            if (request()->ajax()) {

                $waybill = Invoice::select('e.entity_name','invoices.id', 'invoices.doc_no','invoices.id', 'invoices.guid_id', 'invoices.amt', 'invoices.amt_vat', 'invoices.doc_date', 'co.co_desc', 'co.co_code', 'cur_code', 'e.entity_name')
                    ->whereIn('invoices.entity_id', $entity_ids)
                    ->leftJoin('entities as e', 'invoices.entity_id', '=', 'e.id')
                    ->leftJoin('currencies as cu', 'invoices.cur_tra_id', '=', 'cu.id')
                    ->leftJoin('companies as co', 'invoices.co_id', '=', 'co.id');
                if (request()->has('entity_id')) {
                    $entity_id = request()->get('entity_id');
                    if (!empty($entity_id)) {
                        $waybill->where('invoices.entity_id', $entity_id);
                    }
                }
                if (request()->has('status')) {
                    $status = request()->get('status');
                    $end = date('Y-m-d', time());
                    if ($status == 'active') {
                        $waybill->whereDate('doc_date', '>=', $end);
                    } else if ($status == 'passive') {
                        $waybill->whereDate('doc_date', '<', $end);
                    }
                }
                if (!empty(request()->start_date) && !empty(request()->end_date)) {
                    $start = date('Y-m-d', strtotime(request()->start_date));
                    $end = date('Y-m-d', strtotime(request()->end_date));
                    $waybill->whereDate('doc_date', '>=', $start)
                        ->whereDate('doc_date', '<=', $end);
                }
                return Datatables::of($waybill)
                    ->addColumn(
                        'action', function ($row) {
                        if (!empty($row->guid_id)) {
                        $action = '<ul class="nk-tb-actions gx-1">
                                                                                             <li>
                                                                        <div class="drodown">
                                                                            <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                                <ul class="link-list-opt no-bdr">
                                                                                     <li><a href="https://portal.uyumsoft.com.tr/Genel/Fatura/'.$row->guid_id.'" target="_blank"><em class="icon ni ni-file-pdf"></em><span>Faturayı Görüntüle</span></a></li>
                                                                                </ul>
                                                                            </div>
                                                                        </div>
                                                                    </li>
                                                                </ul>';
                        return $action;
                        }
                    } )
                    ->editColumn('doc_no', function ($row) {
                        if(empty($row->description))
                            return $row->doc_no;
                        else
                            return $row->doc_no . ' <i class="text-info ni ni-info-fill" data-bs-toggle="tooltip" title="'.$row->description.'"></i>';

                    })
                    ->editColumn('doc_date', function ($row) {
                        return date('d.m.Y',strtotime($row->doc_date));
                    })
                    ->editColumn('entity_name', function ($row) {
                        return limit_words($row->entity_name, 3);
                    })
                    ->editColumn('co_desc', function ($row) {
                        return '<button class="btn" data-bs-toggle="tooltip" data-bs-title="'.$row->co_desc.'">'.$row->co_code.'</button>';
                    })
                    ->editColumn('amt', function ($row) {
                        return Number::currency($row->amt, $row->cur_code, 'tr');
                    })
                    ->editColumn('amt_vat', function ($row) {
                        return Number::currency(($row->amt+$row->amt_vat), $row->cur_code, 'tr');
                    })

                    ->setRowAttr([
                        'data-href' => function ($row) {
                            return  action([\App\Http\Controllers\Frontend\InvoiceController::class, 'show'], [$row->id]);
                        }, ])
                    ->rawColumns(['action', 'doc_date', 'doc_no' ,'co_desc', 'id' ])
                    ->removeColumn('co_code', 'guid_id')
                    ->make(true);
            }


        $data['entities'] = Entity::whereIn('id' ,$entity_ids)->orderBy('entity_name')->pluck('entity_name as name', 'id');

        return view('frontend.invoice.index', $data);
    }

    public function show($id)
    {
        $order = Order::find($id);
        $orderDetails = uyumapi("SELECT amt, amt_vat, qty, shipping_date, unit_price, line_no, item_name 
       FROM psmt_order_d
       LEFT JOIN invd_item ON psmt_order_d.item_id = invd_item.item_id
       where order_m_id = " .$order->id ." ORDER BY line_no ASC"
        );

        $data['order'] = $order;
        $data['orderDetails'] = $orderDetails;

        return view('frontend.order.modal.show', $data);

    }


    public function stockCards()
    {
        $user_id = auth()->user();
        if (!$user_id) {
            return redirect()->route('login');
        }
        $user_id = $user_id->id;

        $items = uyumapi('SELECT item_id, item_name, item_code from INVD_ITEM  order by item_id desc limit 1000');

        return view('frontend.order.stock_cards')->with(compact('items'));
    }





}
