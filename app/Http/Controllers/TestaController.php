<?php

namespace App\Http\Controllers;

use App\Facades\AI;
use App\Models\ApiAuth;
use App\Models\BankBranch;
use App\Models\Category;
use App\Models\City;
use App\Models\Company;
use App\Models\ContractDiscount;
use App\Models\PaymentMethod;
use App\Models\PaymentTransaction;
use App\Models\Price;
use App\Models\PriceGroup;
use App\Models\Setting;
use App\Models\Shipment;
use App\Models\Temp;
use App\Models\Town;
use App\Models\Offer;
use App\Models\Contract;
use App\Models\Country;
use App\Models\Currency;
use App\Models\Entity;
use App\Models\Product;
use App\Models\State;
use App\Models\Unit;
use App\Models\UserProfile;
use App\Services\CreditCardDetector;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Invoice;
use App\Models\Waybill;
use App\Models\CurrencyExchangeRate;
use App\Models\Bank;
use App\Models\LetterCredit;
use App\Models\Dbs;
use DB;
use App\Models\PriceList;
use Mail;
use Carbon\Carbon;
use App\Models\Cart;
use App\Models\DailyCurRate;
use App\Models\User;
use GuzzleHttp\Client;
use Spatie\LaravelPdf\Facades\Pdf;
use Illuminate\Support\Number;
use App\Mail\NewPaymentMail;
use App\Services\BkmBinService;
use App\Models\BkmBin;
use Illuminate\Support\Facades\Schema;

class TestaController extends Controller
{

// Kullanım örneği:

    private $cardDetector;

    public function __construct(CreditCardDetector $cardDetector)
    {
        $this->cardDetector = $cardDetector;
    }

    public function index(Request $request)
    {

        $data['banks'] = config('laravel-pos.banks');
        return $data['banks'];

//        $api_auth = ApiAuth::find(1);
//
//        $decrypted = Crypt::decryptString($api_auth->password);
//        return $decrypted;

        $invoices = uyumapi('SELECT * FROM psmt_offer_m where offer_m_id = 11208 limit 10');

dd($invoices);
        $entity = uyumapi('SELECT count(*) FROM psmt_invoice_d  ');
        dd($entity);
        $product_id = 119045; $contract_id = 1288;
        $contract = Contract::find($contract_id);
        $price_list = $contract->priceList->first();
        $product_price = $price_list->prices->where('item_id', $product_id)->first();

            $unit_price_tra = $product_price->unit_price_tra;

//            $contract_discount = ContractDiscount::where('form_contract_m_id', $contract_id)->where('item_id', $product_id)->where('disc1_id','>',0)->first();
//            if ($contract_discount) {
//                $unit_price_tra = $unit_price_tra - ($unit_price_tra * $contract_discount->discount1->disc_rate / 100);
//            }

            dd($unit_price_tra);

     //   $price_lists = uyumapi('SELECT * from FINT_FORM_CONTRACT_DISCOUNT where form_contract_m_id = 1562 order by  form_contrat_plist_id desc limit 10');
        $price_lists = uyumapi('SELECT * from find_disc where disc_id = 294  limit 10');
return $price_lists;

        $client = new Client();
        $request_data = [
            'dil' => 'tr',
            'msgheader' => 'AKDAGYALITM',
            'usercode' => '8508404923',
            'password' => '92@BB6D',
            'gsmno' => '05325800089',
            'message' => 'Test  Mesajı'
        ];
        $response = $client->get('https://api.netgsm.com.tr/sms/send/get/?'.http_build_query($request_data));

        return $response->getBody()->getContents();




        $invoices = uyumapi('SELECT * FROM invd_item_unit where item_id =119280 limit 100');

        return $invoices;


       return  DB::select('SELECT e.id AS customer_id, e.entity_name, COUNT(o.id) AS total_orders
FROM entities e
JOIN orders o ON e.id = o.entity_id
WHERE o.doc_date >= CURDATE() - INTERVAL 1 MONTH
GROUP BY e.id
ORDER BY total_orders DESC
LIMIT 1');

        $response = AI::twoStageQuery('en çok sipariş veren müşteri, son bir ay');

        return $response;
        die();

        // Tüm tabloları listeleme
        $tables = DB::select('SHOW TABLES');

        foreach ($tables as $table) {
            $tableName = array_values((array)$table)[0];
            $columns = Schema::getColumnListing($tableName);

            echo "Tablo: $tableName\n";

            foreach ($columns as $column) {
                $type = DB::select("SHOW COLUMNS FROM {$tableName} WHERE Field = '{$column}'")[0]->Type;
                echo "  - $column: $type\n";
            }

            echo "\n";
        }
        die();

        $apiKey = env('OPENAI_API_KEY');

        dd($apiKey);

        $pos_installments = Setting::select('name','val')->where('name', 'like', '%taksit_sayisi_%')->get()->toArray();
        return $pos_installments;

        $invoice_m_ids = uyumapi('SELECT * FROM  psmt_invoice_m WHERE e_invoice_status = 99 limit 10');
return $invoice_m_ids;


        $invoice_m_ids = uyumapi('SELECT item_m_id FROM  invt_item_m WHERE purchase_sales = 2 and is_waybil = 1 and e_despatch_status = 98');

        Waybill::whereIn('id' , $invoice_m_ids)->update(['uuid' => null]);

        return $invoice_m_ids;

        $binData = $bkmBinService->queryBin();
        foreach ($binData as $bin) {


            foreach ($bin as $bank) {

                BkmBin::insert($bank);



            }
        }
        // Do something with $binData
    }
    public function indexs(Request $request)
    {


        $user = auth()->user();
        $entity = Entity::find($user->active_entity_id);
        $input = [
            'entity_name' => $entity->entity_name,
            'pos_name' => 'Akbank',
            'amount' => 100,
            'card_holder' => 'Hasan',
            'installment_count' => 1,
            'card_number' => '5269 **** **** 2095',
            'created_at' => now(),
        ];
        $emails = ['<EMAIL>'];
        $data = [
            'name' => "AkdagTasyunuPortal",
            'user' => $user,
            'input' => $input
        ];
        Mail::to($emails)->queue(new NewPaymentMail($data));


        die();
        $number = "1.234.567,89";
        echo formatNumber($number); // Çıktı: 1234567,89

die();

//        $temps = Temp::take(1500)->get();
//        foreach ($temps as $temp) {
//            $temp->amount = str_replace('.', '', $temp->amount);
//            $temp->amount = str_replace(',', '.', $temp->amount);
//            $temp->amount = number_format($temp->amount, 4, '.', '');
//
//            $input = [
//                'entity_id' => $temp->entity_id,
//                'pos_name' => $temp->banka,
//                'amount' => $temp->amount,
//                'status' => 'approved',
//                'response' => null,
//                'installment_count' => empty($temp->vade) ? 1 : $temp->vade,
//                'card_number' => $temp->kart,
//                'created_at' => date('Y-m-d H:i:s', strtotime($temp->tarih)),
//            ];
//
//
//            $paymentTransaction = PaymentTransaction::create($input);
//
//
//        }
//
//        die();


        $invoice_m_id = $request->input('invoice_m_id', 0);

        $invoice_m_ids = uyumapi('SELECT * FROM  invt_item_m WHERE purchase_sales = 2 and is_waybil = 1 and item_m_id = 630762 LIMIT 10');

        // $invoice_m_ids = uyumapi('SELECT * FROM invt_item_m WHERE doc_no = '.$doc.' LIMIT 10');

        return $invoice_m_ids;


        $invoice_m_ids = uyumapi('SELECT invoice_m_id, guid_id FROM psmt_invoice_m WHERE invoice_m_id > '.$invoice_m_id.'  ORDER BY invoice_m_id  LIMIT 3000');

        foreach ($invoice_m_ids as $invoice_m_id_item) {
            $invoice_m_id = $invoice_m_id_item['invoice_m_id'];

    Invoice::where('id', $invoice_m_id)->update(['guid_id' => $invoice_m_id_item['guid_id']]);


        }
        return '<a href="?invoice_m_id='.$invoice_m_id.'">Next</a>';

        $price_lists = PriceList::where('ispassive', 0)->where('id', 950)->orderByDesc('id')->get();
        foreach ($price_lists as $price_list) {
            $price_ids = uyumapi('SELECT price_list_d_id from invt_price_list_d  where price_list_m_id = '.$price_list->id);
            if (!empty($price_ids)) {
                return $price_ids;
            } else {
               return 'Price List D is empty';
            }
            $price = Price::whereNotIn('id', $price_ids)->where('price_list_m_id', $price_list->id)->delete();

        }



        $user = User::find(139);

        dd($user->getRoleNames()) ;


        $entities = uyumapi('
SELECT entity_id, entity_type, entity_name, entity_code
FROM find_entity
    ORDER BY entity_id desc
limit 1000
');
        return $entities;

        $number = '************** 2095';
        function ccMasking($number, $maskingCharacter = 'X') {
            $number = str_replace(' ', '', $number);
            return substr($number, 0, 4) . str_repeat($maskingCharacter, strlen($number) - 8) . substr($number, -4);
        }

        return ccMasking($number);


            $order_m_ids_list = uyumapi('SElECT order_m_id fROM psmt_order_m ');
            foreach ($order_m_ids_list as $order_m_id) {
                $order_m_ids[] = $order_m_id['order_m_id'];
            }

        $deleted_order_ids = Order::whereNotIn('id', $order_m_ids)->pluck('id');

            if($deleted_order_ids->count() > 0) {
                return $deleted_order_ids;

                Order::whereIn('id', $deleted_order_ids)->delete();
            }

     die();return uyumapi('
SELECT order_m_id, doc_no, doc_date, address1, amt, cur_tra_id, form_contract_m_id, entity_id, purchase_sales, doc_tra_id, co_id, sales_person_id, branch_id, payment_method_id, amt_vat, country_id, order_status, city_id, create_date as created_at, update_date as updated_at, town_id, shipping_date, note3
        FROM psmt_order_m
        WHERE purchase_sales = 2 AND co_id = 2725
 ORDER BY order_m_id DESC LIMIT 100
');

        return uyumapi('SElECT * fROM psmt_order_m order by order_m_id desc limit 10 ');
        $covering_price = Price::select('unit_price_tra', 'cur_tra_id')->where('item_id', 122739)->where('price_list_id', 951)->first();

        return $covering_price;

        return date('d.m.Y');
        $id = 1288;
        $contract_end_date = '2025-12-31T00:00:00';
      //  $contract_end_date = date('Y-m-d', strtotime($contract_end_date));


       // uyumapi('UPDATE FINT_FORM_CONTRACT_M SET CONTRACT_END_DATE = \'31.12.2024\' WHERE FORM_CONTRACT_M_ID = 1288');
       // uyumapi('UPDATE FINT_FORM_CONTRACT_TERM SET END_DATE = '31.12.2025' WHERE FORM_CONTRACT_M_ID = 1288');



        $client = new Client();
        $request_data = [
            'dil' => 'tr',
            'msgheader' => 'AKDAGYALITM',
            'usercode' => '8508404923',
            'password' => '92@BB6D',
            'gsmno' => '05325800089',
            'message' => 'Test  Mesajı'
        ];
        $response = $client->get('https://api.netgsm.com.tr/sms/send/get/?'.http_build_query($request_data));
       return $response->getBody()->getContents();

        $users = User::where('id', '>', 58)->get();

        foreach ($users as $temp) {

                $input = ['user_id' => $temp->id,'name' => $temp->name,  'last_name' => $temp->last_name, 'first_name' => $temp->first_name, 'email_verified_at' => now(), 'avatar' => 'img/default-avatar.jpgg'];

                $User = UserProfile::create($input);


        }
        die();
        config('app.languages');

        return uyumapi('SElect * fROM find_co_entity limit 100 ');
        return uyumapi('SElect co_entity_id fROM FIND_CO_ENTITY_B2B  where branch_id = 6774 and is_default = 1');
        $cardNumber = $request->input('card_number', '520019520019520019');
        $cardInfo = $this->cardDetector->detectCardType($cardNumber);
        $bb = 'taksit_sayisi_'. $cardInfo['bank'] . '_' . $cardInfo['card_type'];
        $taksit_sayisi = Setting::where('name', $bb)->value('val');
         return $taksit_sayisi;


        // Banka tespiti yap
        $bank = $this->cardDetector->detectBank($cardNumber);

        return response()->json([
            'bank' => $bank,
            'is_valid' => true
        ]);

        return detectCardType('****************');
        return uyumapi('SElect * fROM psmt_order_d  where order_m_id = 24919 and qty_shipping <> qty and qty_shipping>0 limit 10 ', true);


//        $emails = '<EMAIL>';
//        $data = array('name' => "Akdağ Taşyünü");
//        Mail::send(['html' => 'emails.test'], $data, function ($message) use ($emails) {
//            $message->to($emails)->subject
//            ('YeniSiparis: PF4546');
//            $message->from('<EMAIL>', 'Akdağ Taşyünü');
//        });
//
//        die();

        $contracts = Contract::select('entity_id')->distinct()->groupBy('entity_id')->where('ispassive', 0)->get();
        return $contracts;
        foreach ($contracts as $contract) {
        $amounts = uyumapi('SELECT 
     sm.form_contract_m_id AS "contract_id",
    COALESCE(
        (SELECT SUM(    
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                ELSE (a.amt_with_disc + a.amt_vat) 
            END
        ) 
        FROM invt_item_d a 
        LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) - 
    COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                ELSE 0 
            END
        ) 
        FROM psmt_invoice_d a 
        LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) AS "shipping_amount",
    COALESCE(
        (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
        FROM psmt_order_d a 
        LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
        WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) AS "order_amount",
    (sm.amt + 
    COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) 
                ELSE 0 
            END
        ) 
        FROM psmt_invoice_d a 
        LEFT JOIN psmt_invoice_m b ON b.invoice_m_id = a.invoice_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    )) - 
    (COALESCE(
        (SELECT SUM(
            CASE 
                WHEN b.purchase_sales IN (1, 3) THEN (a.amt_with_disc + a.amt_vat) * -1 
                ELSE (a.amt_with_disc + a.amt_vat) 
            END
        ) 
        FROM invt_item_d a 
        LEFT JOIN invt_item_m b ON b.item_m_id = a.item_m_id 
        WHERE a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    ) + 
    COALESCE(
        (SELECT ROUND(SUM(((a.amt_with_disc + a.amt_vat) / a.qty) * (a.qty - a.qty_shipping)), 2)
        FROM psmt_order_d a 
        LEFT JOIN psmt_order_m b ON b.order_m_id = a.order_m_id 
        WHERE a.order_status = 1 AND a.purchase_sales = 2 AND a.form_contract_m_id = sm.form_contract_m_id
        ), 0
    )) AS "remaining_amount"
FROM fint_form_contract_m sm 
LEFT JOIN find_entity ck ON ck.entity_id = sm.entity_id
WHERE  ck.entity_id = '.$contract->entity_id.'
  AND sm.ispassive = 0');
        foreach ($amounts as $amount) {
            $contract = Contract::find($amount['contract_id']);
            $contract->update([
                'shipping_amount' => $amount['shipping_amount'],
                'order_amount' => $amount['order_amount'],
                'remaining_amount' => $amount['remaining_amount'],
            ]);
        }
        }
die();
        return uyumapi('SElect source_app, source_app2, amt_tra, cur_tra_id fROM fint_form_contract_d where source_app = 1000 and form_contract_m_id = 1105  order by source_app ');
        return uyumapi('SElect * fROM fint_form_contract_d where form_contract_m_id = 1226 order by plus_minus asc limit 100 ');
        return uyumapi('SElect * fROM fint_form_contract_m where entity_id = 1050373 order by form_contract_m_id desc limit 100 ');
        return uyumapi('SElect * fROM invd_price_rule_m order by price_rule_m_id desc limit 10 ');

        return uyumapi('SElect * fROM invt_price_list_d where price_list_m_id = 943  limit 10 ');
        $latest_price_list_id = PriceList::where('ispassive', 0)->where('co_id', 2725)->where('is_default', 1)->orderByDesc('id')->value('id');

        $product_ids = Price::where('price_list_m_id', $latest_price_list_id)->pluck('item_id');


        return uyumapi('SElect * fROM psmt_order_d where order_m_id = 24952 limit 10 ');

        $i = 1;
        $cart = Cart::find(178);

        foreach ($cart->cart_items as $item) {
           echo  $i++ * 10 . '<br>';

        }
die();

        return uyumapi('SElect * fROM psmt_order_m where order_m_id = 24952 limit 10 ');

//        $cart = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->where('contract_id', 0)->whereNull('ordered_at')->first();

        // return $cart;

//        return uyumapi('SElect * fROM gnld_branch_item_doc_tra limit 100');
        return uyumapi('SElect * fROM psmt_order_m where payment_method_id = 80 order by order_m_id desc limit 100 ');
//        return uyumapi('SElect * fROM psmt_order_d order by order_m_id desc limit 10 ');
//return uyumapi('SELECT * from invd_item_unit limit 20', false);
        $cart = Cart::where('entity_id', $user->active_entity_id)->where('user_id', $user->id)->where('contract_id', 0)->whereNull('ordered_at')->first();



        $cart->update(['ordered_at' => now() ]); // uyuma aktarıldıktan sonra sipariş tarihi güncellenir
        }



}
