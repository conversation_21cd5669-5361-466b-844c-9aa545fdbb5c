<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Carbon\Carbon;

class SetLocale
{
    public function handle(Request $request, Closure $next)
    {
        $language = session()->get('locale', config('app.locale'));

        App::setLocale($language);
        setlocale(LC_TIME, $language);
        Carbon::setLocale($language);

        return $next($request);
    }
}