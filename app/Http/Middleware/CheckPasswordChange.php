<?php

namespace App\Http\Middleware;

use App\Models\Setting;
use Closure;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class CheckPasswordChange
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $user = Auth::user();
        if ($user) {
            $passwordChangeRequired = false;

            if ($user->password_changed_at == null) {
                $passwordChangeRequired = true;
                $message = 'İlk oturumunuzda geçici şifrenizi değiştirmeniz gerekmektedir. Şifrenizi değiştirmeden devam edemezsiniz.';

            } else {
                $lastPasswordChange = Carbon::parse($user->password_changed_at);
                if($user->hasRole('bayi')) {
                    $password_change_frequency = Setting::where('name', 'password_change_frequency')->value('val');
                    if ($lastPasswordChange->diffInMonths(Carbon::now()) >= $password_change_frequency) {
                        $passwordChangeRequired = true;
                        $message = 'Şifrenizin '.$password_change_frequency.' ayda bir değişmesi gerekmekte. Şifrenizi değiştirmeden devam edemezsiniz.';
                    }
                }

            }

            if ($passwordChangeRequired) {
                return redirect('/profil/sifre-degistir/'.encode_id($user->id))->with('error', $message);
            }
        }

        return $next($request);
    }
}