<?php

/*
 * Global helpers file with misc functions.
 */


use App\Models\ApiAuth;
use App\Models\Order;
use Illuminate\Support\Number;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Artisan;
use App\Models\Contract;


if (!function_exists('app_name')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function app_name()
    {
        return config('app.name');
    }
}

/*
 * Global helpers file with misc functions.
 */
if (!function_exists('user_registration')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function user_registration()
    {
        $user_registration = false;

        if (env('USER_REGISTRATION') === 'true') {
            $user_registration = true;
        }

        return $user_registration;
    }
}

/*
 *
 * label_case
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('label_case')) {
    /**
     * Prepare the Column Name for Lables.
     */
    function label_case($text)
    {
        $order = ['_', '-'];
        $replace = ' ';

        $new_text = trim(\Illuminate\Support\Str::title(str_replace('"', '', $text)));
        $new_text = trim(\Illuminate\Support\Str::title(str_replace($order, $replace, $text)));

        return preg_replace('!\s+!', ' ', $new_text);
    }
}

/*
 *
 * show_column_value
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('show_column_value')) {
    /**
     * Generates the function comment for the given function.
     *
     * @param string $valueObject Model Object
     * @param string $column Column Name
     * @param string $return_format Return Type
     * @param mixed $valueObject The value object.
     * @param mixed $column The column.
     * @param string $return_format The return format. Default is empty string.
     * @return string Raw/Formatted Column Value
     * @return mixed The column value or formatted value.
     */
    function show_column_value($valueObject, $column, $return_format = '')
    {
        $column_name = $column->Field;
        $column_type = $column->Type;

        $value = $valueObject->$column_name;

        if (!$value) {
            return $value;
        }

        if ($return_format === 'raw') {
            return $value;
        }

        if (($column_type === 'date') && $value !== '') {
            $datetime = \Carbon\Carbon::parse($value);

            return $datetime->isoFormat('LL');
        }
        if (($column_type === 'datetime' || $column_type === 'timestamp') && $value !== '') {
            $datetime = \Carbon\Carbon::parse($value);

            return $datetime->isoFormat('LLLL');
        }
        if ($column_type === 'json') {
            $return_text = json_encode($value);
        } elseif ($column_type !== 'json' && \Illuminate\Support\Str::endsWith(strtolower($value), ['png', 'jpg', 'jpeg', 'gif', 'svg'])) {
            $img_path = asset($value);

            $return_text = '<figure class="figure">
                                <a href="' . $img_path . '" data-lightbox="image-set" data-title="Path: ' . $value . '">
                                    <img src="' . $img_path . '" style="max-width:200px;" class="figure-img img-fluid rounded img-thumbnail" alt="">
                                </a>
                                <figcaption class="figure-caption">Yol: ' . $value . '</figcaption>
                            </figure>';
        } else {
            $return_text = $value;
        }

        return $return_text;
    }
}

/*
 *
 * fielf_required
 * Show a * if field is required
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('fielf_required')) {
    /**
     * Prepare the Column Name for Lables.
     */
    function fielf_required($required)
    {
        $return_text = '';

        if ($required !== '') {
            $return_text = '<span class="text-danger">*</span>';
        }

        return $return_text;
    }
}

/**
 * Get or Set the Settings Values.
 */
if (!function_exists('setting')) {
    function setting($key, $default = null)
    {
        if (is_null($key)) {
            return new App\Models\Setting();
        }

        if (is_array($key)) {
            return App\Models\Setting::set($key[0], $key[1]);
        }

        $value = App\Models\Setting::get($key);

        return is_null($value) ? value($default) : $value;
    }
}

/*
 * Show Human readable file size
 */
if (!function_exists('humanFilesize')) {
    function humanFilesize($size, $precision = 2)
    {
        $units = ['B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        $step = 1024;
        $i = 0;

        while ($size / $step > 0.9) {
            $size /= $step;
            $i++;
        }

        return round($size, $precision) . $units[$i];
    }
}

/*
 *
 * Encode Id to a Hashids\Hashids
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('encode_id')) {
    /**
     * Prepare the Column Name for Lables.
     */
    function encode_id($id)
    {
        $hashids = new Hashids\Hashids(config('app.salt'), 8, 'abcdefghijklmnopqrstuvwxyz1234567890');

        return $hashids->encode($id);
    }
}

/*
 *
 * Decode Id to a Hashids\Hashids
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('decode_id')) {
    /**
     * Prepare the Column Name for Lables.
     */
    function decode_id($hashid)
    {
        if (is_numeric($hashid)) {
            return false;
        }
        $hashids = new Hashids\Hashids(config('app.salt'), 8, 'abcdefghijklmnopqrstuvwxyz1234567890');
        $id = $hashids->decode($hashid);

        if (count($id)) {
            return $id[0];
        }
        abort(404);
    }
}

/*
 *
 * Prepare a Slug for a given string
 * Laravel default str_slug does not work for Unicode
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('slug_format')) {
    /**
     * Format a string to Slug.
     */
    function slug_format($string)
    {
        $base_string = $string;

        $string = preg_replace('/\s+/u', '-', trim($string));
        $string = str_replace('/', '-', $string);
        $string = str_replace('\\', '-', $string);
        $string = strtolower($string);

        return substr($string, 0, 190);
    }
}

/*
 *
 * icon
 * A short and easy way to show icon fornts
 * Default value will be check icon from FontAwesome (https://fontawesome.com)
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('icon')) {
    /**
     * Format a string to Slug.
     */
    function icon($string = 'fa-regular fa-circle-check')
    {
        return "<i class='" . $string . "'></i>&nbsp;";
    }
}

/*
 *
 * logUserAccess
 * Get current user's `name` and `id` and
 * log as debug data. Additional text can be added too.
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('logUserAccess')) {
    /**
     * Format a string to Slug.
     */
    function logUserAccess($text = '')
    {
        $auth_text = '';

        if (\Auth::check()) {
            $auth_text = 'Kullanıcı:' . \Auth::user()->name . ' (No:' . \Auth::user()->id . ')';
        }

        \Log::debug(label_case($text) . " | {$auth_text}");
    }
}


/*
 *
 * bn2enNumber
 * Convert a English number to Turkish
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('en2trDate')) {
    /**
     * Convert a English number to Turkish.
     */
    function en2trDate($date)
    {
        // Convert numbers
        $search_array = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'];
        $replace_array = ['১', '২', '৩', '৪', '৫', '৬', '৭', '৮', '৯', '০'];
        $bn_date = str_replace($search_array, $replace_array, $date);

        // Convert Short Week Day Names
        $search_array = ['Fri', 'Sat', 'Sun', 'Mon', 'Tue', 'Wed', 'Thu'];
        $replace_array = ['Cum', 'Cmt', 'Paz', 'Pzt', 'Sal', 'Çar', 'Per'];
        $bn_date = str_replace($search_array, $replace_array, $bn_date);

        // Convert Month Names
        $search_array = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
        $replace_array = ['Ocak', 'Şubat', 'Mart', 'Nisan', 'Mayıs', 'Haziran', 'Temmuz', 'Ağustos', 'Eylül', 'Ekim', 'Kasım', 'Aralık'];
        $bn_date = str_replace($search_array, $replace_array, $bn_date);

        // Convert Short Month Names
        $search_array = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $replace_array = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz', 'Eyl', 'Ağu', 'Eyl', 'Eki', 'Kas', 'Ara'];
        $bn_date = str_replace($search_array, $replace_array, $bn_date);

        // Convert AM-PM
        $search_array = ['am', 'pm', 'AM', 'PM'];
        $replace_array = ['sb', 'og', 'SB', 'OG'];

        return str_replace($search_array, $replace_array, $bn_date);
    }
}


/*
 *
 * Decode Id to a Hashids\Hashids
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('generate_rgb_code')) {
    /**
     * Prepare the Column Name for Lables.
     */
    function generate_rgb_code($opacity = '0.9')
    {
        $str = '';
        for ($i = 1; $i <= 3; $i++) {
            $num = mt_rand(0, 255);
            $str .= "{$num},";
        }
        $str .= "{$opacity},";

        return substr($str, 0, -1);
    }
}

/*
 *
 * Return Date with weekday
 *
 * ------------------------------------------------------------------------
 */
if (!function_exists('date_today')) {
    /**
     * Return Date with weekday.
     *
     * Carbon Locale will be considered here
     * Example:
     * শুক্রবার, ২৪ জুলাই ২০২০
     * Friday, July 24, 2020
     */
    function date_today()
    {
        return \Carbon\Carbon::now()->isoFormat('dddd, LL');
    }
}

if (!function_exists('language_direction')) {
    /**
     * return direction of languages.
     *
     * @return string
     */
    function language_direction($language = null)
    {
        if (empty($language)) {
            $language = app()->getLocale();
        }
        $language = strtolower(substr($language, 0, 2));
        $rtlLanguages = [
            'ar', //  'العربية', Arabic
            'arc', //  'ܐܪܡܝܐ', Aramaic
            'bcc', //  'بلوچی مکرانی', Southern Balochi
            'bqi', //  'بختياري', Bakthiari
            'ckb', //  'Soranî / کوردی', Sorani Kurdish
            'dv', //  'ދިވެހިބަސް', Dhivehi
            'fa', //  'فارسی', Persian
            'glk', //  'گیلکی', Gilaki
            'he', //  'עברית', Hebrew
            'lrc', //- 'لوری', Northern Luri
            'mzn', //  'مازِرونی', Mazanderani
            'pnb', //  'پنجابی', Western Punjabi
            'ps', //  'پښتو', Pashto
            'sd', //  'سنڌي', Sindhi
            'ug', //  'Uyghurche / ئۇيغۇرچە', Uyghur
            'ur', //  'اردو', Urdu
            'yi', //  'ייִדיש', Yiddish
        ];
        if (in_array($language, $rtlLanguages)) {
            return 'rtl';
        }

        return 'ltr';
    }
}

/*
 * Application Demo Mode check
 */
if (!function_exists('clean_note_large')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function clean_note_large($text)
    {
        $text = str_replace('/', '', $text);
        $text = trim(preg_replace('/\s\s+/', ' ', $text));
        $text = mb_convert_case($text, MB_CASE_TITLE, "UTF-8");

        return $text;
    }
}

/*
 * Application Demo Mode check
 */
if (!function_exists('limit_words')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function limit_words($text, $limit)
    {
        $words = explode(" ", $text);
        return implode(" ", array_splice($words, 0, $limit));
    }
}

/*
 * Application Demo Mode check
 */
if (!function_exists('words_to_lower')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function words_to_lower($text)
    {
        $str = mb_convert_case($text, MB_CASE_TITLE, "UTF-8");
        $str = str_replace('aş.', 'AŞ.', $str);
        $str = str_replace('ltd.', 'Ltd.', $str);
        return $str;
    }
}

if (!function_exists('get_string_between')) {
    function get_string_between($string, $start, $end)
    {
        $string = ' ' . $string;
        $ini = strpos($string, $start);
        if ($ini == 0) return '';
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;
        return substr($string, $ini, $len);
    }
}


if (!function_exists('format_money')) {
    function format_money($input, $symbol = null)
    {
        return number_format($input, 2, ',', '.') . ' ' . $symbol;
    }
}

if (!function_exists('clean_money')) {
    function clean_money($input)
    {
        $input = preg_replace("/[^0-9,]/", "", $input);
        $input = str_replace(',', '.', $input);

        return $input;
    }
}


if (!function_exists('replace_turkish_characters')) {
    function replace_turkish_characters($input)
    {

        $input = str_replace('ı', 'i', $input);
        $input = str_replace('ş', 's', $input);
        $input = str_replace('ç', 'c', $input);
        $input = str_replace('ö', 'o', $input);
        $input = str_replace('ğ', 'g', $input);
        $input = str_replace('ü', 'u', $input);
        $input = str_replace('İ', 'i', $input);
        $input = str_replace('Ğ', 'g', $input);
        $input = str_replace('Ü', 'u', $input);
        $input = str_replace('Ö', 'o', $input);
        $input = str_replace('Ç', 'c', $input);
        $input = str_replace('Ş', 's', $input);

        return $input;
    }
}

if (!function_exists('current_month_name')) {
    function current_month_name($input)
    {
        if ($input == 'short') {

            switch (date('n')) {
                case 1:
                    return 'Oca';
                case 2:
                    return 'Şub';
                case 3:
                    return 'Mar';
                case 4:
                    return 'Nis';
                case 5:
                    return 'May';
                case 6:
                    return 'Haz';
                case 7:
                    return 'Tem';
                case 8:
                    return 'Ağu';
                case 9:
                    return 'Eyl';
                case 10:
                    return 'Eki';
                case 11:
                    return 'Kas';
                case 12:
                    return 'Ara';
            }

        } else {
            return date('F');
        }
    }
}

/*
 * Application Demo Mode check
 */

if (!function_exists('post_order_to_uyum')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function post_order_to_uyum($cart, $dd = false)
    {
        $user = Auth()->user();

        // $cart->id'nin uzunluğunu hesapla
        $idLength = strlen((string)$cart->id);
// İhtiyaç duyulan sıfır sayısını hesapla (15 - sabit karakterler - id uzunluğu)
// Sabit karakterler: "B2B-" (4) + Yıl (4) = 8 karakter
        $zeroCount = max(0, 15 - 8 - $idLength);
        $zeros = str_repeat('0', $zeroCount);
// Yeni order numarası oluştur
        $orderNo = 'B2B-' . date('Y') . $zeros . $cart->id;

        $priceListCode = '';
        $curRateTypeId = 0;

        $incotermsId = $cart->incoterms_id;
        $i = 1;
        if ($cart->cur_tra_id == 115) {
            $curRateTypeId = 235;
        } else if ($cart->cur_tra_id == 116) {
            $curRateTypeId = 277;
        }

//        30 KKTEKÇEKİM
//31 EFTHAVALE
//71 ÇEK30
//100 KKTAKSİTLİ
        $zz_ton_price = $cart->zz_ton_price;
        $paymentMethodId = 31; // todo
        if ($cart->contract_id) {
            $paymentMethodId = 76;
            $zz_ton_price = $cart->contract->zz_ton_price;
        }
        $value = [
            "entityId" => $cart->entity_id, // 1047759 akdag granit
            "entityCode" => $cart->entity->entity_code,
            "isApproveByMaster" => true,
            "formContractMId" => $cart->contract_id,
            "orderStatus" => "Açık",
            "entityRequestDate" => "0001-01-01T00:00:00",
            "docTraId" => 2903,
            "docTraCode" => "",
            "amtVatTra" => $cart->amt_vat_tra,
            "amtVat" => $cart->amt_vat,
            "amtTra" => $cart->amt_tra,
            "amt" => $cart->amt,
            "amtReceiptTra" => $cart->amt_receipt_tra,
            "amtReceipt" => $cart->amt_receipt,
            "amtRoundTra" => 0,
            "amtRound" => 0,
            "curTra" => $cart->cur_tra_id,
            "curCode" => $cart->currency->cur_code,
            "curId" => 0,
            "curRateTypeId" => $curRateTypeId,
            "curRateTypeCode" => "",
            "dueDate" => "",
            "dueDay" => 0,
            "shippingDate" => "",
            "deliveryDate" => "",
            "salesPersonCode" => "",
            "salesPersonId" => $cart->sales_person_id,
            "paymentPlanMId" => 0,
            "paymentPlanCode" => "",
            "transportTypeCode" => "",
            "transportTypeId" => 57, // KARAYOLU
            "transporterCode" => "",
            "transporterId" => 0,
            "incotermsId" => $incotermsId,
            "incotermsName" => "",
            "paymentMethodId" => $paymentMethodId,
            "paymentMethodCode" => "",
            "gnlNote1" => "",
            "gnlNote2" => "",
            "gnlNote3" => "",
            "gnlNote4" => "",
            "gnlNote5" => "",
            "gnlNote6" => "",
            "gnlNote7" => "",
            "gnlNote8" => "",
            "gnlNote9" => "",
            "gnlNote10" => "",
            "address1" => "",
            "address2" => "",
            "address3" => "",
            "cityId" => "",
            "cityName" => "",
            "townName" => "",
            "townId" => "",
            "countyId" => "",
//            "shippingAddress1" => $cart->shipping_address->address1,
//            "shippingAddress2" => $cart->shipping_address->address2,
//            "shippingAddress3" => $cart->shipping_address->address3,
//            "shippingCityId" => $cart->shipping_address->city_id,
//            "shippingTownId" => $cart->shipping_address->town_id,
//            "shippingCountyId" => $cart->shipping_address->country_id,
            "shippingCityId" => $cart->city_id,
            "shippingTownId" => $cart->town_id,
            "shippingCountyId" => $cart->country_id,
            "shippingEntityCode" => "",
            "shippingEntityId" => 0,
            "addressTypeCode" => "",
            // "isShippingAddressType" => true,
            "webAddress" => "",
            "firstName" => "",
            "familyName" => "",
            "email" => "",
            //"isDocDifferentCur" => true,
            "zipCode" => "",
           // "shippingZipCode" => $cart->shipping_address->zip_code,
            "vehicleCode" => "",
            "vehicleId" => 0,
            "driverIdentifyNo" => "",
            "driverGsmNo" => "",
            "shippingDesc1" => "",
            "transportEquipment" => "",
            "driverFamilyName" => "",
            "licencePlate" => "",
            //"isTCMB" => true,
            //"isLocalCurAction" => true,
            "shippingFirstname" => "",
            "shippingFamilyname" => "",
            "shippingTel1" => "",
            "shippingTel2" => "",
            "coId" => $cart->co_id,
            "coCode" => $cart->company->co_code,
            "branchId" => $cart->branch_id,
            "branchCode" => "",
            "docDate" => date('Y-m-d'),
            "docNo" => $orderNo,
            "docNumberDId" => 0,
            "catCode1Id" => 0,
            "catCode1" => "",
            "catCode2Id" => 0,
            "catCode2" => "",
            "sourceApp" => "SatışSiparişi",
            "sourceApp2" => "",
            "sourceApp3" => "",
            "sourceMId" => 0,
            "sourceDId" => 0,
            "controller" => [
                "register1" => "",
                "register2" => "",
                "register3" => "",
                "register4" => "",
                "generalNote1" => "",
                "generalNote2" => "",
                "generalNote3" => "",
                "generalNote4" => "",
                "generalNote5" => "",
                "generalNote6" => "",
                "generalNote7" => "",
                "generalNote8" => "",
                "generalNote9" => "",
                "generalNote10" => "",
                "noteLarge" => ""
            ],
            "note1" => "",
            "note2" => "",
            "note3" => $cart->notes,
            "createUserId" => 0,
            "currencyOption" => 1,
            "entityOrderNo" => $orderNo,
            "entityOrderDate" => $cart->created_at,
            "disc0Id" => 0,
            "discCode0" => "",
            "disc0Rate" => 0,
            "amtDisc0Tra" => 0,
            "discCalcType0" => "Oran",
            "dynamicFields" => null,
            //"isWarning" => true,
            "promptValues" => ""
        ];
        foreach ($cart->cart_items as $cart_item) {
            $value["details"][] =
                [
                    "colorId" => 0,
                    "colorCode" => "",
                    "packageTypeCode" => "",
                    "packageTypeId" => 0,
                    "itemId" => $cart_item->product_id,
                    "expenseId" => 0,
                    "deliveryDate" => "",
                    "shippingDate" => "",
                    "entityRequestDate" => "",
                    "entityItemId" => 0,
                    "entityItemCode" => "",
                    "lineType" => "S",
                    "dcardId" => $cart_item->product_id,
                    "dcardCode" => "",
                    "unitId" => $cart_item->product->unit_id,
                    "unitCode" => $cart_item->product->unit->unit_code,
                    "qty" => $cart_item->qty,
                    "qtyPrm" => 0,
                    "qtyFreePrm" => 0,
                    "qtyFreeSec" => 0,
                    "dueDay" => 0,
                    "campaignId" => 0,
                    "unitPriceTra" => $cart_item->unit_price_tra,
                    "unitPrice" => $cart_item->unit_price,
                    "vatStatus" => "Hariç",
                    "vatId" => $cart_item->vat_id,
                    "vatCode" => "",
                    "vatRate" => $cart_item->vat_rate,
                    "amtVat" => $cart_item->amt_vat,
                    "abtActCode" => "",
                    "abtActId" => 0,
                    "abtBudgetId" => 0,
                    "abtBudgetCode" => "",
                    "otvCode" => "",
                    "oivCode" => "",
                    "vatDiscCode" => "",
                    "priceListCode" => $priceListCode,
                    "priceListId" => 0,
                    "priceListDId" => 0,
                    "whouseId" => $cart_item->whouse_id,
                    "whouseCode" => "",
                    "abtBudgetD2Id" => 0,
                    "disc1Id" => 0,
                    "disc1Code" => "",
                    "disc1Rate" => 0,
                    "amtDisc1" => 0,
                    "amtDisc1Tra" => 0,
                    "disc2Id" => 0,
                    "disc2Code" => "",
                    "disc2Rate" => 0,
                    "amtDisc2" => 0,
                    "amtDisc2Tra" => 0,
                    "disc3Id" => 0,
                    "disc3Code" => "",
                    "disc3Rate" => 0,
                    "amtDisc3" => 0,
                    "amtDisc3Tra" => 0,
                    "amtDisc" => 0,
                    "amtWithDisc" => 0,
                    "amt" => $cart_item->amt,
                    "amtTra" => $cart_item->amt_tra,
                    "formContractMId" => $cart_item->contract_id,
                    "formContractCode" => "",
                    "itemAttribute1Id" => 0,
                    "itemAttributeCode1" => "",
                    "itemAttribute2Id" => 0,
                    "itemAttributeCode2" => "",
                    "itemAttribute3Id" => 0,
                    "itemAttributeCode3" => "",
                    "itemGnlAttribute1Id" => 0,
                    "itemGnlAttributeCode1" => "",
                    "itemGnlAttribute2Id" => 0,
                    "itemGnlAttributeCode2" => "",
                    "itemGnlAttribute3Id" => 0,
                    "itemGnlAttributeCode3" => "",
                    "discCalcType1" => 0,
                    "discCalcType2" => 0,
                    "discCalcType3" => 0,
                    "referanceDocNo" => "",
                    "lotId" => 0,
                    "lotCode" => "",
                    "qualityId" => 0,
                    "qualityCode" => "",
                    "barcode" => "",
                    "itemNameManual" => "",
                    "salesPersonId" => $cart_item->sales_person_id,
                    "isItemAttribute" => true,
                    "registerId" => 0,
                    "registerFullName" => "",
                    "promptValues" => "",
                    "lineNo" => $i++ * 10,
                    "curTraId" => $cart_item->cur_tra_id,
                    "curCode" => $cart_item->currency->cur_code,
                    "curRateTypeId" => $curRateTypeId,
                    "curRateTypeCode" => "",
                    "curRateTra" => $cart_item->cur_rate_tra,
                    "note1" => "",
                    "note2" => "",
                    "note3" => "",
                    "noteLarge" => "",
                    "catCode1Id" => 0,
                    "catCode1" => "",
                    "catCode2Id" => 0,
                    "catCode2" => "",
                    "sourceMId" => 0,
                    "sourceDId" => 0,
                    "sourceD3Id" => 0,
                    "sourceOrderMId" => 0,
                    "sourceOrderDId" => 0,
                    "costCenterId" => 0,
                    "costCenterCode" => "",
                    "projectMId" => 0,
                    "projectCode" => "",
                    "gainLossTypeId" => 0,
                    "gainLossTypeCode" => "",
                    "analysisCode" => "",
                    "analysisId" => 0,
                    "taxTemplateName" => "",
                    "taxTemplateMId" => 0,
                    "plusMinus" => "Borç",
                    "contactName" => "",
                    "contactId" => 0,
                    "sourceApp" => "SatışSiparişi",
                    "sourceApp2" => "",
                    "sourceApp3" => "",
                    "dynamicFields" => [ 'zzCalcTonPrice' => (float)$zz_ton_price]
                ];
        }

        if ($dd) {
            return $value;
        }

//        if ($user->id == 1) {
//            return $value;
//        }

        try {
            $result = uyumapi($value, false, 'UyumApi/v1/PSM/InsertOrderM');

            if ($result['statusCode'] == 200) {
                $order_m_id = $result['result'];
                $cart->update(['ordered_at' => now(), 'order_m_id' => $order_m_id]); // uyuma aktarıldıktan sonra sipariş tarihi güncellenir
            } else {
                if (isset($result['responseException']['exceptionMessage'])) {
                    return ['success' => false, 'message' => $result['responseException']['exceptionMessage']];
                }
            }
            // sözleşme kalan tutarı portalda güncelliyoruz
            if ($cart->contract_id > 0) {
                $contract = Contract::find($cart->contract_id);
                if ($contract) {
                    $remaining_amount = $contract->remaining_amount - $cart->amt_receipt_tra;
                    $remaining_amount = number_format($remaining_amount, 2);
                    $remaining_amount = str_replace(',', '', $remaining_amount);
                    if ($remaining_amount >= 0) {
                        $contract->update(['remaining_amount' => $remaining_amount]);
                    }
                }
            }

            $orders = uyumapi('
SELECT order_m_id, doc_no, doc_date, address1, amt, cur_tra_id, form_contract_m_id, entity_id, purchase_sales, doc_tra_id, co_id, sales_person_id, branch_id, payment_method_id, amt_vat, country_id, order_status, city_id, create_date as created_at, update_date as updated_at, town_id, shipping_date, note3
        FROM psmt_order_m
        WHERE purchase_sales = 2 AND co_id = 2725
 ORDER BY order_m_id DESC LIMIT 3
');

            foreach ($orders as $order) {
                $id = $order['order_m_id'];
                unset($order['order_m_id']);
                $order['shipping_date'] = $order['shipping_date'] == '0001-01-01T00:00:00' ? null : $order['shipping_date'];
                $order['created_at'] = '0001-01-01T00:00:00' ? null : $order['create_date'];
                $order['updated_at'] = '0001-01-01T00:00:00' ? null : $order['updated_at'];
                $order['form_contract_m_id'] = $order['form_contract_m_id'] == 0 ? null : $order['form_contract_m_id'];
                Order::updateOrCreate(['id' => $id], $order);
            }

            dispatch(function () {
                Artisan::call('get:contract-amounts');
            })->onQueue('background');

            $emails = $user->email;
            if ($emails) {
                $messageContent = $user->active_entity->entity_name . ' cari firması için ' . date('d.m.Y') . ' tarihinde, ' . number_format($cart->amt, 2) . ' TL tutarlı siparişiniz oluşturuldu.';

                $data = array('name' => "Akdag", 'messageContent' => $messageContent);
                dispatch(function () use ($emails, $data) {
                    Mail::send(['html' => 'emails.mail'], $data, function ($message) use ($emails) {
                        $message->to($emails)->subject('Yeni Sipariş Oluşturuldu: ');
                        $message->from('<EMAIL>', env('APP_NAME', 'B2B Portal'));
                    });
                })->onQueue('emails');
            }

            return ['success' => true, 'message' => 'İşlem başarılı'];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }

    }

}
if (!function_exists('ccMasking')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function ccMasking($number, $maskingCharacter = 'X')
    {
        $number = str_replace(' ', '', $number);
        return substr($number, 0, 4) . str_repeat($maskingCharacter, strlen($number) - 8) . substr($number, -4);
    }
}

if (!function_exists('uyumapi')) {
    /**
     * Helper to grab the application name.
     *
     * @return mixed
     */
    function uyumapi($value, $dd = false, $action = "UyumApi/v1/GNL/GetDataTableWithSQL")
    {

        $api_auth = ApiAuth::find(1);
        $headers = array('Accept' => 'application/json', 'Authorization' => 'Bearer ' . $api_auth->access_token, 'uyumSecretKey' => $api_auth->uyumSecretKey);
        $response = Http::withHeaders($headers)->post($api_auth->url . $action, ['value' => $value]);
        $results = array();
        if ($response['statusCode'] == 401) {
            Artisan::call('app:api-key-refresh');
            $api_auth = ApiAuth::find(1);
            $headers = array('Accept' => 'application/json', 'Authorization' => 'Bearer ' . $api_auth->access_token, 'uyumSecretKey' => $api_auth->uyumSecretKey);
            $response = Http::withHeaders($headers)->post($api_auth->url . $action, ['value' => $value]);
        }
        if ($dd) return ($response);

        if (isset($response['result'])) {
            if (is_numeric($response['result'])) return $response; // sipariş oluşturulduğunda order_m_id döner

            foreach ($response['result'] as $result) {
                $result = (array)$result;
                $results[] = array_change_key_case($result, CASE_LOWER);
            }
        } else {
            return ($response);
        }

        return $results;
    }


    if (!function_exists('formatNumber')) {
        function formatNumber($number)
        {
            // Önce string'e çevirip boşlukları temizleyelim
            $number = trim((string)$number);

            // Son karakteri nokta veya virgül ise kaldır
            $number = rtrim($number, '.,');

            // Binlik ayırıcıları (nokta veya virgül) kaldır
            $parts = preg_split('/[,.]/', $number);

            // Eğer parça sayısı 1'den fazlaysa (yani ondalık kısım varsa)
            if (count($parts) > 1) {
                // Son parçayı ondalık kısım olarak al, diğerlerini birleştir
                $decimal = array_pop($parts);
                $integer = implode('', $parts);
                return $integer . '.' . $decimal;
            }

            // Ondalık kısım yoksa sadece tam sayıyı döndür
            return implode('', $parts);
        }


    }

    function get_from_api($api_auth, $api_url, $sql, $dd = false)
    {

        $postData = ['value' => $sql];
        try {
            $headers = array('Accept: application/json', 'Authorization: Bearer ' . $api_auth->access_token, 'uyumSecretKey: ' . $api_auth->uyumSecretKey);
            $url = $api_url . '/UyumApi/v1/GNL/GetDataTableWithSQL';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'POST');
            curl_setopt($ch, CURLOPT_POST, 1);
            if ($api_auth->port) curl_setopt($ch, CURLOPT_PORT, $api_auth->port);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            $server_output = curl_exec($ch);
            curl_close($ch);
            $json_result = json_decode($server_output);
            if ($dd) {
                dd($json_result);
            }
            if (isset($json_result->result)) {
                $results = array();
                foreach ($json_result->result as $result) {
                    $result = (array)$result;
                    $results[] = array_change_key_case($result, CASE_LOWER);
                }
            } else {
                $results = $json_result;
            }

            if (isset($results[1][0])) {
                if ($results[1][0] == 'Yetkisiz işlem.') {
                    Artisan::call('app:api-key-refresh');
                }
            }

        } catch (Exception $e) {
            $results = array();
        }
        return $results;
    }
}