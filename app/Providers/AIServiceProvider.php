<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\DB;

class AIServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton('ai.client', function ($app) {
            return new class {
                /**
                 * AI API'ye istek gönder ve sonucu döndür
                 *
                 * @param string $prompt
                 * @param array $options
                 * @return string|null
                 */
                public function query(string $prompt, array $options = []): ?string
                {
                    $cacheKey = 'ai_query_' . md5($prompt . json_encode($options));

                    // Önbellekte yanıt var mı kontrol et
                    if (config('ai.enable_cache', true) && Cache::has($cacheKey)) {
                        return Cache::get($cacheKey);
                    }

                    $apiKey = config('ai.api_key') ?? env('OPENAI_API_KEY');

                    if (!$apiKey) {
                        throw new \Exception('AI API anahtarı bulunamadı. .env dosyanızı kontrol edin.');
                    }

                    $model = $options['model'] ?? config('ai.default_model', 'gpt-4-turbo');
                    $temperature = $options['temperature'] ?? config('ai.temperature', 0.3);
                    $maxTokens = $options['max_tokens'] ?? config('ai.max_tokens', 4000);

                    $systemPrompt = $options['system_prompt'] ?? config('ai.system_prompt',
                        "Sen bir B2B web sitesi için veri analiz asistanısın. Kullanıcının sorularına veritabanından sağlanan veriler doğrultusunda yanıt ver."
                    );

                    try {
                        $response = Http::withHeaders([
                            'Authorization' => "Bearer $apiKey",
                            'Content-Type' => 'application/json',
                        ])->post('https://api.openai.com/v1/chat/completions', [
                            'model' => $model,
                            'messages' => [
                                [
                                    'role' => 'system',
                                    'content' => $systemPrompt
                                ],
                                [
                                    'role' => 'user',
                                    'content' => $prompt
                                ]
                            ],
                            'temperature' => $temperature,
                            'max_tokens' => $maxTokens,
                        ]);

                        $result = $response->json();

                        if (isset($result['choices'][0]['message']['content'])) {
                            $content = $result['choices'][0]['message']['content'];

                            // Yanıtı önbelleğe al (eğer yapılandırmada etkinse)
                            if (config('ai.enable_cache', true)) {
                                $cacheTtl = config('ai.cache_ttl', 60 * 24); // 1 gün varsayılan
                                Cache::put($cacheKey, $content, now()->addMinutes($cacheTtl));
                            }

                            return $content;
                        }

                        return null;
                    } catch (\Exception $e) {
                        report($e);
                        return null;
                    }
                }

                /**
                 * İki aşamalı sorgu işlemi: SQL Sorgusu oluştur ve Rapor üret
                 *
                 * @param string $userQuery
                 * @param array $options
                 * @return string|null
                 */
                public function twoStageQuery(string $userQuery, array $options = []): ?string
                {
                    $cacheKey = 'ai_two_stage_query_' . md5($userQuery . json_encode($options));

                    // Önbellekte yanıt var mı kontrol et
                    if (config('ai.enable_cache', true) && Cache::has($cacheKey)) {
                        return Cache::get($cacheKey);
                    }

                    $apiKey = config('ai.api_key') ?? env('OPENAI_API_KEY');

                    if (!$apiKey) {
                        throw new \Exception('AI API anahtarı bulunamadı. .env dosyanızı kontrol edin.');
                    }

                    try {
                        // ADIM 1: SQL sorgusu oluştur
                        $databaseSchema = $this->getDatabaseSchema();
                        $sqlPrompt = $this->prepareSqlPrompt($userQuery, $databaseSchema);

                        $sqlModel = config('ai.sql_model', 'gpt-4');
                        $sqlTemperature = config('ai.sql_temperature', 0.3);


                        $sqlResponse = Http::withHeaders([
                            'Authorization' => "Bearer $apiKey",
                            'Content-Type' => 'application/json',
                        ])->post('https://api.openai.com/v1/chat/completions', [
                            'model' => $sqlModel,
                            'messages' => [
                                [
                                    'role' => 'system',
                                    'content' => 'Sen bir SQL uzmanısın. Kullanıcının sorgusu için yalnızca gerekli SQL sorgusunu veya sorguları döndür. Açıklama, ek metin veya yorum ekleme. Sadece çalıştırılabilir SQL sorgusu döndür.'
                                ],
                                [
                                    'role' => 'user',
                                    'content' => 'Aşağıdaki veritabanı şeması ve kullanıcı sorgusu için SQL sorgusu oluştur: '.$sqlPrompt
                                ]
                            ],
                            'temperature' => $sqlTemperature,
                            'max_tokens' => 10000,
                        ]);

                        $sqlResult = $sqlResponse->json();

                        if (!isset($sqlResult['choices'][0]['message']['content'])) {
                            throw new \Exception('SQL sorgusu oluşturulamadı');
                        }

                        $sqlQuery = $sqlResult['choices'][0]['message']['content'];
                        $sqlQuery = $this->cleanSqlQuery($sqlQuery);
                        dd($sqlQuery);

                        // ADIM 2: SQL sorgusunu çalıştır
                       // $queryResults = $this->executeSqlQuery($sqlQuery);
                        $results = DB::select($sqlQuery);
                      //  $queryResults = collect($results);
                        // ADIM 3: Rapor oluştur
                        $context = $this->prepareContextFromData($queryResults);
                        $finalPrompt = "Kullanıcı sorusu: $userQuery\n\nVeritabanından gelen ilgili veriler: $context\n\n";
                        $finalPrompt .= "Lütfen bu soruya veritabanı verilerini kullanarak cevap ver ve HTML formatında döndür. Veri yoksa veya yetersizse bunu belirt.";


                        $reportModel = config('ai.report_model', 'gpt-4');
                        $reportTemperature = config('ai.report_temperature', 0.7);

                        $finalResponse = Http::withHeaders([
                            'Authorization' => "Bearer $apiKey",
                            'Content-Type' => 'application/json',
                        ])->post('https://api.openai.com/v1/chat/completions', [
                            'model' => $reportModel,
                            'messages' => [
                                [
                                    'role' => 'system',
                                    'content' => 'Sen bir B2B web sitesi için veri analisti asistanısın. Veritabanı verilerini kullanarak iş zekası raporu hazırla.'
                                ],
                                [
                                    'role' => 'user',
                                    'content' => $finalPrompt
                                ]
                            ],
                            'temperature' => $reportTemperature,
                            'max_tokens' => 2000,
                        ]);

                        $result = $finalResponse->json();

                        if (isset($result['choices'][0]['message']['content'])) {
                            $content = $result['choices'][0]['message']['content'];

                            // Yanıtı önbelleğe al
                            if (config('ai.enable_cache', true)) {
                                $cacheTtl = config('ai.cache_ttl', 60 * 24);
                                Cache::put($cacheKey, $content, now()->addMinutes($cacheTtl));
                            }

                            return $content;
                        }

                        return null;
                    } catch (\Exception $e) {
                        report($e);

                        // Hata durumunda standart sorgu yöntemini kullan
                        return $this->query($userQuery, $options);
                    }
                }

                /**
                 * SQL sorgusunu temizle
                 */
                private function cleanSqlQuery($sql)
                {
                    // ```sql ve ``` gibi kod blokları varsa kaldır
                    $sql = preg_replace('/```sql|```/', '', $sql);
                    return trim($sql);
                }

                /**
                 * SQL sorgusunu çalıştır
                 */
                private function executeSqlQuery($sql)
                {
                    // Güvenlik için SELECT sorgularını kabul et
                    if (!preg_match('/^\s*SELECT/i', $sql)) {
                        throw new \Exception('Sadece SELECT sorguları kabul edilir');
                    }

                    // SQL injection koruması için bir kara liste kontrol et
                    $blacklist = ['DROP', 'DELETE', 'UPDATE', 'INSERT', 'ALTER', 'TRUNCATE', 'GRANT', 'REVOKE'];
                    foreach ($blacklist as $term) {
                        if (stripos($sql, $term) !== false) {
                            throw new \Exception('Güvenlik ihlali: İzin verilmeyen SQL ifadesi');
                        }
                    }

                    // Sorguyu çalıştır
                    $results = DB::select($sql);
                    return collect($results);
                }

                /**
                 * Veritabanı şema bilgilerini al
                 */
                private function getDatabaseSchema()
                {
                    // Tabloları al
                    $tables = DB::select('SHOW TABLES');
                    $schema = [];

                    foreach ($tables as $table) {
                        $tableName = reset($table); // İlk değeri al (tablo adı)

                        // Her tablonun sütun bilgilerini al
                        $columns = DB::select("DESCRIBE {$tableName}");

                        $schema[$tableName] = [];

                        foreach ($columns as $column) {
                            $schema[$tableName][] = [
                                'column' => $column->Field,
                                'type' => $column->Type,
                                'key' => $column->Key
                            ];
                        }
                    }

                    return $schema;
                }

                /**
                 * SQL sorgusu oluşturmak için prompt hazırla
                 */
                private function prepareSqlPrompt($query, $schema)
                {
                    $prompt = "### Veritabanı Şeması:\n\n";

                    foreach ($schema as $table => $columns) {
                        $prompt .= "Tablo: {$table}\n";
                        $prompt .= "Sütunlar:\n";

                        foreach ($columns as $column) {
                            $prompt .= "- {$column['column']} ({$column['type']})";

                            if (!empty($column['key'])) {
                                $prompt .= " [" . $column['key'] . "]";
                            }

                            $prompt .= "\n";
                        }

                        $prompt .= "\n";
                    }

                    $prompt .= "### Önemli İlişkiler ve Bilgiler:\n";
                    $prompt .= "- products tablosunda ürün bilgileri bulunur\n";
                    $prompt .= "- orders tablosu siparişlerin ana bilgilerini içerir\n";
                    $prompt .= "- orders_d tablosu sipariş detaylarını içerir (order_m_id ile orders tablosuna bağlanır)\n";
                    $prompt .= "- entities tablosu müşteri bilgilerini içerir\n";
                    $prompt .= "- contracts tablosu sözleşme bilgilerini içerir\n";
                    $prompt .= "- invoices tablosu fatura bilgilerini içerir\n";
                    $prompt .= "- payments tablosu ödeme bilgilerini içerir\n\n";

                    $prompt .= "### Kullanıcı Sorgusu:\n";
                    $prompt .= "{$query}\n\n";

                    $prompt .= "### Talimatlar:\n";
                    $prompt .= "Yukarıdaki kullanıcı sorgusunu analiz ederek, gerekli verileri çekmek için bir SQL sorgusu oluştur.\n";
                    $prompt .= "Sadece SQL sorgusunu döndür, açıklama ekleme.\n";
                    $prompt .= "Mümkünse ilişkili tabloları JOIN kullanarak bağla.\n";
                    $prompt .= "Eğer sorgu belirli bir zaman aralığı belirtiyorsa (son 7 gün, son 30 gün vb.) bunu WHERE koşulunda belirt.\n";
                    $prompt .= "Sıralama, gruplama veya toplam hesaplamaları gerektiğinde kullan.\n";
                    $prompt .= "Sorguda tarih alanları için MySQL tarih formatını kullan (YYYY-MM-DD).\n";

                    return $prompt;
                }

                /**
                 * Veri setini AI'ya gönderilecek metin bağlamına dönüştür
                 */
                private function prepareContextFromData($data)
                {
                    // Eğer data koleksiyon ise (SQL sorgusu sonucu)
                    if ($data instanceof \Illuminate\Support\Collection) {
                        if ($data->isEmpty()) {
                            return "Verilen sorgu için veritabanında eşleşen veri bulunamadı.";
                        }

                        $context = "Veritabanı sorgu sonuçları:\n\n";

                        // İlk 50 satırı ekle
                        $limitedData = $data->take(50);

                        // Tablo başlıklarını al (ilk satırın anahtarları)
                        if ($limitedData->isNotEmpty()) {
                            $firstItem = $limitedData->first();
                            if (is_object($firstItem)) {
                                $headers = array_keys(get_object_vars($firstItem));
                            } else if (is_array($firstItem)) {
                                $headers = array_keys($firstItem);
                            } else {
                                $headers = ['value'];
                            }

                            $context .= "Sütunlar: " . implode(", ", $headers) . "\n\n";

                            // Verileri satır satır ekle
                            foreach ($limitedData as $index => $row) {
                                $context .= "Satır " . ($index + 1) . ":\n";

                                if (is_object($row)) {
                                    foreach (get_object_vars($row) as $key => $value) {
                                        $context .= "- $key: " . $this->formatValue($value) . "\n";
                                    }
                                } else if (is_array($row)) {
                                    foreach ($row as $key => $value) {
                                        $context .= "- $key: " . $this->formatValue($value) . "\n";
                                    }
                                } else {
                                    $context .= "- value: " . $this->formatValue($row) . "\n";
                                }

                                $context .= "\n";
                            }

                            // Eğer veri seti büyükse, bunun bir özeti olduğunu belirt
                            if ($data->count() > 50) {
                                $context .= "Not: Toplam " . $data->count() . " satır veri bulunmaktadır, yukarıda ilk 50 satır gösterilmiştir.\n";
                            }
                        }

                        return $context;
                    }

                    return "Veri bulunamadı veya desteklenmeyen veri formatı.";
                }

                /**
                 * Değerleri formatla
                 */
                private function formatValue($value)
                {
                    if (is_null($value)) {
                        return 'NULL';
                    } else if (is_bool($value)) {
                        return $value ? 'true' : 'false';
                    } else if (is_array($value) || is_object($value)) {
                        return json_encode($value, JSON_UNESCAPED_UNICODE);
                    } else {
                        return (string) $value;
                    }
                }
            };
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->publishes([
            __DIR__.'/../config/ai.php' => config_path('ai.php'),
        ], 'ai-config');
    }
}