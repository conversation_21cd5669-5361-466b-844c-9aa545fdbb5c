<?php

namespace App\Services;

class CreditCardDetector
{
    private array $bankPrefixes = [
        // Ziraat Bankası
        '454671', '454672', '454673', '454674', '527682', '528208', '535585', '543771', '547564',

        // Garanti Bankası
        '520019', '520988', '521824', '521825', '521832', '522204', '528824', '533171', '534261',
        '542030', '544078', '550470', '559289',

        // İş Bankası
        '402282', '402283', '402284', '402285', '402286', '402287', '402288', '404935', '408580',
        '418342', '418343', '418344', '418345', '418346', '435508', '439057', '444676', '444677',
        '444678', '446392', '450803', '450804', '450805', '450806', '450807', '450808', '454318',
        '454358', '454359', '454360', '510152', '510153', '510154', '510155', '510156', '510157',
        '510158', '531157', '531241', '531242', '531244', '531245', '531246', '531247', '531248',
        '532913', '534264', '534265', '534266', '534267', '534268', '534269', '535429', '535430',
        '535434', '535435', '535436', '535437', '535438', '535439', '537829', '537857', '537859',
        '539374', '539386', '539401', '539403', '542190', '542191', '542192', '542798', '544078',
        '544079', '544083', '544084', '544086', '544088', '544089', '545120', '545616', '545731',
        '545742', '545795', '545865', '546001', '546058', '546059', '546077', '547380', '549220',
        '549597', '552096', '553058', '553096', '554961', '554969', '554970', '554972', '554973',
        '557023', '557056', '557945', '558699',

        // Yapı Kredi
        '402278', '402279', '402280', '404809', '405901', '405904', '411684', '413382', '428462',
        '434728', '434742', '442106', '446386', '455645', '455646', '455647', '455648', '455649',
        '455650', '455651', '455652', '455653', '455654', '455655', '455656', '455657', '479794',
        '479795', '490175', '490176', '490177', '490178', '490179', '490180', '490937', '490939',
        '490940', '490941', '490942', '490943', '490944', '492186', '492187', '492188', '492189',
        '492190', '492191', '499821', '499823', '499824', '499825', '499826', '499827', '499828',
        '510620', '510621', '510622', '510623', '510624', '512117', '512118', '512119', '512120',
        '521682', '524346', '524347', '524348', '524349', '530866', '531095', '531096', '531097',
        '531098', '535429', '535430', '535432', '535433', '535434', '536699', '537511', '537666',
        '538975', '538976', '538977', '538978', '538979', '539052', '539148', '539149', '539150',
        '539151', '539152', '539153', '539154', '539155', '539156', '539157', '539158', '539159',
        '540061', '540062', '540063', '540122', '540129', '540134', '542117', '544171', '545847',
        '546097', '546098', '546770', '546771', '547287', '547705', '549449', '549450', '552145',
        '553130', '553131', '469884', '677858', '558489', '558490', '558491',

        // Akbank
        '402277', '402276', '402275', '402274', '402273', '402272', '402271', '402270', '411156',
        '411157', '411158', '411159', '411160', '411161', '411162', '411163', '411164', '418345',
        '420556', '420557', '512754', '512755', '512756', '512757', '512758', '520123', '520932',
        '520940', '520941', '520942', '520943', '520983', '520984', '520985', '520986', '520987',
        '520988', '520989', '521807', '521808', '521809', '521825', '521826', '521827', '521828',
        '521829', '521830', '521831', '524347', '524348', '524349', '524350', '524351', '524352',
        '524353', '524354', '524355', '524356', '524357', '524358', '524359', '525315', '525316',
        '525317', '525318', '525319', '525320', '532581', '533169', '533170', '533171', '533172',
        '533173', '533174', '542111', '542112', '542113', '542114', '542115', '542116', '542117',
        '542118', '542659', '542660', '542713', '543738', '552608', '552609', '553056', '553057',
        '553058', '553059', '553060', '553061', '553062', '553063', '553069', '553086', '553095',
        '553858', '553859', '553860', '553861', '553862', '553863', '553864', '553865', '554573',
    ];

    // Ticari kart BIN'leri
    private array $commercialPrefixes = [
        // Ziraat Bankası Ticari
        '558699', '558706', '558709', '402947',

        // Garanti Bankası Ticari
        '377137', '377138', '377139', '377140', '377141', '377142', '377143', '377144', '377145',
        '554960', '554961', '554962', '554963', '554964', '554965', '515456', '515457', '515458',

        // İş Bankası Ticari
        '408625', '408626', '408627', '408628', '408629', '424909', '424910', '424911', '424912',
        '424913', '424914', '424915', '424916', '424917', '424918', '424919', '424920', '424921',
        '424922', '424923', '424924', '424925', '424926', '424927', '424928', '424929', '424930',
        '428462', '428463', '428464', '428465', '428466', '428467', '428468', '428469', '428470',

        // Yapı Kredi Ticari
        '479794', '479795', '479796', '479797', '479798', '479799', '676123', '676124', '676125',
        '676126', '676127', '676128', '676129', '676130', '676131', '676132', '676133', '676134',

        // Akbank Ticari
        '375622', '375623', '375624', '375625', '375626', '375627', '375628', '375629', '375630',
        '552608', '552609', '552610', '552611', '552612', '552613', '552614', '552615', '552616',
        '552617', '552618', '552619', '552620', '552621', '552622', '552623', '552624', '552625'
    ];

    // American Express ticari kart BIN'leri
    private array $amexCommercialPrefixes = [
        '378734', '375987', '375988', '375989', '375990', '375991', '375992', '375993', '375994',
        '375995', '375996', '375997', '375998', '375999', '376000', '376001', '376002', '376003'
    ];

    /**
     * Kartın tipini tespit eder (Ticari/Bireysel)
     *
     * @param string $cardNumber
     * @return array
     */
    public function detectCardType(string $cardNumber): array
    {
        $cleanNumber = preg_replace('/\D/', '', $cardNumber);
        $prefix6 = substr($cleanNumber, 0, 6);
        $prefix4 = substr($cleanNumber, 0, 4);
        $prefix2 = substr($cleanNumber, 0, 2);

        $isCommercial = false;
        $cardType = 'bireysel';
        $details = [];

        // Ticari kart kontrolü
        if (in_array($prefix6, $this->commercialPrefixes)) {
            $isCommercial = true;
            $cardType = 'ticari';
        }

        // American Express ticari kart kontrolü
        if (in_array($prefix6, $this->amexCommercialPrefixes)) {
            $isCommercial = true;
            $cardType = 'commercial_amex';
        }

        // MasterCard World Corporate kontrolü (özel seri)
        if ($prefix2 == '55' && in_array($prefix6, ['557483', '557484', '557485', '557486', '557487'])) {
            $isCommercial = true;
            $cardType = 'commercial_world';
        }

        // Visa Corporate/Business kontrolü
        if ($prefix2 == '48' || ($prefix2 == '40' && $prefix4 == '4046')) {
            $isCommercial = true;
            $cardType = 'commercial_visa';
        }

        // Kart markasını tespit et
        $brand = $this->detectCardBrand($cleanNumber);

        // Banka adını tespit et
        $bank = $this->detectBank($cleanNumber);

        return [
            'is_commercial' => $isCommercial,
            'card_type' => $cardType,
            'brand' => $brand,
            'bank' => $bank,
            'bin' => $prefix6,
            'is_valid' => $this->isValidCardNumber($cleanNumber)
        ];
    }

    /**
     * Kartın markasını tespit eder
     *
     * @param string $cardNumber
     * @return string
     */
    private function detectCardBrand(string $cardNumber): string
    {
        $prefix1 = substr($cardNumber, 0, 1);
        $prefix2 = substr($cardNumber, 0, 2);
        $prefix4 = substr($cardNumber, 0, 4);

        // Visa: 4 ile başlar
        if ($prefix1 == '4') {
            return 'visa';
        }

        // Mastercard: 51-55 arası başlar
        if ($prefix2 >= '51' && $prefix2 <= '55') {
            return 'mastercard';
        }

        // American Express: 34 veya 37 ile başlar
        if ($prefix2 == '34' || $prefix2 == '37') {
            return 'amex';
        }

        // Troy: 65 ile başlar veya özel seriler
        if ($prefix2 == '65' || in_array($prefix4, ['9792', '6500'])) {
            return 'troy';
        }

        return 'unknown';
    }

    /**
     * Kredi kartı numarasından banka tespiti yapar
     *
     * @param string $cardNumber
     * @return string|null
     */
    public function detectBank(string $cardNumber): ?string
    {
        // Kartın ilk 6 hanesini al
        $prefix = substr(preg_replace('/\D/', '', $cardNumber), 0, 6);

        // Bankayı tespit et
        if (in_array($prefix, $this->bankPrefixes)) {
            foreach ($this->bankPrefixes as $bankPrefix) {
                if (strpos($prefix, $bankPrefix) === 0) {
                    return $this->getBankName($bankPrefix);
                }
            }
        }

        return null;
    }

    /**
     * Banka prefix'ine göre banka adını döndürür
     *
     * @param string $prefix
     * @return string
     */
    private function getBankName(string $prefix): string
    {
        // Ziraat Bankası
        if (in_array($prefix, ['454671', '454672', '454673', '454674', '527682', '528208', '535585', '543771', '547564'])) {
            return 'ziraat_bankasi';
        }

        // Garanti Bankası
        if (in_array($prefix, ['520019', '520988', '521824', '521825', '521832', '522204', '528824', '533171', '534261', '542030', '544078', '550470', '559289'])) {
            return 'garanti_bankasi';
        }

        // İş Bankası
        if (in_array($prefix, ['402282', '402283', '402284', '402285', '402286', '402287', '402288'])) {
            return 'isbank';
        }

        // Yapı Kredi
        if (in_array($prefix, ['402278', '402279', '402280', '404809', '405901', '405904', '450634'])) {
            return 'yapi_kredi_bankasi';
        }

        // Akbank
        if (in_array($prefix, ['402277', '402276', '402275', '402274', '402273', '402272', '402271'])) {
            return 'akbank';
        }

        return 'Bilinmeyen Banka';
    }

    /**
     * Kredi kartı numarasının geçerli olup olmadığını kontrol eder (Luhn Algorithm)
     *
     * @param string $cardNumber
     * @return bool
     */
    public function isValidCardNumber(string $cardNumber): bool
    {
        $number = preg_replace('/\D/', '', $cardNumber);
        $sum = 0;
        $isEven = false;

        // Sağdan sola doğru işlem yap
        for ($i = strlen($number) - 1; $i >= 0; $i--) {
            $digit = (int)$number[$i];

            if ($isEven) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }

            $sum += $digit;
            $isEven = !$isEven;
        }

        return ($sum % 10) === 0;
    }
}
