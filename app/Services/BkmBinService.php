<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;

class BkmBinService
{
    private string $baseUrl;
    private string $clientId;
    private string $clientSecret;

    public function __construct()
    {
        $this->baseUrl = config('services.bkm.base_url', 'https://api-prod.bkm.com.tr');
        $this->clientId = config('services.bkm.client_id');
        $this->clientSecret = config('services.bkm.client_secret');
    }

    /**
     * Get access token from BKM API
     */
    private function getAccessToken(): string
    {
        // Check if token exists in cache
        if (Cache::has('bkm_access_token')) {
            return Cache::get('bkm_access_token');
        }

        $response = Http::asForm()->post($this->baseUrl . '/oauth-provider/oauth2/token', [
            'grant_type' => 'client_credentials',
            'scope' => 'public_bin_read',
            'client_id' => $this->clientId,
            'client_secret' => $this->clientSecret,
        ]);

        if (!$response->successful()) {
            throw new \Exception('BKM API token alınamadı: ' . $response->body());
        }

        $data = $response->json();

        // Cache token for 55 minutes (token expires in 60 minutes)
        Cache::put('bkm_access_token', $data['access_token'], now()->addMinutes(55));

        return $data['access_token'];
    }

    /**
     * Query BIN information
     */
    public function queryBin(): array
    {
        $token = $this->getAccessToken();

        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $token
        ])->get($this->baseUrl . '/public-bin-api/v1/bin');

        if (!$response->successful()) {
            throw new \Exception('BIN sorgulanamadı: ' . $response->body());
        }

        return $response->json();
    }
}
