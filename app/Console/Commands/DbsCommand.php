<?php

namespace App\Console\Commands;

use App\Models\Dbs;
use Illuminate\Console\Command;

class DbsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:dbs';

    /**
     * The console command description.
     */
    protected $description = 'Get DBS.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $dbs_docs = uyumapi('SELECT create_date as created_at, update_date as updated_at, akdag_dbs_tra_m_id as id, co_id, entity_id, bank_id, doc_no, doc_date,due_day, due_date, amt from zzzt_akdag_dbs_tra_m limit 100');

            foreach ($dbs_docs as $dbs) {
                if ($dbs['updated_at'] == '0001-01-01T00:00:00') {
                    $dbs['updated_at'] = null;
                } else {
                    $dbs['updated_at'] = date('Y-m-d H:m:s', strtotime($dbs['updated_at']));
                }
                if ($dbs['created_at'] == '0001-01-01T00:00:00') {
                    $dbs['created_at'] = null;
                } else {
                    $dbs['created_at'] = date('Y-m-d H:m:s', strtotime($dbs['created_at']));
                }
                unset($dbs['create_user_id']); unset($dbs['update_user_id']);

                Dbs::updateOrCreate(['id' => $dbs['id']], $dbs);
            }

            sleep(1);

//        $amounts = uyumapi('SELECT * from akdag_dbs_limit');
//
//        foreach ($amounts as $amount) {
//            Dbs::where('id',$amount['akdag_dbs_tra_m_id'])->update(['amount_used' => $amount['kullanilan_limit'], 'remaining_amount' => $amount['kalan_limit']]);
//        }
    }

}
