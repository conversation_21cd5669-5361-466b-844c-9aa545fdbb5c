<?php

namespace App\Console\Commands;

use App\Models\Bank;
use App\Models\TransportType;
use Illuminate\Console\Command;

class TransportTypesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:transport-types';

    /**
     * The console command description.
     */
    protected $description = 'Get transport types.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $banks = uyumapi('SELECT transport_type_id, description, transport_type_code from psmd_transport_type');
        foreach ($banks as $bank) {
            $id = $bank['transport_type_id']; unset($bank['transport_type_id']);
            TransportType::updateOrCreate(['id' => $id], $bank);
        }


    }



}
