<?php

namespace App\Console\Commands;

use App\Models\ContractPriceList;
use DB;
use Illuminate\Console\Command;

class ContractPriceListsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contract-price-lists';

    /**
     * The console command description.
     */
    protected $description = 'Get price lists for contract.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        DB::statement('TRUNCATE TABLE contract_price_list');
        $price_lists = uyumapi('SELECT form_contract_m_id as contract_id, price_list_m_id as price_list_id from fint_form_contract_price');
        foreach ($price_lists as $price_list) {
            ContractPriceList::firstOrCreate($price_list);

        }
    }

}
