<?php

namespace App\Console\Commands;

use App\Models\Town;
use Illuminate\Console\Command;

class TownsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:towns';

    /**
     * The console command description.
     */
    protected $description = 'Get towns.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $towns = uyumapi('SELECT town_id, town_name, city_id from gnld_town ');
        foreach ($towns as $town) {
            $id = $town['town_id']; unset($town['town_id']);
            Town::updateOrCreate(['id' => $id], $town);
        }
    }

}
