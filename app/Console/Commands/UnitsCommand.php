<?php

namespace App\Console\Commands;

use App\Models\Unit;
use Illuminate\Console\Command;

class UnitsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:units';

    /**
     * The console command description.
     */
    protected $description = 'Get units.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $units = uyumapi('SELECT unit_id, unit_code, description as unit_name, iso_unit_code from invd_unit ');
        foreach ($units as $unit) {
            $id = $unit['unit_id']; unset($unit['unit_id']);

            Unit::updateOrCreate(['id' => $id], $unit);
        }
    }


}
