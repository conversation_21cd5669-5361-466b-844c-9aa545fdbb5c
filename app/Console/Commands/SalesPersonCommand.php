<?php

namespace App\Console\Commands;

use App\Models\SalesPerson;
use Illuminate\Console\Command;

class SalesPersonCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:sales-persons';

    /**
     * The console command description.
     */
    protected $description = 'Get sales persons.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $sales_persons = uyumapi('SELECT * from find_sales_person');
            foreach ($sales_persons as $sales_person) {
                $id = $sales_person['sales_person_id']; unset($sales_person['sales_person_id']);

                SalesPerson::updateOrCreate(['id' => $id], $sales_person);
            }


    }



}
