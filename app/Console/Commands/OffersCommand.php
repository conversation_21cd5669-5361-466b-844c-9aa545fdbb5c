<?php

namespace App\Console\Commands;

use App\Models\Offer;
use Illuminate\Console\Command;

class OffersCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:offers';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
       
            $offers = uyumapi('SELECT offer_m_id,address1,address2,address3,amt,amt_vat,amt_vat_tra,country_id,city_id,town_id,co_id,doc_date,doc_no,entity_id,doc_tra_id,cur_tra_id,sales_person_id,offer_status,create_user_id,branch_id,amt_receipt,amt_receipt_tra,amt_tra,payment_method_id,shipping_entity_id,cur_rate_tra,cur_rate_type_id,due_day,purchase_sales,gnl_note1,incoterms_id FROM psmt_offer_m limit 100 ');

            foreach ($offers as $offer) {
                $id = $offer['offer_m_id']; unset($offer['offer_m_id']);
                Offer::updateOrCreate(['id' => $id], $offer);
            }
    }
}
