<?php

namespace App\Console\Commands;

use App\Models\Offer;
use App\Models\Product;
use Illuminate\Console\Command;

class ProductsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $items = uyumapi('SELECT item_id, item_code, item_name, item_name2, unit_id, categories1_id, categories2_id, categories3_id, categories4_id, categories5_id, categories6_id, categories7_id, brand_id,density,density_unit_id,depth,height,metric_unit_id,net_weight,volume_unit_id,weight_unit_id,width,volume,default_tax_id, create_date, update_date from invd_item where ispassive = 0 order by update_date desc, create_date desc limit 1000');

    //    $items = uyumapi('SELECT item_id, item_code, item_name, item_name2, unit_id, categories1_id, categories2_id, categories3_id, categories4_id, categories5_id, categories6_id, categories7_id, brand_id,density,density_unit_id,depth,height,metric_unit_id,net_weight,volume_unit_id,weight_unit_id,width,volume,default_tax_id, create_date, update_date from invd_item where ispassive = 0 and item_id in (SELECT item_id FROM invd_branch_item where co_id = 2725)');

        foreach ($items as $item) {
            $id = $item['item_id']; unset($item['item_id']);
            $item['create_date'] = date('Y-m-d H:m:s', strtotime($item['create_date']));
            if ($item['update_date'] !== '0001-01-01T00:00:00') {
                $item['update_date'] = date('Y-m-d H:m:s', strtotime($item['update_date']));
            } else {
                $item['update_date'] = null;
            }


            Product::updateOrCreate(['id' => $id], $item);

        }

    }
}
