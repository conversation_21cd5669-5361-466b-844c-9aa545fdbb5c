<?php

namespace App\Console\Commands;

use App\Models\PriceRule;
use Illuminate\Console\Command;

class PriceRulesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:price-rules';

    /**
     * The console command description.
     */
    protected $description = 'Get price-rules.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $brands = uyumapi('SELECT price_rule_m_id, rule_code, rule_name from invd_price_rule_m');
            foreach ($brands as $brand) {
                $id = $brand['price_rule_m_id']; unset($brand['price_rule_m_id']);
                PriceRule::updateOrCreate(['id' => $id], $brand);
            }


    }



}
