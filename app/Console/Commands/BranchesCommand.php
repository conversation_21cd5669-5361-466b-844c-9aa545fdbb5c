<?php

namespace App\Console\Commands;

use App\Models\Branch;
use Illuminate\Console\Command;

class   BranchesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:branches';

    /**
     * The console command description.
     */
    protected $description = 'Get branches.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
            $companies = uyumapi('SELECT branch_id, branch_code, branch_desc, co_id, branch_short_desc, tel1, email, ispassive, tax_office_id, tax_no, town_id, city_id, country_id from gnld_branch');
        foreach ($companies as $company) {
            $id = $company['branch_id']; unset($company['branch_id']);

                Branch::updateOrCreate(['id' => $id], $company);
            }

    }



}
