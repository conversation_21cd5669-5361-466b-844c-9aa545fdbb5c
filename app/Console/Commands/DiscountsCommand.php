<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Discount;

class DiscountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:discounts';

    /**
     * The console command description.
     */
    protected $description = 'Get discounts.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $banks = uyumapi('SELECT * from find_disc');
            foreach ($banks as $bank) {
                $id = $bank['disc_id']; unset($bank['disc_id']);
                Discount::updateOrCreate(['id' => $id], $bank);
            }


    }



}
