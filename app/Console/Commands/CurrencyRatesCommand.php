<?php

namespace App\Console\Commands;

use App\Models\Currency;
use App\Models\CurrencyExchangeRate;
use Illuminate\Console\Command;

class CurrencyRatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:currency-rates';

    /**
     * The console command description.
     */
    protected $description = 'Get currency rates.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $doviz = simplexml_load_file('http://www.tcmb.gov.tr/kurlar/today.xml');
        $json = json_encode($doviz);
        $json = str_replace("@", "", $json);
        $array = json_decode($json, TRUE);
        $date = date('Y-m-d', strtotime($array['attributes']['Date']));
        foreach ($array['Currency'] as $currency) {
            if (!empty($currency['BanknoteBuying'])) {
                $data = ['unit' => $currency['Unit'], 'buying_rate' => $currency['BanknoteBuying'], 'selling_rate' => $currency['BanknoteSelling']];
                $currency_id = Currency::where('cur_code', $currency['attributes']['Kod'])->value('id');
                if ($currency_id) {
                    CurrencyExchangeRate::updateOrCreate(['currency_id' => $currency_id, 'date' => $date], $data);
                }
            }

        }
    }

}
