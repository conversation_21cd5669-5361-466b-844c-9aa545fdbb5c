<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use App\Models\ApiAuth;
class ApiKeyRefreshCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:api-key-refresh';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $api_auth = ApiAuth::find(1);

        $decrypted = Crypt::decryptString($api_auth->password);
        $authData = array('username' => $api_auth->username, 'password' => $decrypted);
        $result = \Http::post($api_auth->url . '/UyumApi/v1/GNL/UyumLogin', $authData);

        $json = json_decode($result, true);
        $access_token = $json['result']['access_token'];
        $uyumSecretKey = $json['result']['uyumSecretKey'];

        $api_auth->update(['access_token' => $access_token, 'uyumSecretKey' => $uyumSecretKey, 'updated_at' => now()]);

    }
}
