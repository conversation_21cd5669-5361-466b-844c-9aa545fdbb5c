<?php

namespace App\Console\Commands;

use App\Models\Waybill;
use Illuminate\Console\Command;

class WaybillsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:waybills';

    /**
     * The console command description.
     */
    protected $description = 'Get waybills.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $waybills = uyumapi('SELECT item_m_id, address1, address2, address3, amt, amt_vat, amt_vat_tra, country_id, cur_tra_id, city_id, town_id, co_id, doc_date, doc_no, invoice_status, entity_id, doc_tra_id, e_doc_no, uuid FROM invt_item_m WHERE purchase_sales = 2 and is_waybil = 1  ORDER BY update_date DESC, create_date DESC  LIMIT 50');
        foreach ($waybills as $waybill) {

            $id = $waybill['item_m_id']; unset($waybill['item_m_id']);

            Waybill::updateOrCreate(['id' => $id], $waybill);
        }
    }

}
