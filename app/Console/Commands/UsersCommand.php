<?php

namespace App\Console\Commands;

use App\Events\Frontend\UserRegistered;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Auth\Events\Registered;
use Illuminate\Support\Facades\Hash;

class UsersCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:users';

    /**
     * The console command description.
     */
    protected $description = 'Get users.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $users = uyumapi('SELECT us_id, us_name as first_name, us_surname as last_name, us_username, email , register_id, mobile_tel as mobile, create_date as created_at, update_date as updated_at from users where email is not null and is_web_servis_user = 0 and us_enabled = 1 ');
            foreach ($users as $user) {
                $user['created_at'] = date('Y-m-d H:m:s', strtotime($user['created_at']));
                $user['updated_at'] = null;
                if ($user['updated_at'] !== '0001-01-01T00:00:00') {
                    $user['updated_at'] = date('Y-m-d H:m:s', strtotime($user['updated_at']));
                }

                $user['username'] = replace_turkish_characters(mb_strtolower($user['first_name'] . $user['last_name']));
                $user['password'] = Hash::make('pasU129*!@#');
                $user['name'] = $user['first_name'] . ' ' . $user['last_name'];
                $user['status']  = 1;
                $us = User::where('email', $user['email'])->first();
                if ($us) {
                    $user['email'] = $user['username'].'@akdagtasyunu.com';
                }
                $us = User::where('email', $user['email'])->first();
                if (!$us) {
                    $user = User::create($user);
                    $user->assignRole('User');
                    event(new Registered($user));
                    event(new UserRegistered($user));
                }


            }


    }



}
