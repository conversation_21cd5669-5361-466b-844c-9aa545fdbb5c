<?php

namespace App\Console\Commands;

use App\Models\InvdFreeUnit;
use Illuminate\Console\Command;

class InvdFreeUnitCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:free-units';

    /**
     * The console command description.
     */
    protected $description = 'Get DBS.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $dbs_docs = uyumapi('SELECT free_unit_id, item_id, ispassive, description, free_unit_code from invd_free_unit ');

            foreach ($dbs_docs as $dbs) {
               $id = $dbs['free_unit_id']; unset($dbs['free_unit_id']);
                InvdFreeUnit::updateOrCreate(['id' => $id], $dbs);
            }


    }

}
