<?php

namespace App\Console\Commands;

use App\Models\Country;
use Illuminate\Console\Command;

class CountriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:countries';

    /**
     * The console command description.
     */
    protected $description = 'Get countries.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $countries = uyumapi('SELECT country_id, country_name, iso_country_code from gnld_country ');
        foreach ($countries as $country) {
            $id = $country['country_id']; unset($country['country_id']);
            Country::updateOrCreate(['id' => $id], $country);
        }
    }


}
