<?php

namespace App\Console\Commands;

use App\Models\Company;
use Illuminate\Console\Command;

class   CompaniesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:companies';

    /**
     * The console command description.
     */
    protected $description = 'Get companies.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
            $companies = uyumapi('SELECT * from gnld_company');
        foreach ($companies as $company) {
            $id = $company['co_id']; unset($company['co_id']);
                Company::updateOrCreate(['id' => $id], $company);
            }

    }



}
