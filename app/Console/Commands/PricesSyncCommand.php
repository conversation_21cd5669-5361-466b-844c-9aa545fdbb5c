<?php

namespace App\Console\Commands;

use App\Models\Price;
use App\Models\PriceList;
use Illuminate\Console\Command;
use Carbon\Carbon;

class PricesSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:prices-sync';

    /**
     * The console command description.
     */
    protected $description = 'Get prices sync.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


        $price_lists = PriceList::where('ispassive', 0)->orderByDesc('id')->get();
        foreach ($price_lists as $price_list) {
            $price_ids = uyumapi('SELECT price_list_d_id from invt_price_list_d  where price_list_m_id = '.$price_list->id);
            if (!empty($price_ids)) {
                Price::whereNotIn('id', $price_ids)->where('price_list_m_id', $price_list->id)->delete();

            }
        }


    }
}
