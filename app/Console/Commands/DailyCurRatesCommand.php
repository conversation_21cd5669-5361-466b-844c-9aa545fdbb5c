<?php

namespace App\Console\Commands;

use App\Models\DailyCurRate;
use Illuminate\Console\Command;

class DailyCurRatesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:daily-cur-rates';

    /**
     * The console command description.
     */
    protected $description = 'Get cur rates.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $daily_cur_rates = uyumapi('SELECT daily_cur_rate_id,cur_to_id,cur_from_id, cur_rate_type_id, doc_date, cur_rate_tra from gnld_daily_cur_rate order by daily_cur_rate_id desc limit 150');

        foreach ($daily_cur_rates as $daily_cur_rate) {
            $id = $daily_cur_rate['daily_cur_rate_id']; unset($daily_cur_rate['daily_cur_rate_id']);
            DailyCurRate::updateOrCreate(['id' => $id], $daily_cur_rate);
        }


    }



}
