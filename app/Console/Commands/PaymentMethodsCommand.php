<?php

namespace App\Console\Commands;

use App\Models\PaymentMethod;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class PaymentMethodsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:payment-methods';

    /**
     * The console command description.
     */
    protected $description = 'Get payment methods.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $payment_methods = uyumapi('SELECT payment_method_id, description, payment_method_code, payment_method_type, is_shown_on_b2b, ispassive from ftmd_payment_method ');
        foreach ($payment_methods as $payment_method) {
            $id = $payment_method['payment_method_id']; unset($payment_method['payment_method_id']);
            PaymentMethod::updateOrCreate(['id' => $id], $payment_method);
        }


    }


}
