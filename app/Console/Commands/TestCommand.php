<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use App\Models\User;


class TestCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'app:test';

    /**
     * The console command description.
     */
    protected $description = 'Run test.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $user = User::find(1);
        $user->updated_at = now();
        $user->save();
        Log::info('Test command executed.');

    }



}
