<?php

namespace App\Console\Commands;

use App\Models\Product;
use App\Models\ProductBranch;
use Illuminate\Console\Command;

class ProductBranchCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'get:product-branch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $items = uyumapi('SELECT branch_item_id, co_id, branch_id, item_id, is_open_to_internet, unit_id FROM invd_branch_item where co_id = 2725');

        foreach ($items as $item) {
            $id = $item['branch_item_id']; unset($item['branch_item_id']);
            ProductBranch::updateOrCreate(['id' => $id], $item);
        }

        Product::query()->update(['is_open_to_internet' => 0]);
        $items = Product::whereIn('id', ProductBranch::where('is_open_to_internet', 1)->pluck('item_id')->toArray());
        $items->update(['is_open_to_internet' => 1]);

    }
}
