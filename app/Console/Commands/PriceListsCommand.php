<?php

namespace App\Console\Commands;

use App\Models\PriceList;
use Illuminate\Console\Command;

class PriceListsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:price-lists';

    /**
     * The console command description.
     */
    protected $description = 'Get price lists.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $price_lists = uyumapi('SELECT price_list_m_id, branch_id, co_id, entity_id, price_list_id, purchase_sales, start_date, end_date, description_1, zz_ton_price, zz_brand_id, ispassive, create_date as created_at, update_date as updated_at from invt_price_list_m ');
        foreach ($price_lists as $price_list) {

            if ($price_list['updated_at'] == '0001-01-01T00:00:00') {
                $price_list['updated_at'] = null;
            } else {
                $price_list['updated_at'] = date('Y-m-d H:m:s', strtotime($price_list['updated_at']));
            }
            if ($price_list['created_at'] == '0001-01-01T00:00:00') {
                $price_list['created_at'] = null;
            } else {
                $price_list['created_at'] = date('Y-m-d H:m:s', strtotime($price_list['created_at']));
            }
            $id = $price_list['price_list_m_id']; unset($price_list['price_list_m_id']);
            PriceList::updateOrCreate(['id' => $id], $price_list);
        }
    }
}
