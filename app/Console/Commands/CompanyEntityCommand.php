<?php

namespace App\Console\Commands;

use App\Models\CompanyEntity;
use Illuminate\Console\Command;
use App\Models\Entity;

class CompanyEntityCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:company-entity';

    /**
     * The console command description.
     */
    protected $description = 'Get item units.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $co_entitys = uyumapi('SELECT co_entity_id, co_id, entity_id, sales_person_id, due_day from find_co_entity  ORDER BY update_date desc, create_date limit 100');
        foreach ($co_entitys as $co_entity) {
            $id = $co_entity['co_entity_id']; unset($co_entity['co_entity_id']);
            CompanyEntity::updateOrCreate(['id' => $id], $co_entity);
        }
        $co_entity_ids = uyumapi('Select co_entity_id fROM FIND_CO_ENTITY_B2B  where branch_id = 6774 and is_default = 1');
        CompanyEntity::whereNotNull('is_default')->update(['is_default' => null]);
        CompanyEntity::whereIn('id', $co_entity_ids)->update(['is_default' => 1]);

        $entity_ids = CompanyEntity::whereNotNull('is_default')->pluck('entity_id')->toArray();
        Entity::whereNotNull('is_default')->update(['is_default' => null]);
        Entity::whereIn('id', $entity_ids)->update(['is_default' => 1]);
    }


}
