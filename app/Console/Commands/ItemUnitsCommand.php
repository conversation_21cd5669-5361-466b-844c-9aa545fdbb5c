<?php

namespace App\Console\Commands;

use App\Models\ItemUnit;
use Illuminate\Console\Command;

class ItemUnitsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:item-units';

    /**
     * The console command description.
     */
    protected $description = 'Get item units.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
            $banks = uyumapi('SELECT item_unit_id,depth,height,item_id,line_no,rate2,unit2_id,unit_id,volume,weight,width,rate from invd_item_unit');
            foreach ($banks as $bank) {
                $id = $bank['item_unit_id']; unset($bank['item_unit_id']);
                ItemUnit::updateOrCreate(['id' => $id], $bank);
            }
    }



}
