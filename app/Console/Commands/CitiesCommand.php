<?php

namespace App\Console\Commands;

use App\Models\City;
use Illuminate\Console\Command;

class CitiesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:cities';

    /**
     * The console command description.
     */
    protected $description = 'Get cities.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $cities = uyumapi('SELECT city_id, city_name, country_id from gnld_city ');
        foreach ($cities as $city) {
            $id = $city['city_id']; unset($city['city_id']);
            City::updateOrCreate(['id' => $id], $city);
        }

    }

}
