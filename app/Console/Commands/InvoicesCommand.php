<?php

namespace App\Console\Commands;

use App\Models\Invoice;
use Illuminate\Console\Command;

class InvoicesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:invoices';

    /**
     * The console command description.
     */
    protected $description = 'Get invoices.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $invoices = uyumapi('SELECT invoice_m_id, address1, address2, address3, amt, amt_vat, amt_tra, amt_vat_tra, country_id, cur_tra_id, city_id, town_id, co_id, branch_id, doc_date, due_date, doc_no, entity_id, doc_tra_id, e_doc_no, purchase_sales, e_invoice_status, request_status, is_cancel_invoice, purchase_sales, guid_id FROM psmt_invoice_m  ORDER BY update_date DESC, create_date DESC LIMIT 100');
        foreach ($invoices as $invoice) {

            if ($invoice['e_invoice_status'] === 98) {
                $invoice['guid_id'] = null;
            }
            $id = $invoice['invoice_m_id']; unset($invoice['invoice_m_id']);

            Invoice::updateOrCreate(['id' => $id], $invoice);
        }
    }

}
