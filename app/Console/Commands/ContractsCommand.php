<?php

namespace App\Console\Commands;

use App\Models\Contract;
use App\Models\ContractExpense;
use App\Models\Entity;
use Illuminate\Console\Command;

class ContractsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contracts';

    /**
     * The console command description.
     */
    protected $description = 'Get contracts.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $contracts = uyumapi('SELECT o.form_contract_m_id, o.doc_no, o.doc_date, o.sales_person_id, o.description, o.ispassive, o.request_status, o.note_large, o.entity_id, o.due_day, o.contract_start_date, o.doc_tra_id, o.contract_end_date, o.amt, o.cur_tra_id, o.zz_ton_price, d.description as doc_description
FROM fint_form_contract_m o 
left join gnld_doc_tra d on o.doc_tra_id = d.doc_tra_id
');

            $ids = [];
            foreach ($contracts as $contract) {
                $id = $contract['form_contract_m_id']; unset($contract['form_contract_m_id']);
                if (empty($contract['zz_ton_price'])) {
                    $contract['zz_ton_price'] = 0;
                }
                if ($contract['doc_no'] == $contract['description']) $contract['description'] = null;
                $ids[] = $id;

                $contract['used_amount'] = ContractExpense::where('form_contract_m_id', $id)->where('plus_minus', 0)->where('input_output', -1)->sum('amt_tra');

                Contract::updateOrCreate(['id' => $id], $contract);
            }
            Contract::whereNotIn('id', $ids)->delete();

            // update total amounts
        $contracts = Contract::select('entity_id')->groupBy('entity_id')->where('ispassive', 0)->get();
        Entity::whereNotNull('contract_total_amount')->update(['contract_total_amount' => null, 'contract_used_amount' => null]);
        foreach ($contracts as $contract) {

            $contract_total_amount = Contract::where('entity_id', $contract->entity_id)->sum('amt');
            $contract_used_amount = Contract::where('entity_id', $contract->entity_id)->sum('used_amount');

            Entity::where('id', $contract->entity_id)->update(['contract_total_amount' => $contract_total_amount, 'contract_used_amount' => $contract_used_amount]);
        }

    }


}
