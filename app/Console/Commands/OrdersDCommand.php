<?php

namespace App\Console\Commands;

use App\Models\OrderD;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class OrdersDCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:orders-d';

    /**
     * The console command description.
     */
    protected $description = 'Get orders.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $orders = uyumapi('SELECT order_d_id, order_m_id, amt_tra, amt, amt_vat, item_id,line_no,note1, qty, qty_shipping, zz_qty2, doc_date, unit_price
        FROM psmt_order_d
        WHERE purchase_sales = 2 AND co_id = 2725
 ORDER BY order_d_id DESC LIMIT 100
 
');
        foreach ($orders as $order) {
            $id = $order['order_d_id']; unset($order['order_d_id']);
            $order['doc_date'] = date('Y-m-d', strtotime($order['doc_date']));
            OrderD::updateOrCreate(['id' => $id], $order);
        }
    }

}
