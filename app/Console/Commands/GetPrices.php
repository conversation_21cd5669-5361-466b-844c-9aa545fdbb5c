<?php

namespace App\Console\Commands;

use App\Models\Price;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class GetPrices extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */

    protected $signature = 'app:get-prices {--batch-size=1000} {--chunk-size=500}';
    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $batchSize = $this->option('batch-size');
        $chunkSize = $this->option('chunk-size');

        $this->info('Starting price import...');

        // Önce toplam kayıt sayısını öğren
        $totalCount = uyumapi("SELECT COUNT(*) as total FROM invt_price_list_d")[0]['total'] ?? 0;
        // tüm data gelmeyecekse
        // $totalCount = 1000;
        $this->info("Total records to process: {$totalCount}");

        // Mevcut kayıtlar<PERSON> belleğe al (performans için)
        $existingIds = Price::pluck('id')->flip()->toArray();

        $offset = 0;
        $totalProcessed = 0;

        while ($offset < $totalCount) {
            // Batch halinde veri çek
            $price_lists = uyumapi("
                SELECT 
                    price_list_d_id,price_list_id,price_list_m_id,branch_id,co_id,
                    cur_tra_id,end_date,entity_id,entity_item_id,entity_price_grp_id,
                    ispassive,item_attribute1_id,item_attribute2_id,item_attribute3_id,
                    item_id,item_price_grp_id,package_type_id,purchase_sales,quality_id,
                    start_date,unit_id,unit_price_tra,unit_price_tra_max,unit_price_tra_min,
                    vat_status,line_no,line_type,dcard_id,rule_id,rate,lot_id,
                    create_date as created_at, update_date as updated_at 
                FROM invt_price_list_d  
                ORDER BY price_list_d_id DESC 
                LIMIT {$batchSize}  OFFSET {$offset}
            ");

            if (empty($price_lists)) {
                $this->warn("No more records found at offset {$offset}");
                break;
            }

            $recordCount = count($price_lists);

            // Veriyi chunk'lara böl ve işle
            $chunks = array_chunk($price_lists, $chunkSize);

            foreach ($chunks as $chunk) {
                $this->processChunk($chunk, $existingIds);
            }

            $totalProcessed += $recordCount;
            $this->info("Processed: {$totalProcessed}/{$totalCount} records (" . round(($totalProcessed / $totalCount) * 100, 2) . "%)");

            $offset += $batchSize;

            // Memory temizliği
            unset($price_lists, $chunks);

            // API'ye çok yük binmesin diye kısa bekleme
            usleep(100000); // 0.1 saniye
        }

        $this->info("Import completed. Total processed: {$totalProcessed}");
    }

    /**
     * Chunk'ı işle
     */
    private function processChunk(array $chunk, array &$existingIds)
    {
        $insertsData = [];
        $updatesData = [];

        foreach ($chunk as $price_list) {
            $id = $price_list['price_list_d_id'];
            unset($price_list['price_list_d_id']);

            // Tarih formatlarını düzelt
            $price_list['created_at'] = $this->formatDate($price_list['created_at']);
            $price_list['updated_at'] = $this->formatDate($price_list['updated_at']);

            // Mevcut kayıt kontrolü
            if (isset($existingIds[$id])) {
                $updatesData[] = array_merge($price_list, ['id' => $id]);
            } else {
                $insertsData[] = array_merge($price_list, ['id' => $id]);
                $existingIds[$id] = true; // Yeni eklenen ID'yi kaydet
            }
        }

        // Bulk insert
        if (!empty($insertsData)) {
            $this->bulkInsert($insertsData);
        }

        // Bulk update
        if (!empty($updatesData)) {
            $this->bulkUpdate($updatesData);
        }
    }

    /**
     * Bulk insert işlemi
     */
    private function bulkInsert(array $data)
    {
        try {
            Price::insert($data);
        } catch (\Exception $e) {
            $this->error("Bulk insert error: " . $e->getMessage());
            // Hata durumunda tek tek insert dene
            foreach ($data as $item) {
                try {
                    Price::create($item);
                } catch (\Exception $e) {
                    $this->error("Individual insert error for ID {$item['id']}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Bulk update işlemi
     */
    private function bulkUpdate(array $data)
    {
        DB::transaction(function () use ($data) {
            foreach ($data as $item) {
                $id = $item['id'];
                unset($item['id']);

                try {
                    Price::where('id', $id)->update($item);
                } catch (\Exception $e) {
                    $this->error("Update error for ID {$id}: " . $e->getMessage());
                }
            }
        });
    }

    /**
     * Tarih formatını düzelt
     */
    private function formatDate($date)
    {
        if ($date === '0001-01-01T00:00:00' || empty($date)) {
            return null;
        }

        try {
            return date('Y-m-d H:i:s', strtotime($date));
        } catch (\Exception $e) {
            return null;
        }
    }
}

// ALTERNATIVE VERSION: Daha da hızlı için Raw SQL kullanımı

class PricesCommandUltraFast extends Command
{
    protected $signature = 'get:prices-ultra {--batch=2000}';
    protected $description = 'Ultra fast price import using raw SQL.';

    public function handle()
    {
        $batchSize = $this->option('batch-size');
        $offset = 0;
        $totalProcessed = 0;

        do {
            $price_lists = uyumapi("
                SELECT 
                    price_list_d_id,price_list_id,price_list_m_id,branch_id,co_id,
                    cur_tra_id,end_date,entity_id,entity_item_id,entity_price_grp_id,
                    ispassive,item_attribute1_id,item_attribute2_id,item_attribute3_id,
                    item_id,item_price_grp_id,package_type_id,purchase_sales,quality_id,
                    start_date,unit_id,unit_price_tra,unit_price_tra_max,unit_price_tra_min,
                    vat_status,line_no,line_type,dcard_id,rule_id,rate,lot_id,
                    create_date, update_date
                FROM invt_price_list_d  
                ORDER BY update_date DESC, create_date DESC 
                LIMIT {$batchSize} OFFSET {$offset}
            ");

            if (empty($price_lists)) {
                break;
            }

            $this->upsertWithRawSQL($price_lists);

            $totalProcessed += count($price_lists);
            $this->info("Processed: {$totalProcessed} records");
            $offset += $batchSize;

        } while (count($price_lists) === $batchSize);

        $this->info("Ultra fast import completed. Total: {$totalProcessed}");
    }

    private function upsertWithRawSQL(array $data)
    {
        if (empty($data)) return;

        $values = [];
        $columns = [
            'id', 'price_list_id', 'price_list_m_id', 'branch_id', 'co_id',
            'cur_tra_id', 'end_date', 'entity_id', 'entity_item_id', 'entity_price_grp_id',
            'ispassive', 'item_attribute1_id', 'item_attribute2_id', 'item_attribute3_id',
            'item_id', 'item_price_grp_id', 'package_type_id', 'purchase_sales', 'quality_id',
            'start_date', 'unit_id', 'unit_price_tra', 'unit_price_tra_max', 'unit_price_tra_min',
            'vat_status', 'line_no', 'line_type', 'dcard_id', 'rule_id', 'rate', 'lot_id',
            'created_at', 'updated_at'
        ];

        foreach ($data as $row) {
            $created_at = $this->formatDateForSQL($row['create_date']);
            $updated_at = $this->formatDateForSQL($row['update_date']);

            $values[] = sprintf(
                "(%d, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, '%s', %s)",
                $row['price_list_d_id'],
                $this->nullOrQuote($row['price_list_id']),
                $this->nullOrQuote($row['price_list_m_id']),
                $this->nullOrQuote($row['branch_id']),
                $this->nullOrQuote($row['co_id']),
                $this->nullOrQuote($row['cur_tra_id']),
                $this->nullOrQuote($row['end_date']),
                $this->nullOrQuote($row['entity_id']),
                $this->nullOrQuote($row['entity_item_id']),
                $this->nullOrQuote($row['entity_price_grp_id']),
                $this->nullOrQuote($row['ispassive']),
                $this->nullOrQuote($row['item_attribute1_id']),
                $this->nullOrQuote($row['item_attribute2_id']),
                $this->nullOrQuote($row['item_attribute3_id']),
                $this->nullOrQuote($row['item_id']),
                $this->nullOrQuote($row['item_price_grp_id']),
                $this->nullOrQuote($row['package_type_id']),
                $this->nullOrQuote($row['purchase_sales']),
                $this->nullOrQuote($row['quality_id']),
                $this->nullOrQuote($row['start_date']),
                $this->nullOrQuote($row['unit_id']),
                $this->nullOrQuote($row['unit_price_tra']),
                $this->nullOrQuote($row['unit_price_tra_max']),
                $this->nullOrQuote($row['unit_price_tra_min']),
                $this->nullOrQuote($row['vat_status']),
                $this->nullOrQuote($row['line_no']),
                $this->nullOrQuote($row['line_type']),
                $this->nullOrQuote($row['dcard_id']),
                $this->nullOrQuote($row['rule_id']),
                $this->nullOrQuote($row['rate']),
                $this->nullOrQuote($row['lot_id']),
                $created_at,
                $updated_at ?: 'NULL'
            );
        }

        $sql = sprintf(
            "INSERT INTO prices (%s) VALUES %s ON DUPLICATE KEY UPDATE %s",
            implode(', ', $columns),
            implode(', ', $values),
            implode(', ', array_map(function ($col) {
                return $col === 'id' ? '' : "{$col} = VALUES({$col})";
            }, array_filter($columns, function ($col) {
                return $col !== 'id';
            })))
        );

        DB::statement($sql);
    }

    private function nullOrQuote($value)
    {
        return $value === null ? 'NULL' : "'" . addslashes($value) . "'";
    }

    private function formatDateForSQL($date)
    {
        if ($date === '0001-01-01T00:00:00' || empty($date)) {
            return 'NULL';
        }
        return "'" . date('Y-m-d H:i:s', strtotime($date)) . "'";
    }

}
