<?php

namespace App\Console\Commands;

use App\Models\DocTra;
use Illuminate\Console\Command;

class DocTraCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:doc-tra';

    /**
     * The console command description.
     */
    protected $description = 'Get addresses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $banks = uyumapi('SELECT doc_tra_id, description, doc_tra_code from GNLD_DOC_TRA');

        foreach ($banks as $bank) {
            $id = $bank['doc_tra_id']; unset($bank['doc_tra_id']);
            DocTra::updateOrCreate(['id' => $id], $bank);
        }


    }



}
