<?php

namespace App\Console\Commands;

use App\Models\Warehouse;
use Illuminate\Console\Command;

class WarehousesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:warehouses';

    /**
     * The console command description.
     */
    protected $description = 'Get warehouses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $warehouses = uyumapi('SELECT whouse_id, whouse_code, description from invd_whouse ');
        foreach ($warehouses as $warehouse) {
            $id = $warehouse['whouse_id']; unset($warehouse['whouse_id']);
            Warehouse::updateOrCreate(['id' => $id], $warehouse);
        }
    }


}
