<?php

namespace App\Console\Commands;

use App\Models\Category;
use Illuminate\Console\Command;

class CategoriesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:categories';

    /**
     * The console command description.
     */
    protected $description = 'Get categories.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


        $categories = uyumapi('SELECT categories_id, categories_code, description, step, parent_cat_id, categories_page, zz_b2b from gnld_categories ');
        foreach ($categories as $category) {
            $id = $category['categories_id']; unset($category['categories_id']);
            $data = [
                'categories_code' => $category['categories_code'],
                'description' => $category['description'],
                'step' => $category['step'],
                'parent_cat_id' => $category['parent_cat_id'],
                'categories_page' => $category['categories_page'],
                'zz_b2b' => $category['zz_b2b'],
                'group_name' => 'uyumsoft',
            ];

            Category::updateOrCreate(['id' => $id], $data);
        }


    }


}
