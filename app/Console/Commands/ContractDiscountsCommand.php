<?php
namespace App\Console\Commands;

use App\Models\ContractDiscount;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ContractDiscountsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contract-discounts {--batch-size=1000} {--chunk-size=500}';

    /**
     * The console command description.
     */
    protected $description = 'Get prices with optimized performance.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $batchSize = $this->option('batch-size');
        $chunkSize = $this->option('chunk-size');

        $this->info('Starting price import...');

        // Önce toplam kayıt sayısını öğren
       // $totalCount = uyumapi("SELECT COUNT(*) as total FROM invt_price_list_d")[0]['total'] ?? 0;
        // tüm data gelmeyecekse
        $totalCount = 1000;
        $this->info("Total records to process: {$totalCount}");

        // Mevcut kayıtları belleğe al (performans için)
        $existingIds = ContractDiscount::pluck('id')->flip()->toArray();

        $offset = 0;
        $totalProcessed = 0;

        while ($offset < $totalCount) {
            // Batch halinde veri çek
            $price_lists = uyumapi("
                SELECT 
                   form_contrat_plist_id, form_contract_m_id, item_id, disc1_id, disc2_id, disc3_id, dcard_id
                FROM FINT_FORM_CONTRACT_DISCOUNT  
                ORDER BY form_contrat_plist_id DESC 
                LIMIT {$batchSize} OFFSET {$offset}
            ");

            if (empty($price_lists)) {
                $this->warn("No more records found at offset {$offset}");
                break;
            }

            $recordCount = count($price_lists);

            // Veriyi chunk'lara böl ve işle
            $chunks = array_chunk($price_lists, $chunkSize);

            foreach ($chunks as $chunk) {
                $this->processChunk($chunk, $existingIds);
            }

            $totalProcessed += $recordCount;
            $this->info("Processed: {$totalProcessed}/{$totalCount} records (" . round(($totalProcessed/$totalCount)*100, 2) . "%)");

            $offset += $batchSize;

            // Memory temizliği
            unset($price_lists, $chunks);

            // API'ye çok yük binmesin diye kısa bekleme
            usleep(100000); // 0.1 saniye
        }

        $this->info("Import completed. Total processed: {$totalProcessed}");
    }

    /**
     * Chunk'ı işle
     */
    private function processChunk(array $chunk, array &$existingIds)
    {
        $insertsData = [];
        $updatesData = [];
        foreach ($chunk as $price_list) {
            $id = $price_list['form_contrat_plist_id'];
            unset($price_list['form_contrat_plist_id']);

            // Mevcut kayıt kontrolü
            if (isset($existingIds[$id])) {
                $updatesData[] = array_merge($price_list, ['id' => $id]);
            } else {
                $insertsData[] = array_merge($price_list, ['id' => $id]);
                $existingIds[$id] = true; // Yeni eklenen ID'yi kaydet
            }
        }

        // Bulk insert
        if (!empty($insertsData)) {
            $this->bulkInsert($insertsData);
        }

        // Bulk update
        if (!empty($updatesData)) {
            $this->bulkUpdate($updatesData);
        }
    }

    /**
     * Bulk insert işlemi
     */
    private function bulkInsert(array $data)
    {
        try {
            ContractDiscount::insert($data);
        } catch (\Exception $e) {
            $this->error("Bulk insert error: " . $e->getMessage());
            // Hata durumunda tek tek insert dene
            foreach ($data as $item) {
                try {
                    ContractDiscount::create($item);
                } catch (\Exception $e) {
                    $this->error("Individual insert error for ID {$item['id']}: " . $e->getMessage());
                }
            }
        }
    }

    /**
     * Bulk update işlemi
     */
    private function bulkUpdate(array $data)
    {
        DB::transaction(function () use ($data) {
            foreach ($data as $item) {
                $id = $item['id'];
                unset($item['id']);

                try {
                    ContractDiscount::where('id', $id)->update($item);
                } catch (\Exception $e) {
                    $this->error("Update error for ID {$id}: " . $e->getMessage());
                }
            }
        });
    }

    /**
     * Tarih formatını düzelt
     */
    private function formatDate($date)
    {
        if ($date === '0001-01-01T00:00:00' || empty($date)) {
            return null;
        }

        try {
            return date('Y-m-d H:i:s', strtotime($date));
        } catch (\Exception $e) {
            return null;
        }
    }
}
