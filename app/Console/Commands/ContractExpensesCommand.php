<?php

namespace App\Console\Commands;

use App\Models\ContractExpense;
use Illuminate\Console\Command;

class ContractExpensesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:contract-expenses';

    /**
     * The console command description.
     */
    protected $description = 'Get contract expenses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $banks = uyumapi('SELECT form_contract_d_id, form_contract_m_id, source_app, source_app2, plus_minus, input_output,qty,amt,amt_tra from fint_form_contract_d where source_app = 1000 ORDER BY update_date DESC, create_date DESC LIMIT 100');
            foreach ($banks as $bank) {
                $id = $bank['form_contract_d_id']; unset($bank['form_contract_d_id']);
                ContractExpense::updateOrCreate(['id' => $id], $bank);
            }


    }



}
