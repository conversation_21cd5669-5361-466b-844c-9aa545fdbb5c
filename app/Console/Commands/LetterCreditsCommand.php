<?php

namespace App\Console\Commands;

use App\Models\LetterCredit;
use Illuminate\Console\Command;

class LetterCreditsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:letter-credits';

    /**
     * The console command description.
     */
    protected $description = 'Get currency rates.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $letter_credits = uyumapi('SELECT * from fint_letter_credit');
        foreach ($letter_credits as $credit_letter) {
            $id = $credit_letter['letter_credit_id']; unset($credit_letter['letter_credit_id']);
            $create_date = date('Y-m-d H:m:s', strtotime($credit_letter['create_date']));
            $update_date = null;
            if ($credit_letter['update_date'] !== '0001-01-01T00:00:00') {
                $update_date = date('Y-m-d H:m:s', strtotime($credit_letter['update_date']));
            }
            $data = [
                'letter_credit_no' => $credit_letter['letter_credit_no'],
                'due_date' => date('Y-m-d', strtotime($credit_letter['due_date'])),
                'doc_date' => date('Y-m-d', strtotime($credit_letter['doc_date'])),
                'cur_id' => $credit_letter['cur_id'],
                'entity_id' => $credit_letter['entity_id'],
                'co_id' => $credit_letter['co_id'],
                'bank_id' => $credit_letter['bank_id'],
                'bank_branch_id' => $credit_letter['bank_branch_id'],
                'credit_acc_id' => $credit_letter['credit_acc_id'],
                'cur_tra_id' => $credit_letter['cur_tra_id'],
                'amt_tra' => $credit_letter['amt_tra'],
                'payment_amt_tra' => $credit_letter['payment_amt_tra'],
                'amt' => $credit_letter['amt'],
                'letter_credit_status' => $credit_letter['letter_credit_status'],
                'note1' => $credit_letter['note1'],
                'note2' => $credit_letter['note2'],
                'create_date' => $create_date,
                'update_date' => $update_date,
            ];
            LetterCredit::updateOrCreate(['id' => $id], $data);
        }
    }


}
