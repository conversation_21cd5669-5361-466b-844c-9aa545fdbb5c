<?php

namespace App\Console\Commands;

use App\Models\OfferD;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class OffersDCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:offers-d';

    /**
     * The console command description.
     */
    protected $description = 'Get offers.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $orders = uyumapi('SELECT offer_d_id, offer_m_id, offer_status, purchase_sales, qty_demand, qty_rejected,qty_reserved,qty_shipping, amt, amt_tra, amt_vat, cur_rate_tra, cur_rate_type_id,cur_tra_id,qty,vat_rate,unit_price,unit_price_tra,vat_id,vat_status,line_type,due_day,doc_date,branch_id,co_id,dcard_id,item_id,note1,note2,source_app,unit_id,whouse_id,contact_id,request_status,sales_person_id,default_asort_mode,amt_with_disc_tra,reservation_type,entity_offer_date
        FROM psmt_offer_d
        where offer_d_id between 55000 and 60000
 ORDER BY offer_d_id DESC');

        foreach ($orders as $order) {
            $id = $order['offer_d_id']; unset($order['offer_d_id']);
            $order['doc_date'] = date('Y-m-d', strtotime($order['doc_date']));
            OfferD::updateOrCreate(['id' => $id], $order);
        }
    }

}
