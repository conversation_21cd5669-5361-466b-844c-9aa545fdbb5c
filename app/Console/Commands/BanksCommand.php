<?php

namespace App\Console\Commands;

use App\Models\Bank;
use Illuminate\Console\Command;

class BanksCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:banks';

    /**
     * The console command description.
     */
    protected $description = 'Get banks.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $banks = uyumapi('SELECT * from find_bank_card');
            foreach ($banks as $bank) {
                $id = $bank['bank_id']; unset($bank['bank_id']);
                Bank::updateOrCreate(['id' => $id], $bank);
            }


    }



}
