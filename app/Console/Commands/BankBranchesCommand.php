<?php

namespace App\Console\Commands;

use App\Models\BankBranch;
use Illuminate\Console\Command;

class BankBranchesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:bank-branches';

    /**
     * The console command description.
     */
    protected $description = 'Get bank branches.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {>
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


            $bank_branches = uyumapi('SELECT * from find_bank_branch');
            foreach ($bank_branches as $bank_branch) {
                $id = $bank_branch['bank_branch_id'];
                unset($bank_branch['bank_branch_id']);
                $data = [
                    'bank_id' => $bank_branch['bank_id'],
                    'description' => $bank_branch['description'],
                ];
                BankBranch::updateOrCreate(['id' => $id], $data);
            }


    }

}
