<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderStatusCommand extends Command
{
    protected $signature = 'get:orders:status 
                            {--chunk=500 : Number of orders to process at once}
                            {--all : Check all orders regardless of status}';

    protected $description = 'Check and update order statuses from API';

    const STATUS_CLOSED = 1;
    const STATUS_OPEN = 2;

    private $stats = [
        'total' => 0,
        'checked' => 0,
        'updated' => 0,
        'closed' => 0,
        'reopened' => 0,
        'not_found' => 0,
        'errors' => 0
    ];

    public function handle()
    {
        $startTime = microtime(true);
        $chunkSize = $this->option('chunk');
        $checkAll = $this->option('all');

        $this->info('Starting order status check...');
        $this->info('Chunk size: ' . $chunkSize);

        try {
            // Toplam sipariş sayısını al
            $query = Order::query();
            if (!$checkAll) {
                // Sadece açık siparişleri kontrol et
                $query->where('order_status', self::STATUS_OPEN);
            }

            $this->stats['total'] = $query->count();
            $this->info("Total orders to check: {$this->stats['total']}");

            if ($this->stats['total'] == 0) {
                $this->info('No orders to check.');
                return 0;
            }

            // Progress bar oluştur
            $progressBar = $this->output->createProgressBar($this->stats['total']);
            $progressBar->start();

            // Siparişleri chunk halinde işle
            $query->chunk($chunkSize, function ($orders) use ($progressBar) {
                $this->processOrderChunk($orders);
                $progressBar->advance($orders->count());
            });

            $progressBar->finish();
            $this->info(''); // Yeni satır

            // Sonuçları göster
            $this->displayResults($startTime);

        } catch (\Exception $e) {
            $this->error('Error occurred: ' . $e->getMessage());
            Log::error('Order status check error', [
                'exception' => $e,
                'stats' => $this->stats
            ]);
            return 1;
        }

        return 0;
    }

    private function processOrderChunk($orders)
    {
        $orderIds = $orders->pluck('id')->toArray();

        if (empty($orderIds)) {
            return;
        }

        try {
            // API'den sipariş durumlarını al
            $apiData = $this->fetchOrderStatusesFromApi($orderIds);

            // Her siparişi kontrol et ve güncelle
            DB::transaction(function () use ($orders, $apiData) {
                foreach ($orders as $order) {
                    $this->stats['checked']++;

                    if (isset($apiData[$order->id])) {
                        $this->updateOrderIfNeeded($order, $apiData[$order->id]);
                    } else {
                        // API'de bulunamayan sipariş
                        $this->stats['not_found']++;
                        Log::warning('Order not found in API', [
                            'order_id' => $order->id,
                            'doc_no' => $order->doc_no
                        ]);
                    }
                }
            });

        } catch (\Exception $e) {
            $this->stats['errors'] += $orders->count();
            Log::error('Error processing order chunk', [
                'order_ids' => $orderIds,
                'exception' => $e->getMessage()
            ]);
        }

        // API'yi yormamak için kısa bekleme
        usleep(100000); // 0.1 saniye
    }

    private function fetchOrderStatusesFromApi(array $orderIds)
    {
        $idList = implode(',', $orderIds);

        $query = "
            SELECT order_m_id, order_status, update_date, doc_no
            FROM psmt_order_m
            WHERE order_m_id IN ({$idList})
        ";

        $apiOrders = uyumapi($query);

        $result = [];
        foreach ($apiOrders as $apiOrder) {
            $result[$apiOrder['order_m_id']] = [
                'status' => $apiOrder['order_status'],
                'updated_at' => $apiOrder['update_date'],
                'doc_no' => $apiOrder['doc_no']
            ];
        }

        return $result;
    }

    private function updateOrderIfNeeded($order, $apiData)
    {
        $currentStatus = (int) $order->order_status;
        $apiStatus = (int) $apiData['status'];

        // Durum değişmişse güncelle
        if ($currentStatus !== $apiStatus) {
            $order->update([
                'order_status' => $apiStatus,
                'updated_at' => $apiData['updated_at']
            ]);

            $this->stats['updated']++;

            // Durum değişikliğini logla
            $statusChange = $this->getStatusChangeDescription($currentStatus, $apiStatus);

            $this->line(sprintf(
                "  Order #%s (%s): %s",
                $order->id,
                $order->doc_no,
                $statusChange
            ));

            // İstatistikleri güncelle
            if ($currentStatus == self::STATUS_OPEN && $apiStatus == self::STATUS_CLOSED) {
                $this->stats['closed']++;
            } elseif ($currentStatus == self::STATUS_CLOSED && $apiStatus == self::STATUS_OPEN) {
                $this->stats['reopened']++;
            }

            // Önemli değişiklikleri logla
            Log::info('Order status changed', [
                'order_id' => $order->id,
                'doc_no' => $order->doc_no,
                'old_status' => $currentStatus,
                'new_status' => $apiStatus,
                'change' => $statusChange
            ]);
        }
    }

    private function getStatusChangeDescription($oldStatus, $newStatus)
    {
        $statusNames = [
            self::STATUS_CLOSED => 'Closed',
            self::STATUS_OPEN => 'Open'
        ];

        $oldName = $statusNames[$oldStatus] ?? "Unknown({$oldStatus})";
        $newName = $statusNames[$newStatus] ?? "Unknown({$newStatus})";

        return "{$oldName} → {$newName}";
    }

    private function displayResults($startTime)
    {
        $duration = round(microtime(true) - $startTime, 2);
        $minutes = floor($duration / 60);
        $seconds = $duration % 60;

        $this->info('');
        $this->info('=== Status Check Complete ===');
        $this->info(sprintf('Duration: %d min %d sec', $minutes, $seconds));
        $this->info('');

        $this->table(
            ['Metric', 'Count'],
            [
                ['Total Orders', number_format($this->stats['total'])],
                ['Checked', number_format($this->stats['checked'])],
                ['Updated', number_format($this->stats['updated'])],
                ['Closed', number_format($this->stats['closed'])],
                ['Reopened', number_format($this->stats['reopened'])],
                ['Not Found in API', number_format($this->stats['not_found'])],
                ['Errors', number_format($this->stats['errors'])],
            ]
        );

        // Performans metrikleri
        if ($duration > 0) {
            $ordersPerSecond = round($this->stats['checked'] / $duration, 2);
            $this->info("Performance: {$ordersPerSecond} orders/second");
        }
    }
}