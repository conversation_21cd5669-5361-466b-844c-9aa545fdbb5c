<?php

namespace App\Console\Commands;

use App\Models\Shipment;
use Illuminate\Console\Command;

class ShipmentsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:shipments';

    /**
     * The console command description.
     */
    protected $description = 'Get shipments.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $shipments = uyumapi('SELECT referral_orders_m_id, co_id, branch_id, doc_date, doc_no, order_doc_no, entity_id, shipping_entity_id, shipping_address1, shipping_address2, shipping_address3, shipping_country_id, shipping_city_id, shipping_town_id, transport_type_id, source_m_id, create_date as created_at, update_date as updated_at from psmt_referral_orders_m ORDER BY updated_at DESC, created_at DESC LIMIT 200');

            foreach ($shipments as $shipment) {
                if ($shipment['updated_at'] == '0001-01-01T00:00:00') {
                    $shipment['updated_at'] = null;
                } else {
                    $shipment['updated_at'] = date('Y-m-d H:m:s', strtotime($shipment['updated_at']));
                }
                if ($shipment['created_at'] == '0001-01-01T00:00:00') {
                    $shipment['created_at'] = null;
                } else {
                    $shipment['created_at'] = date('Y-m-d H:m:s', strtotime($shipment['created_at']));
                }
                $id = $shipment['referral_orders_m_id']; unset($shipment['referral_orders_m_id']);
                Shipment::updateOrCreate(['id' => $id], $shipment);
            }




    }

}
