<?php

namespace App\Console\Commands;

use App\Models\Tax;
use Illuminate\Console\Command;

class TaxesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:taxes';

    /**
     * The console command description.
     */
    protected $description = 'Get taxes.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $taxes = uyumapi('SELECT tax_id, description,tax_code,tax_calc_type,tax_rate,tax_type_id from invd_tax');
        foreach ($taxes as $tax) {
            $id = $tax['tax_id']; unset($tax['tax_id']);
            Tax::updateOrCreate(['id' => $id], $tax);
        }


    }



}
