<?php

namespace App\Console\Commands;

use App\Models\Entity;
use Illuminate\Console\Command;

class EntitiesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:entities';

    /**
     * The console command description.
     */
    protected $description = 'Get entities.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {


        $entities = uyumapi('
SELECT entity_id, entity_type, entity_name, entity_code, address1, address2, address3, zip_code, email, tel1, web_site, tax_no, tax_office_id, city_id, town_id, country_id, create_date as created_at, update_date as updated_at
FROM find_entity
    ORDER BY update_date desc, create_date DESC
limit 100
');
        foreach ($entities as $entity) {
            $id = $entity['entity_id']; unset($entity['entity_id']);

            if ($entity['updated_at'] == '0001-01-01T00:00:00') {
                $entity['updated_at'] = null;
            } else {
                $entity['updated_at'] = date('Y-m-d H:m:s', strtotime($entity['updated_at']));
            }
            if ($entity['created_at'] == '0001-01-01T00:00:00') {
                $entity['created_at'] = null;
            } else {
                $entity['created_at'] = date('Y-m-d H:m:s', strtotime($entity['created_at']));
            }

            Entity::updateOrCreate(['id' => $id], $entity);

        }

    }


}
