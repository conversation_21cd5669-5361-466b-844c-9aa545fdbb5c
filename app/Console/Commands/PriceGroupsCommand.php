<?php

namespace App\Console\Commands;

use App\Models\PriceGroup;
use Illuminate\Console\Command;

class PriceGroupsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:price-groups';

    /**
     * The console command description.
     */
    protected $description = 'Get price groups.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $price_lists = uyumapi('SELECT price_list_id, co_id, description, price_list_code, create_date as created_at, update_date as updated_at from invd_price_list');
        foreach ($price_lists as $price_list) {
            $id = $price_list['price_list_id']; unset($price_list['price_list_id']);
            if ($price_list['updated_at'] == '0001-01-01T00:00:00') {
                $price_list['updated_at'] = null;
            } else {
                $price_list['updated_at'] = date('Y-m-d H:m:s', strtotime($price_list['updated_at']));
            }
            if ($price_list['created_at'] == '0001-01-01T00:00:00') {
                $price_list['created_at'] = null;
            } else {
                $price_list['created_at'] = date('Y-m-d H:m:s', strtotime($price_list['created_at']));
            }
            PriceGroup::updateOrCreate(['id' => $id], $price_list);
        }


    }



}
