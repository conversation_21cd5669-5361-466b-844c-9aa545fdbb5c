<?php

namespace App\Console\Commands;

use App\Models\Currency;
use Illuminate\Console\Command;

class CurrenciesCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:currencies';

    /**
     * The console command description.
     */
    protected $description = 'Get currencies.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

        $gnld_currency = uyumapi('SELECT cur_id, cur_code, cur_symbol, description from gnld_currency limit 100');
        foreach ($gnld_currency as $currency) {
            $id = $currency['cur_id']; unset($currency['cur_id']);
            Currency::updateOrCreate(['id' => $id], $currency);
        }
    }

}
