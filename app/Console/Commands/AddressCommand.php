<?php

namespace App\Console\Commands;

use App\Models\Address;
use Illuminate\Console\Command;

class AddressCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:addresses';

    /**
     * The console command description.
     */
    protected $description = 'Get addresses.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $addresses = uyumapi('SELECT entity_adress_id, city_id, country_id, mobile_tel, note1, tel1, town_id, zip_code, address1, address2, address3, entity_id, ispassive from FIND_ENTITY_ADRESS');
            foreach ($addresses as $addres) {
                Address::updateOrCreate(['entity_adress_id' => $addres['entity_adress_id']], $addres);
            }


    }



}
