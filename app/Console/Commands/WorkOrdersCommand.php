<?php

namespace App\Console\Commands;

use App\Models\PrdtWorderM;
use Illuminate\Console\Command;

class WorkOrdersCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:worders';

    /**
     * The console command description.
     */
    protected $description = 'Get work orders.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $banks = uyumapi('SELECT worder_m_id, co_id, branch_id, item_id, worder_no, entity_id, wo_type_id, worder_type,qty, qty_man, qty_prm, qty_man_prm, qty_planned_rework, qty_wo_actual_rework,unit_id, open_close, worder_status, note_large, note_large2, source_app, source_m_id, source_d_id, prd_source_app, create_date, update_date from PRDT_WORDER_M order by worder_m_id desc limit 1000');
            foreach ($banks as $bank) {

                if ($bank['update_date'] == '0001-01-01T00:00:00') {
                    $bank['update_date'] = null;
                } else {
                    $bank['update_date'] = date('Y-m-d H:m:s', strtotime($bank['update_date']));
                }
                $id = $bank['worder_m_id']; unset($bank['worder_m_id']);
                PrdtWorderM::updateOrCreate(['id' => $id], $bank);
            }


    }



}
