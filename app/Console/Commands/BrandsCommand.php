<?php

namespace App\Console\Commands;

use App\Models\Brand;
use Illuminate\Console\Command;

class BrandsCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'get:brands';

    /**
     * The console command description.
     */
    protected $description = 'Get brands.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {

            $brands = uyumapi('SELECT brand_id, description from invd_brand');
            foreach ($brands as $brand) {
                $id = $brand['brand_id']; unset($brand['brand_id']);
                Brand::updateOrCreate(['id' => $id], $brand);
            }


    }



}
