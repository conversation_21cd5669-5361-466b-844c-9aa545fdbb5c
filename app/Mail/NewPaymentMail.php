<?php

// app/Mail/NewPaymentMail.php
namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class NewPaymentMail extends Mailable
{
    use Queueable, SerializesModels;

    public $name;
    public $user;
    public $input;  // public olarak tanımladık

    public function __construct($data)
    {
        $this->name = $data['name'];
        $this->user = $data['user'];
        $this->input = $data['input'];  // input değişkenini atadık
    }

    public function build()
    {
        return $this->view('emails.new_payment')
            ->from('<EMAIL>', env('APP_NAME', 'B2B Portal'))
            ->subject('Yeni Kart Çekimi');
    }
}