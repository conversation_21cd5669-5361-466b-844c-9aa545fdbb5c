<?php

namespace App\Listeners\Auth;

use App\Events\Auth\UserLoginSuccess;
use Carbon\Carbon;

class UpdateProfileLoginData
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(UserLoginSuccess $event)
    {
        try {
            $user = $event->user;

            $request = $event->request;
            $user_profile = $user->userprofile;

            /*
             * Updating user profile data after successful login
             */
            if ($user_profile) {
                $user_profile->last_login = Carbon::now();
                $user_profile->last_ip = $request->getClientIp();
                $user_profile->login_count += 1;
                $user_profile->save();
            }

        } catch (\Exception $e) {
            logger()->error($e);
        }

        logger('Kullanıcı Oturum Açma Başarılı. Ad: '.$user->name.' | Id: '.$user->id.' | Email: '.$user->email.' | Kullanıcı adı: '.$user->username.' IP:'.$request->getClientIp().' | UpdateProfileLoginData');
    }
}
