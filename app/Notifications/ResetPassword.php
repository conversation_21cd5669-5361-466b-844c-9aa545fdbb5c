<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Lang;

class ResetPassword extends Notification
{
    use Queueable;

    /**
     * Create a new notification instance.
     */

    public function __construct($token)
    {
        $this->token = $token;
    }
    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject(Lang::get('Şifre <PERSON>ıfırlama İşlemi'))
            ->line(Lang::get('Bu e-postayı, hesabınız için bir şifre sıfırlama isteği aldığımız için alıyorsunuz.'))
            ->action(Lang::get('<PERSON><PERSON><PERSON>'), url(config('app.url').route('password.reset', ['token' => $this->token, 'email' => $notifiable->getEmailForPasswordReset()], false)));
//            ->line(Lang::get('Bu şifre sıfırlama bağlantısı :count dakika içinde sona erecektir.', ['count' => config('auth.passwords.'.config('auth.defaults.passwords').'.expire')]))
//            ->line(Lang::get('Şifre sıfırlama talebinde bulunmadıysanız başka bir işlem yapmanıza gerek yoktur.'));
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            //
        ];
    }
}
