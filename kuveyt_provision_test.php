<?php

/**
 * KuveytTurk POS Provision Test
 * 
 * Bu script, provision request'in doğru çalışıp çalışmadığını test eder.
 */

echo "=== KuveytTurk POS Provision Test ===\n\n";

// Test 1: XML Request Builder
echo "Test 1: XML Request Builder\n";
echo "----------------------------\n";

function buildKuveytPosProvisionXml(array $data): string
{
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>1.0.0</APIVersion>';
    $xml .= '<HashData></HashData>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<CardNumber></CardNumber>';
    $xml .= '<CardExpireDateYear></CardExpireDateYear>';
    $xml .= '<CardExpireDateMonth></CardExpireDateMonth>';
    $xml .= '<CardCVV2></CardCVV2>';
    $xml .= '<CardHolderName></CardHolderName>';
    $xml .= '<CardType></CardType>';
    $xml .= '<BatchID>0</BatchID>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<DisplayAmount>' . htmlspecialchars($data['Amount']) . '</DisplayAmount>';
    $xml .= '<CurrencyCode>0949</CurrencyCode>';
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>';
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

// Gerçek verilerle test
$testData = [
    'MerchantId' => '588693',
    'CustomerId' => '98267952',
    'UserName' => 'kwebservis',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'Amount' => '1000', // String olarak
    'MerchantOrderId' => 'P598',
    'BusinessKey' => '202508159552000000000017190'
];

$xmlRequest = buildKuveytPosProvisionXml($testData);
echo "✓ XML Request oluşturuldu (" . strlen($xmlRequest) . " karakter)\n";
echo "✓ Merchant ID: " . $testData['MerchantId'] . "\n";
echo "✓ Amount: " . $testData['Amount'] . "\n";
echo "✓ Business Key: " . $testData['BusinessKey'] . "\n\n";

// Test 2: URL ve Endpoint Kontrolü
echo "Test 2: Endpoint Kontrolü\n";
echo "--------------------------\n";

$provisionUrl = 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelProvisionGate';
echo "✓ Provision URL: $provisionUrl\n";

// URL'in erişilebilir olup olmadığını kontrol et
$headers = @get_headers($provisionUrl);
if ($headers) {
    echo "✓ Endpoint erişilebilir\n";
    echo "✓ Response: " . $headers[0] . "\n";
} else {
    echo "✗ Endpoint erişilemez veya DNS sorunu\n";
}

echo "\n";

// Test 3: XML Validation
echo "Test 3: XML Validation\n";
echo "-----------------------\n";

$dom = new DOMDocument();
$dom->loadXML($xmlRequest);

if ($dom->schemaValidate) {
    echo "✓ XML formatı geçerli\n";
} else {
    echo "? XML schema validation yapılamadı (normal)\n";
}

// Required elementleri kontrol et
$requiredElements = [
    'MerchantId', 'CustomerId', 'UserName', 'TransactionType', 
    'Amount', 'MerchantOrderId', 'BusinessKey'
];

foreach ($requiredElements as $element) {
    $nodes = $dom->getElementsByTagName($element);
    if ($nodes->length > 0 && !empty($nodes->item(0)->textContent)) {
        echo "✓ $element: " . $nodes->item(0)->textContent . "\n";
    } else {
        echo "✗ $element eksik veya boş\n";
    }
}

echo "\n";

// Test 4: Simulated Response Test
echo "Test 4: Response Parsing Test\n";
echo "------------------------------\n";

$successResponse = '<?xml version="1.0" encoding="utf-8"?>
<KuveytTurkVPosMessage>
    <ResponseCode>00</ResponseCode>
    <ResponseMessage>İşlem başarılı</ResponseMessage>
    <OrderId>284062648</OrderId>
    <MerchantOrderId>P598</MerchantOrderId>
    <Amount>1000</Amount>
    <ProvisionNumber>123456789</ProvisionNumber>
    <RRN>987654321</RRN>
    <Stan>001</Stan>
    <InstallmentCount>0</InstallmentCount>
</KuveytTurkVPosMessage>';

function parseProvisionResponse($xmlResponse) {
    $dom = new DOMDocument();
    if (!$dom->loadXML($xmlResponse)) {
        return null;
    }
    
    $responseCode = $dom->getElementsByTagName('ResponseCode')->item(0)->textContent ?? null;
    $responseMessage = $dom->getElementsByTagName('ResponseMessage')->item(0)->textContent ?? null;
    $provisionNumber = $dom->getElementsByTagName('ProvisionNumber')->item(0)->textContent ?? null;
    
    return [
        'response_code' => $responseCode,
        'response_message' => $responseMessage,
        'provision_number' => $provisionNumber
    ];
}

$parsed = parseProvisionResponse($successResponse);
if ($parsed && $parsed['response_code'] === '00') {
    echo "✓ Başarılı response parse edildi\n";
    echo "✓ Response Code: " . $parsed['response_code'] . "\n";
    echo "✓ Provision Number: " . $parsed['provision_number'] . "\n";
} else {
    echo "✗ Response parse edilemedi\n";
}

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile ödeme yapın\n";
echo "2. Logları takip edin:\n";
echo "   tail -f storage/logs/laravel.log | grep -i provision\n";
echo "\n3. Aranacak log mesajları:\n";
echo "   - 'KuveytPos provision request starting'\n";
echo "   - 'KuveytPos provision data prepared'\n";
echo "   - 'KuveytPos sending provision request'\n";
echo "   - 'KuveytPos provision response received'\n";
echo "   - 'KuveytPos provision successful' (başarılı ise)\n";
echo "\n4. Hata durumunda:\n";
echo "   - 'KuveytPos provision HTTP request failed'\n";
echo "   - 'KuveytPos provision request failed'\n";
echo "\n5. Başarılı olursa para karttan çekilmeli!\n";
