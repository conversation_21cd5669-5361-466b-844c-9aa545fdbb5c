@charset "UTF-8";
/* stylelint-disable-line scss/dollar-variable-default */
/* stylelint-disable-line scss/dollar-variable-default */
/*!
 * CoreUI - HTML, CSS, and JavaScript UI Components Library
 * @version v4.2.3
 * @link https://coreui.io/
 * Copyright (c) 2022 creativeLabs <PERSON>
 * License MIT  (https://coreui.io/license/)
 */
:root {
  --cui-blue: #0d6efd;
  --cui-indigo: #6610f2;
  --cui-purple: #6f42c1;
  --cui-pink: #d63384;
  --cui-red: #dc3545;
  --cui-orange: #fd7e14;
  --cui-yellow: #ffc107;
  --cui-green: #198754;
  --cui-teal: #20c997;
  --cui-cyan: #0dcaf0;
  --cui-white: #fff;
  --cui-gray: #8a93a2;
  --cui-gray-dark: #636f83;
  --cui-gray-100: #ebedef;
  --cui-gray-200: #d8dbe0;
  --cui-gray-300: #c4c9d0;
  --cui-gray-400: #b1b7c1;
  --cui-gray-500: #9da5b1;
  --cui-gray-600: #8a93a2;
  --cui-gray-700: #768192;
  --cui-gray-800: #636f83;
  --cui-gray-900: #4f5d73;
  --cui-primary: #321fdb;
  --cui-secondary: #9da5b1;
  --cui-success: #2eb85c;
  --cui-info: #39f;
  --cui-warning: #f9b115;
  --cui-danger: #e55353;
  --cui-light: #ebedef;
  --cui-dark: #4f5d73;
  --cui-primary-rgb: 50, 31, 219;
  --cui-secondary-rgb: 157, 165, 177;
  --cui-success-rgb: 46, 184, 92;
  --cui-info-rgb: 51, 153, 255;
  --cui-warning-rgb: 249, 177, 21;
  --cui-danger-rgb: 229, 83, 83;
  --cui-light-rgb: 235, 237, 239;
  --cui-dark-rgb: 79, 93, 115;
  --cui-white-rgb: 255, 255, 255;
  --cui-black-rgb: 0, 0, 21;
  --cui-body-color-rgb: 44, 56, 74;
  --cui-body-bg-rgb: 255, 255, 255;
  --cui-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", "Liberation Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --cui-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --cui-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --cui-body-font-family: var(--cui-font-sans-serif);
  --cui-body-font-size: 1rem;
  --cui-body-font-weight: 400;
  --cui-body-line-height: 1.5;
  --cui-body-color: rgba(44, 56, 74, 0.95);
  --cui-body-bg: #fff;
}

*,
*::before,
*::after {
  box-sizing: border-box;
}

@media (prefers-reduced-motion: no-preference) {
  :root {
    scroll-behavior: smooth;
  }
}

body {
  margin: 0;
  font-family: var(--cui-body-font-family);
  font-size: var(--cui-body-font-size);
  font-weight: var(--cui-body-font-weight);
  line-height: var(--cui-body-line-height);
  color: var(--cui-body-color);
  text-align: var(--cui-body-text-align);
  background-color: var(--cui-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 21, 0);
}

hr {
  margin: 1rem 0;
  color: var(--cui-hr-color, inherit);
  background-color: currentColor;
  border: 0;
  opacity: 0.25;
}

hr:not([size]) {
  height: 1px;
}

.vr {
  display: flex;
  flex: 0 0 1px;
  width: 1px;
  padding: 0 !important;
  margin: 0;
  color: var(--cui-vr-color, inherit);
  background-color: currentColor;
  border: 0;
  opacity: 0.25;
}

h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
  color: var(--cui-headings-color, unset);
}

h1, .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  h1, .h1 {
    font-size: 2.5rem;
  }
}

h2, .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  h2, .h2 {
    font-size: 2rem;
  }
}

h3, .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  h3, .h3 {
    font-size: 1.75rem;
  }
}

h4, .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  h4, .h4 {
    font-size: 1.5rem;
  }
}

h5, .h5 {
  font-size: 1.25rem;
}

h6, .h6 {
  font-size: 1rem;
}

p {
  margin-top: 0;
  margin-bottom: 1rem;
}

abbr[title],
abbr[data-coreui-original-title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}

address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}

ol,
ul,
dl {
  margin-top: 0;
  margin-bottom: 1rem;
}

ol ol,
ul ul,
ol ul,
ul ol {
  margin-bottom: 0;
}

dt {
  font-weight: 700;
}

dd {
  margin-bottom: 0.5rem;
}
html:not([dir=rtl]) dd {
  margin-left: 0;
}
*[dir=rtl] dd {
  margin-right: 0;
}

blockquote {
  margin: 0 0 1rem;
}

b,
strong {
  font-weight: bolder;
}

small, .small {
  font-size: 0.875em;
}

mark, .mark {
  padding: 0.2em;
  background-color: var(--cui-mark-bg, #fcf8e3);
}

sub,
sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

a {
  color: var(--cui-link-color, #321fdb);
  text-decoration: underline;
}
a:hover {
  color: var(--cui-link-hover-color, #2819af);
}

a:not([href]):not([class]), a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}

pre,
code,
kbd,
samp {
  font-family: var(--cui-font-monospace);
  font-size: 1em;
  direction: ltr /* rtl:ignore */;
  unicode-bidi: bidi-override;
}

pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
  color: var(--cui-pre-color, unset);
}
pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}

code {
  font-size: 0.875em;
  color: var(--cui-code-color, #d63384);
  word-wrap: break-word;
}
a > code {
  color: inherit;
}

kbd {
  padding: 0.2rem 0.4rem;
  font-size: 0.875em;
  color: var(--cui-kbd-color, rgba(255, 255, 255, 0.87));
  background-color: var(--cui-kbd-bg, #4f5d73);
  border-radius: 0.2rem;
}
kbd kbd {
  padding: 0;
  font-size: 1em;
  font-weight: 700;
}

figure {
  margin: 0 0 1rem;
}

img,
svg {
  vertical-align: middle;
}

table {
  caption-side: bottom;
  border-collapse: collapse;
}

caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--cui-table-caption-color, rgba(44, 56, 74, 0.38));
  text-align: left;
}

th {
  font-weight: 600;
  text-align: inherit;
  text-align: -webkit-match-parent;
}

thead,
tbody,
tfoot,
tr,
td,
th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}

label {
  display: inline-block;
}

button {
  border-radius: 0;
}

button:focus:not(:focus-visible) {
  outline: 0;
}

input,
button,
select,
optgroup,
textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

button,
select {
  text-transform: none;
}

[role=button] {
  cursor: pointer;
}

select {
  word-wrap: normal;
}
select:disabled {
  opacity: 1;
}

[list]::-webkit-calendar-picker-indicator {
  display: none;
}

button,
[type=button],
[type=reset],
[type=submit] {
  -webkit-appearance: button;
}
button:not(:disabled),
[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled) {
  cursor: pointer;
}

::-moz-focus-inner {
  padding: 0;
  border-style: none;
}

textarea {
  resize: vertical;
}

fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}

legend {
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
html:not([dir=rtl]) legend {
  float: left;
}
*[dir=rtl] legend {
  float: right;
}
@media (min-width: 1200px) {
  legend {
    font-size: 1.5rem;
  }
}
legend + * {
  clear: left;
}

::-webkit-datetime-edit-fields-wrapper,
::-webkit-datetime-edit-text,
::-webkit-datetime-edit-minute,
::-webkit-datetime-edit-hour-field,
::-webkit-datetime-edit-day-field,
::-webkit-datetime-edit-month-field,
::-webkit-datetime-edit-year-field {
  padding: 0;
}

::-webkit-inner-spin-button {
  height: auto;
}

[type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}

*[dir=rtl] [type=tel],
*[dir=rtl] [type=url],
*[dir=rtl] [type=email],
*[dir=rtl] [type=number] {
  direction: ltr;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-color-swatch-wrapper {
  padding: 0;
}

::-webkit-file-upload-button {
  font: inherit;
}

::file-selector-button {
  font: inherit;
}

::-webkit-file-upload-button {
  font: inherit;
  -webkit-appearance: button;
}

output {
  display: inline-block;
}

iframe {
  border: 0;
}

summary {
  display: list-item;
  cursor: pointer;
}

progress {
  vertical-align: baseline;
}

[hidden] {
  display: none !important;
}

.lead {
  font-size: 1.25rem;
  font-weight: 300;
}

.display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-1 {
    font-size: 5rem;
  }
}

.display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-2 {
    font-size: 4.5rem;
  }
}

.display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-3 {
    font-size: 4rem;
  }
}

.display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-4 {
    font-size: 3.5rem;
  }
}

.display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-5 {
    font-size: 3rem;
  }
}

.display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .display-6 {
    font-size: 2.5rem;
  }
}

.list-unstyled {
  list-style: none;
}
html:not([dir=rtl]) .list-unstyled {
  padding-left: 0;
}
*[dir=rtl] .list-unstyled {
  padding-right: 0;
}

.list-inline {
  list-style: none;
}
html:not([dir=rtl]) .list-inline {
  padding-left: 0;
}
*[dir=rtl] .list-inline {
  padding-right: 0;
}

.list-inline-item {
  display: inline-block;
}
html:not([dir=rtl]) .list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
*[dir=rtl] .list-inline-item:not(:last-child) {
  margin-left: 0.5rem;
}

.initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}

.blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.blockquote > :last-child {
  margin-bottom: 0;
}

.blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #8a93a2;
}
.blockquote-footer::before {
  content: "— ";
}

.img-fluid {
  max-width: 100%;
  height: auto;
}

.img-thumbnail {
  padding: 0.25rem;
  background-color: var(--cui-thumbnail-bg, #fff);
  border: 1px solid var(--cui-thumbnail-border-color, #c4c9d0);
  border-radius: 0.25rem;
  max-width: 100%;
  height: auto;
}

.figure {
  display: inline-block;
}

.figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}

.figure-caption {
  font-size: 0.875em;
  color: var(--cui-figure-caption-color, #8a93a2);
}

.container,
.container-fluid,
.container-xxl,
.container-xl,
.container-lg,
.container-md,
.container-sm {
  width: 100%;
  padding-right: var(--cui-gutter-x, 0.75rem);
  padding-left: var(--cui-gutter-x, 0.75rem);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container-sm, .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .container-md, .container-sm, .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .container-lg, .container-md, .container-sm, .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container {
    max-width: 1320px;
  }
}
.row {
  --cui-gutter-x: 1.5rem;
  --cui-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--cui-gutter-y));
  margin-right: calc(-0.5 * var(--cui-gutter-x));
  margin-left: calc(-0.5 * var(--cui-gutter-x));
}
.row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--cui-gutter-x) * 0.5);
  padding-left: calc(var(--cui-gutter-x) * 0.5);
  margin-top: var(--cui-gutter-y);
}

.col {
  flex: 1 0 0%;
}

.row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}

.row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}

.row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}

.row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}

.row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}

.row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}

.row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}

.col-auto {
  flex: 0 0 auto;
  width: auto;
}

.col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}

.col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}

.col-3 {
  flex: 0 0 auto;
  width: 25%;
}

.col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}

.col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}

.col-6 {
  flex: 0 0 auto;
  width: 50%;
}

.col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}

.col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}

.col-9 {
  flex: 0 0 auto;
  width: 75%;
}

.col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}

.col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}

.col-12 {
  flex: 0 0 auto;
  width: 100%;
}

html:not([dir=rtl]) .offset-1 {
  margin-left: 8.33333333%;
}
*[dir=rtl] .offset-1 {
  margin-right: 8.33333333%;
}

html:not([dir=rtl]) .offset-2 {
  margin-left: 16.66666667%;
}
*[dir=rtl] .offset-2 {
  margin-right: 16.66666667%;
}

html:not([dir=rtl]) .offset-3 {
  margin-left: 25%;
}
*[dir=rtl] .offset-3 {
  margin-right: 25%;
}

html:not([dir=rtl]) .offset-4 {
  margin-left: 33.33333333%;
}
*[dir=rtl] .offset-4 {
  margin-right: 33.33333333%;
}

html:not([dir=rtl]) .offset-5 {
  margin-left: 41.66666667%;
}
*[dir=rtl] .offset-5 {
  margin-right: 41.66666667%;
}

html:not([dir=rtl]) .offset-6 {
  margin-left: 50%;
}
*[dir=rtl] .offset-6 {
  margin-right: 50%;
}

html:not([dir=rtl]) .offset-7 {
  margin-left: 58.33333333%;
}
*[dir=rtl] .offset-7 {
  margin-right: 58.33333333%;
}

html:not([dir=rtl]) .offset-8 {
  margin-left: 66.66666667%;
}
*[dir=rtl] .offset-8 {
  margin-right: 66.66666667%;
}

html:not([dir=rtl]) .offset-9 {
  margin-left: 75%;
}
*[dir=rtl] .offset-9 {
  margin-right: 75%;
}

html:not([dir=rtl]) .offset-10 {
  margin-left: 83.33333333%;
}
*[dir=rtl] .offset-10 {
  margin-right: 83.33333333%;
}

html:not([dir=rtl]) .offset-11 {
  margin-left: 91.66666667%;
}
*[dir=rtl] .offset-11 {
  margin-right: 91.66666667%;
}

.g-0,
.gx-0 {
  --cui-gutter-x: 0;
}

.g-0,
.gy-0 {
  --cui-gutter-y: 0;
}

.g-1,
.gx-1 {
  --cui-gutter-x: 0.25rem;
}

.g-1,
.gy-1 {
  --cui-gutter-y: 0.25rem;
}

.g-2,
.gx-2 {
  --cui-gutter-x: 0.5rem;
}

.g-2,
.gy-2 {
  --cui-gutter-y: 0.5rem;
}

.g-3,
.gx-3 {
  --cui-gutter-x: 1rem;
}

.g-3,
.gy-3 {
  --cui-gutter-y: 1rem;
}

.g-4,
.gx-4 {
  --cui-gutter-x: 1.5rem;
}

.g-4,
.gy-4 {
  --cui-gutter-y: 1.5rem;
}

.g-5,
.gx-5 {
  --cui-gutter-x: 3rem;
}

.g-5,
.gy-5 {
  --cui-gutter-y: 3rem;
}

@media (min-width: 576px) {
  .col-sm {
    flex: 1 0 0%;
  }

  .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  html:not([dir=rtl]) .offset-sm-0 {
    margin-left: 0;
  }
  *[dir=rtl] .offset-sm-0 {
    margin-right: 0;
  }

  html:not([dir=rtl]) .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  *[dir=rtl] .offset-sm-1 {
    margin-right: 8.33333333%;
  }

  html:not([dir=rtl]) .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  *[dir=rtl] .offset-sm-2 {
    margin-right: 16.66666667%;
  }

  html:not([dir=rtl]) .offset-sm-3 {
    margin-left: 25%;
  }
  *[dir=rtl] .offset-sm-3 {
    margin-right: 25%;
  }

  html:not([dir=rtl]) .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  *[dir=rtl] .offset-sm-4 {
    margin-right: 33.33333333%;
  }

  html:not([dir=rtl]) .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  *[dir=rtl] .offset-sm-5 {
    margin-right: 41.66666667%;
  }

  html:not([dir=rtl]) .offset-sm-6 {
    margin-left: 50%;
  }
  *[dir=rtl] .offset-sm-6 {
    margin-right: 50%;
  }

  html:not([dir=rtl]) .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  *[dir=rtl] .offset-sm-7 {
    margin-right: 58.33333333%;
  }

  html:not([dir=rtl]) .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  *[dir=rtl] .offset-sm-8 {
    margin-right: 66.66666667%;
  }

  html:not([dir=rtl]) .offset-sm-9 {
    margin-left: 75%;
  }
  *[dir=rtl] .offset-sm-9 {
    margin-right: 75%;
  }

  html:not([dir=rtl]) .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  *[dir=rtl] .offset-sm-10 {
    margin-right: 83.33333333%;
  }

  html:not([dir=rtl]) .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  *[dir=rtl] .offset-sm-11 {
    margin-right: 91.66666667%;
  }

  .g-sm-0,
.gx-sm-0 {
    --cui-gutter-x: 0;
  }

  .g-sm-0,
.gy-sm-0 {
    --cui-gutter-y: 0;
  }

  .g-sm-1,
.gx-sm-1 {
    --cui-gutter-x: 0.25rem;
  }

  .g-sm-1,
.gy-sm-1 {
    --cui-gutter-y: 0.25rem;
  }

  .g-sm-2,
.gx-sm-2 {
    --cui-gutter-x: 0.5rem;
  }

  .g-sm-2,
.gy-sm-2 {
    --cui-gutter-y: 0.5rem;
  }

  .g-sm-3,
.gx-sm-3 {
    --cui-gutter-x: 1rem;
  }

  .g-sm-3,
.gy-sm-3 {
    --cui-gutter-y: 1rem;
  }

  .g-sm-4,
.gx-sm-4 {
    --cui-gutter-x: 1.5rem;
  }

  .g-sm-4,
.gy-sm-4 {
    --cui-gutter-y: 1.5rem;
  }

  .g-sm-5,
.gx-sm-5 {
    --cui-gutter-x: 3rem;
  }

  .g-sm-5,
.gy-sm-5 {
    --cui-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .col-md {
    flex: 1 0 0%;
  }

  .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  html:not([dir=rtl]) .offset-md-0 {
    margin-left: 0;
  }
  *[dir=rtl] .offset-md-0 {
    margin-right: 0;
  }

  html:not([dir=rtl]) .offset-md-1 {
    margin-left: 8.33333333%;
  }
  *[dir=rtl] .offset-md-1 {
    margin-right: 8.33333333%;
  }

  html:not([dir=rtl]) .offset-md-2 {
    margin-left: 16.66666667%;
  }
  *[dir=rtl] .offset-md-2 {
    margin-right: 16.66666667%;
  }

  html:not([dir=rtl]) .offset-md-3 {
    margin-left: 25%;
  }
  *[dir=rtl] .offset-md-3 {
    margin-right: 25%;
  }

  html:not([dir=rtl]) .offset-md-4 {
    margin-left: 33.33333333%;
  }
  *[dir=rtl] .offset-md-4 {
    margin-right: 33.33333333%;
  }

  html:not([dir=rtl]) .offset-md-5 {
    margin-left: 41.66666667%;
  }
  *[dir=rtl] .offset-md-5 {
    margin-right: 41.66666667%;
  }

  html:not([dir=rtl]) .offset-md-6 {
    margin-left: 50%;
  }
  *[dir=rtl] .offset-md-6 {
    margin-right: 50%;
  }

  html:not([dir=rtl]) .offset-md-7 {
    margin-left: 58.33333333%;
  }
  *[dir=rtl] .offset-md-7 {
    margin-right: 58.33333333%;
  }

  html:not([dir=rtl]) .offset-md-8 {
    margin-left: 66.66666667%;
  }
  *[dir=rtl] .offset-md-8 {
    margin-right: 66.66666667%;
  }

  html:not([dir=rtl]) .offset-md-9 {
    margin-left: 75%;
  }
  *[dir=rtl] .offset-md-9 {
    margin-right: 75%;
  }

  html:not([dir=rtl]) .offset-md-10 {
    margin-left: 83.33333333%;
  }
  *[dir=rtl] .offset-md-10 {
    margin-right: 83.33333333%;
  }

  html:not([dir=rtl]) .offset-md-11 {
    margin-left: 91.66666667%;
  }
  *[dir=rtl] .offset-md-11 {
    margin-right: 91.66666667%;
  }

  .g-md-0,
.gx-md-0 {
    --cui-gutter-x: 0;
  }

  .g-md-0,
.gy-md-0 {
    --cui-gutter-y: 0;
  }

  .g-md-1,
.gx-md-1 {
    --cui-gutter-x: 0.25rem;
  }

  .g-md-1,
.gy-md-1 {
    --cui-gutter-y: 0.25rem;
  }

  .g-md-2,
.gx-md-2 {
    --cui-gutter-x: 0.5rem;
  }

  .g-md-2,
.gy-md-2 {
    --cui-gutter-y: 0.5rem;
  }

  .g-md-3,
.gx-md-3 {
    --cui-gutter-x: 1rem;
  }

  .g-md-3,
.gy-md-3 {
    --cui-gutter-y: 1rem;
  }

  .g-md-4,
.gx-md-4 {
    --cui-gutter-x: 1.5rem;
  }

  .g-md-4,
.gy-md-4 {
    --cui-gutter-y: 1.5rem;
  }

  .g-md-5,
.gx-md-5 {
    --cui-gutter-x: 3rem;
  }

  .g-md-5,
.gy-md-5 {
    --cui-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .col-lg {
    flex: 1 0 0%;
  }

  .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  html:not([dir=rtl]) .offset-lg-0 {
    margin-left: 0;
  }
  *[dir=rtl] .offset-lg-0 {
    margin-right: 0;
  }

  html:not([dir=rtl]) .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  *[dir=rtl] .offset-lg-1 {
    margin-right: 8.33333333%;
  }

  html:not([dir=rtl]) .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  *[dir=rtl] .offset-lg-2 {
    margin-right: 16.66666667%;
  }

  html:not([dir=rtl]) .offset-lg-3 {
    margin-left: 25%;
  }
  *[dir=rtl] .offset-lg-3 {
    margin-right: 25%;
  }

  html:not([dir=rtl]) .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  *[dir=rtl] .offset-lg-4 {
    margin-right: 33.33333333%;
  }

  html:not([dir=rtl]) .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  *[dir=rtl] .offset-lg-5 {
    margin-right: 41.66666667%;
  }

  html:not([dir=rtl]) .offset-lg-6 {
    margin-left: 50%;
  }
  *[dir=rtl] .offset-lg-6 {
    margin-right: 50%;
  }

  html:not([dir=rtl]) .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  *[dir=rtl] .offset-lg-7 {
    margin-right: 58.33333333%;
  }

  html:not([dir=rtl]) .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  *[dir=rtl] .offset-lg-8 {
    margin-right: 66.66666667%;
  }

  html:not([dir=rtl]) .offset-lg-9 {
    margin-left: 75%;
  }
  *[dir=rtl] .offset-lg-9 {
    margin-right: 75%;
  }

  html:not([dir=rtl]) .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  *[dir=rtl] .offset-lg-10 {
    margin-right: 83.33333333%;
  }

  html:not([dir=rtl]) .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  *[dir=rtl] .offset-lg-11 {
    margin-right: 91.66666667%;
  }

  .g-lg-0,
.gx-lg-0 {
    --cui-gutter-x: 0;
  }

  .g-lg-0,
.gy-lg-0 {
    --cui-gutter-y: 0;
  }

  .g-lg-1,
.gx-lg-1 {
    --cui-gutter-x: 0.25rem;
  }

  .g-lg-1,
.gy-lg-1 {
    --cui-gutter-y: 0.25rem;
  }

  .g-lg-2,
.gx-lg-2 {
    --cui-gutter-x: 0.5rem;
  }

  .g-lg-2,
.gy-lg-2 {
    --cui-gutter-y: 0.5rem;
  }

  .g-lg-3,
.gx-lg-3 {
    --cui-gutter-x: 1rem;
  }

  .g-lg-3,
.gy-lg-3 {
    --cui-gutter-y: 1rem;
  }

  .g-lg-4,
.gx-lg-4 {
    --cui-gutter-x: 1.5rem;
  }

  .g-lg-4,
.gy-lg-4 {
    --cui-gutter-y: 1.5rem;
  }

  .g-lg-5,
.gx-lg-5 {
    --cui-gutter-x: 3rem;
  }

  .g-lg-5,
.gy-lg-5 {
    --cui-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .col-xl {
    flex: 1 0 0%;
  }

  .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  html:not([dir=rtl]) .offset-xl-0 {
    margin-left: 0;
  }
  *[dir=rtl] .offset-xl-0 {
    margin-right: 0;
  }

  html:not([dir=rtl]) .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  *[dir=rtl] .offset-xl-1 {
    margin-right: 8.33333333%;
  }

  html:not([dir=rtl]) .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  *[dir=rtl] .offset-xl-2 {
    margin-right: 16.66666667%;
  }

  html:not([dir=rtl]) .offset-xl-3 {
    margin-left: 25%;
  }
  *[dir=rtl] .offset-xl-3 {
    margin-right: 25%;
  }

  html:not([dir=rtl]) .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  *[dir=rtl] .offset-xl-4 {
    margin-right: 33.33333333%;
  }

  html:not([dir=rtl]) .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  *[dir=rtl] .offset-xl-5 {
    margin-right: 41.66666667%;
  }

  html:not([dir=rtl]) .offset-xl-6 {
    margin-left: 50%;
  }
  *[dir=rtl] .offset-xl-6 {
    margin-right: 50%;
  }

  html:not([dir=rtl]) .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  *[dir=rtl] .offset-xl-7 {
    margin-right: 58.33333333%;
  }

  html:not([dir=rtl]) .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  *[dir=rtl] .offset-xl-8 {
    margin-right: 66.66666667%;
  }

  html:not([dir=rtl]) .offset-xl-9 {
    margin-left: 75%;
  }
  *[dir=rtl] .offset-xl-9 {
    margin-right: 75%;
  }

  html:not([dir=rtl]) .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  *[dir=rtl] .offset-xl-10 {
    margin-right: 83.33333333%;
  }

  html:not([dir=rtl]) .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  *[dir=rtl] .offset-xl-11 {
    margin-right: 91.66666667%;
  }

  .g-xl-0,
.gx-xl-0 {
    --cui-gutter-x: 0;
  }

  .g-xl-0,
.gy-xl-0 {
    --cui-gutter-y: 0;
  }

  .g-xl-1,
.gx-xl-1 {
    --cui-gutter-x: 0.25rem;
  }

  .g-xl-1,
.gy-xl-1 {
    --cui-gutter-y: 0.25rem;
  }

  .g-xl-2,
.gx-xl-2 {
    --cui-gutter-x: 0.5rem;
  }

  .g-xl-2,
.gy-xl-2 {
    --cui-gutter-y: 0.5rem;
  }

  .g-xl-3,
.gx-xl-3 {
    --cui-gutter-x: 1rem;
  }

  .g-xl-3,
.gy-xl-3 {
    --cui-gutter-y: 1rem;
  }

  .g-xl-4,
.gx-xl-4 {
    --cui-gutter-x: 1.5rem;
  }

  .g-xl-4,
.gy-xl-4 {
    --cui-gutter-y: 1.5rem;
  }

  .g-xl-5,
.gx-xl-5 {
    --cui-gutter-x: 3rem;
  }

  .g-xl-5,
.gy-xl-5 {
    --cui-gutter-y: 3rem;
  }
}
@media (min-width: 1400px) {
  .col-xxl {
    flex: 1 0 0%;
  }

  .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }

  .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }

  .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }

  .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }

  .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }

  .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }

  .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }

  .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }

  .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }

  .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }

  .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }

  .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }

  .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }

  .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }

  .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }

  .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }

  .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }

  .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }

  .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }

  .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }

  html:not([dir=rtl]) .offset-xxl-0 {
    margin-left: 0;
  }
  *[dir=rtl] .offset-xxl-0 {
    margin-right: 0;
  }

  html:not([dir=rtl]) .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  *[dir=rtl] .offset-xxl-1 {
    margin-right: 8.33333333%;
  }

  html:not([dir=rtl]) .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  *[dir=rtl] .offset-xxl-2 {
    margin-right: 16.66666667%;
  }

  html:not([dir=rtl]) .offset-xxl-3 {
    margin-left: 25%;
  }
  *[dir=rtl] .offset-xxl-3 {
    margin-right: 25%;
  }

  html:not([dir=rtl]) .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  *[dir=rtl] .offset-xxl-4 {
    margin-right: 33.33333333%;
  }

  html:not([dir=rtl]) .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  *[dir=rtl] .offset-xxl-5 {
    margin-right: 41.66666667%;
  }

  html:not([dir=rtl]) .offset-xxl-6 {
    margin-left: 50%;
  }
  *[dir=rtl] .offset-xxl-6 {
    margin-right: 50%;
  }

  html:not([dir=rtl]) .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  *[dir=rtl] .offset-xxl-7 {
    margin-right: 58.33333333%;
  }

  html:not([dir=rtl]) .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  *[dir=rtl] .offset-xxl-8 {
    margin-right: 66.66666667%;
  }

  html:not([dir=rtl]) .offset-xxl-9 {
    margin-left: 75%;
  }
  *[dir=rtl] .offset-xxl-9 {
    margin-right: 75%;
  }

  html:not([dir=rtl]) .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  *[dir=rtl] .offset-xxl-10 {
    margin-right: 83.33333333%;
  }

  html:not([dir=rtl]) .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  *[dir=rtl] .offset-xxl-11 {
    margin-right: 91.66666667%;
  }

  .g-xxl-0,
.gx-xxl-0 {
    --cui-gutter-x: 0;
  }

  .g-xxl-0,
.gy-xxl-0 {
    --cui-gutter-y: 0;
  }

  .g-xxl-1,
.gx-xxl-1 {
    --cui-gutter-x: 0.25rem;
  }

  .g-xxl-1,
.gy-xxl-1 {
    --cui-gutter-y: 0.25rem;
  }

  .g-xxl-2,
.gx-xxl-2 {
    --cui-gutter-x: 0.5rem;
  }

  .g-xxl-2,
.gy-xxl-2 {
    --cui-gutter-y: 0.5rem;
  }

  .g-xxl-3,
.gx-xxl-3 {
    --cui-gutter-x: 1rem;
  }

  .g-xxl-3,
.gy-xxl-3 {
    --cui-gutter-y: 1rem;
  }

  .g-xxl-4,
.gx-xxl-4 {
    --cui-gutter-x: 1.5rem;
  }

  .g-xxl-4,
.gy-xxl-4 {
    --cui-gutter-y: 1.5rem;
  }

  .g-xxl-5,
.gx-xxl-5 {
    --cui-gutter-x: 3rem;
  }

  .g-xxl-5,
.gy-xxl-5 {
    --cui-gutter-y: 3rem;
  }
}
.table {
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-bg: transparent;
  --cui-table-border-color: #d8dbe0;
  --cui-table-accent-bg: transparent;
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-striped-bg: rgba(0, 0, 21, 0.05);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(0, 0, 21, 0.1);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(0, 0, 21, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: var(--cui-table-color);
  vertical-align: top;
  border-color: var(--cui-table-border-color);
}
.table > :not(caption) > * > * {
  padding: 0.5rem 0.5rem;
  color: var(--cui-table-color);
  background-color: var(--cui-table-bg);
  border-bottom-color: var(--cui-table-border-color);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--cui-table-accent-bg);
}
.table > tbody {
  vertical-align: inherit;
}
.table > thead {
  vertical-align: bottom;
}

.caption-top {
  caption-side: top;
}

.table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}

.table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}

.table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.table-borderless > :not(:first-child) {
  border-top-width: 0;
}

.table-striped > tbody > tr:nth-of-type(odd) > * {
  --cui-table-accent-bg: var(--cui-table-striped-bg);
  color: var(--cui-table-striped-color);
}

.table-active {
  --cui-table-accent-bg: var(--cui-table-active-bg);
  color: var(--cui-table-active-color);
}

.table-hover > tbody > tr:hover > * {
  --cui-table-accent-bg: var(--cui-table-hover-bg);
  color: var(--cui-table-hover-color);
}

.table-primary {
  --cui-table-bg: #d6d2f8;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(198, 196, 232, 0.995);
  --cui-table-striped-bg: rgba(206, 203, 240, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(198, 196, 232, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(202, 199, 236, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-secondary {
  --cui-table-bg: #ebedef;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(218, 220, 224, 0.995);
  --cui-table-striped-bg: rgba(226, 229, 232, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(218, 220, 224, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(222, 225, 228, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-success {
  --cui-table-bg: #d5f1de;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(198, 224, 208, 0.995);
  --cui-table-striped-bg: rgba(205, 233, 215, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(198, 224, 208, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(201, 228, 212, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-danger {
  --cui-table-bg: #fadddd;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(231, 206, 208, 0.995);
  --cui-table-striped-bg: rgba(241, 214, 214, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(231, 206, 208, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(236, 210, 211, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-warning {
  --cui-table-bg: #feefd0;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(235, 222, 196, 0.995);
  --cui-table-striped-bg: rgba(244, 231, 202, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(235, 222, 196, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(240, 226, 199, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-info {
  --cui-table-bg: #d6ebff;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(198, 219, 238, 0.995);
  --cui-table-striped-bg: rgba(206, 227, 247, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(198, 219, 238, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(202, 223, 243, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-light {
  --cui-table-bg: #fbfbfc;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(232, 233, 236, 0.995);
  --cui-table-striped-bg: rgba(242, 242, 244, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(232, 233, 236, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(237, 238, 240, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-dark {
  --cui-table-bg: #dcdfe3;
  --cui-table-color: rgba(44, 56, 74, 0.95);
  --cui-table-border-color: rgba(204, 208, 213, 0.995);
  --cui-table-striped-bg: rgba(212, 215, 220, 0.9975);
  --cui-table-striped-color: rgba(44, 56, 74, 0.95);
  --cui-table-active-bg: rgba(204, 208, 213, 0.995);
  --cui-table-active-color: rgba(44, 56, 74, 0.95);
  --cui-table-hover-bg: rgba(208, 212, 217, 0.99625);
  --cui-table-hover-color: rgba(44, 56, 74, 0.95);
  color: var(--cui-table-color);
  border-color: var(--cui-table-border-color);
}

.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 575.98px) {
  .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.form-label {
  margin-bottom: 0.5rem;
  color: var(--cui-form-label-color, );
}

.col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
  color: var(--cui-form-label-color, );
}

.col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
}

.col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
}

.form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: var(--cui-form-text-color, rgba(44, 56, 74, 0.38));
}

.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--cui-input-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-input-bg, #fff);
  background-clip: padding-box;
  border: 1px solid var(--cui-input-border-color, #b1b7c1);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control {
    transition: none;
  }
}
.form-control[type=file] {
  overflow: hidden;
}
.form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control:focus {
  color: var(--cui-input-focus-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-input-focus-bg, #fff);
  border-color: var(--cui-input-focus-border-color, #998fed);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}
.form-control::-webkit-date-and-time-value {
  height: 1.5em;
}
.form-control::-moz-placeholder {
  color: var(--cui-input-placeholder-color, #8a93a2);
  opacity: 1;
}
.form-control:-ms-input-placeholder {
  color: var(--cui-input-placeholder-color, #8a93a2);
  opacity: 1;
}
.form-control::placeholder {
  color: var(--cui-input-placeholder-color, #8a93a2);
  opacity: 1;
}
.form-control:disabled, .form-control[readonly] {
  background-color: var(--cui-input-disabled-bg, #d8dbe0);
  border-color: var(--cui-input-disabled-border-color, #b1b7c1);
  opacity: 1;
}
.form-control::-webkit-file-upload-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
  color: var(--cui-form-file-button-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-form-file-button-bg, #d8dbe0);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
.form-control::file-selector-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
  color: var(--cui-form-file-button-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-form-file-button-bg, #d8dbe0);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
  .form-control::file-selector-button {
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: var(--cui-form-file-button-hover-bg, #cdd0d5);
}
.form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: var(--cui-form-file-button-hover-bg, #cdd0d5);
}
.form-control::-webkit-file-upload-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  -webkit-margin-end: 0.75rem;
          margin-inline-end: 0.75rem;
  color: var(--cui-form-file-button-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-form-file-button-bg, #d8dbe0);
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  -webkit-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .form-control::-webkit-file-upload-button {
    -webkit-transition: none;
    transition: none;
  }
}
.form-control:hover:not(:disabled):not([readonly])::-webkit-file-upload-button {
  background-color: var(--cui-form-file-button-hover-bg, #cdd0d5);
}

.form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: var(--cui-input-plaintext-color, rgba(44, 56, 74, 0.95));
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}

.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}
.form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}
.form-control-sm::-webkit-file-upload-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  -webkit-margin-end: 0.5rem;
          margin-inline-end: 0.5rem;
}

.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}
.form-control-lg::-webkit-file-upload-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  -webkit-margin-end: 1rem;
          margin-inline-end: 1rem;
}
.form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  -webkit-margin-end: 1rem;
          margin-inline-end: 1rem;
}
.form-control-lg::-webkit-file-upload-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  -webkit-margin-end: 1rem;
          margin-inline-end: 1rem;
}

textarea.form-control {
  min-height: calc(1.5em + 0.75rem + 2px);
}
textarea.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
}
textarea.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
}

.form-control-color {
  width: 3rem;
  height: auto;
  padding: 0.375rem;
}
.form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.form-control-color::-moz-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}
.form-control-color::-webkit-color-swatch {
  height: 1.5em;
  border-radius: 0.25rem;
}

.form-select {
  display: block;
  width: 100%;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--cui-form-select-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-form-select-bg, #fff);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23636f83' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: 16px 12px;
  border: 1px solid var(--cui-form-select-border-color, #b1b7c1);
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
html:not([dir=rtl]) .form-select {
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
}
*[dir=rtl] .form-select {
  padding: 0.375rem 0.75rem 0.375rem 2.25rem;
}
html:not([dir=rtl]) .form-select {
  background-position: right 0.75rem center;
}
*[dir=rtl] .form-select {
  background-position: left 0.75rem center;
}
@media (prefers-reduced-motion: reduce) {
  .form-select {
    transition: none;
  }
}
.form-select:focus {
  border-color: var(--cui-form-select-focus-border-color, #998fed);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}
.form-select[multiple], .form-select[size]:not([size="1"]) {
  background-image: none;
}
html:not([dir=rtl]) .form-select[multiple], html:not([dir=rtl]) .form-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
}
*[dir=rtl] .form-select[multiple], *[dir=rtl] .form-select[size]:not([size="1"]) {
  padding-left: 0.75rem;
}
.form-select:disabled {
  color: var(--cui-form-select-disabled-color, );
  background-color: var(--cui-form-select-disabled-bg, #d8dbe0);
  border-color: var(--cui-form-select-disabled-border-color, #b1b7c1);
}
.form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 var(--cui-form-select-color, rgba(44, 56, 74, 0.95));
}

.form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}
html:not([dir=rtl]) .form-select-sm {
  padding-left: 0.5rem;
}
*[dir=rtl] .form-select-sm {
  padding-right: 0.5rem;
}

.form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}
html:not([dir=rtl]) .form-select-lg {
  padding-left: 1rem;
}
*[dir=rtl] .form-select-lg {
  padding-right: 1rem;
}

.form-check {
  display: block;
  min-height: 1.5rem;
  margin-bottom: 0.125rem;
}
html:not([dir=rtl]) .form-check {
  padding-left: 1.5em;
}
*[dir=rtl] .form-check {
  padding-right: 1.5em;
}
html:not([dir=rtl]) .form-check .form-check-input {
  float: left;
}
*[dir=rtl] .form-check .form-check-input {
  float: right;
}
html:not([dir=rtl]) .form-check .form-check-input {
  margin-left: -1.5em;
}
*[dir=rtl] .form-check .form-check-input {
  margin-right: -1.5em;
}

.form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: var(--cui-form-check-input-bg, #fff);
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 21, 0.25);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  -webkit-print-color-adjust: exact;
          color-adjust: exact;
}
.form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.form-check-input[type=radio] {
  border-radius: 50%;
}
.form-check-input:active {
  filter: brightness(90%);
}
.form-check-input:focus {
  border-color: #998fed;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}
.form-check-input:checked {
  background-color: var(--cui-form-check-input-checked-bg-color, #321fdb);
  border-color: var(--cui-form-check-input-checked-border-color, #321fdb);
}
.form-check-input:checked[type=checkbox] {
  background-image: var(--cui-form-check-input-checked-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='rgba%28255, 255, 255, 0.87%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e"));
}
.form-check-input:checked[type=radio] {
  background-image: var(--cui-form-check-radio-checked-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='rgba%28255, 255, 255, 0.87%29'/%3e%3c/svg%3e"));
}
.form-check-input[type=checkbox]:indeterminate {
  background-color: #321fdb;
  border-color: #321fdb;
  background-image: var(--cui-form-check-input-indeterminate-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='rgba%28255, 255, 255, 0.87%29' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e"));
}
.form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label {
  opacity: 0.5;
}

.form-check-label {
  color: var(--cui-form-check-label-color, unset);
}

html:not([dir=rtl]) .form-switch {
  padding-left: 2.5em;
}
*[dir=rtl] .form-switch {
  padding-right: 2.5em;
}
.form-switch .form-check-input {
  width: 2em;
  background-image: var(--cui-form-switch-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 21, 0.25%29'/%3e%3c/svg%3e"));
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
html:not([dir=rtl]) .form-switch .form-check-input {
  margin-left: -2.5em;
}
*[dir=rtl] .form-switch .form-check-input {
  margin-right: -2.5em;
}
@media (prefers-reduced-motion: reduce) {
  .form-switch .form-check-input {
    transition: none;
  }
}
.form-switch .form-check-input:focus {
  background-image: var(--cui-form-switch-focus-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23998fed'/%3e%3c/svg%3e"));
}
.form-switch .form-check-input:checked {
  background-position: right center;
  background-image: var(--cui-form-switch-checked-bg-image, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%28255, 255, 255, 0.87%29'/%3e%3c/svg%3e"));
}

.form-switch-lg {
  min-height: 1.25em;
}
html:not([dir=rtl]) .form-switch-lg {
  padding-left: 2.25em;
}
*[dir=rtl] .form-switch-lg {
  padding-right: 2.25em;
}
.form-switch-lg .form-check-input {
  width: 1.75em;
  height: 1.25em;
}
html:not([dir=rtl]) .form-switch-lg .form-check-input {
  margin-left: -2.25em;
}
*[dir=rtl] .form-switch-lg .form-check-input {
  margin-right: -2.25em;
}
.form-switch-lg .form-check-label {
  padding-top: calc((1.25em - 1rem) / 2);
}

.form-switch-xl {
  min-height: 1.5em;
}
html:not([dir=rtl]) .form-switch-xl {
  padding-left: 2.5em;
}
*[dir=rtl] .form-switch-xl {
  padding-right: 2.5em;
}
.form-switch-xl .form-check-input {
  width: 2em;
  height: 1.5em;
}
html:not([dir=rtl]) .form-switch-xl .form-check-input {
  margin-left: -2.5em;
}
*[dir=rtl] .form-switch-xl .form-check-input {
  margin-right: -2.5em;
}
.form-switch-xl .form-check-label {
  padding-top: calc((1.5em - 1rem) / 2);
}

.form-check-inline {
  display: inline-block;
}
html:not([dir=rtl]) .form-check-inline {
  margin-right: 1rem;
}
*[dir=rtl] .form-check-inline {
  margin-left: 1rem;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.btn-check[disabled] + .btn, .btn-check:disabled + .btn {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}

.form-check-primary {
  --cui-form-check-input-checked-bg-color: #321fdb;
  --cui-form-check-input-checked-border-color: #321fdb;
  --cui-form-check-input-indeterminate-bg-color: #321fdb;
  --cui-form-check-input-indeterminate-border-color: #321fdb;
}

.form-check-secondary {
  --cui-form-check-input-checked-bg-color: #9da5b1;
  --cui-form-check-input-checked-border-color: #9da5b1;
  --cui-form-check-input-indeterminate-bg-color: #9da5b1;
  --cui-form-check-input-indeterminate-border-color: #9da5b1;
}

.form-check-success {
  --cui-form-check-input-checked-bg-color: #2eb85c;
  --cui-form-check-input-checked-border-color: #2eb85c;
  --cui-form-check-input-indeterminate-bg-color: #2eb85c;
  --cui-form-check-input-indeterminate-border-color: #2eb85c;
}

.form-check-info {
  --cui-form-check-input-checked-bg-color: #39f;
  --cui-form-check-input-checked-border-color: #39f;
  --cui-form-check-input-indeterminate-bg-color: #39f;
  --cui-form-check-input-indeterminate-border-color: #39f;
}

.form-check-warning {
  --cui-form-check-input-checked-bg-color: #f9b115;
  --cui-form-check-input-checked-border-color: #f9b115;
  --cui-form-check-input-indeterminate-bg-color: #f9b115;
  --cui-form-check-input-indeterminate-border-color: #f9b115;
}

.form-check-danger {
  --cui-form-check-input-checked-bg-color: #e55353;
  --cui-form-check-input-checked-border-color: #e55353;
  --cui-form-check-input-indeterminate-bg-color: #e55353;
  --cui-form-check-input-indeterminate-border-color: #e55353;
}

.form-check-light {
  --cui-form-check-input-checked-bg-color: #ebedef;
  --cui-form-check-input-checked-border-color: #ebedef;
  --cui-form-check-input-indeterminate-bg-color: #ebedef;
  --cui-form-check-input-indeterminate-border-color: #ebedef;
}

.form-check-dark {
  --cui-form-check-input-checked-bg-color: #4f5d73;
  --cui-form-check-input-checked-border-color: #4f5d73;
  --cui-form-check-input-indeterminate-bg-color: #4f5d73;
  --cui-form-check-input-indeterminate-border-color: #4f5d73;
}

.form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.form-range:focus {
  outline: 0;
}
.form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}
.form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}
.form-range::-moz-focus-outer {
  border: 0;
}
.form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: var(--cui-form-range-thumb-bg, #321fdb);
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.form-range::-webkit-slider-thumb:active {
  background-color: var(--cui-form-range-thumb-active-bg, #c2bcf4);
}
.form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--cui-form-range-track-bg, #c4c9d0);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: var(--cui-form-range-thumb-bg, #321fdb);
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.form-range::-moz-range-thumb:active {
  background-color: var(--cui-form-range-thumb-active-bg, #c2bcf4);
}
.form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: var(--cui-form-range-track-bg, #c4c9d0);
  border-color: transparent;
  border-radius: 1rem;
}
.form-range:disabled {
  pointer-events: none;
}
.form-range:disabled::-webkit-slider-thumb {
  background-color: var(--cui-form-range-thumb-disabled-bg, #9da5b1);
}
.form-range:disabled::-moz-range-thumb {
  background-color: var(--cui-form-range-thumb-disabled-bg, #9da5b1);
}

.form-floating {
  position: relative;
}
.form-floating > .form-control,
.form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.form-floating > label {
  position: absolute;
  top: 0;
  height: 100%;
  padding: 1rem 0.75rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
html:not([dir=rtl]) .form-floating > label {
  left: 0;
}
*[dir=rtl] .form-floating > label {
  right: 0;
}
@media (prefers-reduced-motion: reduce) {
  .form-floating > label {
    transition: none;
  }
}
.form-floating > .form-control {
  padding: 1rem 0.75rem;
}
.form-floating > .form-control::-moz-placeholder {
  color: transparent;
}
.form-floating > .form-control:-ms-input-placeholder {
  color: transparent;
}
.form-floating > .form-control::placeholder {
  color: transparent;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-ms-input-placeholder) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:not(:-ms-input-placeholder) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:focus ~ label,
.form-floating > .form-control:not(:placeholder-shown) ~ label,
.form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

.input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.input-group > .form-control,
.input-group > .form-select {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.input-group > .form-control:focus,
.input-group > .form-select:focus {
  z-index: 3;
}
.input-group .btn {
  position: relative;
  z-index: 2;
}
.input-group .btn:focus {
  z-index: 3;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: var(--cui-input-group-addon-color, rgba(44, 56, 74, 0.95));
  text-align: center;
  white-space: nowrap;
  background-color: var(--cui-input-group-addon-bg, #d8dbe0);
  border: 1px solid var(--cui-input-group-addon-border-color, #b1b7c1);
  border-radius: 0.25rem;
}

.input-group-lg > .form-control,
.input-group-lg > .form-select,
.input-group-lg > .input-group-text,
.input-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

.input-group-sm > .form-control,
.input-group-sm > .form-select,
.input-group-sm > .input-group-text,
.input-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

html:not([dir=rtl]) .input-group-lg > .form-select,
html:not([dir=rtl]) .input-group-sm > .form-select {
  padding-right: 3rem;
}
*[dir=rtl] .input-group-lg > .form-select,
*[dir=rtl] .input-group-sm > .form-select {
  padding-left: 3rem;
}

html:not([dir=rtl]) .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
html:not([dir=rtl]) .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-top-right-radius: 0;
}
*[dir=rtl] .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
*[dir=rtl] .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-top-left-radius: 0;
}
html:not([dir=rtl]) .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
html:not([dir=rtl]) .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-bottom-right-radius: 0;
}
*[dir=rtl] .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu),
*[dir=rtl] .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3) {
  border-bottom-left-radius: 0;
}
html:not([dir=rtl]) .input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
html:not([dir=rtl]) .input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-top-right-radius: 0;
}
*[dir=rtl] .input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
*[dir=rtl] .input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-top-left-radius: 0;
}
html:not([dir=rtl]) .input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
html:not([dir=rtl]) .input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-bottom-right-radius: 0;
}
*[dir=rtl] .input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu),
*[dir=rtl] .input-group.has-validation > .dropdown-toggle:nth-last-child(n+4) {
  border-bottom-left-radius: 0;
}
html:not([dir=rtl]) .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
}
*[dir=rtl] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-right: -1px;
}
html:not([dir=rtl]) .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-top-left-radius: 0;
}
*[dir=rtl] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-top-right-radius: 0;
}
html:not([dir=rtl]) .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-bottom-left-radius: 0;
}
*[dir=rtl] .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  border-bottom-right-radius: 0;
}

.valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #2eb85c;
}

.valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000015;
  background-color: rgba(46, 184, 92, 0.9);
  border-radius: 0.25rem;
}

.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip,
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip {
  display: block;
}

.was-validated .form-control:valid, .form-control.is-valid {
  border-color: #2eb85c;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%232eb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
html:not([dir=rtl]) .was-validated .form-control:valid, html:not([dir=rtl]) .form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
}
*[dir=rtl] .was-validated .form-control:valid, *[dir=rtl] .form-control.is-valid {
  padding-left: calc(1.5em + 0.75rem);
}
html:not([dir=rtl]) .was-validated .form-control:valid, html:not([dir=rtl]) .form-control.is-valid {
  background-position: right calc(0.375em + 0.1875rem) center;
}
*[dir=rtl] .was-validated .form-control:valid, *[dir=rtl] .form-control.is-valid {
  background-position: left calc(0.375em + 0.1875rem) center;
}
.was-validated .form-control:valid:focus, .form-control.is-valid:focus {
  border-color: #2eb85c;
  box-shadow: 0 0 0 0.25rem rgba(46, 184, 92, 0.25);
}

html:not([dir=rtl]) .was-validated textarea.form-control:valid, html:not([dir=rtl]) textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
}
*[dir=rtl] .was-validated textarea.form-control:valid, *[dir=rtl] textarea.form-control.is-valid {
  padding-left: calc(1.5em + 0.75rem);
}
html:not([dir=rtl]) .was-validated textarea.form-control:valid, html:not([dir=rtl]) textarea.form-control.is-valid {
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
*[dir=rtl] .was-validated textarea.form-control:valid, *[dir=rtl] textarea.form-control.is-valid {
  background-position: top calc(0.375em + 0.1875rem) left calc(0.375em + 0.1875rem);
}

.was-validated .form-select:valid, .form-select.is-valid {
  border-color: #2eb85c;
}
.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23636f83' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%232eb85c' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
html:not([dir=rtl]) .was-validated .form-select:valid:not([multiple]):not([size]), html:not([dir=rtl]) .was-validated .form-select:valid:not([multiple])[size="1"], html:not([dir=rtl]) .form-select.is-valid:not([multiple]):not([size]), html:not([dir=rtl]) .form-select.is-valid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
}
*[dir=rtl] .was-validated .form-select:valid:not([multiple]):not([size]), *[dir=rtl] .was-validated .form-select:valid:not([multiple])[size="1"], *[dir=rtl] .form-select.is-valid:not([multiple]):not([size]), *[dir=rtl] .form-select.is-valid:not([multiple])[size="1"] {
  padding-left: 4.125rem;
}
html:not([dir=rtl]) .was-validated .form-select:valid:not([multiple]):not([size]), html:not([dir=rtl]) .was-validated .form-select:valid:not([multiple])[size="1"], html:not([dir=rtl]) .form-select.is-valid:not([multiple]):not([size]), html:not([dir=rtl]) .form-select.is-valid:not([multiple])[size="1"] {
  background-position: right 0.75rem center, center right 2.25rem;
}
*[dir=rtl] .was-validated .form-select:valid:not([multiple]):not([size]), *[dir=rtl] .was-validated .form-select:valid:not([multiple])[size="1"], *[dir=rtl] .form-select.is-valid:not([multiple]):not([size]), *[dir=rtl] .form-select.is-valid:not([multiple])[size="1"] {
  background-position: left 0.75rem center, center left 2.25rem;
}
.was-validated .form-select:valid:focus, .form-select.is-valid:focus {
  border-color: #2eb85c;
  box-shadow: 0 0 0 0.25rem rgba(46, 184, 92, 0.25);
}

.was-validated .form-check-input:valid, .form-check-input.is-valid {
  border-color: #2eb85c;
}
.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked {
  background-color: #2eb85c;
}
.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(46, 184, 92, 0.25);
}
.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label {
  color: #2eb85c;
}

html:not([dir=rtl]) .form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
*[dir=rtl] .form-check-inline .form-check-input ~ .valid-feedback {
  margin-right: 0.5em;
}

.was-validated .input-group .form-control:valid, .input-group .form-control.is-valid,
.was-validated .input-group .form-select:valid,
.input-group .form-select.is-valid {
  z-index: 1;
}
.was-validated .input-group .form-control:valid:focus, .input-group .form-control.is-valid:focus,
.was-validated .input-group .form-select:valid:focus,
.input-group .form-select.is-valid:focus {
  z-index: 3;
}

.invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #e55353;
}

.invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #000015;
  background-color: rgba(229, 83, 83, 0.9);
  border-radius: 0.25rem;
}

.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip,
.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip {
  display: block;
}

.was-validated .form-control:invalid, .form-control.is-invalid {
  border-color: #e55353;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e55353'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e55353' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
html:not([dir=rtl]) .was-validated .form-control:invalid, html:not([dir=rtl]) .form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
}
*[dir=rtl] .was-validated .form-control:invalid, *[dir=rtl] .form-control.is-invalid {
  padding-left: calc(1.5em + 0.75rem);
}
html:not([dir=rtl]) .was-validated .form-control:invalid, html:not([dir=rtl]) .form-control.is-invalid {
  background-position: right calc(0.375em + 0.1875rem) center;
}
*[dir=rtl] .was-validated .form-control:invalid, *[dir=rtl] .form-control.is-invalid {
  background-position: left calc(0.375em + 0.1875rem) center;
}
.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus {
  border-color: #e55353;
  box-shadow: 0 0 0 0.25rem rgba(229, 83, 83, 0.25);
}

html:not([dir=rtl]) .was-validated textarea.form-control:invalid, html:not([dir=rtl]) textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
}
*[dir=rtl] .was-validated textarea.form-control:invalid, *[dir=rtl] textarea.form-control.is-invalid {
  padding-left: calc(1.5em + 0.75rem);
}
html:not([dir=rtl]) .was-validated textarea.form-control:invalid, html:not([dir=rtl]) textarea.form-control.is-invalid {
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
*[dir=rtl] .was-validated textarea.form-control:invalid, *[dir=rtl] textarea.form-control.is-invalid {
  background-position: top calc(0.375em + 0.1875rem) left calc(0.375em + 0.1875rem);
}

.was-validated .form-select:invalid, .form-select.is-invalid {
  border-color: #e55353;
}
.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23636f83' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23e55353'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23e55353' stroke='none'/%3e%3c/svg%3e");
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
html:not([dir=rtl]) .was-validated .form-select:invalid:not([multiple]):not([size]), html:not([dir=rtl]) .was-validated .form-select:invalid:not([multiple])[size="1"], html:not([dir=rtl]) .form-select.is-invalid:not([multiple]):not([size]), html:not([dir=rtl]) .form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
}
*[dir=rtl] .was-validated .form-select:invalid:not([multiple]):not([size]), *[dir=rtl] .was-validated .form-select:invalid:not([multiple])[size="1"], *[dir=rtl] .form-select.is-invalid:not([multiple]):not([size]), *[dir=rtl] .form-select.is-invalid:not([multiple])[size="1"] {
  padding-left: 4.125rem;
}
html:not([dir=rtl]) .was-validated .form-select:invalid:not([multiple]):not([size]), html:not([dir=rtl]) .was-validated .form-select:invalid:not([multiple])[size="1"], html:not([dir=rtl]) .form-select.is-invalid:not([multiple]):not([size]), html:not([dir=rtl]) .form-select.is-invalid:not([multiple])[size="1"] {
  background-position: right 0.75rem center, center right 2.25rem;
}
*[dir=rtl] .was-validated .form-select:invalid:not([multiple]):not([size]), *[dir=rtl] .was-validated .form-select:invalid:not([multiple])[size="1"], *[dir=rtl] .form-select.is-invalid:not([multiple]):not([size]), *[dir=rtl] .form-select.is-invalid:not([multiple])[size="1"] {
  background-position: left 0.75rem center, center left 2.25rem;
}
.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus {
  border-color: #e55353;
  box-shadow: 0 0 0 0.25rem rgba(229, 83, 83, 0.25);
}

.was-validated .form-check-input:invalid, .form-check-input.is-invalid {
  border-color: #e55353;
}
.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked {
  background-color: #e55353;
}
.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(229, 83, 83, 0.25);
}
.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label {
  color: #e55353;
}

html:not([dir=rtl]) .form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
*[dir=rtl] .form-check-inline .form-check-input ~ .invalid-feedback {
  margin-right: 0.5em;
}

.was-validated .input-group .form-control:invalid, .input-group .form-control.is-invalid,
.was-validated .input-group .form-select:invalid,
.input-group .form-select.is-invalid {
  z-index: 2;
}
.was-validated .input-group .form-control:invalid:focus, .input-group .form-control.is-invalid:focus,
.was-validated .input-group .form-select:invalid:focus,
.input-group .form-select.is-invalid:focus {
  z-index: 3;
}

.btn {
  display: inline-block;
  font-weight: 400;
  line-height: 1.5;
  color: var(--cui-btn-color, rgba(44, 56, 74, 0.95));
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  background-color: var(--cui-btn-bg, transparent);
  border: 1px solid var(--cui-btn-border-color, transparent);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  border-radius: 0.25rem;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .btn {
    transition: none;
  }
}
.btn:hover {
  color: var(--cui-btn-hover-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-btn-hover-bg);
  border-color: var(--cui-btn-hover-border-color, transparent);
}
.btn-check:focus + .btn, .btn:focus {
  color: var(--cui-btn-hover-color);
  background-color: var(--cui-btn-hover-bg);
  border-color: var(--cui-btn-hover-border-color, transparent);
  outline: 0;
  box-shadow: 0 0 0 0.25rem var(--cui-btn-shadow);
}
.btn-check:checked + .btn, .btn-check:active + .btn, .btn:active, .btn.active, .show > .btn.dropdown-toggle {
  color: var(--cui-btn-active-color);
  background-color: var(--cui-btn-active-bg);
  border-color: var(--cui-btn-active-border-color, transparent);
}
.btn-check:checked + .btn:focus, .btn-check:active + .btn:focus, .btn:active:focus, .btn.active:focus, .show > .btn.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.25rem var(--cui-btn-shadow);
}
.btn:disabled, .btn.disabled, fieldset:disabled .btn {
  color: var(--cui-btn-disabled-color);
  pointer-events: none;
  background-color: var(--cui-btn-disabled-bg);
  border-color: var(--cui-btn-disabled-border-color, transparent);
  opacity: 0.65;
}

.btn-primary {
  --cui-btn-bg: #321fdb;
  --cui-btn-border-color: #321fdb;
  --cui-btn-color: rgba(255, 255, 255, 0.87);
  --cui-btn-hover-bg: #5141e0;
  --cui-btn-hover-border-color: #4735df;
  --cui-btn-hover-color: rgba(255, 255, 255, 0.87);
  --cui-btn-active-bg: #5b4ce2;
  --cui-btn-active-border-color: #4735df;
  --cui-btn-active-color: rgba(255, 255, 255, 0.87);
  --cui-btn-disabled-bg: #321fdb;
  --cui-btn-disabled-border-color: #321fdb;
  --cui-btn-disabled-color: rgba(255, 255, 255, 0.87);
  --cui-btn-shadow: rgba(50, 31, 219, 0.5);
}

.btn-secondary {
  --cui-btn-bg: #9da5b1;
  --cui-btn-border-color: #9da5b1;
  --cui-btn-color: #000015;
  --cui-btn-hover-bg: #acb3bd;
  --cui-btn-hover-border-color: #a7aeb9;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #b1b7c1;
  --cui-btn-active-border-color: #a7aeb9;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-bg: #9da5b1;
  --cui-btn-disabled-border-color: #9da5b1;
  --cui-btn-disabled-color: #000015;
  --cui-btn-shadow: rgba(157, 165, 177, 0.5);
}

.btn-success {
  --cui-btn-bg: #2eb85c;
  --cui-btn-border-color: #2eb85c;
  --cui-btn-color: #000015;
  --cui-btn-hover-bg: #4dc374;
  --cui-btn-hover-border-color: #43bf6c;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #58c67d;
  --cui-btn-active-border-color: #43bf6c;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-bg: #2eb85c;
  --cui-btn-disabled-border-color: #2eb85c;
  --cui-btn-disabled-color: #000015;
  --cui-btn-shadow: rgba(46, 184, 92, 0.5);
}

.btn-danger {
  --cui-btn-bg: #e55353;
  --cui-btn-border-color: #e55353;
  --cui-btn-color: #000015;
  --cui-btn-hover-bg: #e96d6d;
  --cui-btn-hover-border-color: #e86464;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #ea7575;
  --cui-btn-active-border-color: #e86464;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-bg: #e55353;
  --cui-btn-disabled-border-color: #e55353;
  --cui-btn-disabled-color: #000015;
  --cui-btn-shadow: rgba(229, 83, 83, 0.5);
}

.btn-warning {
  --cui-btn-bg: #f9b115;
  --cui-btn-border-color: #f9b115;
  --cui-btn-color: rgba(44, 56, 74, 0.95);
  --cui-btn-hover-bg: #d49612;
  --cui-btn-hover-border-color: #c78e11;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #c78e11;
  --cui-btn-active-border-color: #bb8510;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-bg: #f9b115;
  --cui-btn-disabled-border-color: #f9b115;
  --cui-btn-disabled-color: rgba(44, 56, 74, 0.95);
  --cui-btn-shadow: rgba(249, 177, 21, 0.5);
}

.btn-info {
  --cui-btn-bg: #39f;
  --cui-btn-border-color: #39f;
  --cui-btn-color: #000015;
  --cui-btn-hover-bg: #52a8ff;
  --cui-btn-hover-border-color: #47a3ff;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #5cadff;
  --cui-btn-active-border-color: #47a3ff;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-bg: #39f;
  --cui-btn-disabled-border-color: #39f;
  --cui-btn-disabled-color: #000015;
  --cui-btn-shadow: rgba(51, 153, 255, 0.5);
}

.btn-light {
  --cui-btn-bg: #ebedef;
  --cui-btn-border-color: #ebedef;
  --cui-btn-color: rgba(44, 56, 74, 0.95);
  --cui-btn-hover-bg: #c8c9cb;
  --cui-btn-hover-border-color: #bcbebf;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #bcbebf;
  --cui-btn-active-border-color: #b0b2b3;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-bg: #ebedef;
  --cui-btn-disabled-border-color: #ebedef;
  --cui-btn-disabled-color: rgba(44, 56, 74, 0.95);
  --cui-btn-shadow: rgba(235, 237, 239, 0.5);
}

.btn-dark {
  --cui-btn-bg: #4f5d73;
  --cui-btn-border-color: #4f5d73;
  --cui-btn-color: rgba(255, 255, 255, 0.87);
  --cui-btn-hover-bg: #697588;
  --cui-btn-hover-border-color: #616d81;
  --cui-btn-hover-color: #fff;
  --cui-btn-active-bg: #727d8f;
  --cui-btn-active-border-color: #616d81;
  --cui-btn-active-color: #fff;
  --cui-btn-disabled-bg: #4f5d73;
  --cui-btn-disabled-border-color: #4f5d73;
  --cui-btn-disabled-color: rgba(255, 255, 255, 0.87);
  --cui-btn-shadow: rgba(79, 93, 115, 0.5);
}

.btn-outline-primary {
  --cui-btn-border-color: #321fdb;
  --cui-btn-color: #321fdb;
  --cui-btn-hover-bg: #5141e0;
  --cui-btn-hover-border-color: #4735df;
  --cui-btn-hover-color: rgba(255, 255, 255, 0.87);
  --cui-btn-active-bg: #5b4ce2;
  --cui-btn-active-border-color: #4735df;
  --cui-btn-active-color: rgba(255, 255, 255, 0.87);
  --cui-btn-disabled-color: #321fdb;
  --cui-btn-shadow: rgba(50, 31, 219, 0.5);
}

.btn-outline-secondary {
  --cui-btn-border-color: #9da5b1;
  --cui-btn-color: #9da5b1;
  --cui-btn-hover-bg: #acb3bd;
  --cui-btn-hover-border-color: #a7aeb9;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #b1b7c1;
  --cui-btn-active-border-color: #a7aeb9;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #9da5b1;
  --cui-btn-shadow: rgba(157, 165, 177, 0.5);
}

.btn-outline-success {
  --cui-btn-border-color: #2eb85c;
  --cui-btn-color: #2eb85c;
  --cui-btn-hover-bg: #4dc374;
  --cui-btn-hover-border-color: #43bf6c;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #58c67d;
  --cui-btn-active-border-color: #43bf6c;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #2eb85c;
  --cui-btn-shadow: rgba(46, 184, 92, 0.5);
}

.btn-outline-danger {
  --cui-btn-border-color: #e55353;
  --cui-btn-color: #e55353;
  --cui-btn-hover-bg: #e96d6d;
  --cui-btn-hover-border-color: #e86464;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #ea7575;
  --cui-btn-active-border-color: #e86464;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #e55353;
  --cui-btn-shadow: rgba(229, 83, 83, 0.5);
}

.btn-outline-warning {
  --cui-btn-border-color: #f9b115;
  --cui-btn-color: #f9b115;
  --cui-btn-hover-bg: #d49612;
  --cui-btn-hover-border-color: #c78e11;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #c78e11;
  --cui-btn-active-border-color: #bb8510;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #f9b115;
  --cui-btn-shadow: rgba(249, 177, 21, 0.5);
}

.btn-outline-info {
  --cui-btn-border-color: #39f;
  --cui-btn-color: #39f;
  --cui-btn-hover-bg: #52a8ff;
  --cui-btn-hover-border-color: #47a3ff;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #5cadff;
  --cui-btn-active-border-color: #47a3ff;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #39f;
  --cui-btn-shadow: rgba(51, 153, 255, 0.5);
}

.btn-outline-light {
  --cui-btn-border-color: #ebedef;
  --cui-btn-color: #ebedef;
  --cui-btn-hover-bg: #c8c9cb;
  --cui-btn-hover-border-color: #bcbebf;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #bcbebf;
  --cui-btn-active-border-color: #b0b2b3;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #ebedef;
  --cui-btn-shadow: rgba(235, 237, 239, 0.5);
}

.btn-outline-dark {
  --cui-btn-border-color: #4f5d73;
  --cui-btn-color: #4f5d73;
  --cui-btn-hover-bg: #697588;
  --cui-btn-hover-border-color: #616d81;
  --cui-btn-hover-color: #fff;
  --cui-btn-active-bg: #727d8f;
  --cui-btn-active-border-color: #616d81;
  --cui-btn-active-color: #fff;
  --cui-btn-disabled-color: #4f5d73;
  --cui-btn-shadow: rgba(79, 93, 115, 0.5);
}

.btn-ghost-primary {
  --cui-btn-color: #321fdb;
  --cui-btn-hover-bg: #5141e0;
  --cui-btn-hover-border-color: #4735df;
  --cui-btn-hover-color: rgba(255, 255, 255, 0.87);
  --cui-btn-active-bg: #5b4ce2;
  --cui-btn-active-border-color: #4735df;
  --cui-btn-active-color: rgba(255, 255, 255, 0.87);
  --cui-btn-disabled-color: #321fdb;
  --cui-btn-shadow: rgba(50, 31, 219, 0.5);
}

.btn-ghost-secondary {
  --cui-btn-color: #9da5b1;
  --cui-btn-hover-bg: #acb3bd;
  --cui-btn-hover-border-color: #a7aeb9;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #b1b7c1;
  --cui-btn-active-border-color: #a7aeb9;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #9da5b1;
  --cui-btn-shadow: rgba(157, 165, 177, 0.5);
}

.btn-ghost-success {
  --cui-btn-color: #2eb85c;
  --cui-btn-hover-bg: #4dc374;
  --cui-btn-hover-border-color: #43bf6c;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #58c67d;
  --cui-btn-active-border-color: #43bf6c;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #2eb85c;
  --cui-btn-shadow: rgba(46, 184, 92, 0.5);
}

.btn-ghost-danger {
  --cui-btn-color: #e55353;
  --cui-btn-hover-bg: #e96d6d;
  --cui-btn-hover-border-color: #e86464;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #ea7575;
  --cui-btn-active-border-color: #e86464;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #e55353;
  --cui-btn-shadow: rgba(229, 83, 83, 0.5);
}

.btn-ghost-warning {
  --cui-btn-color: #f9b115;
  --cui-btn-hover-bg: #d49612;
  --cui-btn-hover-border-color: #c78e11;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #c78e11;
  --cui-btn-active-border-color: #bb8510;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #f9b115;
  --cui-btn-shadow: rgba(249, 177, 21, 0.5);
}

.btn-ghost-info {
  --cui-btn-color: #39f;
  --cui-btn-hover-bg: #52a8ff;
  --cui-btn-hover-border-color: #47a3ff;
  --cui-btn-hover-color: #000015;
  --cui-btn-active-bg: #5cadff;
  --cui-btn-active-border-color: #47a3ff;
  --cui-btn-active-color: #000015;
  --cui-btn-disabled-color: #39f;
  --cui-btn-shadow: rgba(51, 153, 255, 0.5);
}

.btn-ghost-light {
  --cui-btn-color: #ebedef;
  --cui-btn-hover-bg: #c8c9cb;
  --cui-btn-hover-border-color: #bcbebf;
  --cui-btn-hover-color: rgba(44, 56, 74, 0.95);
  --cui-btn-active-bg: #bcbebf;
  --cui-btn-active-border-color: #b0b2b3;
  --cui-btn-active-color: rgba(44, 56, 74, 0.95);
  --cui-btn-disabled-color: #ebedef;
  --cui-btn-shadow: rgba(235, 237, 239, 0.5);
}

.btn-ghost-dark {
  --cui-btn-color: #4f5d73;
  --cui-btn-hover-bg: #697588;
  --cui-btn-hover-border-color: #616d81;
  --cui-btn-hover-color: #fff;
  --cui-btn-active-bg: #727d8f;
  --cui-btn-active-border-color: #616d81;
  --cui-btn-active-color: #fff;
  --cui-btn-disabled-color: #4f5d73;
  --cui-btn-shadow: rgba(79, 93, 115, 0.5);
}

.btn-link {
  font-weight: 400;
  color: var(--cui-btn-link-color, #321fdb);
  text-decoration: underline;
}
.btn-link:hover {
  color: var(--cui-btn-link-hover-color, #2819af);
}
.btn-link:disabled, .btn-link.disabled {
  color: var(--cui-btn-link-disabled-color, #8a93a2);
}

.btn-lg, .btn-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.3rem;
}

.btn-sm, .btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.2rem;
}

.fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .fade {
    transition: none;
  }
}
.fade:not(.show) {
  opacity: 0;
}

.collapse:not(.show) {
  display: none;
}

.collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing {
    transition: none;
  }
}
.collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .collapsing.collapse-horizontal {
    transition: none;
  }
}

.dropup,
.dropend,
.dropdown,
.dropstart {
  position: relative;
}

.dropdown-toggle {
  white-space: nowrap;
}
.dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
html:not([dir=rtl]) .dropdown-toggle::after {
  margin-left: 0.255em;
}
*[dir=rtl] .dropdown-toggle::after {
  margin-right: 0.255em;
}
html:not([dir=rtl]) .dropdown-toggle:empty::after {
  margin-left: 0;
}
*[dir=rtl] .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropdown-menu {
  position: absolute;
  z-index: 1000;
  display: none;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0;
  font-size: 1rem;
  color: var(--cui-dropdown-color, rgba(44, 56, 74, 0.95));
  text-align: start;
  list-style: none;
  background-color: var(--cui-dropdown-bg, #fff);
  background-clip: padding-box;
  border: 1px solid var(--cui-dropdown-border-color, rgba(0, 0, 21, 0.15));
  border-radius: 0.25rem;
}
.dropdown-menu[data-coreui-popper] {
  top: 100%;
  margin-top: 0.125rem;
}
html:not([dir=rtl]) .dropdown-menu[data-coreui-popper] {
  left: 0;
}
*[dir=rtl] .dropdown-menu[data-coreui-popper] {
  right: 0;
}

.dropdown-menu-start {
  --cui-position: start;
}
.dropdown-menu-start[data-coreui-popper] {
  right: auto;
  left: 0;
}

.dropdown-menu-end {
  --cui-position: end;
}
.dropdown-menu-end[data-coreui-popper] {
  right: 0;
  left: auto;
}

@media (min-width: 576px) {
  .dropdown-menu-sm-start {
    --cui-position: start;
  }
  .dropdown-menu-sm-start[data-coreui-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-sm-end {
    --cui-position: end;
  }
  .dropdown-menu-sm-end[data-coreui-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .dropdown-menu-md-start {
    --cui-position: start;
  }
  .dropdown-menu-md-start[data-coreui-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-md-end {
    --cui-position: end;
  }
  .dropdown-menu-md-end[data-coreui-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .dropdown-menu-lg-start {
    --cui-position: start;
  }
  .dropdown-menu-lg-start[data-coreui-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-lg-end {
    --cui-position: end;
  }
  .dropdown-menu-lg-end[data-coreui-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .dropdown-menu-xl-start {
    --cui-position: start;
  }
  .dropdown-menu-xl-start[data-coreui-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xl-end {
    --cui-position: end;
  }
  .dropdown-menu-xl-end[data-coreui-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .dropdown-menu-xxl-start {
    --cui-position: start;
  }
  .dropdown-menu-xxl-start[data-coreui-popper] {
    right: auto;
    left: 0;
  }

  .dropdown-menu-xxl-end {
    --cui-position: end;
  }
  .dropdown-menu-xxl-end[data-coreui-popper] {
    right: 0;
    left: auto;
  }
}
.dropup .dropdown-menu[data-coreui-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: 0.125rem;
}
.dropup .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
html:not([dir=rtl]) .dropup .dropdown-toggle::after {
  margin-left: 0.255em;
}
*[dir=rtl] .dropup .dropdown-toggle::after {
  margin-right: 0.255em;
}
html:not([dir=rtl]) .dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
*[dir=rtl] .dropup .dropdown-toggle:empty::after {
  margin-right: 0;
}

.dropend .dropdown-menu[data-coreui-popper] {
  top: 0;
  margin-top: 0;
}
html:not([dir=rtl]) .dropend .dropdown-menu[data-coreui-popper] {
  right: auto;
}
*[dir=rtl] .dropend .dropdown-menu[data-coreui-popper] {
  left: auto;
}
html:not([dir=rtl]) .dropend .dropdown-menu[data-coreui-popper] {
  left: 100%;
}
*[dir=rtl] .dropend .dropdown-menu[data-coreui-popper] {
  right: 100%;
}
html:not([dir=rtl]) .dropend .dropdown-menu[data-coreui-popper] {
  margin-left: 0.125rem;
}
*[dir=rtl] .dropend .dropdown-menu[data-coreui-popper] {
  margin-right: 0.125rem;
}
.dropend .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
html:not([dir=rtl]) .dropend .dropdown-toggle::after {
  margin-left: 0.255em;
}
*[dir=rtl] .dropend .dropdown-toggle::after {
  margin-right: 0.255em;
}
html:not([dir=rtl]) .dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
*[dir=rtl] .dropend .dropdown-toggle:empty::after {
  margin-right: 0;
}
.dropend .dropdown-toggle::after {
  vertical-align: 0;
}

.dropstart .dropdown-menu[data-coreui-popper] {
  top: 0;
  margin-top: 0;
}
html:not([dir=rtl]) .dropstart .dropdown-menu[data-coreui-popper] {
  right: 100%;
}
*[dir=rtl] .dropstart .dropdown-menu[data-coreui-popper] {
  left: 100%;
}
html:not([dir=rtl]) .dropstart .dropdown-menu[data-coreui-popper] {
  left: auto;
}
*[dir=rtl] .dropstart .dropdown-menu[data-coreui-popper] {
  right: auto;
}
html:not([dir=rtl]) .dropstart .dropdown-menu[data-coreui-popper] {
  margin-right: 0.125rem;
}
*[dir=rtl] .dropstart .dropdown-menu[data-coreui-popper] {
  margin-left: 0.125rem;
}
.dropstart .dropdown-toggle::after {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
}
html:not([dir=rtl]) .dropstart .dropdown-toggle::after {
  margin-left: 0.255em;
}
*[dir=rtl] .dropstart .dropdown-toggle::after {
  margin-right: 0.255em;
}
.dropstart .dropdown-toggle::after {
  display: none;
}
.dropstart .dropdown-toggle::before {
  display: inline-block;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
html:not([dir=rtl]) .dropstart .dropdown-toggle::before {
  margin-right: 0.255em;
}
*[dir=rtl] .dropstart .dropdown-toggle::before {
  margin-left: 0.255em;
}
html:not([dir=rtl]) .dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
*[dir=rtl] .dropstart .dropdown-toggle:empty::after {
  margin-right: 0;
}
.dropstart .dropdown-toggle::before {
  vertical-align: 0;
}

.dropdown-divider {
  height: 0;
  margin: 0.5rem 0;
  overflow: hidden;
  border-top: 1px solid var(--cui-dropdown-divider-bg, rgba(0, 0, 21, 0.15));
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 0.25rem 1rem;
  clear: both;
  font-weight: 400;
  color: var(--cui-dropdown-link-color, #4f5d73);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.dropdown-item:hover, .dropdown-item:focus {
  color: var(--cui-dropdown-link-hover-color, #475468);
  background-color: var(--cui-dropdown-link-hover-bg, #d8dbe0);
}
.dropdown-item.active, .dropdown-item:active {
  color: var(--cui-dropdown-link-active-color, rgba(255, 255, 255, 0.87));
  text-decoration: none;
  background-color: var(--cui-dropdown-link-active-bg, #321fdb);
}
.dropdown-item.disabled, .dropdown-item:disabled {
  color: var(--cui-dropdown-link-disabled-color, #9da5b1);
  pointer-events: none;
  background-color: transparent;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-header {
  display: block;
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--cui-dropdown-header-color, #8a93a2);
  white-space: nowrap;
}

.dropdown-item-text {
  display: block;
  padding: 0.25rem 1rem;
  color: var(--cui-dropdown-link-color, #4f5d73);
}

.dropdown-menu-dark {
  --cui-dropdown-color: #c4c9d0;
  --cui-dropdown-bg: #636f83;
  --cui-dropdown-border-color: rgba(0, 0, 21, 0.15);
  --cui-dropdown-link-color: #c4c9d0;
  --cui-dropdown-link-hover-color: rgba(255, 255, 255, 0.87);
  --cui-dropdown-link-active-color: rgba(255, 255, 255, 0.87);
  --cui-dropdown-link-disabled-color: #9da5b1;
  --cui-dropdown-divider-bg: rgba(0, 0, 21, 0.15);
  --cui-dropdown-header-color: #9da5b1;
}
.dropdown-menu-dark .dropdown-item:hover, .dropdown-menu-dark .dropdown-item:focus {
  background-color: var(--cui-dropdown-dark-link-hover-bg, rgba(255, 255, 255, 0.15));
}
.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-item:active {
  background-color: var(--cui-dropdown-dark-link-active-bg, #321fdb);
}

.btn-group,
.btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.btn-group > .btn,
.btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.btn-group > .btn-check:checked + .btn,
.btn-group > .btn-check:focus + .btn,
.btn-group > .btn:hover,
.btn-group > .btn:focus,
.btn-group > .btn:active,
.btn-group > .btn.active,
.btn-group-vertical > .btn-check:checked + .btn,
.btn-group-vertical > .btn-check:focus + .btn,
.btn-group-vertical > .btn:hover,
.btn-group-vertical > .btn:focus,
.btn-group-vertical > .btn:active,
.btn-group-vertical > .btn.active {
  z-index: 1;
}

.btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.btn-toolbar .input-group {
  width: auto;
}

html:not([dir=rtl]) .btn-group > .btn:not(:first-child),
html:not([dir=rtl]) .btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
*[dir=rtl] .btn-group > .btn:not(:first-child),
*[dir=rtl] .btn-group > .btn-group:not(:first-child) {
  margin-right: -1px;
}
html:not([dir=rtl]) .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
html:not([dir=rtl]) .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
}
*[dir=rtl] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
*[dir=rtl] .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-left-radius: 0;
}
html:not([dir=rtl]) .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
html:not([dir=rtl]) .btn-group > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
}
*[dir=rtl] .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
*[dir=rtl] .btn-group > .btn-group:not(:last-child) > .btn {
  border-bottom-left-radius: 0;
}
html:not([dir=rtl]) .btn-group > .btn:nth-child(n+3),
html:not([dir=rtl]) .btn-group > :not(.btn-check) + .btn,
html:not([dir=rtl]) .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
}
*[dir=rtl] .btn-group > .btn:nth-child(n+3),
*[dir=rtl] .btn-group > :not(.btn-check) + .btn,
*[dir=rtl] .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-right-radius: 0;
}
html:not([dir=rtl]) .btn-group > .btn:nth-child(n+3),
html:not([dir=rtl]) .btn-group > :not(.btn-check) + .btn,
html:not([dir=rtl]) .btn-group > .btn-group:not(:first-child) > .btn {
  border-bottom-left-radius: 0;
}
*[dir=rtl] .btn-group > .btn:nth-child(n+3),
*[dir=rtl] .btn-group > :not(.btn-check) + .btn,
*[dir=rtl] .btn-group > .btn-group:not(:first-child) > .btn {
  border-bottom-right-radius: 0;
}

.dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
html:not([dir=rtl]) .dropdown-toggle-split::after, html:not([dir=rtl]) .dropup .dropdown-toggle-split::after, html:not([dir=rtl]) .dropend .dropdown-toggle-split::after {
  margin-left: 0;
}
*[dir=rtl] .dropdown-toggle-split::after, *[dir=rtl] .dropup .dropdown-toggle-split::after, *[dir=rtl] .dropend .dropdown-toggle-split::after {
  margin-right: 0;
}
html:not([dir=rtl]) .dropstart .dropdown-toggle-split::before {
  margin-right: 0;
}
*[dir=rtl] .dropstart .dropdown-toggle-split::before {
  margin-left: 0;
}

.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}

.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}

.btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.btn-group-vertical > .btn,
.btn-group-vertical > .btn-group {
  width: 100%;
}
.btn-group-vertical > .btn:not(:first-child),
.btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.btn-group-vertical > .btn ~ .btn,
.btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0;
  list-style: none;
}
html:not([dir=rtl]) .nav {
  padding-left: 0;
}
*[dir=rtl] .nav {
  padding-right: 0;
}

.nav-link {
  display: block;
  padding: 0.5rem 1rem;
  color: var(--cui-nav-link-color, #321fdb);
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .nav-link {
    transition: none;
  }
}
.nav-link:hover, .nav-link:focus {
  color: var(--cui-nav-link-hover-color, #2819af);
}
.nav-link.disabled {
  color: var(--cui-nav-link-disabled-color, #8a93a2);
  pointer-events: none;
  cursor: default;
}

.nav-tabs {
  border-bottom: 1px solid var(--cui-nav-tabs-border-color, #c4c9d0);
}
.nav-tabs .nav-link {
  margin-bottom: -1px;
  background: none;
  border: 1px solid transparent;
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--cui-nav-tabs-link-hover-border-color, #d8dbe0 #d8dbe0 #c4c9d0);
}
.nav-tabs .nav-link.disabled {
  color: var(--cui-nav-link-disabled-color, #8a93a2);
  background-color: transparent;
  border-color: transparent;
}
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.show .nav-link {
  color: var(--cui-nav-tabs-link-active-color, #768192);
  background-color: var(--cui-nav-tabs-link-active-bg, #fff);
  border-color: var(--cui-nav-tabs-link-active-border-color, #c4c9d0 #c4c9d0 #fff);
}
.nav-tabs .dropdown-menu {
  margin-top: -1px;
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: 0.25rem;
}
.nav-pills .nav-link.active,
.nav-pills .show > .nav-link {
  color: var(--cui-nav-pills-link-active-color, rgba(255, 255, 255, 0.87));
  background-color: var(--cui-nav-pills-link-active-bg, #321fdb);
}

.nav-fill > .nav-link,
.nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}

.nav-justified > .nav-link,
.nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}

.nav-fill .nav-item .nav-link,
.nav-justified .nav-item .nav-link {
  width: 100%;
}

.tab-content > .tab-pane {
  display: none;
}
.tab-content > .active {
  display: block;
}

.navbar {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.navbar .header > .container,
.navbar .header > .container-fluid,
.navbar .header > .container-sm,
.navbar .header > .container-md,
.navbar .header > .container-lg,
.navbar .header > .container-xl,
.navbar .header > .container-xxl, .navbar > .container,
.navbar > .container-fluid,
.navbar > .container-sm,
.navbar > .container-md,
.navbar > .container-lg,
.navbar > .container-xl,
.navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.navbar-brand {
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  color: var(--cui-navbar-brand-color);
  font-size: 1.25rem;
  text-decoration: none;
  white-space: nowrap;
}
html:not([dir=rtl]) .navbar-brand {
  margin-right: 1rem;
}
*[dir=rtl] .navbar-brand {
  margin-left: 1rem;
}
.navbar-brand:hover, .navbar-brand:focus {
  color: var(--cui-navbar-brand-hover-color);
}

.navbar-nav {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  list-style: none;
}
html:not([dir=rtl]) .navbar-nav {
  padding-left: 0;
}
*[dir=rtl] .navbar-nav {
  padding-right: 0;
}
.navbar-nav .nav-link {
  padding-right: 0;
  padding-left: 0;
  color: var(--cui-navbar-color);
}
.navbar-nav .nav-link:hover, .navbar-nav .nav-link:focus {
  color: var(--cui-navbar-hover-color);
}
.navbar-nav .nav-link.disabled {
  color: var(--cui-navbar-disabled-color);
}
.navbar-nav .dropdown-menu {
  position: static;
}
.navbar-nav .show > .nav-link,
.navbar-nav .nav-link.active {
  color: var(--cui-navbar-active-color);
}

.navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--cui-navbar-color);
}
.navbar-text a,
.navbar-text a:hover,
.navbar-text a:focus {
  color: var(--cui-navbar-active-color);
}

.navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}

.navbar-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  line-height: 1;
  color: var(--cui-navbar-color);
  background-color: transparent;
  border: 1px solid var(--cui-navbar-toggler-border-color, transparent);
  border-radius: 0.25rem;
  transition: box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .navbar-toggler {
    transition: none;
  }
}
.navbar-toggler:hover {
  text-decoration: none;
}
.navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 0.25rem;
}

.navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--cui-navbar-toggler-icon);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}

.navbar-nav-scroll {
  max-height: var(--cui-scroll-height, 75vh);
  overflow-y: auto;
}

@media (min-width: 576px) {
  .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .navbar-expand-sm .offcanvas-header {
    display: none;
  }
  .navbar-expand-sm .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }
  .navbar-expand-sm .offcanvas-top,
.navbar-expand-sm .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-md .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .navbar-expand-md .offcanvas-header {
    display: none;
  }
  .navbar-expand-md .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }
  .navbar-expand-md .offcanvas-top,
.navbar-expand-md .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .navbar-expand-lg .offcanvas-header {
    display: none;
  }
  .navbar-expand-lg .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }
  .navbar-expand-lg .offcanvas-top,
.navbar-expand-lg .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }
  .navbar-expand-xl .offcanvas-top,
.navbar-expand-xl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: 0.5rem;
    padding-left: 0.5rem;
  }
  .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .navbar-expand-xxl .offcanvas-header {
    display: none;
  }
  .navbar-expand-xxl .offcanvas {
    position: inherit;
    bottom: 0;
    z-index: 1000;
    flex-grow: 1;
    visibility: visible !important;
    background-color: transparent;
    border-right: 0;
    border-left: 0;
    transition: none;
    transform: none;
  }
  .navbar-expand-xxl .offcanvas-top,
.navbar-expand-xxl .offcanvas-bottom {
    height: auto;
    border-top: 0;
    border-bottom: 0;
  }
  .navbar-expand-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.navbar-expand .navbar-nav {
  flex-direction: row;
}
.navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.navbar-expand .navbar-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}
.navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.navbar-expand .navbar-toggler {
  display: none;
}
.navbar-expand .offcanvas-header {
  display: none;
}
.navbar-expand .offcanvas {
  position: inherit;
  bottom: 0;
  z-index: 1000;
  flex-grow: 1;
  visibility: visible !important;
  background-color: transparent;
  border-right: 0;
  border-left: 0;
  transition: none;
  transform: none;
}
.navbar-expand .offcanvas-top,
.navbar-expand .offcanvas-bottom {
  height: auto;
  border-top: 0;
  border-bottom: 0;
}
.navbar-expand .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}

.navbar-light {
  --cui-navbar-brand-color: rgba(44, 56, 74, 0.95);
  --cui-navbar-brand-hover-color: rgba(44, 56, 74, 0.95);
  --cui-navbar-color: rgba(44, 56, 74, 0.681);
  --cui-navbar-hover-color: rgba(44, 56, 74, 0.95);
  --cui-navbar-active-color: rgba(44, 56, 74, 0.95);
  --cui-navbar-disabled-color: rgba(44, 56, 74, 0.38);
  --cui-navbar-toggler-border-color: rgba(0, 0, 21, 0.1);
  --cui-navbar-toggler-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%2844, 56, 74, 0.681%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.navbar-dark {
  --cui-navbar-brand-color: rgba(255, 255, 255, 0.87);
  --cui-navbar-brand-hover-color: rgba(255, 255, 255, 0.87);
  --cui-navbar-color: rgba(255, 255, 255, 0.6);
  --cui-navbar-hover-color: rgba(255, 255, 255, 0.87);
  --cui-navbar-active-color: rgba(255, 255, 255, 0.87);
  --cui-navbar-disabled-color: rgba(255, 255, 255, 0.38);
  --cui-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --cui-navbar-toggler-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.6%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--cui-card-bg, #fff);
  background-clip: border-box;
  border: 1px solid var(--cui-card-border-color, rgba(0, 0, 21, 0.125));
  border-radius: 0.25rem;
}
.card > hr {
  margin-right: 0;
  margin-left: 0;
}
.card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.card > .card-header + .list-group,
.card > .list-group + .card-footer {
  border-top: 0;
}

.card-body {
  flex: 1 1 auto;
  padding: 1rem 1rem;
  color: var(--cui-card-color, unset);
}

.card-title {
  margin-bottom: 0.5rem;
}

.card-subtitle {
  margin-top: -0.25rem;
  margin-bottom: 0;
}

.card-text:last-child {
  margin-bottom: 0;
}

html:not([dir=rtl]) .card-link + .card-link {
  margin-left: 1rem;
}
*[dir=rtl] .card-link + .card-link {
  margin-right: 1rem;
}

.card-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  color: var(--cui-card-cap-color, unset);
  background-color: var(--cui-card-cap-bg, rgba(0, 0, 21, 0.03));
  border-bottom: 1px solid var(--cui-card-border-color, rgba(0, 0, 21, 0.125));
}
.card-header:first-child {
  border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;
}

.card-footer {
  padding: 0.5rem 1rem;
  color: var(--cui-card-cap-color, unset);
  background-color: var(--cui-card-cap-bg, rgba(0, 0, 21, 0.03));
  border-top: 1px solid var(--cui-card-border-color, rgba(0, 0, 21, 0.125));
}
.card-footer:last-child {
  border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);
}

.card-header-tabs {
  margin-right: -0.5rem;
  margin-bottom: -0.5rem;
  margin-left: -0.5rem;
  border-bottom: 0;
}

.card-header-pills {
  margin-right: -0.5rem;
  margin-left: -0.5rem;
}

.card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 1rem;
  border-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-top,
.card-img-bottom {
  width: 100%;
}

.card-img,
.card-img-top {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}

.card-img,
.card-img-bottom {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}

.card-group > .card {
  margin-bottom: 0.75rem;
}
@media (min-width: 576px) {
  .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  html:not([dir=rtl]) .card-group > .card + .card {
    margin-left: 0;
  }
  *[dir=rtl] .card-group > .card + .card {
    margin-right: 0;
  }
  html:not([dir=rtl]) .card-group > .card + .card {
    border-left: 0;
  }
  *[dir=rtl] .card-group > .card + .card {
    border-right: 0;
  }
  html:not([dir=rtl]) .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .card-group > .card:not(:last-child) {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .card-group > .card:not(:last-child) {
    border-bottom-right-radius: 0;
  }
  *[dir=rtl] .card-group > .card:not(:last-child) {
    border-bottom-left-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-top,
.card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .card-group > .card:not(:last-child) .card-img-bottom,
.card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  html:not([dir=rtl]) .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
  }
  *[dir=rtl] .card-group > .card:not(:first-child) {
    border-top-right-radius: 0;
  }
  html:not([dir=rtl]) .card-group > .card:not(:first-child) {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .card-group > .card:not(:first-child) {
    border-bottom-right-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-top,
.card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .card-group > .card:not(:first-child) .card-img-bottom,
.card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}

.accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 1rem 1.25rem;
  font-size: 1rem;
  color: var(--cui-accordion-button-color, rgba(44, 56, 74, 0.95));
  text-align: left;
  background-color: var(--cui-accordion-button-bg, #fff);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button {
    transition: none;
  }
}
.accordion-button:not(.collapsed) {
  color: var(--cui-accordion-button-active-color, #2d1cc5);
  background-color: var(--cui-accordion-button-active-bg, #ebe9fb);
  box-shadow: inset 0 -1px 0 var(--cui-accordion-border-color, rgba(0, 0, 21, 0.125));
}
.accordion-button:not(.collapsed)::after {
  background-image: var(--cui-accordion-button-active-icon, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%232d1cc5'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"));
  transform: rotate(-180deg);
}
.accordion-button::after {
  flex-shrink: 0;
  width: 1.25rem;
  height: 1.25rem;
  content: "";
  background-image: var(--cui-accordion-button-icon, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%2844, 56, 74, 0.95%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"));
  background-repeat: no-repeat;
  background-size: 1.25rem;
  transition: transform 0.2s ease-in-out;
}
html:not([dir=rtl]) .accordion-button::after {
  margin-left: auto;
}
*[dir=rtl] .accordion-button::after {
  margin-right: auto;
}
@media (prefers-reduced-motion: reduce) {
  .accordion-button::after {
    transition: none;
  }
}
.accordion-button:hover {
  z-index: 2;
}
.accordion-button:focus {
  z-index: 3;
  border-color: var(--cui-accordion-button-focus-border-color, #998fed);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}

.accordion-header {
  margin-bottom: 0;
}

.accordion-item {
  background-color: var(--cui-accordion-bg, #fff);
  border: 1px solid var(--cui-accordion-border-color, rgba(0, 0, 21, 0.125));
}
.accordion-item:first-of-type {
  border-top-left-radius: 0.25rem;
  border-top-right-radius: 0.25rem;
}
.accordion-item:first-of-type .accordion-button {
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
.accordion-item:not(:first-of-type) {
  border-top: 0;
}
.accordion-item:last-of-type {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: calc(0.25rem - 1px);
  border-bottom-left-radius: calc(0.25rem - 1px);
}
.accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}

.accordion-body {
  padding: 1rem 1.25rem;
}

.accordion-flush .accordion-collapse {
  border-width: 0;
}
.accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.accordion-flush .accordion-item .accordion-button {
  border-radius: 0;
}

.breadcrumb {
  display: flex;
  flex-wrap: wrap;
  padding: 0 0;
  margin-bottom: 1rem;
  list-style: none;
  background-color: var(--cui-breadcrumb-bg, unset);
}

html:not([dir=rtl]) .breadcrumb-item + .breadcrumb-item {
  padding-left: 0.5rem;
}
*[dir=rtl] .breadcrumb-item + .breadcrumb-item {
  padding-right: 0.5rem;
}
.breadcrumb-item + .breadcrumb-item::before {
  color: var(--cui-breadcrumb-divider-color, #8a93a2);
}
html:not([dir=rtl]) .breadcrumb-item + .breadcrumb-item::before {
  float: left;
}
*[dir=rtl] .breadcrumb-item + .breadcrumb-item::before {
  float: right;
}
html:not([dir=rtl]) .breadcrumb-item + .breadcrumb-item::before {
  padding-right: 0.5rem;
}
*[dir=rtl] .breadcrumb-item + .breadcrumb-item::before {
  padding-left: 0.5rem;
}
html:not([dir=rtl]) .breadcrumb-item + .breadcrumb-item::before {
  content: var(--cui-breadcrumb-divider, "/");
}
*[dir=rtl] .breadcrumb-item + .breadcrumb-item::before {
  content: var(--cui-breadcrumb-divider-flipped, "/");
}
.breadcrumb-item.active {
  color: var(--cui-breadcrumb-active-color, #8a93a2);
}

.pagination {
  display: flex;
  list-style: none;
}
html:not([dir=rtl]) .pagination {
  padding-left: 0;
}
*[dir=rtl] .pagination {
  padding-right: 0;
}

.page-link {
  position: relative;
  display: block;
  color: var(--cui-pagination-color, #321fdb);
  text-decoration: none;
  background-color: var(--cui-pagination-bg, #fff);
  border: 1px solid var(--cui-pagination-border-color, #c4c9d0);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .page-link {
    transition: none;
  }
}
.page-link:hover {
  z-index: 2;
  color: var(--cui-pagination-hover-color, #2819af);
  background-color: var(--cui-pagination-hover-bg, #d8dbe0);
  border-color: var(--cui-pagination-hover-border-color, #c4c9d0);
}
.page-link:focus {
  z-index: 3;
  color: var(--cui-pagination-focus-color, #2819af);
  background-color: var(--cui-pagination-focus-bg, #d8dbe0);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
}

html:not([dir=rtl]) .page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
*[dir=rtl] .page-item:not(:first-child) .page-link {
  margin-right: -1px;
}
.page-item.active .page-link {
  z-index: 3;
  color: var(--cui-pagination-active-color, rgba(255, 255, 255, 0.87));
  background-color: var(--cui-pagination-active-bg, #321fdb);
  border-color: var(--cui-pagination-active-border-color, #321fdb);
}
.page-item.disabled .page-link {
  color: var(--cui-pagination-disabled-color, #8a93a2);
  pointer-events: none;
  background-color: var(--cui-pagination-disabled-bg, #fff);
  border-color: var(--cui-pagination-disabled-border-color, #c4c9d0);
}

.page-link {
  padding: 0.375rem 0.75rem;
}

html:not([dir=rtl]) .page-item:first-child .page-link {
  border-top-left-radius: 0.25rem;
}
*[dir=rtl] .page-item:first-child .page-link {
  border-top-right-radius: 0.25rem;
}
html:not([dir=rtl]) .page-item:first-child .page-link {
  border-bottom-left-radius: 0.25rem;
}
*[dir=rtl] .page-item:first-child .page-link {
  border-bottom-right-radius: 0.25rem;
}
html:not([dir=rtl]) .page-item:last-child .page-link {
  border-top-right-radius: 0.25rem;
}
*[dir=rtl] .page-item:last-child .page-link {
  border-top-left-radius: 0.25rem;
}
html:not([dir=rtl]) .page-item:last-child .page-link {
  border-bottom-right-radius: 0.25rem;
}
*[dir=rtl] .page-item:last-child .page-link {
  border-bottom-left-radius: 0.25rem;
}

.pagination-lg .page-link {
  padding: 0.75rem 1.5rem;
  font-size: 1.25rem;
}
html:not([dir=rtl]) .pagination-lg .page-item:first-child .page-link {
  border-top-left-radius: 0.3rem;
}
*[dir=rtl] .pagination-lg .page-item:first-child .page-link {
  border-top-right-radius: 0.3rem;
}
html:not([dir=rtl]) .pagination-lg .page-item:first-child .page-link {
  border-bottom-left-radius: 0.3rem;
}
*[dir=rtl] .pagination-lg .page-item:first-child .page-link {
  border-bottom-right-radius: 0.3rem;
}
html:not([dir=rtl]) .pagination-lg .page-item:last-child .page-link {
  border-top-right-radius: 0.3rem;
}
*[dir=rtl] .pagination-lg .page-item:last-child .page-link {
  border-top-left-radius: 0.3rem;
}
html:not([dir=rtl]) .pagination-lg .page-item:last-child .page-link {
  border-bottom-right-radius: 0.3rem;
}
*[dir=rtl] .pagination-lg .page-item:last-child .page-link {
  border-bottom-left-radius: 0.3rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}
html:not([dir=rtl]) .pagination-sm .page-item:first-child .page-link {
  border-top-left-radius: 0.2rem;
}
*[dir=rtl] .pagination-sm .page-item:first-child .page-link {
  border-top-right-radius: 0.2rem;
}
html:not([dir=rtl]) .pagination-sm .page-item:first-child .page-link {
  border-bottom-left-radius: 0.2rem;
}
*[dir=rtl] .pagination-sm .page-item:first-child .page-link {
  border-bottom-right-radius: 0.2rem;
}
html:not([dir=rtl]) .pagination-sm .page-item:last-child .page-link {
  border-top-right-radius: 0.2rem;
}
*[dir=rtl] .pagination-sm .page-item:last-child .page-link {
  border-top-left-radius: 0.2rem;
}
html:not([dir=rtl]) .pagination-sm .page-item:last-child .page-link {
  border-bottom-right-radius: 0.2rem;
}
*[dir=rtl] .pagination-sm .page-item:last-child .page-link {
  border-bottom-left-radius: 0.2rem;
}

.badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 700;
  line-height: 1;
  color: var(--cui-badge-color, rgba(255, 255, 255, 0.87));
  text-align: center;
  white-space: nowrap;
  vertical-align: text-bottom;
  border-radius: 0.25rem;
}
.badge:empty {
  display: none;
}

.btn .badge {
  position: relative;
  top: -1px;
}

.badge-sm {
  padding: 0.3em 0.5em;
  font-size: 0.65em;
}

.alert {
  position: relative;
  padding: 1rem 1rem;
  margin-bottom: 1rem;
  color: var(--cui-alert-color);
  background-color: var(--cui-alert-bg);
  border: 1px solid var(--cui-alert-border-color, transparent);
  border-radius: 0.25rem;
}

.alert-heading {
  color: inherit;
}

.alert-link {
  font-weight: 700;
  color: var(--cui-alert-link-color);
}

html:not([dir=rtl]) .alert-dismissible {
  padding-right: 3rem;
}
*[dir=rtl] .alert-dismissible {
  padding-left: 3rem;
}
.alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}
html:not([dir=rtl]) .alert-dismissible .btn-close {
  right: 0;
}
*[dir=rtl] .alert-dismissible .btn-close {
  left: 0;
}

.alert-primary {
  --cui-alert-color: #1e1383;
  --cui-alert-bg: #d6d2f8;
  --cui-alert-border-color: #c2bcf4;
  --cui-alert-link-color: #180f69;
}

.alert-secondary {
  --cui-alert-color: #5e636a;
  --cui-alert-bg: #ebedef;
  --cui-alert-border-color: #e2e4e8;
  --cui-alert-link-color: #4b4f55;
}

.alert-success {
  --cui-alert-color: #1c6e37;
  --cui-alert-bg: #d5f1de;
  --cui-alert-border-color: #c0eace;
  --cui-alert-link-color: #16582c;
}

.alert-danger {
  --cui-alert-color: #893232;
  --cui-alert-bg: #fadddd;
  --cui-alert-border-color: #f7cbcb;
  --cui-alert-link-color: #6e2828;
}

.alert-warning {
  --cui-alert-color: rgba(89, 77, 48, 0.97);
  --cui-alert-bg: #feefd0;
  --cui-alert-border-color: #fde8b9;
  --cui-alert-link-color: #77550a;
}

.alert-info {
  --cui-alert-color: #1f5c99;
  --cui-alert-bg: #d6ebff;
  --cui-alert-border-color: #c2e0ff;
  --cui-alert-link-color: #194a7a;
}

.alert-light {
  --cui-alert-color: rgba(85, 92, 103, 0.97);
  --cui-alert-bg: #fbfbfc;
  --cui-alert-border-color: #f9fafa;
  --cui-alert-link-color: #717272;
}

.alert-dark {
  --cui-alert-color: #2f3845;
  --cui-alert-bg: #dcdfe3;
  --cui-alert-border-color: #caced5;
  --cui-alert-link-color: #262d37;
}

@-webkit-keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}

@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.progress {
  display: flex;
  height: 1rem;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: var(--cui-progress-bg, #d8dbe0);
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--cui-progress-bar-color, rgba(255, 255, 255, 0.87));
  text-align: center;
  white-space: nowrap;
  background-color: var(--cui-progress-bar-bg, #321fdb);
  transition: width 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar {
    transition: none;
  }
}

.progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: 1rem 1rem;
}

.progress-bar-animated {
  -webkit-animation: 1s linear infinite progress-bar-stripes;
          animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .progress-bar-animated {
    -webkit-animation: none;
            animation: none;
  }
}

.progress-thin {
  height: 4px;
}

.progress.progress-white {
  background-color: rgba(255, 255, 255, 0.2);
}
.progress.progress-white .progress-bar {
  background-color: #fff;
}

.progress-group {
  display: flex;
  flex-flow: row wrap;
  margin-bottom: 1rem;
}

.progress-group-prepend {
  flex: 0 0 100px;
  align-self: center;
}

.progress-group-header {
  display: flex;
  flex-basis: 100%;
  align-items: center;
  margin-bottom: 0.25rem;
}

.progress-group-bars {
  flex-grow: 1;
  align-self: center;
}
.progress-group-bars .progress:not(:last-child) {
  margin-bottom: 2px;
}

.progress-group-header + .progress-group-bars {
  flex-basis: 100%;
}

.list-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
  border-radius: 0.25rem;
}
html:not([dir=rtl]) .list-group {
  padding-left: 0;
}
*[dir=rtl] .list-group {
  padding-right: 0;
}

.list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.list-group-numbered > li::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}

.list-group-item-action {
  width: 100%;
  color: var(--cui-list-group-action-color, #768192);
  text-align: inherit;
}
.list-group-item-action:hover, .list-group-item-action:focus {
  z-index: 1;
  color: var(--cui-list-group-action-hover-color, #768192);
  text-decoration: none;
  background-color: var(--cui-list-group-hover-bg, #ebedef);
}
.list-group-item-action:active {
  color: var(--cui-list-group-action-active-color, rgba(44, 56, 74, 0.95));
  background-color: var(--cui-list-group-action-active-bg, #d8dbe0);
  border-color: var(--cui-list-group-action-active-border-color);
}

.list-group-item {
  position: relative;
  display: block;
  padding: 0.5rem 1rem;
  color: var(--cui-list-group-color, unset);
  text-decoration: none;
  background-color: var(--cui-list-group-bg, #fff);
  border: 1px solid var(--cui-list-group-border-color, rgba(0, 0, 21, 0.125));
}
.list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.list-group-item.disabled, .list-group-item:disabled {
  color: var(--cui-list-group-disabled-color, #8a93a2);
  pointer-events: none;
  background-color: var(--cui-list-group-disabled-bg, #fff);
}
.list-group-item.active {
  z-index: 2;
  color: var(--cui-list-group-active-color, rgba(255, 255, 255, 0.87));
  background-color: var(--cui-list-group-active-bg, #321fdb);
  border-color: var(--cui-list-group-active-border-color, #321fdb);
}
.list-group-item + .list-group-item {
  border-top-width: 0;
}
.list-group-item + .list-group-item.active {
  margin-top: -1px;
  border-top-width: 1px;
}

.list-group-horizontal {
  flex-direction: row;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item:first-child {
  border-bottom-left-radius: 0.25rem;
}
*[dir=rtl] .list-group-horizontal > .list-group-item:first-child {
  border-bottom-right-radius: 0.25rem;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item:first-child {
  border-top-right-radius: 0;
}
*[dir=rtl] .list-group-horizontal > .list-group-item:first-child {
  border-top-left-radius: 0;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item:last-child {
  border-top-right-radius: 0.25rem;
}
*[dir=rtl] .list-group-horizontal > .list-group-item:last-child {
  border-top-left-radius: 0.25rem;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item:last-child {
  border-bottom-left-radius: 0;
}
*[dir=rtl] .list-group-horizontal > .list-group-item:last-child {
  border-bottom-right-radius: 0;
}
.list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: 1px;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item + .list-group-item {
  border-left-width: 0;
}
*[dir=rtl] .list-group-horizontal > .list-group-item + .list-group-item {
  border-right-width: 0;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: -1px;
}
*[dir=rtl] .list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-right: -1px;
}
html:not([dir=rtl]) .list-group-horizontal > .list-group-item + .list-group-item.active {
  border-left-width: 1px;
}
*[dir=rtl] .list-group-horizontal > .list-group-item + .list-group-item.active {
  border-right-width: 1px;
}

@media (min-width: 576px) {
  .list-group-horizontal-sm {
    flex-direction: row;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item:first-child {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item:first-child {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item:last-child {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item:last-child {
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: 1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-left-width: 0;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-right-width: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-right: -1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    border-left-width: 1px;
  }
  *[dir=rtl] .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    border-right-width: 1px;
  }
}
@media (min-width: 768px) {
  .list-group-horizontal-md {
    flex-direction: row;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item:first-child {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item:first-child {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item:last-child {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item:last-child {
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: 1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-left-width: 0;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-right-width: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-right: -1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    border-left-width: 1px;
  }
  *[dir=rtl] .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    border-right-width: 1px;
  }
}
@media (min-width: 992px) {
  .list-group-horizontal-lg {
    flex-direction: row;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item:first-child {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item:first-child {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item:last-child {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item:last-child {
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: 1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-left-width: 0;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-right-width: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-right: -1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    border-left-width: 1px;
  }
  *[dir=rtl] .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    border-right-width: 1px;
  }
}
@media (min-width: 1200px) {
  .list-group-horizontal-xl {
    flex-direction: row;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item:first-child {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item:first-child {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item:last-child {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item:last-child {
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: 1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-left-width: 0;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-right-width: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-right: -1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    border-left-width: 1px;
  }
  *[dir=rtl] .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    border-right-width: 1px;
  }
}
@media (min-width: 1400px) {
  .list-group-horizontal-xxl {
    flex-direction: row;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item:first-child {
    border-bottom-left-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item:first-child {
    border-bottom-right-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item:first-child {
    border-top-right-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item:first-child {
    border-top-left-radius: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-right-radius: 0.25rem;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item:last-child {
    border-top-left-radius: 0.25rem;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item:last-child {
    border-bottom-left-radius: 0;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item:last-child {
    border-bottom-right-radius: 0;
  }
  .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: 1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-left-width: 0;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-right-width: 0;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: -1px;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-right: -1px;
  }
  html:not([dir=rtl]) .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    border-left-width: 1px;
  }
  *[dir=rtl] .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    border-right-width: 1px;
  }
}
.list-group-flush {
  border-radius: 0;
}
.list-group-flush > .list-group-item {
  border-width: 0 0 1px;
}
.list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}

.list-group-item-primary {
  --cui-list-group-color: #1e1383;
  --cui-list-group-bg: #d6d2f8;
  --cui-list-group-hover-bg: #2d1cc5;
  --cui-list-group-action-hover-color: #1e1383;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #1e1383;
  --cui-list-group-action-active-border-color: #1e1383;
}

.list-group-item-secondary {
  --cui-list-group-color: #5e636a;
  --cui-list-group-bg: #ebedef;
  --cui-list-group-hover-bg: #8d959f;
  --cui-list-group-action-hover-color: #5e636a;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #5e636a;
  --cui-list-group-action-active-border-color: #5e636a;
}

.list-group-item-success {
  --cui-list-group-color: #1c6e37;
  --cui-list-group-bg: #d5f1de;
  --cui-list-group-hover-bg: #29a653;
  --cui-list-group-action-hover-color: #1c6e37;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #1c6e37;
  --cui-list-group-action-active-border-color: #1c6e37;
}

.list-group-item-danger {
  --cui-list-group-color: #893232;
  --cui-list-group-bg: #fadddd;
  --cui-list-group-hover-bg: #ce4b4b;
  --cui-list-group-action-hover-color: #893232;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #893232;
  --cui-list-group-action-active-border-color: #893232;
}

.list-group-item-warning {
  --cui-list-group-color: rgba(89, 77, 48, 0.97);
  --cui-list-group-bg: #feefd0;
  --cui-list-group-hover-bg: #e09f13;
  --cui-list-group-action-hover-color: rgba(89, 77, 48, 0.97);
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: rgba(89, 77, 48, 0.97);
  --cui-list-group-action-active-border-color: rgba(89, 77, 48, 0.97);
}

.list-group-item-info {
  --cui-list-group-color: #1f5c99;
  --cui-list-group-bg: #d6ebff;
  --cui-list-group-hover-bg: #2e8ae6;
  --cui-list-group-action-hover-color: #1f5c99;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #1f5c99;
  --cui-list-group-action-active-border-color: #1f5c99;
}

.list-group-item-light {
  --cui-list-group-color: rgba(85, 92, 103, 0.97);
  --cui-list-group-bg: #fbfbfc;
  --cui-list-group-hover-bg: #d4d5d7;
  --cui-list-group-action-hover-color: rgba(85, 92, 103, 0.97);
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: rgba(85, 92, 103, 0.97);
  --cui-list-group-action-active-border-color: rgba(85, 92, 103, 0.97);
}

.list-group-item-dark {
  --cui-list-group-color: #2f3845;
  --cui-list-group-bg: #dcdfe3;
  --cui-list-group-hover-bg: #475468;
  --cui-list-group-action-hover-color: #2f3845;
  --cui-list-group-action-active-color: #fff;
  --cui-list-group-action-active-bg: #2f3845;
  --cui-list-group-action-active-border-color: #2f3845;
}

.btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: var(--cui-btn-close-color, rgba(44, 56, 74, 0.95));
  background: transparent var(--cui-btn-close-bg, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%2844, 56, 74, 0.95%29'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e")) center/1em auto no-repeat;
  border: 0;
  border-radius: 0.25rem;
  opacity: 0.5;
}
.btn-close:hover {
  color: var(--cui-btn-close-color, rgba(44, 56, 74, 0.95));
  text-decoration: none;
  opacity: 0.75;
}
.btn-close:focus {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(50, 31, 219, 0.25);
  opacity: 1;
}
.btn-close:disabled, .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
  opacity: 0.25;
}

.btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}

.toast {
  width: 350px;
  max-width: 100%;
  font-size: 0.875rem;
  color: var(--cui-toast-color, unset);
  pointer-events: auto;
  background-color: var(--cui-toast-background-color, rgba(255, 255, 255, 0.85));
  background-clip: padding-box;
  border: 1px solid var(--cui-toast-border-color, rgba(0, 0, 21, 0.1));
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 21, 0.15);
  border-radius: 0.25rem;
}
.toast.showing {
  opacity: 0;
}
.toast:not(.show) {
  display: none;
}

.toast-container {
  z-index: 1090;
  width: -webkit-max-content;
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.toast-container > :not(:last-child) {
  margin-bottom: 0.75rem;
}

.toast-header {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  color: var(--cui-toast-header-color, #8a93a2);
  background-color: var(--cui-toast-header-background-color, rgba(255, 255, 255, 0.85));
  background-clip: padding-box;
  border-bottom: 1px solid var(--cui-toast-header-border-color, rgba(0, 0, 21, 0.05));
  border-top-left-radius: calc(0.25rem - 1px);
  border-top-right-radius: calc(0.25rem - 1px);
}
html:not([dir=rtl]) .toast-header .btn-close {
  margin-right: -0.375rem;
}
*[dir=rtl] .toast-header .btn-close {
  margin-left: -0.375rem;
}
html:not([dir=rtl]) .toast-header .btn-close {
  margin-left: 0.75rem;
}
*[dir=rtl] .toast-header .btn-close {
  margin-right: 0.75rem;
}

.toast-body {
  padding: 0.75rem;
  word-wrap: break-word;
}

.modal {
  position: fixed;
  top: 0;
  z-index: 1055;
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}
html:not([dir=rtl]) .modal {
  left: 0;
}
*[dir=rtl] .modal {
  right: 0;
}

.modal-dialog {
  position: relative;
  width: auto;
  margin: 0.5rem;
  pointer-events: none;
}
.modal.fade .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .modal-dialog {
    transition: none;
  }
}
.modal.show .modal-dialog {
  transform: none;
}
.modal.modal-static .modal-dialog {
  transform: scale(1.02);
}

.modal-dialog-scrollable {
  height: calc(100% - 1rem);
}
.modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}

.modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - 1rem);
}

.modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--cui-modal-content-color, unset);
  pointer-events: auto;
  background-color: var(--cui-modal-content-bg, #fff);
  background-clip: padding-box;
  border: 1px solid var(--cui-modal-content-border-color, rgba(0, 0, 21, 0.2));
  border-radius: 0.3rem;
  outline: 0;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1050;
  width: 100vw;
  height: 100vh;
  background-color: var(--cui-modal-backdrop-bg, #000015);
}
.modal-backdrop.fade {
  opacity: 0;
}
.modal-backdrop.show {
  opacity: 0.5;
}

.modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
  border-bottom: 1px solid var(--cui-modal-header-border-color, #d8dbe0);
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.modal-header .btn-close {
  padding: 0.5rem 0.5rem;
}
html:not([dir=rtl]) .modal-header .btn-close {
  margin: -0.5rem -0.5rem -0.5rem auto;
}
*[dir=rtl] .modal-header .btn-close {
  margin: -0.5rem auto -0.5rem -0.5rem;
}

.modal-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: 1rem;
}

.modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: 0.75rem;
  border-top: 1px solid var(--cui-modal-footer-border-color, #d8dbe0);
  border-bottom-right-radius: calc(0.3rem - 1px);
  border-bottom-left-radius: calc(0.3rem - 1px);
}
.modal-footer > * {
  margin: 0.25rem;
}

@media (min-width: 576px) {
  .modal-dialog {
    max-width: 500px;
    margin: 1.75rem auto;
  }

  .modal-dialog-scrollable {
    height: calc(100% - 3.5rem);
  }

  .modal-dialog-centered {
    min-height: calc(100% - 3.5rem);
  }

  .modal-sm {
    max-width: 300px;
  }
}
@media (min-width: 992px) {
  .modal-lg,
.modal-xl {
    max-width: 800px;
  }
}
@media (min-width: 1200px) {
  .modal-xl {
    max-width: 1140px;
  }
}
.modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.modal-fullscreen .modal-header {
  border-radius: 0;
}
.modal-fullscreen .modal-body {
  overflow-y: auto;
}
.modal-fullscreen .modal-footer {
  border-radius: 0;
}

@media (max-width: 575.98px) {
  .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 767.98px) {
  .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 991.98px) {
  .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1199.98px) {
  .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
}
@media (max-width: 1399.98px) {
  .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-header {
    border-radius: 0;
  }
  .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
  .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
}
.tooltip {
  position: absolute;
  z-index: 1080;
  display: block;
  margin: 0;
  font-family: var(--cui-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  opacity: 0;
}
.tooltip.show {
  opacity: 0.9;
}
.tooltip .tooltip-arrow {
  position: absolute;
  display: block;
  width: 0.8rem;
  height: 0.4rem;
}
.tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-tooltip-top, .bs-tooltip-auto[data-popper-placement^=top] {
  padding: 0.4rem 0;
}
.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: 0.4rem 0.4rem 0;
  border-top-color: var(--cui-tooltip-arrow-color, #000015);
}

.bs-tooltip-end, .bs-tooltip-auto[data-popper-placement^=right] {
  padding: 0 0.4rem;
}
.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: 0.4rem 0.4rem 0.4rem 0;
  border-right-color: var(--cui-tooltip-arrow-color, #000015);
}

.bs-tooltip-bottom, .bs-tooltip-auto[data-popper-placement^=bottom] {
  padding: 0.4rem 0;
}
.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 0.4rem 0.4rem;
  border-bottom-color: var(--cui-tooltip-arrow-color, #000015);
}

.bs-tooltip-start, .bs-tooltip-auto[data-popper-placement^=left] {
  padding: 0 0.4rem;
}
.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: 0.4rem;
  height: 0.8rem;
}
.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: 0.4rem 0 0.4rem 0.4rem;
  border-left-color: var(--cui-tooltip-arrow-color, #000015);
}

.tooltip-inner {
  max-width: 200px;
  padding: 0.25rem 0.5rem;
  color: var(--cui-tooltip-color, rgba(255, 255, 255, 0.87));
  text-align: center;
  background-color: var(--cui-tooltip-bg, #000015);
  border-radius: 0.25rem;
}

.popover {
  position: absolute;
  top: 0;
  left: 0 /* rtl:ignore */;
  z-index: 1070;
  display: block;
  max-width: 276px;
  font-family: var(--cui-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: 0.875rem;
  word-wrap: break-word;
  background-color: var(--cui-popover-bg, #fff);
  background-clip: padding-box;
  border: 1px solid var(--cui-popover-border-color, rgba(0, 0, 21, 0.2));
  border-radius: 0.3rem;
}
.popover .popover-arrow {
  position: absolute;
  display: block;
  width: 1rem;
  height: 0.5rem;
}
.popover .popover-arrow::before, .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
}

.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-0.5rem - 1px);
}
.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: var(--cui-popover-arrow-outer-color, rgba(0, 0, 21, 0.25));
}
.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: 1px;
  border-width: 0.5rem 0.5rem 0;
  border-top-color: var(--cui-popover-arrow-color, #fff);
}

.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: var(--cui-popover-arrow-outer-color, rgba(0, 0, 21, 0.25));
}
.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: 1px;
  border-width: 0.5rem 0.5rem 0.5rem 0;
  border-right-color: var(--cui-popover-arrow-color, #fff);
}

.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-0.5rem - 1px);
}
.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: var(--cui-popover-arrow-outer-color, rgba(0, 0, 21, 0.25));
}
.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: 1px;
  border-width: 0 0.5rem 0.5rem 0.5rem;
  border-bottom-color: var(--cui-popover-arrow-color, #fff);
}
.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: 1rem;
  margin-left: -0.5rem;
  content: "";
  border-bottom: 1px solid var(--cui-popover-header-bg, #f0f0f0);
}

.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-0.5rem - 1px);
  width: 0.5rem;
  height: 1rem;
}
.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: var(--cui-popover-arrow-outer-color, rgba(0, 0, 21, 0.25));
}
.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: 1px;
  border-width: 0.5rem 0 0.5rem 0.5rem;
  border-left-color: var(--cui-popover-arrow-color, #fff);
}

.popover-header {
  padding: 0.5rem 1rem;
  margin-bottom: 0;
  font-size: 1rem;
  color: var(--cui-popover-header-color, unset);
  background-color: var(--cui-popover-header-bg, #f0f0f0);
  border-bottom: 1px solid var(--cui-popover-header-border-color, #d8d8d8);
  border-top-left-radius: calc(0.3rem - 1px);
  border-top-right-radius: calc(0.3rem - 1px);
}
.popover-header:empty {
  display: none;
}

.popover-body {
  padding: 1rem 1rem;
  color: var(--cui-popover-body-color, rgba(44, 56, 74, 0.95));
}

.carousel {
  position: relative;
}

.carousel.pointer-event {
  touch-action: pan-y;
}

.carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}

.carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-item {
    transition: none;
  }
}

.carousel-item.active,
.carousel-item-next,
.carousel-item-prev {
  display: block;
}

/* rtl:begin:ignore */
.carousel-item-next:not(.carousel-item-start),
.active.carousel-item-end {
  transform: translateX(100%);
}

.carousel-item-prev:not(.carousel-item-end),
.active.carousel-item-start {
  transform: translateX(-100%);
}

/* rtl:end:ignore */
.carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.carousel-fade .carousel-item.active,
.carousel-fade .carousel-item-next.carousel-item-start,
.carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-fade .active.carousel-item-start,
.carousel-fade .active.carousel-item-end {
    transition: none;
  }
}

.carousel-control-prev,
.carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: var(--cui-carousel-control-color, rgba(255, 255, 255, 0.87));
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-control-prev,
.carousel-control-next {
    transition: none;
  }
}
.carousel-control-prev:hover, .carousel-control-prev:focus,
.carousel-control-next:hover,
.carousel-control-next:focus {
  color: var(--cui-carousel-control-color, rgba(255, 255, 255, 0.87));
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}

.carousel-control-prev {
  left: 0;
}

.carousel-control-next {
  right: 0;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}

/* rtl:options: {
  "autoRename": true,
  "stringMap":[ {
    "name"    : "prev-next",
    "search"  : "prev",
    "replace" : "next"
  } ]
} */
.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%28255, 255, 255, 0.87%29'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%28255, 255, 255, 0.87%29'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

.carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.carousel-indicators [data-coreui-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: var(--cui-carousel-indicator-active-bg, #fff);
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .carousel-indicators [data-coreui-target] {
    transition: none;
  }
}
.carousel-indicators .active {
  opacity: 1;
}

.carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: var(--cui-carousel-caption-color, rgba(255, 255, 255, 0.87));
  text-align: center;
}

.carousel-dark {
  --cui-carousel-indicator-active-bg: #000015;
  --cui-carousel-caption-color: rgba(44, 56, 74, 0.95);
}
.carousel-dark .carousel-control-prev-icon,
.carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
.carousel-dark .carousel-indicators [data-coreui-target] {
  background-color: #000015;
}
.carousel-dark .carousel-caption {
  color: rgba(44, 56, 74, 0.95);
}

@-webkit-keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}

@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.spinner-border {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  border: 0.25em solid currentColor;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: 0.75s linear infinite spinner-border;
          animation: 0.75s linear infinite spinner-border;
}

.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  border-width: 0.2em;
}

@-webkit-keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}

@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.spinner-grow {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  vertical-align: -0.125em;
  background-color: currentColor;
  border-radius: 50%;
  opacity: 0;
  -webkit-animation: 0.75s linear infinite spinner-grow;
          animation: 0.75s linear infinite spinner-grow;
}

.spinner-grow-sm {
  width: 1rem;
  height: 1rem;
}

@media (prefers-reduced-motion: reduce) {
  .spinner-border,
.spinner-grow {
    -webkit-animation-duration: 1.5s;
            animation-duration: 1.5s;
  }
}
.offcanvas {
  position: fixed;
  bottom: 0;
  z-index: 1045;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--cui-offcanvas-color, unset);
  visibility: hidden;
  background-color: var(--cui-offcanvas-bg-color, #fff);
  background-clip: padding-box;
  outline: 0;
  transition: transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .offcanvas {
    transition: none;
  }
}

.offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: var(--cui-offcanvas-backdrop-bg, #000015);
}
.offcanvas-backdrop.fade {
  opacity: 0;
}
.offcanvas-backdrop.show {
  opacity: 0.5;
}

.offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1rem;
}
.offcanvas-header .btn-close {
  padding: 0.5rem 0.5rem;
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
html:not([dir=rtl]) .offcanvas-header .btn-close {
  margin-right: -0.5rem;
}
*[dir=rtl] .offcanvas-header .btn-close {
  margin-left: -0.5rem;
}

.offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
}

.offcanvas-body {
  flex-grow: 1;
  padding: 1rem 1rem;
  overflow-y: auto;
}

.offcanvas-start {
  top: 0;
  width: 400px;
}
html:not([dir=rtl]) .offcanvas-start {
  left: 0;
}
*[dir=rtl] .offcanvas-start {
  right: 0;
}
html:not([dir=rtl]) .offcanvas-start {
  border-right: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
}
*[dir=rtl] .offcanvas-start {
  border-left: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
}
html:not([dir=rtl]) .offcanvas-start {
  transform: translateX(-100%);
}
*[dir=rtl] .offcanvas-start {
  transform: translateX(100%);
}

.offcanvas-end {
  top: 0;
  width: 400px;
}
html:not([dir=rtl]) .offcanvas-end {
  right: 0;
}
*[dir=rtl] .offcanvas-end {
  left: 0;
}
html:not([dir=rtl]) .offcanvas-end {
  border-left: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
}
*[dir=rtl] .offcanvas-end {
  border-right: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
}
html:not([dir=rtl]) .offcanvas-end {
  transform: translateX(100%);
}
*[dir=rtl] .offcanvas-end {
  transform: translateX(-100%);
}

.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-bottom: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
  transform: translateY(-100%);
}

.offcanvas-bottom {
  right: 0;
  left: 0;
  height: 30vh;
  max-height: 100%;
  border-top: 1px solid var(--cui-offcanvas-border-color, rgba(0, 0, 21, 0.2));
  transform: translateY(100%);
}

.offcanvas.show {
  transform: none !important;
}

.placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentColor;
  opacity: 0.5;
}
.placeholder.btn::before {
  display: inline-block;
  content: "";
}

.placeholder-xs {
  min-height: 0.6em;
}

.placeholder-sm {
  min-height: 0.8em;
}

.placeholder-lg {
  min-height: 1.2em;
}

.placeholder-glow .placeholder {
  -webkit-animation: placeholder-glow 2s ease-in-out infinite;
          animation: placeholder-glow 2s ease-in-out infinite;
}

@-webkit-keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}

@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.placeholder-wave {
  -webkit-mask-image: linear-gradient(130deg, #000015 55%, rgba(0, 0, 0, 0.8) 75%, #000015 95%);
          mask-image: linear-gradient(130deg, #000015 55%, rgba(0, 0, 0, 0.8) 75%, #000015 95%);
  -webkit-mask-size: 200% 100%;
          mask-size: 200% 100%;
  -webkit-animation: placeholder-wave 2s linear infinite;
          animation: placeholder-wave 2s linear infinite;
}

@-webkit-keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}

@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}
.avatar {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  border-radius: 50em;
  transition: margin 0.15s;
  width: 2rem;
  height: 2rem;
  font-size: 0.8rem;
}
@media (prefers-reduced-motion: reduce) {
  .avatar {
    transition: none;
  }
}
.avatar .avatar-status {
  width: 0.5333333333rem;
  height: 0.5333333333rem;
}

.avatar-img {
  width: 100%;
  height: auto;
  border-radius: 50em;
}

.avatar-status {
  position: absolute;
  bottom: 0;
  display: block;
  border: 1px solid #fff;
  border-radius: 50em;
}
html:not([dir=rtl]) .avatar-status {
  right: 0;
}
*[dir=rtl] .avatar-status {
  left: 0;
}

.avatar-sm {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 0.6rem;
}
.avatar-sm .avatar-status {
  width: 0.4rem;
  height: 0.4rem;
}

.avatar-md {
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1rem;
}
.avatar-md .avatar-status {
  width: 0.6666666667rem;
  height: 0.6666666667rem;
}

.avatar-lg {
  width: 3rem;
  height: 3rem;
  font-size: 1.2rem;
}
.avatar-lg .avatar-status {
  width: 0.8rem;
  height: 0.8rem;
}

.avatar-xl {
  width: 4rem;
  height: 4rem;
  font-size: 1.6rem;
}
.avatar-xl .avatar-status {
  width: 1.0666666667rem;
  height: 1.0666666667rem;
}

.avatars-stack {
  display: flex;
}
html:not([dir=rtl]) .avatars-stack .avatar {
  margin-right: -0.8rem;
}
*[dir=rtl] .avatars-stack .avatar {
  margin-left: -0.8rem;
}
html:not([dir=rtl]) .avatars-stack .avatar:hover {
  margin-right: 0;
}
*[dir=rtl] .avatars-stack .avatar:hover {
  margin-left: 0;
}
html:not([dir=rtl]) .avatars-stack .avatar-sm {
  margin-right: -0.6rem;
}
*[dir=rtl] .avatars-stack .avatar-sm {
  margin-left: -0.6rem;
}
html:not([dir=rtl]) .avatars-stack .avatar-md {
  margin-right: -1rem;
}
*[dir=rtl] .avatars-stack .avatar-md {
  margin-left: -1rem;
}
html:not([dir=rtl]) .avatars-stack .avatar-lg {
  margin-right: -1.2rem;
}
*[dir=rtl] .avatars-stack .avatar-lg {
  margin-left: -1.2rem;
}
html:not([dir=rtl]) .avatars-stack .avatar-xl {
  margin-right: -1.6rem;
}
*[dir=rtl] .avatars-stack .avatar-xl {
  margin-left: -1.6rem;
}

.callout {
  padding: 1rem 1rem;
  margin: 1rem 0;
  border: var(--cui-callout-border-width, 1px) solid var(--cui-callout-border-color, #d8dbe0);
  border-radius: 0.25rem;
}
html:not([dir=rtl]) .callout {
  border-left-width: var(--cui-callout-border-left-width, 4px);
}
*[dir=rtl] .callout {
  border-right-width: var(--cui-callout-border-right-width, 4px);
}

html:not([dir=rtl]) .callout-primary {
  border-left-color: var(--cui-callout-border-left-color, #321fdb);
}
*[dir=rtl] .callout-primary {
  border-right-color: var(--cui-callout-border-right-color, #321fdb);
}

html:not([dir=rtl]) .callout-secondary {
  border-left-color: var(--cui-callout-border-left-color, #9da5b1);
}
*[dir=rtl] .callout-secondary {
  border-right-color: var(--cui-callout-border-right-color, #9da5b1);
}

html:not([dir=rtl]) .callout-success {
  border-left-color: var(--cui-callout-border-left-color, #2eb85c);
}
*[dir=rtl] .callout-success {
  border-right-color: var(--cui-callout-border-right-color, #2eb85c);
}

html:not([dir=rtl]) .callout-danger {
  border-left-color: var(--cui-callout-border-left-color, #e55353);
}
*[dir=rtl] .callout-danger {
  border-right-color: var(--cui-callout-border-right-color, #e55353);
}

html:not([dir=rtl]) .callout-warning {
  border-left-color: var(--cui-callout-border-left-color, #f9b115);
}
*[dir=rtl] .callout-warning {
  border-right-color: var(--cui-callout-border-right-color, #f9b115);
}

html:not([dir=rtl]) .callout-info {
  border-left-color: var(--cui-callout-border-left-color, #39f);
}
*[dir=rtl] .callout-info {
  border-right-color: var(--cui-callout-border-right-color, #39f);
}

html:not([dir=rtl]) .callout-light {
  border-left-color: var(--cui-callout-border-left-color, #ebedef);
}
*[dir=rtl] .callout-light {
  border-right-color: var(--cui-callout-border-right-color, #ebedef);
}

html:not([dir=rtl]) .callout-dark {
  border-left-color: var(--cui-callout-border-left-color, #4f5d73);
}
*[dir=rtl] .callout-dark {
  border-right-color: var(--cui-callout-border-right-color, #4f5d73);
}

.footer {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  min-height: 3rem;
  padding: 0.5rem 1rem;
  color: var(--cui-footer-color, rgba(44, 56, 74, 0.95));
  background: var(--cui-footer-bg, #ebedef);
  border-top: var(--cui-footer-border-width, 1px) solid var(--cui-footer-border-color, #d8dbe0);
}

.footer-fixed {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.footer-sticky {
  position: -webkit-sticky;
  position: sticky;
  bottom: 0;
  z-index: 1030;
}

.header {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  min-height: 4rem;
  padding: 0.5rem 0.5rem;
  background: var(--cui-header-bg, #fff);
  border-bottom: var(--cui-header-border-width, 1px) solid var(--cui-header-border-color, #d8dbe0);
}
.header > .container,
.header > .container-fluid,
.header > .container-sm,
.header > .container-md,
.header > .container-lg,
.header > .container-xl,
.header > .container-xxl, .header .navbar > .container,
.header .navbar > .container-fluid,
.header .navbar > .container-sm,
.header .navbar > .container-md,
.header .navbar > .container-lg,
.header .navbar > .container-xl,
.header .navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.header .container:first-child,
.header .container-fluid:first-child,
.header .container-sm:first-child,
.header .container-md:first-child,
.header .container-lg:first-child,
.header .container-xl:first-child,
.header .container-xxl:first-child {
  min-height: 3rem;
}
.header .container:nth-child(n+2),
.header .container-fluid:nth-child(n+2),
.header .container-sm:nth-child(n+2),
.header .container-md:nth-child(n+2),
.header .container-lg:nth-child(n+2),
.header .container-xl:nth-child(n+2),
.header .container-xxl:nth-child(n+2) {
  min-height: 2rem;
}
.header.header-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1029;
}

.header-divider {
  flex-basis: calc(100% + 1rem);
  height: 0;
  margin: 0.5rem -0.5rem;
  border-top: var(--cui-header-divider-border-width, 1px) solid var(--cui-header-divider-border-color, #d8dbe0);
}

.header-brand {
  padding-top: 0.3125rem;
  padding-bottom: 0.3125rem;
  font-size: 1.25rem;
  color: var(--cui-header-brand-color, #4f5d73);
  text-decoration: none;
  white-space: nowrap;
}
html:not([dir=rtl]) .header-brand {
  margin-right: 1rem;
}
*[dir=rtl] .header-brand {
  margin-left: 1rem;
}
.header-brand:hover, .header-brand:focus {
  color: var(--cui-header-brand-hover-color, #475468);
}

.header-nav {
  display: flex;
  flex-direction: row;
  margin-bottom: 0;
  list-style: none;
}
html:not([dir=rtl]) .header-nav {
  padding-left: 0;
}
*[dir=rtl] .header-nav {
  padding-right: 0;
}
.header-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  color: var(--cui-header-color, rgba(44, 56, 74, 0.681));
}
.header-nav .nav-link:hover, .header-nav .nav-link:focus {
  color: var(--cui-header-hover-color, rgba(44, 56, 74, 0.95));
}
.header-nav .nav-link.disabled {
  color: var(--cui-header-disabled-color, rgba(44, 56, 74, 0.38));
}
.header-nav .show > .nav-link,
.header-nav .nav-link.active {
  color: var(--cui-header-active-color, rgba(44, 56, 74, 0.95));
}
.header-nav .dropdown-menu {
  position: absolute;
}

.header-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--cui-header-color, rgba(44, 56, 74, 0.681));
}
.header-text a {
  color: var(--cui-header-active-color, rgba(44, 56, 74, 0.95));
}
.header-text a:hover, .header-text a:focus {
  color: var(--cui-header-active-color, rgba(44, 56, 74, 0.95));
}

.header-toggler {
  padding: 0.25rem 0.75rem;
  font-size: 1.25rem;
  color: var(--cui-header-color, rgba(44, 56, 74, 0.681));
  background-color: var(--cui-header-toggler-bg, transparent);
  border: 0;
  border-radius: 0.25rem;
}
.header-toggler:hover {
  color: rgba(44, 56, 74, 0.95);
  text-decoration: none;
}
.header-toggler:focus {
  outline: 0;
}
.header-toggler:not(:disabled) {
  cursor: pointer;
}

.header-toggler-icon {
  display: block;
  height: 1.5625rem;
  background-image: var(--cui-header-toggler-icon-bg, url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba%2844, 56, 74, 0.681%29' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E"));
  background-repeat: no-repeat;
  background-position: center center;
  background-size: 100% 100%;
}
.header-toggler-icon:hover {
  background-image: var(--cui-header-toggler-hover-icon-bg, url("data:image/svg+xml;charset=utf8,%3Csvg viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath stroke='rgba%2844, 56, 74, 0.95%29' stroke-width='2.25' stroke-linecap='round' stroke-miterlimit='10' d='M4 7h22M4 15h22M4 23h22'/%3E%3C/svg%3E"));
}

.icon {
  display: inline-block;
  color: inherit;
  text-align: center;
  vertical-align: -0.125rem;
  fill: currentColor;
}
.icon:not(.icon-c-s):not(.icon-custom-size) {
  width: 1rem;
  height: 1rem;
  font-size: 1rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-xxl {
  width: 2rem;
  height: 2rem;
  font-size: 2rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-xl {
  width: 1.5rem;
  height: 1.5rem;
  font-size: 1.5rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-lg {
  width: 1.25rem;
  height: 1.25rem;
  font-size: 1.25rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-sm {
  width: 0.875rem;
  height: 0.875rem;
  font-size: 0.875rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-3xl {
  width: 3rem;
  height: 3rem;
  font-size: 3rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-4xl {
  width: 4rem;
  height: 4rem;
  font-size: 4rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-5xl {
  width: 5rem;
  height: 5rem;
  font-size: 5rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-6xl {
  width: 6rem;
  height: 6rem;
  font-size: 6rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-7xl {
  width: 7rem;
  height: 7rem;
  font-size: 7rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-8xl {
  width: 8rem;
  height: 8rem;
  font-size: 8rem;
}
.icon:not(.icon-c-s):not(.icon-custom-size).icon-9xl {
  width: 9rem;
  height: 9rem;
  font-size: 9rem;
}

.sidebar {
  --cui-sidebar-width: 16rem;
  position: relative;
  display: flex;
  flex: 0 0 var(--cui-sidebar-width);
  flex-direction: column;
  order: -1;
  width: var(--cui-sidebar-width);
  padding: 0 0;
  color: var(--cui-sidebar-color, rgba(255, 255, 255, 0.87));
  background: var(--cui-sidebar-bg, #3c4b64);
  box-shadow: none;
  transition: margin-left 0.15s, margin-right 0.15s, box-shadow 0.075s, transform 0.15s, width 0.15s, z-index 0s ease 0.15s;
}
html:not([dir=rtl]) .sidebar {
  border-right: var(--cui-sidebar-border-width, 0) solid var(--cui-sidebar-border-color, transparent);
}
*[dir=rtl] .sidebar {
  border-left: var(--cui-sidebar-border-width, 0) solid var(--cui-sidebar-border-color, transparent);
}
@media (prefers-reduced-motion: reduce) {
  .sidebar {
    transition: none;
  }
}
html:not([dir=rtl]) .sidebar:not(.sidebar-end) {
  margin-left: 0;
}
*[dir=rtl] .sidebar:not(.sidebar-end) {
  margin-right: 0;
}
.sidebar:not(.sidebar-end) ~ * {
  --cui-sidebar-occupy-start: 16rem;
}
.sidebar.sidebar-end {
  order: 99;
}
html:not([dir=rtl]) .sidebar.sidebar-end {
  margin-right: 0;
}
*[dir=rtl] .sidebar.sidebar-end {
  margin-left: 0;
}
.sidebar.sidebar-end ~ * {
  --cui-sidebar-occupy-end: 16rem;
}
.sidebar[class*=bg-] {
  border-color: rgba(0, 0, 21, 0.1);
}
.sidebar.sidebar-sm {
  --cui-sidebar-width: 12rem;
}
@media (min-width: 768px) {
  .sidebar.sidebar-sm:not(.sidebar-end):not(.hide) ~ * {
    --cui-sidebar-occupy-start: 12rem;
  }
  .sidebar.sidebar-sm.sidebar-end:not(.hide) ~ * {
    --cui-sidebar-occupy-end: 12rem;
  }
}
.sidebar.sidebar-lg {
  --cui-sidebar-width: 20rem;
}
@media (min-width: 768px) {
  .sidebar.sidebar-lg:not(.sidebar-end):not(.hide) ~ * {
    --cui-sidebar-occupy-start: 20rem;
  }
  .sidebar.sidebar-lg.sidebar-end:not(.hide) ~ * {
    --cui-sidebar-occupy-end: 20rem;
  }
}
.sidebar.sidebar-xl {
  --cui-sidebar-width: 24rem;
}
@media (min-width: 768px) {
  .sidebar.sidebar-xl:not(.sidebar-end):not(.hide) ~ * {
    --cui-sidebar-occupy-start: 24rem;
  }
  .sidebar.sidebar-xl.sidebar-end:not(.hide) ~ * {
    --cui-sidebar-occupy-end: 24rem;
  }
}
@media (min-width: 768px) {
  html:not([dir=rtl]) .sidebar.hide:not(.sidebar-end) {
    margin-left: calc(-1 * var(--cui-sidebar-width));
  }
  *[dir=rtl] .sidebar.hide:not(.sidebar-end) {
    margin-right: calc(-1 * var(--cui-sidebar-width));
  }
  .sidebar.hide:not(.sidebar-end) ~ * {
    --cui-sidebar-occupy-start: 0;
  }
  html:not([dir=rtl]) .sidebar.hide.sidebar-end {
    margin-right: calc(-1 * var(--cui-sidebar-width));
  }
  *[dir=rtl] .sidebar.hide.sidebar-end {
    margin-left: calc(-1 * var(--cui-sidebar-width));
  }
  .sidebar.hide.sidebar-end ~ * {
    --cui-sidebar-occupy-end: 0;
  }
}
@media (min-width: 768px) {
  .sidebar.sidebar-fixed {
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 1030;
  }
  html:not([dir=rtl]) .sidebar.sidebar-fixed:not(.sidebar-end) {
    left: 0;
  }
  *[dir=rtl] .sidebar.sidebar-fixed:not(.sidebar-end) {
    right: 0;
  }
  html:not([dir=rtl]) .sidebar.sidebar-fixed.sidebar-end {
    right: 0;
  }
  *[dir=rtl] .sidebar.sidebar-fixed.sidebar-end {
    left: 0;
  }
}
@media (min-width: 768px) {
  .sidebar.sidebar-sticky {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    height: 100vh;
  }
}
.sidebar.sidebar-overlaid {
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 1032;
}
html:not([dir=rtl]) .sidebar.sidebar-overlaid:not(.sidebar-end) {
  left: 0;
}
*[dir=rtl] .sidebar.sidebar-overlaid:not(.sidebar-end) {
  right: 0;
}
.sidebar.sidebar-overlaid:not(.sidebar-end) ~ * {
  --cui-sidebar-occupy-start: 0;
}
html:not([dir=rtl]) .sidebar.sidebar-overlaid.sidebar-end {
  right: 0;
}
*[dir=rtl] .sidebar.sidebar-overlaid.sidebar-end {
  left: 0;
}
.sidebar.sidebar-overlaid.sidebar-end ~ * {
  --cui-sidebar-occupy-end: 0;
}
@media (max-width: 767.98px) {
  .sidebar {
    --cui-is-mobile: true;
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: 1031;
  }
  html:not([dir=rtl]) .sidebar:not(.sidebar-end) {
    left: 0;
  }
  *[dir=rtl] .sidebar:not(.sidebar-end) {
    right: 0;
  }
  .sidebar:not(.sidebar-end) ~ * {
    --cui-sidebar-occupy-start: 0 !important;
  }
  html:not([dir=rtl]) .sidebar:not(.sidebar-end):not(.show) {
    margin-left: calc(-1 * var(--cui-sidebar-width));
  }
  *[dir=rtl] .sidebar:not(.sidebar-end):not(.show) {
    margin-right: calc(-1 * var(--cui-sidebar-width));
  }
  html:not([dir=rtl]) .sidebar.sidebar-end {
    right: 0;
  }
  *[dir=rtl] .sidebar.sidebar-end {
    left: 0;
  }
  .sidebar.sidebar-end ~ * {
    --cui-sidebar-occupy-end: 0 !important;
  }
  html:not([dir=rtl]) .sidebar.sidebar-end:not(.show) {
    margin-right: calc(-1 * var(--cui-sidebar-width));
  }
  *[dir=rtl] .sidebar.sidebar-end:not(.show) {
    margin-left: calc(-1 * var(--cui-sidebar-width));
  }
}

.sidebar-close {
  position: absolute;
  top: 0;
  width: 4rem;
  height: 4rem;
  color: var(--cui-sidebar-color, rgba(255, 255, 255, 0.87));
  background: transparent;
  border: 0;
}
html:not([dir=rtl]) .sidebar-close {
  right: 0;
}
*[dir=rtl] .sidebar-close {
  left: 0;
}
.sidebar-close:hover {
  text-decoration: none;
}
.sidebar-close:focus {
  outline: 0;
}

.sidebar-brand {
  display: flex;
  flex: 0 0 4rem;
  align-items: center;
  justify-content: center;
  color: var(--cui-sidebar-brand-color, rgba(255, 255, 255, 0.87));
  background: var(--cui-sidebar-brand-bg, rgba(0, 0, 21, 0.2));
}
.sidebar-brand .sidebar-brand-narrow {
  display: none;
}

.sidebar-header {
  flex: 0 0 4rem;
  padding: 0.75rem 1rem;
  text-align: center;
  background: var(--cui-sidebar-header-bg, rgba(0, 0, 21, 0.2));
  transition: height 0.15s, padding 0.15s;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-header {
    transition: none;
  }
}
.sidebar-header .nav-link {
  display: flex;
  align-items: center;
  min-height: 4rem;
}

.sidebar-footer {
  flex: 0 0 auto;
  padding: 0.75rem 1rem;
  background: var(--cui-sidebar-footer-bg, rgba(0, 0, 21, 0.2));
  transition: height 0.15s, padding 0.15s;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-footer {
    transition: none;
  }
}

.sidebar-toggler {
  display: flex;
  flex: 0 0 3rem;
  justify-content: flex-end;
  width: inherit;
  padding: 0;
  cursor: pointer;
  background-color: var(--cui-sidebar-toggler-bg, rgba(0, 0, 21, 0.2));
  border: 0;
}
@media (max-width: 767.98px) {
  .sidebar-toggler {
    display: none;
  }
}
.sidebar-toggler::before {
  display: block;
  width: 4rem;
  height: 3rem;
  content: "";
  background-image: var(--cui-sidebar-toggler-indicator, url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='%238a93a2' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E"));
  background-repeat: no-repeat;
  background-position: center;
  background-size: 0.75rem;
  transition: transform 0.15s;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-toggler::before {
    transition: none;
  }
}
*[dir=rtl] .sidebar-toggler::before {
  transform: rotate(-180deg);
}
.sidebar-toggler:focus {
  outline: 0;
}
.sidebar-toggler:hover {
  background-color: var(--cui-sidebar-toggler-hover-bg, rgba(0, 0, 0, 0.3));
}
.sidebar-toggler:hover::before {
  background-image: var(--cui-sidebar-toggler-indicator-hover, url("data:image/svg+xml;charset=utf8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 11 14'%3E%3Cpath fill='rgba%28255, 255, 255, 0.87%29' d='M9.148 2.352l-4.148 4.148 4.148 4.148q0.148 0.148 0.148 0.352t-0.148 0.352l-1.297 1.297q-0.148 0.148-0.352 0.148t-0.352-0.148l-5.797-5.797q-0.148-0.148-0.148-0.352t0.148-0.352l5.797-5.797q0.148-0.148 0.352-0.148t0.352 0.148l1.297 1.297q0.148 0.148 0.148 0.352t-0.148 0.352z'/%3E%3C/svg%3E"));
}
.sidebar-end .sidebar-toggler {
  justify-content: flex-start;
}
.sidebar-end .sidebar-toggler::before {
  transform: rotate(-180deg);
}

@media (max-width: 767.98px) {
  .sidebar-backdrop {
    position: fixed;
    top: 0;
    z-index: 1030;
    width: 100vw;
    height: 100vh;
    background-color: #000015;
    transition: opacity 0.15s linear;
  }
  html:not([dir=rtl]) .sidebar-backdrop {
    left: 0;
  }
  *[dir=rtl] .sidebar-backdrop {
    right: 0;
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .sidebar-backdrop {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .sidebar-backdrop.fade {
    opacity: 0;
  }
}
@media (max-width: 767.98px) {
  .sidebar-backdrop.show {
    opacity: 0.5;
  }
}

.sidebar-nav {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  padding: 0;
  margin-bottom: 0;
  overflow-x: hidden;
  overflow-y: auto;
  list-style: none;
}
.sidebar-nav .nav-title {
  padding: 0.75rem 1rem;
  margin-top: 1rem;
  font-size: 80%;
  font-weight: 700;
  color: var(--cui-sidebar-nav-title-color, rgba(255, 255, 255, 0.6));
  text-transform: uppercase;
  transition: height 0.15s, margin 0.15s;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-title {
    transition: none;
  }
}
.sidebar-nav .nav-link {
  display: flex;
  flex: 1;
  align-items: center;
  padding: 0.8445rem 1rem;
  color: var(--cui-sidebar-nav-link-color, rgba(255, 255, 255, 0.6));
  text-decoration: none;
  white-space: nowrap;
  background: var(--cui-sidebar-nav-link-bg, transparent);
  transition: background 0.15s ease, color 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-link {
    transition: none;
  }
}
.sidebar-nav .nav-link.active {
  color: var(--cui-sidebar-nav-link-active-color, rgba(255, 255, 255, 0.87));
  background: var(--cui-sidebar-nav-link-active-bg, rgba(255, 255, 255, 0.05));
}
.sidebar-nav .nav-link.active .nav-icon {
  color: var(--cui-sidebar-nav-link-active-icon-color, rgba(255, 255, 255, 0.87));
}
.sidebar-nav .nav-link.disabled {
  color: var(--cui-sidebar-nav-link-disabled-color, rgba(255, 255, 255, 0.38));
  pointer-events: none;
  cursor: not-allowed;
  background: transparent;
}
.sidebar-nav .nav-link.disabled .nav-icon {
  color: var(--cui-sidebar-nav-link-disabled-icon-color, rgba(255, 255, 255, 0.6));
}
.sidebar-nav .nav-link.disabled:hover {
  color: var(--cui-sidebar-nav-link-disabled-color, rgba(255, 255, 255, 0.38));
}
.sidebar-nav .nav-link.disabled:hover .nav-icon {
  color: var(--cui-sidebar-nav-link-disabled-icon-color, rgba(255, 255, 255, 0.6));
}
.sidebar-nav .nav-link.disabled:hover.nav-dropdown-toggle::after {
  background-image: var(--cui-sidebar-nav-group-indicator-hover, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%28255, 255, 255, 0.87%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"));
}
@media (hover: hover), (-ms-high-contrast: none) {
  .sidebar-nav .nav-link:hover {
    color: var(--cui-sidebar-nav-link-hover-color, rgba(255, 255, 255, 0.87));
    text-decoration: none;
    background: var(--cui-sidebar-nav-link-hover-bg, rgba(255, 255, 255, 0.05));
  }
  .sidebar-nav .nav-link:hover .nav-icon {
    color: var(--cui-sidebar-nav-link-hover-icon-color, rgba(255, 255, 255, 0.87));
  }
  .sidebar-nav .nav-link:hover.nav-group-toggle::after {
    background-image: var(--cui-sidebar-nav-group-indicator-hover, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%28255, 255, 255, 0.87%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"));
  }
}
.sidebar-nav .nav-icon {
  flex: 0 0 4rem;
  height: 1.25rem;
  font-size: 1.25rem;
  color: var(--cui-sidebar-nav-link-icon-color, rgba(255, 255, 255, 0.6));
  text-align: center;
  fill: currentColor;
  transition: inherit;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-icon {
    transition: none;
  }
}
html:not([dir=rtl]) .sidebar-nav .nav-icon:first-child {
  margin-left: -1rem;
}
*[dir=rtl] .sidebar-nav .nav-icon:first-child {
  margin-right: -1rem;
}
.sidebar-nav .nav-group {
  position: relative;
  transition: background 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-group {
    transition: none;
  }
}
.sidebar-nav .nav-group .nav-group-items {
  padding: 0 0;
  overflow: hidden;
  transition: height 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-group .nav-group-items {
    transition: none;
  }
}
.sidebar-nav .nav-group:not(.show) .nav-group-items {
  display: none;
}
.sidebar-nav .nav-group.show {
  background: var(--cui-sidebar-nav-group-bg, rgba(0, 0, 0, 0.2));
}
.sidebar-nav .nav-group.show .nav-group-toggle {
  color: var(--cui-sidebar-nav-group-toggle-show-color, rgba(255, 255, 255, 0.6));
}
.sidebar-nav .nav-group.show > .nav-group-toggle::after {
  transform: rotate(180deg);
}
.sidebar-nav .nav-group.show + .show {
  margin-top: 1px;
}
.sidebar-nav .nav-group-toggle {
  cursor: pointer;
}
.sidebar-nav .nav-group-toggle::after {
  display: block;
  flex: 0 12px;
  height: 12px;
  content: "";
  background-image: var(--cui-sidebar-nav-group-indicator, url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgba%28255, 255, 255, 0.6%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"));
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.15s;
}
html:not([dir=rtl]) .sidebar-nav .nav-group-toggle::after {
  margin-left: auto;
}
*[dir=rtl] .sidebar-nav .nav-group-toggle::after {
  margin-right: auto;
}
@media (prefers-reduced-motion: reduce) {
  .sidebar-nav .nav-group-toggle::after {
    transition: none;
  }
}
.sidebar-nav .nav-group-items {
  padding: 0;
  list-style: none;
}
html:not([dir=rtl]) .sidebar-nav .nav-group-items .nav-link {
  padding-left: 4rem;
}
*[dir=rtl] .sidebar-nav .nav-group-items .nav-link {
  padding-right: 4rem;
}
html:not([dir=rtl]) .sidebar-nav .nav-group-items .nav-link .nav-icon {
  margin-left: -4rem;
}
*[dir=rtl] .sidebar-nav .nav-group-items .nav-link .nav-icon {
  margin-right: -4rem;
}
.sidebar-nav.compact .nav-link,
.sidebar-nav .compact .nav-link {
  padding-top: 0.42225rem;
  padding-bottom: 0.42225rem;
}

@media (min-width: 768px) {
  .sidebar-narrow-unfoldable:not(:hover), .sidebar-narrow {
    z-index: 1031;
    flex: 0 0 4rem;
    width: 4rem;
    padding-bottom: 3rem;
    overflow: visible;
  }
  .sidebar-fixed.sidebar-narrow-unfoldable:not(:hover), .sidebar-fixed.sidebar-narrow {
    z-index: 1031;
    width: 4rem;
  }
  .sidebar-narrow-unfoldable:not(:hover) .sidebar-brand-full, .sidebar-narrow .sidebar-brand-full {
    display: none;
  }
  .sidebar-narrow-unfoldable:not(:hover) .sidebar-brand-narrow, .sidebar-narrow .sidebar-brand-narrow {
    display: block;
  }
  .sidebar-narrow-unfoldable:not(:hover) .d-narrow-none, .sidebar-narrow .d-narrow-none,
.sidebar-narrow-unfoldable:not(:hover) .nav-label,
.sidebar-narrow .nav-label,
.sidebar-narrow-unfoldable:not(:hover) .nav-title,
.sidebar-narrow .nav-title,
.sidebar-narrow-unfoldable:not(:hover) .nav-group-items,
.sidebar-narrow .nav-group-items,
.sidebar-narrow-unfoldable:not(:hover) .sidebar-footer,
.sidebar-narrow .sidebar-footer,
.sidebar-narrow-unfoldable:not(:hover) .sidebar-form,
.sidebar-narrow .sidebar-form,
.sidebar-narrow-unfoldable:not(:hover) .sidebar-header,
.sidebar-narrow .sidebar-header {
    height: 0 !important;
    padding: 0;
    margin: 0;
    visibility: hidden;
    opacity: 0;
  }
  .sidebar-narrow-unfoldable:not(:hover) .sidebar-toggler, .sidebar-narrow .sidebar-toggler {
    position: fixed;
    bottom: 0;
  }
  html:not([dir=rtl]) .sidebar-narrow-unfoldable:not(:hover) .sidebar-toggler::before, html:not([dir=rtl]) .sidebar-narrow .sidebar-toggler::before {
    transform: rotate(-180deg);
  }
  *[dir=rtl] .sidebar-narrow-unfoldable:not(:hover) .sidebar-toggler::before, *[dir=rtl] .sidebar-narrow .sidebar-toggler::before {
    transform: rotate(0deg);
  }
  .sidebar-end.sidebar-narrow-unfoldable:not(:hover) .sidebar-toggler::before, .sidebar-end.sidebar-narrow .sidebar-toggler::before {
    transform: rotate(0deg);
  }
}

.sidebar-narrow:not(.sidebar-end) ~ * {
  --cui-sidebar-occupy-start: 4rem;
}
.sidebar-narrow.sidebar-end ~ * {
  --cui-sidebar-occupy-end: 4rem;
}

.sidebar-narrow-unfoldable {
  position: fixed;
  z-index: 1031;
}
.sidebar-narrow-unfoldable:not(.sidebar-end) ~ * {
  --cui-sidebar-occupy-start: 4rem;
}
.sidebar-narrow-unfoldable.sidebar-end ~ * {
  --cui-sidebar-occupy-end: 4rem;
}
html:not([dir=rtl]) .sidebar-narrow-unfoldable:hover .sidebar-toggler::before {
  transform: rotate(-180deg);
}
*[dir=rtl] .sidebar-narrow-unfoldable:hover .sidebar-toggler::before {
  transform: rotate(0deg);
}
.sidebar-narrow-unfoldable:hover.sidebar-end .sidebar-toggler::before {
  transform: rotate(0deg);
}

html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow-unfoldable:not(.sidebar-end) {
  margin-left: -4rem;
}
*[dir=rtl] .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow-unfoldable:not(.sidebar-end) {
  margin-right: -4rem;
}
html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow-unfoldable.sidebar-end {
  margin-right: -4rem;
}
*[dir=rtl] .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding.sidebar-narrow-unfoldable.sidebar-end {
  margin-left: -4rem;
}

@media (max-width: 575.98px) {
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-left: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-right: -4rem;
  }
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow-unfoldable.sidebar-end {
    margin-right: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-sm.sidebar-narrow-unfoldable.sidebar-end {
    margin-left: -4rem;
  }
}
@media (max-width: 767.98px) {
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-left: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-right: -4rem;
  }
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow-unfoldable.sidebar-end {
    margin-right: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-md.sidebar-narrow-unfoldable.sidebar-end {
    margin-left: -4rem;
  }
}
@media (max-width: 991.98px) {
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-left: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-right: -4rem;
  }
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow-unfoldable.sidebar-end {
    margin-right: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-lg.sidebar-narrow-unfoldable.sidebar-end {
    margin-left: -4rem;
  }
}
@media (max-width: 1199.98px) {
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-left: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-right: -4rem;
  }
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow-unfoldable.sidebar-end {
    margin-right: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xl.sidebar-narrow-unfoldable.sidebar-end {
    margin-left: -4rem;
  }
}
@media (max-width: 1399.98px) {
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow:not(.sidebar-end), html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-left: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow:not(.sidebar-end), *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow-unfoldable:not(.sidebar-end) {
    margin-right: -4rem;
  }
  html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow.sidebar-end, html:not([dir=rtl]) .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow-unfoldable.sidebar-end {
    margin-right: -4rem;
  }
  *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow.sidebar-end, *[dir=rtl] .sidebar:not(.show).sidebar-self-hiding-xxl.sidebar-narrow-unfoldable.sidebar-end {
    margin-left: -4rem;
  }
}
.subheader {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  min-height: 3rem;
  padding: 0.5rem 1rem;
  background: var(--cui-subheader-bg, #fff);
  border-bottom: var(--cui-subheader-border-width, 1px) solid var(--cui-subheader-border-color, #d8dbe0);
}

.subheader-sticky {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1029;
}

.subheader-nav {
  display: flex;
  flex-direction: row;
  margin-bottom: 0;
  list-style: none;
}
html:not([dir=rtl]) .subheader-nav {
  padding-left: 0;
}
*[dir=rtl] .subheader-nav {
  padding-right: 0;
}
.subheader-nav .nav-link {
  padding-right: 0.5rem;
  padding-left: 0.5rem;
  color: var(--cui-subheader-color, rgba(44, 56, 74, 0.681));
}
.subheader-nav .nav-link:hover, .subheader-nav .nav-link:focus {
  color: var(--cui-subheader-hover-color, rgba(44, 56, 74, 0.95));
}
.subheader-nav .nav-link.disabled {
  color: var(--cui-subheader-disabled-color, rgba(44, 56, 74, 0.38));
}
.subheader-nav .show > .nav-link,
.subheader-nav .nav-link.active {
  color: var(--cui-subheader-active-color, rgba(44, 56, 74, 0.95));
}
.subheader-nav .dropdown-menu {
  position: absolute;
}

.subheader-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--cui-subheader-color, rgba(44, 56, 74, 0.681));
}
.subheader-text a {
  color: var(--cui-subheader-active-color, rgba(44, 56, 74, 0.95));
}
.subheader-text a:hover, .subheader-text a:focus {
  color: var(--cui-subheader-active-color, rgba(44, 56, 74, 0.95));
}

.clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.link-primary {
  color: #321fdb;
}
.link-primary:hover, .link-primary:focus {
  color: #5b4ce2;
}

.link-secondary {
  color: #9da5b1;
}
.link-secondary:hover, .link-secondary:focus {
  color: #b1b7c1;
}

.link-success {
  color: #2eb85c;
}
.link-success:hover, .link-success:focus {
  color: #58c67d;
}

.link-info {
  color: #39f;
}
.link-info:hover, .link-info:focus {
  color: #5cadff;
}

.link-warning {
  color: #f9b115;
}
.link-warning:hover, .link-warning:focus {
  color: #c78e11;
}

.link-danger {
  color: #e55353;
}
.link-danger:hover, .link-danger:focus {
  color: #ea7575;
}

.link-light {
  color: #ebedef;
}
.link-light:hover, .link-light:focus {
  color: #bcbebf;
}

.link-dark {
  color: #4f5d73;
}
.link-dark:hover, .link-dark:focus {
  color: #727d8f;
}

.ratio {
  position: relative;
  width: 100%;
}
.ratio::before {
  display: block;
  padding-top: var(--cui-aspect-ratio);
  content: "";
}
.ratio > * {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
}
html:not([dir=rtl]) .ratio > * {
  left: 0;
}
*[dir=rtl] .ratio > * {
  right: 0;
}

.ratio-1x1 {
  --cui-aspect-ratio: 100%;
}

.ratio-4x3 {
  --cui-aspect-ratio: 75%;
}

.ratio-16x9 {
  --cui-aspect-ratio: 56.25%;
}

.ratio-21x9 {
  --cui-aspect-ratio: 42.8571428571%;
}

.fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}

.fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}

.sticky-top {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 1020;
}

@media (min-width: 576px) {
  .sticky-sm-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .sticky-md-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .sticky-lg-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .sticky-xl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .sticky-xxl-top {
    position: -webkit-sticky;
    position: sticky;
    top: 0;
    z-index: 1020;
  }
}
.hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}

.vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}

.visually-hidden,
.visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

.stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.vr {
  display: inline-block;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentColor;
  opacity: 0.25;
}

.align-baseline {
  vertical-align: baseline !important;
}

.align-top {
  vertical-align: top !important;
}

.align-middle {
  vertical-align: middle !important;
}

.align-bottom {
  vertical-align: bottom !important;
}

.align-text-bottom {
  vertical-align: text-bottom !important;
}

.align-text-top {
  vertical-align: text-top !important;
}

html:not([dir=rtl]) .float-start {
  float: left !important;
}
*[dir=rtl] .float-start {
  float: right !important;
}

html:not([dir=rtl]) .float-end {
  float: right !important;
}
*[dir=rtl] .float-end {
  float: left !important;
}

html:not([dir=rtl]) .float-none {
  float: none !important;
}
*[dir=rtl] .float-none {
  float: none !important;
}

.opacity-0 {
  opacity: 0 !important;
}

.opacity-25 {
  opacity: 0.25 !important;
}

.opacity-50 {
  opacity: 0.5 !important;
}

.opacity-75 {
  opacity: 0.75 !important;
}

.opacity-100 {
  opacity: 1 !important;
}

.overflow-auto {
  overflow: auto !important;
}

.overflow-hidden {
  overflow: hidden !important;
}

.overflow-visible {
  overflow: visible !important;
}

.overflow-scroll {
  overflow: scroll !important;
}

.d-inline {
  display: inline !important;
}

.d-inline-block {
  display: inline-block !important;
}

.d-block {
  display: block !important;
}

.d-grid {
  display: grid !important;
}

.d-table {
  display: table !important;
}

.d-table-row {
  display: table-row !important;
}

.d-table-cell {
  display: table-cell !important;
}

.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.d-none {
  display: none !important;
}

.shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 21, 0.15) !important;
}

.shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 21, 0.075) !important;
}

.shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 21, 0.175) !important;
}

.shadow-none {
  box-shadow: none !important;
}

.position-static {
  position: static !important;
}

.position-relative {
  position: relative !important;
}

.position-absolute {
  position: absolute !important;
}

.position-fixed {
  position: fixed !important;
}

.position-sticky {
  position: -webkit-sticky !important;
  position: sticky !important;
}

.top-0 {
  top: 0 !important;
}

.top-50 {
  top: 50% !important;
}

.top-100 {
  top: 100% !important;
}

.bottom-0 {
  bottom: 0 !important;
}

.bottom-50 {
  bottom: 50% !important;
}

.bottom-100 {
  bottom: 100% !important;
}

.start-0 {
  left: 0 !important;
}

.start-50 {
  left: 50% !important;
}

.start-100 {
  left: 100% !important;
}

.end-0 {
  right: 0 !important;
}

.end-50 {
  right: 50% !important;
}

.end-100 {
  right: 100% !important;
}

.translate-middle {
  transform: translate(-50%, -50%) !important;
}

.translate-middle-x {
  transform: translateX(-50%) !important;
}

.translate-middle-y {
  transform: translateY(-50%) !important;
}

.border {
  border: 1px solid var(--cui-border-color, #d8dbe0) !important;
}

.border-0 {
  border: 0 !important;
}

.border-top {
  border-top: 1px solid var(--cui-border-color, #d8dbe0) !important;
}

.border-top-0 {
  border-top: 0 !important;
}

html:not([dir=rtl]) .border-end {
  border-right: 1px solid var(--cui-border-color, #d8dbe0) !important;
}
*[dir=rtl] .border-end {
  border-left: 1px solid var(--cui-border-color, #d8dbe0) !important;
}

html:not([dir=rtl]) .border-end-0 {
  border-right: 0 !important;
}
*[dir=rtl] .border-end-0 {
  border-left: 0 !important;
}

.border-bottom {
  border-bottom: 1px solid var(--cui-border-color, #d8dbe0) !important;
}

.border-bottom-0 {
  border-bottom: 0 !important;
}

html:not([dir=rtl]) .border-start {
  border-left: 1px solid var(--cui-border-color, #d8dbe0) !important;
}
*[dir=rtl] .border-start {
  border-right: 1px solid var(--cui-border-color, #d8dbe0) !important;
}

html:not([dir=rtl]) .border-start-0 {
  border-left: 0 !important;
}
*[dir=rtl] .border-start-0 {
  border-right: 0 !important;
}

.border-primary {
  border-color: #321fdb !important;
}

.border-secondary {
  border-color: #9da5b1 !important;
}

.border-success {
  border-color: #2eb85c !important;
}

.border-info {
  border-color: #39f !important;
}

.border-warning {
  border-color: #f9b115 !important;
}

.border-danger {
  border-color: #e55353 !important;
}

.border-light {
  border-color: #ebedef !important;
}

.border-dark {
  border-color: #4f5d73 !important;
}

.border-white {
  border-color: #fff !important;
}

.border-top-primary {
  border-top-color: #321fdb !important;
}

.border-top-secondary {
  border-top-color: #9da5b1 !important;
}

.border-top-success {
  border-top-color: #2eb85c !important;
}

.border-top-info {
  border-top-color: #39f !important;
}

.border-top-warning {
  border-top-color: #f9b115 !important;
}

.border-top-danger {
  border-top-color: #e55353 !important;
}

.border-top-light {
  border-top-color: #ebedef !important;
}

.border-top-dark {
  border-top-color: #4f5d73 !important;
}

.border-top-white {
  border-top-color: #fff !important;
}

html:not([dir=rtl]) .border-end-primary {
  border-right-color: #321fdb !important;
}
*[dir=rtl] .border-end-primary {
  border-left-color: #321fdb !important;
}

html:not([dir=rtl]) .border-end-secondary {
  border-right-color: #9da5b1 !important;
}
*[dir=rtl] .border-end-secondary {
  border-left-color: #9da5b1 !important;
}

html:not([dir=rtl]) .border-end-success {
  border-right-color: #2eb85c !important;
}
*[dir=rtl] .border-end-success {
  border-left-color: #2eb85c !important;
}

html:not([dir=rtl]) .border-end-info {
  border-right-color: #39f !important;
}
*[dir=rtl] .border-end-info {
  border-left-color: #39f !important;
}

html:not([dir=rtl]) .border-end-warning {
  border-right-color: #f9b115 !important;
}
*[dir=rtl] .border-end-warning {
  border-left-color: #f9b115 !important;
}

html:not([dir=rtl]) .border-end-danger {
  border-right-color: #e55353 !important;
}
*[dir=rtl] .border-end-danger {
  border-left-color: #e55353 !important;
}

html:not([dir=rtl]) .border-end-light {
  border-right-color: #ebedef !important;
}
*[dir=rtl] .border-end-light {
  border-left-color: #ebedef !important;
}

html:not([dir=rtl]) .border-end-dark {
  border-right-color: #4f5d73 !important;
}
*[dir=rtl] .border-end-dark {
  border-left-color: #4f5d73 !important;
}

html:not([dir=rtl]) .border-end-white {
  border-right-color: #fff !important;
}
*[dir=rtl] .border-end-white {
  border-left-color: #fff !important;
}

.border-bottom-primary {
  border-bottom-color: #321fdb !important;
}

.border-bottom-secondary {
  border-bottom-color: #9da5b1 !important;
}

.border-bottom-success {
  border-bottom-color: #2eb85c !important;
}

.border-bottom-info {
  border-bottom-color: #39f !important;
}

.border-bottom-warning {
  border-bottom-color: #f9b115 !important;
}

.border-bottom-danger {
  border-bottom-color: #e55353 !important;
}

.border-bottom-light {
  border-bottom-color: #ebedef !important;
}

.border-bottom-dark {
  border-bottom-color: #4f5d73 !important;
}

.border-bottom-white {
  border-bottom-color: #fff !important;
}

html:not([dir=rtl]) .border-start-primary {
  border-left-color: #321fdb !important;
}
*[dir=rtl] .border-start-primary {
  border-right-color: #321fdb !important;
}

html:not([dir=rtl]) .border-start-secondary {
  border-left-color: #9da5b1 !important;
}
*[dir=rtl] .border-start-secondary {
  border-right-color: #9da5b1 !important;
}

html:not([dir=rtl]) .border-start-success {
  border-left-color: #2eb85c !important;
}
*[dir=rtl] .border-start-success {
  border-right-color: #2eb85c !important;
}

html:not([dir=rtl]) .border-start-info {
  border-left-color: #39f !important;
}
*[dir=rtl] .border-start-info {
  border-right-color: #39f !important;
}

html:not([dir=rtl]) .border-start-warning {
  border-left-color: #f9b115 !important;
}
*[dir=rtl] .border-start-warning {
  border-right-color: #f9b115 !important;
}

html:not([dir=rtl]) .border-start-danger {
  border-left-color: #e55353 !important;
}
*[dir=rtl] .border-start-danger {
  border-right-color: #e55353 !important;
}

html:not([dir=rtl]) .border-start-light {
  border-left-color: #ebedef !important;
}
*[dir=rtl] .border-start-light {
  border-right-color: #ebedef !important;
}

html:not([dir=rtl]) .border-start-dark {
  border-left-color: #4f5d73 !important;
}
*[dir=rtl] .border-start-dark {
  border-right-color: #4f5d73 !important;
}

html:not([dir=rtl]) .border-start-white {
  border-left-color: #fff !important;
}
*[dir=rtl] .border-start-white {
  border-right-color: #fff !important;
}

.border-1 {
  border-width: 1px !important;
}

.border-2 {
  border-width: 2px !important;
}

.border-3 {
  border-width: 3px !important;
}

.border-4 {
  border-width: 4px !important;
}

.border-5 {
  border-width: 5px !important;
}

.border-top-1 {
  border-top-width: 1px !important;
}

.border-top-2 {
  border-top-width: 2px !important;
}

.border-top-3 {
  border-top-width: 3px !important;
}

.border-top-4 {
  border-top-width: 4px !important;
}

.border-top-5 {
  border-top-width: 5px !important;
}

html:not([dir=rtl]) .border-end-1 {
  border-right-width: 1px !important;
}
*[dir=rtl] .border-end-1 {
  border-left-width: 1px !important;
}

html:not([dir=rtl]) .border-end-2 {
  border-right-width: 2px !important;
}
*[dir=rtl] .border-end-2 {
  border-left-width: 2px !important;
}

html:not([dir=rtl]) .border-end-3 {
  border-right-width: 3px !important;
}
*[dir=rtl] .border-end-3 {
  border-left-width: 3px !important;
}

html:not([dir=rtl]) .border-end-4 {
  border-right-width: 4px !important;
}
*[dir=rtl] .border-end-4 {
  border-left-width: 4px !important;
}

html:not([dir=rtl]) .border-end-5 {
  border-right-width: 5px !important;
}
*[dir=rtl] .border-end-5 {
  border-left-width: 5px !important;
}

.border-bottom-1 {
  border-bottom-width: 1px !important;
}

.border-bottom-2 {
  border-bottom-width: 2px !important;
}

.border-bottom-3 {
  border-bottom-width: 3px !important;
}

.border-bottom-4 {
  border-bottom-width: 4px !important;
}

.border-bottom-5 {
  border-bottom-width: 5px !important;
}

html:not([dir=rtl]) .border-start-1 {
  border-left-width: 1px !important;
}
*[dir=rtl] .border-start-1 {
  border-right-width: 1px !important;
}

html:not([dir=rtl]) .border-start-2 {
  border-left-width: 2px !important;
}
*[dir=rtl] .border-start-2 {
  border-right-width: 2px !important;
}

html:not([dir=rtl]) .border-start-3 {
  border-left-width: 3px !important;
}
*[dir=rtl] .border-start-3 {
  border-right-width: 3px !important;
}

html:not([dir=rtl]) .border-start-4 {
  border-left-width: 4px !important;
}
*[dir=rtl] .border-start-4 {
  border-right-width: 4px !important;
}

html:not([dir=rtl]) .border-start-5 {
  border-left-width: 5px !important;
}
*[dir=rtl] .border-start-5 {
  border-right-width: 5px !important;
}

.w-25 {
  width: 25% !important;
}

.w-50 {
  width: 50% !important;
}

.w-75 {
  width: 75% !important;
}

.w-100 {
  width: 100% !important;
}

.w-auto {
  width: auto !important;
}

.mw-100 {
  max-width: 100% !important;
}

.vw-100 {
  width: 100vw !important;
}

.min-vw-100 {
  min-width: 100vw !important;
}

.h-25 {
  height: 25% !important;
}

.h-50 {
  height: 50% !important;
}

.h-75 {
  height: 75% !important;
}

.h-100 {
  height: 100% !important;
}

.h-auto {
  height: auto !important;
}

.mh-100 {
  max-height: 100% !important;
}

.vh-100 {
  height: 100vh !important;
}

.min-vh-100 {
  min-height: 100vh !important;
}

.flex-fill {
  flex: 1 1 auto !important;
}

.flex-row {
  flex-direction: row !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row-reverse {
  flex-direction: row-reverse !important;
}

.flex-column-reverse {
  flex-direction: column-reverse !important;
}

.flex-grow-0 {
  flex-grow: 0 !important;
}

.flex-grow-1 {
  flex-grow: 1 !important;
}

.flex-shrink-0 {
  flex-shrink: 0 !important;
}

.flex-shrink-1 {
  flex-shrink: 1 !important;
}

.flex-wrap {
  flex-wrap: wrap !important;
}

.flex-nowrap {
  flex-wrap: nowrap !important;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}

.gap-0 {
  gap: 0 !important;
}

.gap-1 {
  gap: 0.25rem !important;
}

.gap-2 {
  gap: 0.5rem !important;
}

.gap-3 {
  gap: 1rem !important;
}

.gap-4 {
  gap: 1.5rem !important;
}

.gap-5 {
  gap: 3rem !important;
}

.justify-content-start {
  justify-content: flex-start !important;
}

.justify-content-end {
  justify-content: flex-end !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.justify-content-around {
  justify-content: space-around !important;
}

.justify-content-evenly {
  justify-content: space-evenly !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.align-items-end {
  align-items: flex-end !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-baseline {
  align-items: baseline !important;
}

.align-items-stretch {
  align-items: stretch !important;
}

.align-content-start {
  align-content: flex-start !important;
}

.align-content-end {
  align-content: flex-end !important;
}

.align-content-center {
  align-content: center !important;
}

.align-content-between {
  align-content: space-between !important;
}

.align-content-around {
  align-content: space-around !important;
}

.align-content-stretch {
  align-content: stretch !important;
}

.align-self-auto {
  align-self: auto !important;
}

.align-self-start {
  align-self: flex-start !important;
}

.align-self-end {
  align-self: flex-end !important;
}

.align-self-center {
  align-self: center !important;
}

.align-self-baseline {
  align-self: baseline !important;
}

.align-self-stretch {
  align-self: stretch !important;
}

.order-first {
  order: -1 !important;
}

.order-0 {
  order: 0 !important;
}

.order-1 {
  order: 1 !important;
}

.order-2 {
  order: 2 !important;
}

.order-3 {
  order: 3 !important;
}

.order-4 {
  order: 4 !important;
}

.order-5 {
  order: 5 !important;
}

.order-last {
  order: 6 !important;
}

.m-0 {
  margin: 0 !important;
}

.m-1 {
  margin: 0.25rem !important;
}

.m-2 {
  margin: 0.5rem !important;
}

.m-3 {
  margin: 1rem !important;
}

.m-4 {
  margin: 1.5rem !important;
}

.m-5 {
  margin: 3rem !important;
}

.m-auto {
  margin: auto !important;
}

.mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}

.mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}

.mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}

.mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}

.mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}

.mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}

.my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

.my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}

.my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}

.my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}

.my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}

.my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}

.my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}

.mt-0 {
  margin-top: 0 !important;
}

.mt-1 {
  margin-top: 0.25rem !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mt-3 {
  margin-top: 1rem !important;
}

.mt-4 {
  margin-top: 1.5rem !important;
}

.mt-5 {
  margin-top: 3rem !important;
}

.mt-auto {
  margin-top: auto !important;
}

html:not([dir=rtl]) .me-0 {
  margin-right: 0 !important;
}
*[dir=rtl] .me-0 {
  margin-left: 0 !important;
}

html:not([dir=rtl]) .me-1 {
  margin-right: 0.25rem !important;
}
*[dir=rtl] .me-1 {
  margin-left: 0.25rem !important;
}

html:not([dir=rtl]) .me-2 {
  margin-right: 0.5rem !important;
}
*[dir=rtl] .me-2 {
  margin-left: 0.5rem !important;
}

html:not([dir=rtl]) .me-3 {
  margin-right: 1rem !important;
}
*[dir=rtl] .me-3 {
  margin-left: 1rem !important;
}

html:not([dir=rtl]) .me-4 {
  margin-right: 1.5rem !important;
}
*[dir=rtl] .me-4 {
  margin-left: 1.5rem !important;
}

html:not([dir=rtl]) .me-5 {
  margin-right: 3rem !important;
}
*[dir=rtl] .me-5 {
  margin-left: 3rem !important;
}

html:not([dir=rtl]) .me-auto {
  margin-right: auto !important;
}
*[dir=rtl] .me-auto {
  margin-left: auto !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-2 {
  margin-bottom: 0.5rem !important;
}

.mb-3 {
  margin-bottom: 1rem !important;
}

.mb-4 {
  margin-bottom: 1.5rem !important;
}

.mb-5 {
  margin-bottom: 3rem !important;
}

.mb-auto {
  margin-bottom: auto !important;
}

html:not([dir=rtl]) .ms-0 {
  margin-left: 0 !important;
}
*[dir=rtl] .ms-0 {
  margin-right: 0 !important;
}

html:not([dir=rtl]) .ms-1 {
  margin-left: 0.25rem !important;
}
*[dir=rtl] .ms-1 {
  margin-right: 0.25rem !important;
}

html:not([dir=rtl]) .ms-2 {
  margin-left: 0.5rem !important;
}
*[dir=rtl] .ms-2 {
  margin-right: 0.5rem !important;
}

html:not([dir=rtl]) .ms-3 {
  margin-left: 1rem !important;
}
*[dir=rtl] .ms-3 {
  margin-right: 1rem !important;
}

html:not([dir=rtl]) .ms-4 {
  margin-left: 1.5rem !important;
}
*[dir=rtl] .ms-4 {
  margin-right: 1.5rem !important;
}

html:not([dir=rtl]) .ms-5 {
  margin-left: 3rem !important;
}
*[dir=rtl] .ms-5 {
  margin-right: 3rem !important;
}

html:not([dir=rtl]) .ms-auto {
  margin-left: auto !important;
}
*[dir=rtl] .ms-auto {
  margin-right: auto !important;
}

.p-0 {
  padding: 0 !important;
}

.p-1 {
  padding: 0.25rem !important;
}

.p-2 {
  padding: 0.5rem !important;
}

.p-3 {
  padding: 1rem !important;
}

.p-4 {
  padding: 1.5rem !important;
}

.p-5 {
  padding: 3rem !important;
}

.px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}

.px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}

.px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}

.px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}

.px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}

.px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}

.py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}

.py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}

.py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}

.py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}

.py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}

.py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}

.pt-0 {
  padding-top: 0 !important;
}

.pt-1 {
  padding-top: 0.25rem !important;
}

.pt-2 {
  padding-top: 0.5rem !important;
}

.pt-3 {
  padding-top: 1rem !important;
}

.pt-4 {
  padding-top: 1.5rem !important;
}

.pt-5 {
  padding-top: 3rem !important;
}

html:not([dir=rtl]) .pe-0 {
  padding-right: 0 !important;
}
*[dir=rtl] .pe-0 {
  padding-left: 0 !important;
}

html:not([dir=rtl]) .pe-1 {
  padding-right: 0.25rem !important;
}
*[dir=rtl] .pe-1 {
  padding-left: 0.25rem !important;
}

html:not([dir=rtl]) .pe-2 {
  padding-right: 0.5rem !important;
}
*[dir=rtl] .pe-2 {
  padding-left: 0.5rem !important;
}

html:not([dir=rtl]) .pe-3 {
  padding-right: 1rem !important;
}
*[dir=rtl] .pe-3 {
  padding-left: 1rem !important;
}

html:not([dir=rtl]) .pe-4 {
  padding-right: 1.5rem !important;
}
*[dir=rtl] .pe-4 {
  padding-left: 1.5rem !important;
}

html:not([dir=rtl]) .pe-5 {
  padding-right: 3rem !important;
}
*[dir=rtl] .pe-5 {
  padding-left: 3rem !important;
}

.pb-0 {
  padding-bottom: 0 !important;
}

.pb-1 {
  padding-bottom: 0.25rem !important;
}

.pb-2 {
  padding-bottom: 0.5rem !important;
}

.pb-3 {
  padding-bottom: 1rem !important;
}

.pb-4 {
  padding-bottom: 1.5rem !important;
}

.pb-5 {
  padding-bottom: 3rem !important;
}

html:not([dir=rtl]) .ps-0 {
  padding-left: 0 !important;
}
*[dir=rtl] .ps-0 {
  padding-right: 0 !important;
}

html:not([dir=rtl]) .ps-1 {
  padding-left: 0.25rem !important;
}
*[dir=rtl] .ps-1 {
  padding-right: 0.25rem !important;
}

html:not([dir=rtl]) .ps-2 {
  padding-left: 0.5rem !important;
}
*[dir=rtl] .ps-2 {
  padding-right: 0.5rem !important;
}

html:not([dir=rtl]) .ps-3 {
  padding-left: 1rem !important;
}
*[dir=rtl] .ps-3 {
  padding-right: 1rem !important;
}

html:not([dir=rtl]) .ps-4 {
  padding-left: 1.5rem !important;
}
*[dir=rtl] .ps-4 {
  padding-right: 1.5rem !important;
}

html:not([dir=rtl]) .ps-5 {
  padding-left: 3rem !important;
}
*[dir=rtl] .ps-5 {
  padding-right: 3rem !important;
}

.font-monospace {
  font-family: var(--cui-font-monospace) !important;
}

.fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}

.fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}

.fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}

.fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}

.fs-5 {
  font-size: 1.25rem !important;
}

.fs-6 {
  font-size: 1rem !important;
}

.fst-italic {
  font-style: italic !important;
}

.fst-normal {
  font-style: normal !important;
}

.fw-light {
  font-weight: 300 !important;
}

.fw-lighter {
  font-weight: lighter !important;
}

.fw-normal {
  font-weight: 400 !important;
}

.fw-medium {
  font-weight: 500 !important;
}

.fw-semibold {
  font-weight: 600 !important;
}

.fw-bold {
  font-weight: 700 !important;
}

.fw-bolder {
  font-weight: bolder !important;
}

.lh-1 {
  line-height: 1 !important;
}

.lh-sm {
  line-height: 1.25 !important;
}

.lh-base {
  line-height: 1.5 !important;
}

.lh-lg {
  line-height: 2 !important;
}

html:not([dir=rtl]) .text-start {
  text-align: left !important;
}
*[dir=rtl] .text-start {
  text-align: right !important;
}

html:not([dir=rtl]) .text-end {
  text-align: right !important;
}
*[dir=rtl] .text-end {
  text-align: left !important;
}

html:not([dir=rtl]) .text-center {
  text-align: center !important;
}
*[dir=rtl] .text-center {
  text-align: center !important;
}

.text-decoration-none {
  text-decoration: none !important;
}

.text-decoration-underline {
  text-decoration: underline !important;
}

.text-decoration-line-through {
  text-decoration: line-through !important;
}

.text-lowercase {
  text-transform: lowercase !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-capitalize {
  text-transform: capitalize !important;
}

.text-wrap {
  white-space: normal !important;
}

.text-nowrap {
  white-space: nowrap !important;
}

/* rtl:begin:remove */
.text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}

/* rtl:end:remove */
.text-primary {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-primary-rgb), var(--cui-text-opacity)) !important;
}

.text-secondary {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-secondary-rgb), var(--cui-text-opacity)) !important;
}

.text-success {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-success-rgb), var(--cui-text-opacity)) !important;
}

.text-info {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-info-rgb), var(--cui-text-opacity)) !important;
}

.text-warning {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-warning-rgb), var(--cui-text-opacity)) !important;
}

.text-danger {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-danger-rgb), var(--cui-text-opacity)) !important;
}

.text-light {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-light-rgb), var(--cui-text-opacity)) !important;
}

.text-dark {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-dark-rgb), var(--cui-text-opacity)) !important;
}

.text-black {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-black-rgb), var(--cui-text-opacity)) !important;
}

.text-white {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-white-rgb), var(--cui-text-opacity)) !important;
}

.text-body {
  --cui-text-opacity: 1;
  color: rgba(var(--cui-body-color-rgb), var(--cui-text-opacity)) !important;
}

.text-muted {
  --cui-text-opacity: 1;
  color: rgba(44, 56, 74, 0.38) !important;
}

.text-black-50 {
  --cui-text-opacity: 1;
  color: rgba(0, 0, 21, 0.5) !important;
}

.text-white-50 {
  --cui-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}

.text-reset {
  --cui-text-opacity: 1;
  color: inherit !important;
}

.text-high-emphasis-inverse {
  --cui-text-opacity: 1;
  color: rgba(255, 255, 255, 0.87) !important;
}

.text-medium-emphasis-inverse {
  --cui-text-opacity: 1;
  color: rgba(255, 255, 255, 0.6) !important;
}

.text-disabled-inverse {
  --cui-text-opacity: 1;
  color: rgba(255, 255, 255, 0.38) !important;
}

.text-high-emphasis {
  --cui-text-opacity: 1;
  color: rgba(44, 56, 74, 0.95) !important;
}

.text-medium-emphasis {
  --cui-text-opacity: 1;
  color: rgba(44, 56, 74, 0.681) !important;
}

.text-disabled {
  --cui-text-opacity: 1;
  color: rgba(44, 56, 74, 0.38) !important;
}

.text-opacity-25 {
  --cui-text-opacity: 0.25;
}

.text-opacity-50 {
  --cui-text-opacity: 0.5;
}

.text-opacity-75 {
  --cui-text-opacity: 0.75;
}

.text-opacity-100 {
  --cui-text-opacity: 1;
}

.bg-primary {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-primary-rgb), var(--cui-bg-opacity)) !important;
}

.bg-secondary {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-secondary-rgb), var(--cui-bg-opacity)) !important;
}

.bg-success {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-success-rgb), var(--cui-bg-opacity)) !important;
}

.bg-info {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-info-rgb), var(--cui-bg-opacity)) !important;
}

.bg-warning {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-warning-rgb), var(--cui-bg-opacity)) !important;
}

.bg-danger {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-danger-rgb), var(--cui-bg-opacity)) !important;
}

.bg-light {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-light-rgb), var(--cui-bg-opacity)) !important;
}

.bg-dark {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-dark-rgb), var(--cui-bg-opacity)) !important;
}

.bg-black {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-black-rgb), var(--cui-bg-opacity)) !important;
}

.bg-white {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-white-rgb), var(--cui-bg-opacity)) !important;
}

.bg-body {
  --cui-bg-opacity: 1;
  background-color: rgba(var(--cui-body-bg-rgb), var(--cui-bg-opacity)) !important;
}

.bg-transparent {
  --cui-bg-opacity: 1;
  background-color: transparent !important;
}

.bg-opacity-10 {
  --cui-bg-opacity: 0.1;
}

.bg-opacity-25 {
  --cui-bg-opacity: 0.25;
}

.bg-opacity-50 {
  --cui-bg-opacity: 0.5;
}

.bg-opacity-75 {
  --cui-bg-opacity: 0.75;
}

.bg-opacity-100 {
  --cui-bg-opacity: 1;
}

.bg-gradient {
  background-image: var(--cui-gradient) !important;
}

.user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}

.user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
      -ms-user-select: auto !important;
          user-select: auto !important;
}

.user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
      -ms-user-select: none !important;
          user-select: none !important;
}

.pe-none {
  pointer-events: none !important;
}

.pe-auto {
  pointer-events: auto !important;
}

.rounded {
  border-radius: 0.25rem !important;
}

.rounded-0 {
  border-radius: 0 !important;
}

.rounded-1 {
  border-radius: 0.2rem !important;
}

.rounded-2 {
  border-radius: 0.25rem !important;
}

.rounded-3 {
  border-radius: 0.3rem !important;
}

.rounded-circle {
  border-radius: 50% !important;
}

.rounded-pill {
  border-radius: 50rem !important;
}

.rounded-top {
  border-top-left-radius: 0.25rem !important;
  border-top-right-radius: 0.25rem !important;
}

html:not([dir=rtl]) .rounded-end {
  border-top-right-radius: 0.25rem !important;
}
*[dir=rtl] .rounded-end {
  border-top-left-radius: 0.25rem !important;
}
html:not([dir=rtl]) .rounded-end {
  border-bottom-right-radius: 0.25rem !important;
}
*[dir=rtl] .rounded-end {
  border-bottom-left-radius: 0.25rem !important;
}

.rounded-bottom {
  border-bottom-right-radius: 0.25rem !important;
  border-bottom-left-radius: 0.25rem !important;
}

html:not([dir=rtl]) .rounded-start {
  border-bottom-left-radius: 0.25rem !important;
}
*[dir=rtl] .rounded-start {
  border-bottom-right-radius: 0.25rem !important;
}
html:not([dir=rtl]) .rounded-start {
  border-top-left-radius: 0.25rem !important;
}
*[dir=rtl] .rounded-start {
  border-top-right-radius: 0.25rem !important;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

@media (min-width: 576px) {
  html:not([dir=rtl]) .float-sm-start {
    float: left !important;
  }
  *[dir=rtl] .float-sm-start {
    float: right !important;
  }

  html:not([dir=rtl]) .float-sm-end {
    float: right !important;
  }
  *[dir=rtl] .float-sm-end {
    float: left !important;
  }

  html:not([dir=rtl]) .float-sm-none {
    float: none !important;
  }
  *[dir=rtl] .float-sm-none {
    float: none !important;
  }

  .d-sm-inline {
    display: inline !important;
  }

  .d-sm-inline-block {
    display: inline-block !important;
  }

  .d-sm-block {
    display: block !important;
  }

  .d-sm-grid {
    display: grid !important;
  }

  .d-sm-table {
    display: table !important;
  }

  .d-sm-table-row {
    display: table-row !important;
  }

  .d-sm-table-cell {
    display: table-cell !important;
  }

  .d-sm-flex {
    display: flex !important;
  }

  .d-sm-inline-flex {
    display: inline-flex !important;
  }

  .d-sm-none {
    display: none !important;
  }

  .flex-sm-fill {
    flex: 1 1 auto !important;
  }

  .flex-sm-row {
    flex-direction: row !important;
  }

  .flex-sm-column {
    flex-direction: column !important;
  }

  .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-sm-wrap {
    flex-wrap: wrap !important;
  }

  .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .gap-sm-0 {
    gap: 0 !important;
  }

  .gap-sm-1 {
    gap: 0.25rem !important;
  }

  .gap-sm-2 {
    gap: 0.5rem !important;
  }

  .gap-sm-3 {
    gap: 1rem !important;
  }

  .gap-sm-4 {
    gap: 1.5rem !important;
  }

  .gap-sm-5 {
    gap: 3rem !important;
  }

  .justify-content-sm-start {
    justify-content: flex-start !important;
  }

  .justify-content-sm-end {
    justify-content: flex-end !important;
  }

  .justify-content-sm-center {
    justify-content: center !important;
  }

  .justify-content-sm-between {
    justify-content: space-between !important;
  }

  .justify-content-sm-around {
    justify-content: space-around !important;
  }

  .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-sm-start {
    align-items: flex-start !important;
  }

  .align-items-sm-end {
    align-items: flex-end !important;
  }

  .align-items-sm-center {
    align-items: center !important;
  }

  .align-items-sm-baseline {
    align-items: baseline !important;
  }

  .align-items-sm-stretch {
    align-items: stretch !important;
  }

  .align-content-sm-start {
    align-content: flex-start !important;
  }

  .align-content-sm-end {
    align-content: flex-end !important;
  }

  .align-content-sm-center {
    align-content: center !important;
  }

  .align-content-sm-between {
    align-content: space-between !important;
  }

  .align-content-sm-around {
    align-content: space-around !important;
  }

  .align-content-sm-stretch {
    align-content: stretch !important;
  }

  .align-self-sm-auto {
    align-self: auto !important;
  }

  .align-self-sm-start {
    align-self: flex-start !important;
  }

  .align-self-sm-end {
    align-self: flex-end !important;
  }

  .align-self-sm-center {
    align-self: center !important;
  }

  .align-self-sm-baseline {
    align-self: baseline !important;
  }

  .align-self-sm-stretch {
    align-self: stretch !important;
  }

  .order-sm-first {
    order: -1 !important;
  }

  .order-sm-0 {
    order: 0 !important;
  }

  .order-sm-1 {
    order: 1 !important;
  }

  .order-sm-2 {
    order: 2 !important;
  }

  .order-sm-3 {
    order: 3 !important;
  }

  .order-sm-4 {
    order: 4 !important;
  }

  .order-sm-5 {
    order: 5 !important;
  }

  .order-sm-last {
    order: 6 !important;
  }

  .m-sm-0 {
    margin: 0 !important;
  }

  .m-sm-1 {
    margin: 0.25rem !important;
  }

  .m-sm-2 {
    margin: 0.5rem !important;
  }

  .m-sm-3 {
    margin: 1rem !important;
  }

  .m-sm-4 {
    margin: 1.5rem !important;
  }

  .m-sm-5 {
    margin: 3rem !important;
  }

  .m-sm-auto {
    margin: auto !important;
  }

  .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }

  .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-sm-0 {
    margin-top: 0 !important;
  }

  .mt-sm-1 {
    margin-top: 0.25rem !important;
  }

  .mt-sm-2 {
    margin-top: 0.5rem !important;
  }

  .mt-sm-3 {
    margin-top: 1rem !important;
  }

  .mt-sm-4 {
    margin-top: 1.5rem !important;
  }

  .mt-sm-5 {
    margin-top: 3rem !important;
  }

  .mt-sm-auto {
    margin-top: auto !important;
  }

  html:not([dir=rtl]) .me-sm-0 {
    margin-right: 0 !important;
  }
  *[dir=rtl] .me-sm-0 {
    margin-left: 0 !important;
  }

  html:not([dir=rtl]) .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  *[dir=rtl] .me-sm-1 {
    margin-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  *[dir=rtl] .me-sm-2 {
    margin-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .me-sm-3 {
    margin-right: 1rem !important;
  }
  *[dir=rtl] .me-sm-3 {
    margin-left: 1rem !important;
  }

  html:not([dir=rtl]) .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  *[dir=rtl] .me-sm-4 {
    margin-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .me-sm-5 {
    margin-right: 3rem !important;
  }
  *[dir=rtl] .me-sm-5 {
    margin-left: 3rem !important;
  }

  html:not([dir=rtl]) .me-sm-auto {
    margin-right: auto !important;
  }
  *[dir=rtl] .me-sm-auto {
    margin-left: auto !important;
  }

  .mb-sm-0 {
    margin-bottom: 0 !important;
  }

  .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-sm-3 {
    margin-bottom: 1rem !important;
  }

  .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-sm-5 {
    margin-bottom: 3rem !important;
  }

  .mb-sm-auto {
    margin-bottom: auto !important;
  }

  html:not([dir=rtl]) .ms-sm-0 {
    margin-left: 0 !important;
  }
  *[dir=rtl] .ms-sm-0 {
    margin-right: 0 !important;
  }

  html:not([dir=rtl]) .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ms-sm-1 {
    margin-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ms-sm-2 {
    margin-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ms-sm-3 {
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ms-sm-3 {
    margin-right: 1rem !important;
  }

  html:not([dir=rtl]) .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ms-sm-4 {
    margin-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ms-sm-5 {
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ms-sm-5 {
    margin-right: 3rem !important;
  }

  html:not([dir=rtl]) .ms-sm-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ms-sm-auto {
    margin-right: auto !important;
  }

  .p-sm-0 {
    padding: 0 !important;
  }

  .p-sm-1 {
    padding: 0.25rem !important;
  }

  .p-sm-2 {
    padding: 0.5rem !important;
  }

  .p-sm-3 {
    padding: 1rem !important;
  }

  .p-sm-4 {
    padding: 1.5rem !important;
  }

  .p-sm-5 {
    padding: 3rem !important;
  }

  .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }

  .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .pt-sm-0 {
    padding-top: 0 !important;
  }

  .pt-sm-1 {
    padding-top: 0.25rem !important;
  }

  .pt-sm-2 {
    padding-top: 0.5rem !important;
  }

  .pt-sm-3 {
    padding-top: 1rem !important;
  }

  .pt-sm-4 {
    padding-top: 1.5rem !important;
  }

  .pt-sm-5 {
    padding-top: 3rem !important;
  }

  html:not([dir=rtl]) .pe-sm-0 {
    padding-right: 0 !important;
  }
  *[dir=rtl] .pe-sm-0 {
    padding-left: 0 !important;
  }

  html:not([dir=rtl]) .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  *[dir=rtl] .pe-sm-1 {
    padding-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  *[dir=rtl] .pe-sm-2 {
    padding-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .pe-sm-3 {
    padding-right: 1rem !important;
  }
  *[dir=rtl] .pe-sm-3 {
    padding-left: 1rem !important;
  }

  html:not([dir=rtl]) .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  *[dir=rtl] .pe-sm-4 {
    padding-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .pe-sm-5 {
    padding-right: 3rem !important;
  }
  *[dir=rtl] .pe-sm-5 {
    padding-left: 3rem !important;
  }

  .pb-sm-0 {
    padding-bottom: 0 !important;
  }

  .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-sm-3 {
    padding-bottom: 1rem !important;
  }

  .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }

  .pb-sm-5 {
    padding-bottom: 3rem !important;
  }

  html:not([dir=rtl]) .ps-sm-0 {
    padding-left: 0 !important;
  }
  *[dir=rtl] .ps-sm-0 {
    padding-right: 0 !important;
  }

  html:not([dir=rtl]) .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .ps-sm-1 {
    padding-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .ps-sm-2 {
    padding-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ps-sm-3 {
    padding-left: 1rem !important;
  }
  *[dir=rtl] .ps-sm-3 {
    padding-right: 1rem !important;
  }

  html:not([dir=rtl]) .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .ps-sm-4 {
    padding-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ps-sm-5 {
    padding-left: 3rem !important;
  }
  *[dir=rtl] .ps-sm-5 {
    padding-right: 3rem !important;
  }

  html:not([dir=rtl]) .text-sm-start {
    text-align: left !important;
  }
  *[dir=rtl] .text-sm-start {
    text-align: right !important;
  }

  html:not([dir=rtl]) .text-sm-end {
    text-align: right !important;
  }
  *[dir=rtl] .text-sm-end {
    text-align: left !important;
  }

  html:not([dir=rtl]) .text-sm-center {
    text-align: center !important;
  }
  *[dir=rtl] .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  html:not([dir=rtl]) .float-md-start {
    float: left !important;
  }
  *[dir=rtl] .float-md-start {
    float: right !important;
  }

  html:not([dir=rtl]) .float-md-end {
    float: right !important;
  }
  *[dir=rtl] .float-md-end {
    float: left !important;
  }

  html:not([dir=rtl]) .float-md-none {
    float: none !important;
  }
  *[dir=rtl] .float-md-none {
    float: none !important;
  }

  .d-md-inline {
    display: inline !important;
  }

  .d-md-inline-block {
    display: inline-block !important;
  }

  .d-md-block {
    display: block !important;
  }

  .d-md-grid {
    display: grid !important;
  }

  .d-md-table {
    display: table !important;
  }

  .d-md-table-row {
    display: table-row !important;
  }

  .d-md-table-cell {
    display: table-cell !important;
  }

  .d-md-flex {
    display: flex !important;
  }

  .d-md-inline-flex {
    display: inline-flex !important;
  }

  .d-md-none {
    display: none !important;
  }

  .flex-md-fill {
    flex: 1 1 auto !important;
  }

  .flex-md-row {
    flex-direction: row !important;
  }

  .flex-md-column {
    flex-direction: column !important;
  }

  .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-md-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-md-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-md-wrap {
    flex-wrap: wrap !important;
  }

  .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .gap-md-0 {
    gap: 0 !important;
  }

  .gap-md-1 {
    gap: 0.25rem !important;
  }

  .gap-md-2 {
    gap: 0.5rem !important;
  }

  .gap-md-3 {
    gap: 1rem !important;
  }

  .gap-md-4 {
    gap: 1.5rem !important;
  }

  .gap-md-5 {
    gap: 3rem !important;
  }

  .justify-content-md-start {
    justify-content: flex-start !important;
  }

  .justify-content-md-end {
    justify-content: flex-end !important;
  }

  .justify-content-md-center {
    justify-content: center !important;
  }

  .justify-content-md-between {
    justify-content: space-between !important;
  }

  .justify-content-md-around {
    justify-content: space-around !important;
  }

  .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-md-start {
    align-items: flex-start !important;
  }

  .align-items-md-end {
    align-items: flex-end !important;
  }

  .align-items-md-center {
    align-items: center !important;
  }

  .align-items-md-baseline {
    align-items: baseline !important;
  }

  .align-items-md-stretch {
    align-items: stretch !important;
  }

  .align-content-md-start {
    align-content: flex-start !important;
  }

  .align-content-md-end {
    align-content: flex-end !important;
  }

  .align-content-md-center {
    align-content: center !important;
  }

  .align-content-md-between {
    align-content: space-between !important;
  }

  .align-content-md-around {
    align-content: space-around !important;
  }

  .align-content-md-stretch {
    align-content: stretch !important;
  }

  .align-self-md-auto {
    align-self: auto !important;
  }

  .align-self-md-start {
    align-self: flex-start !important;
  }

  .align-self-md-end {
    align-self: flex-end !important;
  }

  .align-self-md-center {
    align-self: center !important;
  }

  .align-self-md-baseline {
    align-self: baseline !important;
  }

  .align-self-md-stretch {
    align-self: stretch !important;
  }

  .order-md-first {
    order: -1 !important;
  }

  .order-md-0 {
    order: 0 !important;
  }

  .order-md-1 {
    order: 1 !important;
  }

  .order-md-2 {
    order: 2 !important;
  }

  .order-md-3 {
    order: 3 !important;
  }

  .order-md-4 {
    order: 4 !important;
  }

  .order-md-5 {
    order: 5 !important;
  }

  .order-md-last {
    order: 6 !important;
  }

  .m-md-0 {
    margin: 0 !important;
  }

  .m-md-1 {
    margin: 0.25rem !important;
  }

  .m-md-2 {
    margin: 0.5rem !important;
  }

  .m-md-3 {
    margin: 1rem !important;
  }

  .m-md-4 {
    margin: 1.5rem !important;
  }

  .m-md-5 {
    margin: 3rem !important;
  }

  .m-md-auto {
    margin: auto !important;
  }

  .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }

  .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-md-0 {
    margin-top: 0 !important;
  }

  .mt-md-1 {
    margin-top: 0.25rem !important;
  }

  .mt-md-2 {
    margin-top: 0.5rem !important;
  }

  .mt-md-3 {
    margin-top: 1rem !important;
  }

  .mt-md-4 {
    margin-top: 1.5rem !important;
  }

  .mt-md-5 {
    margin-top: 3rem !important;
  }

  .mt-md-auto {
    margin-top: auto !important;
  }

  html:not([dir=rtl]) .me-md-0 {
    margin-right: 0 !important;
  }
  *[dir=rtl] .me-md-0 {
    margin-left: 0 !important;
  }

  html:not([dir=rtl]) .me-md-1 {
    margin-right: 0.25rem !important;
  }
  *[dir=rtl] .me-md-1 {
    margin-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .me-md-2 {
    margin-right: 0.5rem !important;
  }
  *[dir=rtl] .me-md-2 {
    margin-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .me-md-3 {
    margin-right: 1rem !important;
  }
  *[dir=rtl] .me-md-3 {
    margin-left: 1rem !important;
  }

  html:not([dir=rtl]) .me-md-4 {
    margin-right: 1.5rem !important;
  }
  *[dir=rtl] .me-md-4 {
    margin-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .me-md-5 {
    margin-right: 3rem !important;
  }
  *[dir=rtl] .me-md-5 {
    margin-left: 3rem !important;
  }

  html:not([dir=rtl]) .me-md-auto {
    margin-right: auto !important;
  }
  *[dir=rtl] .me-md-auto {
    margin-left: auto !important;
  }

  .mb-md-0 {
    margin-bottom: 0 !important;
  }

  .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-md-3 {
    margin-bottom: 1rem !important;
  }

  .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-md-5 {
    margin-bottom: 3rem !important;
  }

  .mb-md-auto {
    margin-bottom: auto !important;
  }

  html:not([dir=rtl]) .ms-md-0 {
    margin-left: 0 !important;
  }
  *[dir=rtl] .ms-md-0 {
    margin-right: 0 !important;
  }

  html:not([dir=rtl]) .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ms-md-1 {
    margin-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ms-md-2 {
    margin-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ms-md-3 {
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ms-md-3 {
    margin-right: 1rem !important;
  }

  html:not([dir=rtl]) .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ms-md-4 {
    margin-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ms-md-5 {
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ms-md-5 {
    margin-right: 3rem !important;
  }

  html:not([dir=rtl]) .ms-md-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ms-md-auto {
    margin-right: auto !important;
  }

  .p-md-0 {
    padding: 0 !important;
  }

  .p-md-1 {
    padding: 0.25rem !important;
  }

  .p-md-2 {
    padding: 0.5rem !important;
  }

  .p-md-3 {
    padding: 1rem !important;
  }

  .p-md-4 {
    padding: 1.5rem !important;
  }

  .p-md-5 {
    padding: 3rem !important;
  }

  .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }

  .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .pt-md-0 {
    padding-top: 0 !important;
  }

  .pt-md-1 {
    padding-top: 0.25rem !important;
  }

  .pt-md-2 {
    padding-top: 0.5rem !important;
  }

  .pt-md-3 {
    padding-top: 1rem !important;
  }

  .pt-md-4 {
    padding-top: 1.5rem !important;
  }

  .pt-md-5 {
    padding-top: 3rem !important;
  }

  html:not([dir=rtl]) .pe-md-0 {
    padding-right: 0 !important;
  }
  *[dir=rtl] .pe-md-0 {
    padding-left: 0 !important;
  }

  html:not([dir=rtl]) .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  *[dir=rtl] .pe-md-1 {
    padding-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  *[dir=rtl] .pe-md-2 {
    padding-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .pe-md-3 {
    padding-right: 1rem !important;
  }
  *[dir=rtl] .pe-md-3 {
    padding-left: 1rem !important;
  }

  html:not([dir=rtl]) .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  *[dir=rtl] .pe-md-4 {
    padding-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .pe-md-5 {
    padding-right: 3rem !important;
  }
  *[dir=rtl] .pe-md-5 {
    padding-left: 3rem !important;
  }

  .pb-md-0 {
    padding-bottom: 0 !important;
  }

  .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-md-3 {
    padding-bottom: 1rem !important;
  }

  .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }

  .pb-md-5 {
    padding-bottom: 3rem !important;
  }

  html:not([dir=rtl]) .ps-md-0 {
    padding-left: 0 !important;
  }
  *[dir=rtl] .ps-md-0 {
    padding-right: 0 !important;
  }

  html:not([dir=rtl]) .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .ps-md-1 {
    padding-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .ps-md-2 {
    padding-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ps-md-3 {
    padding-left: 1rem !important;
  }
  *[dir=rtl] .ps-md-3 {
    padding-right: 1rem !important;
  }

  html:not([dir=rtl]) .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .ps-md-4 {
    padding-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ps-md-5 {
    padding-left: 3rem !important;
  }
  *[dir=rtl] .ps-md-5 {
    padding-right: 3rem !important;
  }

  html:not([dir=rtl]) .text-md-start {
    text-align: left !important;
  }
  *[dir=rtl] .text-md-start {
    text-align: right !important;
  }

  html:not([dir=rtl]) .text-md-end {
    text-align: right !important;
  }
  *[dir=rtl] .text-md-end {
    text-align: left !important;
  }

  html:not([dir=rtl]) .text-md-center {
    text-align: center !important;
  }
  *[dir=rtl] .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  html:not([dir=rtl]) .float-lg-start {
    float: left !important;
  }
  *[dir=rtl] .float-lg-start {
    float: right !important;
  }

  html:not([dir=rtl]) .float-lg-end {
    float: right !important;
  }
  *[dir=rtl] .float-lg-end {
    float: left !important;
  }

  html:not([dir=rtl]) .float-lg-none {
    float: none !important;
  }
  *[dir=rtl] .float-lg-none {
    float: none !important;
  }

  .d-lg-inline {
    display: inline !important;
  }

  .d-lg-inline-block {
    display: inline-block !important;
  }

  .d-lg-block {
    display: block !important;
  }

  .d-lg-grid {
    display: grid !important;
  }

  .d-lg-table {
    display: table !important;
  }

  .d-lg-table-row {
    display: table-row !important;
  }

  .d-lg-table-cell {
    display: table-cell !important;
  }

  .d-lg-flex {
    display: flex !important;
  }

  .d-lg-inline-flex {
    display: inline-flex !important;
  }

  .d-lg-none {
    display: none !important;
  }

  .flex-lg-fill {
    flex: 1 1 auto !important;
  }

  .flex-lg-row {
    flex-direction: row !important;
  }

  .flex-lg-column {
    flex-direction: column !important;
  }

  .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-lg-wrap {
    flex-wrap: wrap !important;
  }

  .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .gap-lg-0 {
    gap: 0 !important;
  }

  .gap-lg-1 {
    gap: 0.25rem !important;
  }

  .gap-lg-2 {
    gap: 0.5rem !important;
  }

  .gap-lg-3 {
    gap: 1rem !important;
  }

  .gap-lg-4 {
    gap: 1.5rem !important;
  }

  .gap-lg-5 {
    gap: 3rem !important;
  }

  .justify-content-lg-start {
    justify-content: flex-start !important;
  }

  .justify-content-lg-end {
    justify-content: flex-end !important;
  }

  .justify-content-lg-center {
    justify-content: center !important;
  }

  .justify-content-lg-between {
    justify-content: space-between !important;
  }

  .justify-content-lg-around {
    justify-content: space-around !important;
  }

  .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-lg-start {
    align-items: flex-start !important;
  }

  .align-items-lg-end {
    align-items: flex-end !important;
  }

  .align-items-lg-center {
    align-items: center !important;
  }

  .align-items-lg-baseline {
    align-items: baseline !important;
  }

  .align-items-lg-stretch {
    align-items: stretch !important;
  }

  .align-content-lg-start {
    align-content: flex-start !important;
  }

  .align-content-lg-end {
    align-content: flex-end !important;
  }

  .align-content-lg-center {
    align-content: center !important;
  }

  .align-content-lg-between {
    align-content: space-between !important;
  }

  .align-content-lg-around {
    align-content: space-around !important;
  }

  .align-content-lg-stretch {
    align-content: stretch !important;
  }

  .align-self-lg-auto {
    align-self: auto !important;
  }

  .align-self-lg-start {
    align-self: flex-start !important;
  }

  .align-self-lg-end {
    align-self: flex-end !important;
  }

  .align-self-lg-center {
    align-self: center !important;
  }

  .align-self-lg-baseline {
    align-self: baseline !important;
  }

  .align-self-lg-stretch {
    align-self: stretch !important;
  }

  .order-lg-first {
    order: -1 !important;
  }

  .order-lg-0 {
    order: 0 !important;
  }

  .order-lg-1 {
    order: 1 !important;
  }

  .order-lg-2 {
    order: 2 !important;
  }

  .order-lg-3 {
    order: 3 !important;
  }

  .order-lg-4 {
    order: 4 !important;
  }

  .order-lg-5 {
    order: 5 !important;
  }

  .order-lg-last {
    order: 6 !important;
  }

  .m-lg-0 {
    margin: 0 !important;
  }

  .m-lg-1 {
    margin: 0.25rem !important;
  }

  .m-lg-2 {
    margin: 0.5rem !important;
  }

  .m-lg-3 {
    margin: 1rem !important;
  }

  .m-lg-4 {
    margin: 1.5rem !important;
  }

  .m-lg-5 {
    margin: 3rem !important;
  }

  .m-lg-auto {
    margin: auto !important;
  }

  .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }

  .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-lg-0 {
    margin-top: 0 !important;
  }

  .mt-lg-1 {
    margin-top: 0.25rem !important;
  }

  .mt-lg-2 {
    margin-top: 0.5rem !important;
  }

  .mt-lg-3 {
    margin-top: 1rem !important;
  }

  .mt-lg-4 {
    margin-top: 1.5rem !important;
  }

  .mt-lg-5 {
    margin-top: 3rem !important;
  }

  .mt-lg-auto {
    margin-top: auto !important;
  }

  html:not([dir=rtl]) .me-lg-0 {
    margin-right: 0 !important;
  }
  *[dir=rtl] .me-lg-0 {
    margin-left: 0 !important;
  }

  html:not([dir=rtl]) .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  *[dir=rtl] .me-lg-1 {
    margin-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  *[dir=rtl] .me-lg-2 {
    margin-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .me-lg-3 {
    margin-right: 1rem !important;
  }
  *[dir=rtl] .me-lg-3 {
    margin-left: 1rem !important;
  }

  html:not([dir=rtl]) .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  *[dir=rtl] .me-lg-4 {
    margin-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .me-lg-5 {
    margin-right: 3rem !important;
  }
  *[dir=rtl] .me-lg-5 {
    margin-left: 3rem !important;
  }

  html:not([dir=rtl]) .me-lg-auto {
    margin-right: auto !important;
  }
  *[dir=rtl] .me-lg-auto {
    margin-left: auto !important;
  }

  .mb-lg-0 {
    margin-bottom: 0 !important;
  }

  .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-lg-3 {
    margin-bottom: 1rem !important;
  }

  .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-lg-5 {
    margin-bottom: 3rem !important;
  }

  .mb-lg-auto {
    margin-bottom: auto !important;
  }

  html:not([dir=rtl]) .ms-lg-0 {
    margin-left: 0 !important;
  }
  *[dir=rtl] .ms-lg-0 {
    margin-right: 0 !important;
  }

  html:not([dir=rtl]) .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ms-lg-1 {
    margin-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ms-lg-2 {
    margin-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ms-lg-3 {
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ms-lg-3 {
    margin-right: 1rem !important;
  }

  html:not([dir=rtl]) .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ms-lg-4 {
    margin-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ms-lg-5 {
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ms-lg-5 {
    margin-right: 3rem !important;
  }

  html:not([dir=rtl]) .ms-lg-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ms-lg-auto {
    margin-right: auto !important;
  }

  .p-lg-0 {
    padding: 0 !important;
  }

  .p-lg-1 {
    padding: 0.25rem !important;
  }

  .p-lg-2 {
    padding: 0.5rem !important;
  }

  .p-lg-3 {
    padding: 1rem !important;
  }

  .p-lg-4 {
    padding: 1.5rem !important;
  }

  .p-lg-5 {
    padding: 3rem !important;
  }

  .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }

  .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .pt-lg-0 {
    padding-top: 0 !important;
  }

  .pt-lg-1 {
    padding-top: 0.25rem !important;
  }

  .pt-lg-2 {
    padding-top: 0.5rem !important;
  }

  .pt-lg-3 {
    padding-top: 1rem !important;
  }

  .pt-lg-4 {
    padding-top: 1.5rem !important;
  }

  .pt-lg-5 {
    padding-top: 3rem !important;
  }

  html:not([dir=rtl]) .pe-lg-0 {
    padding-right: 0 !important;
  }
  *[dir=rtl] .pe-lg-0 {
    padding-left: 0 !important;
  }

  html:not([dir=rtl]) .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  *[dir=rtl] .pe-lg-1 {
    padding-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  *[dir=rtl] .pe-lg-2 {
    padding-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .pe-lg-3 {
    padding-right: 1rem !important;
  }
  *[dir=rtl] .pe-lg-3 {
    padding-left: 1rem !important;
  }

  html:not([dir=rtl]) .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  *[dir=rtl] .pe-lg-4 {
    padding-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .pe-lg-5 {
    padding-right: 3rem !important;
  }
  *[dir=rtl] .pe-lg-5 {
    padding-left: 3rem !important;
  }

  .pb-lg-0 {
    padding-bottom: 0 !important;
  }

  .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-lg-3 {
    padding-bottom: 1rem !important;
  }

  .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }

  .pb-lg-5 {
    padding-bottom: 3rem !important;
  }

  html:not([dir=rtl]) .ps-lg-0 {
    padding-left: 0 !important;
  }
  *[dir=rtl] .ps-lg-0 {
    padding-right: 0 !important;
  }

  html:not([dir=rtl]) .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .ps-lg-1 {
    padding-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .ps-lg-2 {
    padding-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ps-lg-3 {
    padding-left: 1rem !important;
  }
  *[dir=rtl] .ps-lg-3 {
    padding-right: 1rem !important;
  }

  html:not([dir=rtl]) .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .ps-lg-4 {
    padding-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ps-lg-5 {
    padding-left: 3rem !important;
  }
  *[dir=rtl] .ps-lg-5 {
    padding-right: 3rem !important;
  }

  html:not([dir=rtl]) .text-lg-start {
    text-align: left !important;
  }
  *[dir=rtl] .text-lg-start {
    text-align: right !important;
  }

  html:not([dir=rtl]) .text-lg-end {
    text-align: right !important;
  }
  *[dir=rtl] .text-lg-end {
    text-align: left !important;
  }

  html:not([dir=rtl]) .text-lg-center {
    text-align: center !important;
  }
  *[dir=rtl] .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  html:not([dir=rtl]) .float-xl-start {
    float: left !important;
  }
  *[dir=rtl] .float-xl-start {
    float: right !important;
  }

  html:not([dir=rtl]) .float-xl-end {
    float: right !important;
  }
  *[dir=rtl] .float-xl-end {
    float: left !important;
  }

  html:not([dir=rtl]) .float-xl-none {
    float: none !important;
  }
  *[dir=rtl] .float-xl-none {
    float: none !important;
  }

  .d-xl-inline {
    display: inline !important;
  }

  .d-xl-inline-block {
    display: inline-block !important;
  }

  .d-xl-block {
    display: block !important;
  }

  .d-xl-grid {
    display: grid !important;
  }

  .d-xl-table {
    display: table !important;
  }

  .d-xl-table-row {
    display: table-row !important;
  }

  .d-xl-table-cell {
    display: table-cell !important;
  }

  .d-xl-flex {
    display: flex !important;
  }

  .d-xl-inline-flex {
    display: inline-flex !important;
  }

  .d-xl-none {
    display: none !important;
  }

  .flex-xl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xl-row {
    flex-direction: row !important;
  }

  .flex-xl-column {
    flex-direction: column !important;
  }

  .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .gap-xl-0 {
    gap: 0 !important;
  }

  .gap-xl-1 {
    gap: 0.25rem !important;
  }

  .gap-xl-2 {
    gap: 0.5rem !important;
  }

  .gap-xl-3 {
    gap: 1rem !important;
  }

  .gap-xl-4 {
    gap: 1.5rem !important;
  }

  .gap-xl-5 {
    gap: 3rem !important;
  }

  .justify-content-xl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xl-center {
    justify-content: center !important;
  }

  .justify-content-xl-between {
    justify-content: space-between !important;
  }

  .justify-content-xl-around {
    justify-content: space-around !important;
  }

  .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xl-start {
    align-items: flex-start !important;
  }

  .align-items-xl-end {
    align-items: flex-end !important;
  }

  .align-items-xl-center {
    align-items: center !important;
  }

  .align-items-xl-baseline {
    align-items: baseline !important;
  }

  .align-items-xl-stretch {
    align-items: stretch !important;
  }

  .align-content-xl-start {
    align-content: flex-start !important;
  }

  .align-content-xl-end {
    align-content: flex-end !important;
  }

  .align-content-xl-center {
    align-content: center !important;
  }

  .align-content-xl-between {
    align-content: space-between !important;
  }

  .align-content-xl-around {
    align-content: space-around !important;
  }

  .align-content-xl-stretch {
    align-content: stretch !important;
  }

  .align-self-xl-auto {
    align-self: auto !important;
  }

  .align-self-xl-start {
    align-self: flex-start !important;
  }

  .align-self-xl-end {
    align-self: flex-end !important;
  }

  .align-self-xl-center {
    align-self: center !important;
  }

  .align-self-xl-baseline {
    align-self: baseline !important;
  }

  .align-self-xl-stretch {
    align-self: stretch !important;
  }

  .order-xl-first {
    order: -1 !important;
  }

  .order-xl-0 {
    order: 0 !important;
  }

  .order-xl-1 {
    order: 1 !important;
  }

  .order-xl-2 {
    order: 2 !important;
  }

  .order-xl-3 {
    order: 3 !important;
  }

  .order-xl-4 {
    order: 4 !important;
  }

  .order-xl-5 {
    order: 5 !important;
  }

  .order-xl-last {
    order: 6 !important;
  }

  .m-xl-0 {
    margin: 0 !important;
  }

  .m-xl-1 {
    margin: 0.25rem !important;
  }

  .m-xl-2 {
    margin: 0.5rem !important;
  }

  .m-xl-3 {
    margin: 1rem !important;
  }

  .m-xl-4 {
    margin: 1.5rem !important;
  }

  .m-xl-5 {
    margin: 3rem !important;
  }

  .m-xl-auto {
    margin: auto !important;
  }

  .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }

  .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xl-0 {
    margin-top: 0 !important;
  }

  .mt-xl-1 {
    margin-top: 0.25rem !important;
  }

  .mt-xl-2 {
    margin-top: 0.5rem !important;
  }

  .mt-xl-3 {
    margin-top: 1rem !important;
  }

  .mt-xl-4 {
    margin-top: 1.5rem !important;
  }

  .mt-xl-5 {
    margin-top: 3rem !important;
  }

  .mt-xl-auto {
    margin-top: auto !important;
  }

  html:not([dir=rtl]) .me-xl-0 {
    margin-right: 0 !important;
  }
  *[dir=rtl] .me-xl-0 {
    margin-left: 0 !important;
  }

  html:not([dir=rtl]) .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  *[dir=rtl] .me-xl-1 {
    margin-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  *[dir=rtl] .me-xl-2 {
    margin-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .me-xl-3 {
    margin-right: 1rem !important;
  }
  *[dir=rtl] .me-xl-3 {
    margin-left: 1rem !important;
  }

  html:not([dir=rtl]) .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  *[dir=rtl] .me-xl-4 {
    margin-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .me-xl-5 {
    margin-right: 3rem !important;
  }
  *[dir=rtl] .me-xl-5 {
    margin-left: 3rem !important;
  }

  html:not([dir=rtl]) .me-xl-auto {
    margin-right: auto !important;
  }
  *[dir=rtl] .me-xl-auto {
    margin-left: auto !important;
  }

  .mb-xl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-xl-3 {
    margin-bottom: 1rem !important;
  }

  .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-xl-5 {
    margin-bottom: 3rem !important;
  }

  .mb-xl-auto {
    margin-bottom: auto !important;
  }

  html:not([dir=rtl]) .ms-xl-0 {
    margin-left: 0 !important;
  }
  *[dir=rtl] .ms-xl-0 {
    margin-right: 0 !important;
  }

  html:not([dir=rtl]) .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ms-xl-1 {
    margin-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ms-xl-2 {
    margin-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ms-xl-3 {
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ms-xl-3 {
    margin-right: 1rem !important;
  }

  html:not([dir=rtl]) .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ms-xl-4 {
    margin-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ms-xl-5 {
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ms-xl-5 {
    margin-right: 3rem !important;
  }

  html:not([dir=rtl]) .ms-xl-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ms-xl-auto {
    margin-right: auto !important;
  }

  .p-xl-0 {
    padding: 0 !important;
  }

  .p-xl-1 {
    padding: 0.25rem !important;
  }

  .p-xl-2 {
    padding: 0.5rem !important;
  }

  .p-xl-3 {
    padding: 1rem !important;
  }

  .p-xl-4 {
    padding: 1.5rem !important;
  }

  .p-xl-5 {
    padding: 3rem !important;
  }

  .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }

  .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .pt-xl-0 {
    padding-top: 0 !important;
  }

  .pt-xl-1 {
    padding-top: 0.25rem !important;
  }

  .pt-xl-2 {
    padding-top: 0.5rem !important;
  }

  .pt-xl-3 {
    padding-top: 1rem !important;
  }

  .pt-xl-4 {
    padding-top: 1.5rem !important;
  }

  .pt-xl-5 {
    padding-top: 3rem !important;
  }

  html:not([dir=rtl]) .pe-xl-0 {
    padding-right: 0 !important;
  }
  *[dir=rtl] .pe-xl-0 {
    padding-left: 0 !important;
  }

  html:not([dir=rtl]) .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  *[dir=rtl] .pe-xl-1 {
    padding-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  *[dir=rtl] .pe-xl-2 {
    padding-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .pe-xl-3 {
    padding-right: 1rem !important;
  }
  *[dir=rtl] .pe-xl-3 {
    padding-left: 1rem !important;
  }

  html:not([dir=rtl]) .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  *[dir=rtl] .pe-xl-4 {
    padding-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .pe-xl-5 {
    padding-right: 3rem !important;
  }
  *[dir=rtl] .pe-xl-5 {
    padding-left: 3rem !important;
  }

  .pb-xl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-xl-3 {
    padding-bottom: 1rem !important;
  }

  .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }

  .pb-xl-5 {
    padding-bottom: 3rem !important;
  }

  html:not([dir=rtl]) .ps-xl-0 {
    padding-left: 0 !important;
  }
  *[dir=rtl] .ps-xl-0 {
    padding-right: 0 !important;
  }

  html:not([dir=rtl]) .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .ps-xl-1 {
    padding-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .ps-xl-2 {
    padding-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ps-xl-3 {
    padding-left: 1rem !important;
  }
  *[dir=rtl] .ps-xl-3 {
    padding-right: 1rem !important;
  }

  html:not([dir=rtl]) .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .ps-xl-4 {
    padding-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ps-xl-5 {
    padding-left: 3rem !important;
  }
  *[dir=rtl] .ps-xl-5 {
    padding-right: 3rem !important;
  }

  html:not([dir=rtl]) .text-xl-start {
    text-align: left !important;
  }
  *[dir=rtl] .text-xl-start {
    text-align: right !important;
  }

  html:not([dir=rtl]) .text-xl-end {
    text-align: right !important;
  }
  *[dir=rtl] .text-xl-end {
    text-align: left !important;
  }

  html:not([dir=rtl]) .text-xl-center {
    text-align: center !important;
  }
  *[dir=rtl] .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  html:not([dir=rtl]) .float-xxl-start {
    float: left !important;
  }
  *[dir=rtl] .float-xxl-start {
    float: right !important;
  }

  html:not([dir=rtl]) .float-xxl-end {
    float: right !important;
  }
  *[dir=rtl] .float-xxl-end {
    float: left !important;
  }

  html:not([dir=rtl]) .float-xxl-none {
    float: none !important;
  }
  *[dir=rtl] .float-xxl-none {
    float: none !important;
  }

  .d-xxl-inline {
    display: inline !important;
  }

  .d-xxl-inline-block {
    display: inline-block !important;
  }

  .d-xxl-block {
    display: block !important;
  }

  .d-xxl-grid {
    display: grid !important;
  }

  .d-xxl-table {
    display: table !important;
  }

  .d-xxl-table-row {
    display: table-row !important;
  }

  .d-xxl-table-cell {
    display: table-cell !important;
  }

  .d-xxl-flex {
    display: flex !important;
  }

  .d-xxl-inline-flex {
    display: inline-flex !important;
  }

  .d-xxl-none {
    display: none !important;
  }

  .flex-xxl-fill {
    flex: 1 1 auto !important;
  }

  .flex-xxl-row {
    flex-direction: row !important;
  }

  .flex-xxl-column {
    flex-direction: column !important;
  }

  .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }

  .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }

  .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }

  .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }

  .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }

  .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }

  .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }

  .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }

  .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }

  .gap-xxl-0 {
    gap: 0 !important;
  }

  .gap-xxl-1 {
    gap: 0.25rem !important;
  }

  .gap-xxl-2 {
    gap: 0.5rem !important;
  }

  .gap-xxl-3 {
    gap: 1rem !important;
  }

  .gap-xxl-4 {
    gap: 1.5rem !important;
  }

  .gap-xxl-5 {
    gap: 3rem !important;
  }

  .justify-content-xxl-start {
    justify-content: flex-start !important;
  }

  .justify-content-xxl-end {
    justify-content: flex-end !important;
  }

  .justify-content-xxl-center {
    justify-content: center !important;
  }

  .justify-content-xxl-between {
    justify-content: space-between !important;
  }

  .justify-content-xxl-around {
    justify-content: space-around !important;
  }

  .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }

  .align-items-xxl-start {
    align-items: flex-start !important;
  }

  .align-items-xxl-end {
    align-items: flex-end !important;
  }

  .align-items-xxl-center {
    align-items: center !important;
  }

  .align-items-xxl-baseline {
    align-items: baseline !important;
  }

  .align-items-xxl-stretch {
    align-items: stretch !important;
  }

  .align-content-xxl-start {
    align-content: flex-start !important;
  }

  .align-content-xxl-end {
    align-content: flex-end !important;
  }

  .align-content-xxl-center {
    align-content: center !important;
  }

  .align-content-xxl-between {
    align-content: space-between !important;
  }

  .align-content-xxl-around {
    align-content: space-around !important;
  }

  .align-content-xxl-stretch {
    align-content: stretch !important;
  }

  .align-self-xxl-auto {
    align-self: auto !important;
  }

  .align-self-xxl-start {
    align-self: flex-start !important;
  }

  .align-self-xxl-end {
    align-self: flex-end !important;
  }

  .align-self-xxl-center {
    align-self: center !important;
  }

  .align-self-xxl-baseline {
    align-self: baseline !important;
  }

  .align-self-xxl-stretch {
    align-self: stretch !important;
  }

  .order-xxl-first {
    order: -1 !important;
  }

  .order-xxl-0 {
    order: 0 !important;
  }

  .order-xxl-1 {
    order: 1 !important;
  }

  .order-xxl-2 {
    order: 2 !important;
  }

  .order-xxl-3 {
    order: 3 !important;
  }

  .order-xxl-4 {
    order: 4 !important;
  }

  .order-xxl-5 {
    order: 5 !important;
  }

  .order-xxl-last {
    order: 6 !important;
  }

  .m-xxl-0 {
    margin: 0 !important;
  }

  .m-xxl-1 {
    margin: 0.25rem !important;
  }

  .m-xxl-2 {
    margin: 0.5rem !important;
  }

  .m-xxl-3 {
    margin: 1rem !important;
  }

  .m-xxl-4 {
    margin: 1.5rem !important;
  }

  .m-xxl-5 {
    margin: 3rem !important;
  }

  .m-xxl-auto {
    margin: auto !important;
  }

  .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }

  .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }

  .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }

  .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }

  .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }

  .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }

  .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }

  .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }

  .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }

  .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }

  .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }

  .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }

  .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }

  .mt-xxl-0 {
    margin-top: 0 !important;
  }

  .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }

  .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }

  .mt-xxl-3 {
    margin-top: 1rem !important;
  }

  .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }

  .mt-xxl-5 {
    margin-top: 3rem !important;
  }

  .mt-xxl-auto {
    margin-top: auto !important;
  }

  html:not([dir=rtl]) .me-xxl-0 {
    margin-right: 0 !important;
  }
  *[dir=rtl] .me-xxl-0 {
    margin-left: 0 !important;
  }

  html:not([dir=rtl]) .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  *[dir=rtl] .me-xxl-1 {
    margin-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  *[dir=rtl] .me-xxl-2 {
    margin-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .me-xxl-3 {
    margin-right: 1rem !important;
  }
  *[dir=rtl] .me-xxl-3 {
    margin-left: 1rem !important;
  }

  html:not([dir=rtl]) .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  *[dir=rtl] .me-xxl-4 {
    margin-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .me-xxl-5 {
    margin-right: 3rem !important;
  }
  *[dir=rtl] .me-xxl-5 {
    margin-left: 3rem !important;
  }

  html:not([dir=rtl]) .me-xxl-auto {
    margin-right: auto !important;
  }
  *[dir=rtl] .me-xxl-auto {
    margin-left: auto !important;
  }

  .mb-xxl-0 {
    margin-bottom: 0 !important;
  }

  .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }

  .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }

  .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }

  .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }

  .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }

  .mb-xxl-auto {
    margin-bottom: auto !important;
  }

  html:not([dir=rtl]) .ms-xxl-0 {
    margin-left: 0 !important;
  }
  *[dir=rtl] .ms-xxl-0 {
    margin-right: 0 !important;
  }

  html:not([dir=rtl]) .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  *[dir=rtl] .ms-xxl-1 {
    margin-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  *[dir=rtl] .ms-xxl-2 {
    margin-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  *[dir=rtl] .ms-xxl-3 {
    margin-right: 1rem !important;
  }

  html:not([dir=rtl]) .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  *[dir=rtl] .ms-xxl-4 {
    margin-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  *[dir=rtl] .ms-xxl-5 {
    margin-right: 3rem !important;
  }

  html:not([dir=rtl]) .ms-xxl-auto {
    margin-left: auto !important;
  }
  *[dir=rtl] .ms-xxl-auto {
    margin-right: auto !important;
  }

  .p-xxl-0 {
    padding: 0 !important;
  }

  .p-xxl-1 {
    padding: 0.25rem !important;
  }

  .p-xxl-2 {
    padding: 0.5rem !important;
  }

  .p-xxl-3 {
    padding: 1rem !important;
  }

  .p-xxl-4 {
    padding: 1.5rem !important;
  }

  .p-xxl-5 {
    padding: 3rem !important;
  }

  .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }

  .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }

  .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }

  .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }

  .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }

  .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }

  .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }

  .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }

  .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }

  .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }

  .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }

  .pt-xxl-0 {
    padding-top: 0 !important;
  }

  .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }

  .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }

  .pt-xxl-3 {
    padding-top: 1rem !important;
  }

  .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }

  .pt-xxl-5 {
    padding-top: 3rem !important;
  }

  html:not([dir=rtl]) .pe-xxl-0 {
    padding-right: 0 !important;
  }
  *[dir=rtl] .pe-xxl-0 {
    padding-left: 0 !important;
  }

  html:not([dir=rtl]) .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  *[dir=rtl] .pe-xxl-1 {
    padding-left: 0.25rem !important;
  }

  html:not([dir=rtl]) .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  *[dir=rtl] .pe-xxl-2 {
    padding-left: 0.5rem !important;
  }

  html:not([dir=rtl]) .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  *[dir=rtl] .pe-xxl-3 {
    padding-left: 1rem !important;
  }

  html:not([dir=rtl]) .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  *[dir=rtl] .pe-xxl-4 {
    padding-left: 1.5rem !important;
  }

  html:not([dir=rtl]) .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  *[dir=rtl] .pe-xxl-5 {
    padding-left: 3rem !important;
  }

  .pb-xxl-0 {
    padding-bottom: 0 !important;
  }

  .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }

  .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }

  .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }

  .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }

  .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }

  html:not([dir=rtl]) .ps-xxl-0 {
    padding-left: 0 !important;
  }
  *[dir=rtl] .ps-xxl-0 {
    padding-right: 0 !important;
  }

  html:not([dir=rtl]) .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  *[dir=rtl] .ps-xxl-1 {
    padding-right: 0.25rem !important;
  }

  html:not([dir=rtl]) .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  *[dir=rtl] .ps-xxl-2 {
    padding-right: 0.5rem !important;
  }

  html:not([dir=rtl]) .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  *[dir=rtl] .ps-xxl-3 {
    padding-right: 1rem !important;
  }

  html:not([dir=rtl]) .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  *[dir=rtl] .ps-xxl-4 {
    padding-right: 1.5rem !important;
  }

  html:not([dir=rtl]) .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  *[dir=rtl] .ps-xxl-5 {
    padding-right: 3rem !important;
  }

  html:not([dir=rtl]) .text-xxl-start {
    text-align: left !important;
  }
  *[dir=rtl] .text-xxl-start {
    text-align: right !important;
  }

  html:not([dir=rtl]) .text-xxl-end {
    text-align: right !important;
  }
  *[dir=rtl] .text-xxl-end {
    text-align: left !important;
  }

  html:not([dir=rtl]) .text-xxl-center {
    text-align: center !important;
  }
  *[dir=rtl] .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .fs-1 {
    font-size: 2.5rem !important;
  }

  .fs-2 {
    font-size: 2rem !important;
  }

  .fs-3 {
    font-size: 1.75rem !important;
  }

  .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .d-print-inline {
    display: inline !important;
  }

  .d-print-inline-block {
    display: inline-block !important;
  }

  .d-print-block {
    display: block !important;
  }

  .d-print-grid {
    display: grid !important;
  }

  .d-print-table {
    display: table !important;
  }

  .d-print-table-row {
    display: table-row !important;
  }

  .d-print-table-cell {
    display: table-cell !important;
  }

  .d-print-flex {
    display: flex !important;
  }

  .d-print-inline-flex {
    display: inline-flex !important;
  }

  .d-print-none {
    display: none !important;
  }
}
.wrapper {
  width: 100%;
  will-change: auto;
  transition: padding 0.15s;
}
html:not([dir=rtl]) .wrapper {
  padding-left: var(--cui-sidebar-occupy-start, 0);
}
*[dir=rtl] .wrapper {
  padding-right: var(--cui-sidebar-occupy-start, 0);
}
@media (prefers-reduced-motion: reduce) {
  .wrapper {
    transition: none;
  }
}

/* stylelint-disable declaration-no-important, scss/selector-no-redundant-nesting-selector */
.example:not(:first-child) {
  margin-top: 1.5rem;
}
.example .tab-content {
  background-color: #f9fafa !important;
}
.dark-theme .example .tab-content {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.example + p {
  margin-top: 1.5rem;
}
.example .preview + p {
  margin-top: 2rem;
}
.example .preview > .form-control + .form-control {
  margin-top: 0.5rem;
}
.example .preview > .nav + .nav,
.example .preview > .alert + .alert,
.example .preview > .navbar + .navbar,
.example .preview > .progress + .progress {
  margin-top: 1rem;
}
.example .preview > .dropdown-menu {
  position: static;
  display: block;
}
.example .preview > :last-child {
  margin-bottom: 0;
}
.example .preview > svg + svg,
.example .preview > img + img {
  margin-left: 0.5rem;
}
.example .preview > .btn,
.example .preview > .btn-group {
  margin: 0.25rem 0.125rem;
}
.example .preview > .btn-toolbar + .btn-toolbar {
  margin-top: 0.5rem;
}
.example .preview > .list-group {
  max-width: 400px;
}
.example .preview > [class*=list-group-horizontal] {
  max-width: 100%;
}
.example .preview .fixed-top,
.example .preview .sticky-top {
  position: static;
  margin: -1rem -1rem 1rem;
}
.example .preview .fixed-bottom {
  position: static;
  margin: 1rem -1rem -1rem;
}
@media (min-width: 576px) {
  .example .preview .fixed-top,
.example .preview .sticky-top {
    margin: -1.5rem -1.5rem 1rem;
  }
  .example .preview .fixed-bottom {
    margin: 1rem -1.5rem -1.5rem;
  }
}
.example .preview .pagination {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
