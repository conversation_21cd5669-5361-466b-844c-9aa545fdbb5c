
.nk-sidebar.is-dark {
    background: #0f192a;
    border-right-color: #1c2f50;
}
.nk-sidebar.is-theme {
    background: #0d5f5c;
    border-right-color: #005f5f;
}
.is-dark .nk-sidebar-head {
    border-color: #1c2f50;
}
.is-theme .nk-sidebar-head {
    border-color: #005f5f;
}
.nk-sidebar.is-dark .user-balance-alt, .nk-sidebar.is-theme .user-balance-alt {
    color: rgba(255, 255, 255, 0.8);
}

.nk-header {
    background: #f5f6fa;
    border-bottom-color: #e5e9f2;
}
.nk-header.is-dark:not([class*=bg-]) {
    background: #0f192a;
}
.nk-header.is-theme:not([class*=bg-]) {
    background: #0d5f5c;
}

.is-dark .nk-menu-link {
    color: #8699b2;
}
.is-dark .nk-menu-icon {
    color: #9faec2;
}
.is-dark .nk-menu-badge {
    color: #34edba;
    background: #1c2f50;
}
.is-dark .nk-menu-sub .active > .nk-menu-link,
.is-dark .nk-menu-link:hover, .is-dark .active > .nk-menu-link,
.is-dark .nk-menu-link:hover .nk-menu-icon, .is-dark .nk-menu-item.active > .nk-menu-link .nk-menu-icon, .is-dark .nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon {
    color: #13dba4;
}
.is-dark .nk-news-icon .icon {
    color: #0fac81;
}
.is-dark .nk-news-text .icon {
    color: #8094ae;
}
.is-dark .nk-news-text p span {
    color: rgba(128, 148, 174, 0.9);
}
.is-dark .user-name, .is-dark .nk-news-text p {
    color: #90a1b8;
}
.is-dark .nk-quick-nav-icon {
    color: #9faec2;
}

.is-theme .nk-menu-link {
    color: #a3dfcf;
}
.is-theme .nk-menu-icon {
    color: #c2eadf;
}
.is-theme .nk-menu-badge {
    color: #63f1ca;
    background: #005f5f;
}
.is-theme .nk-news-icon .icon {
    color: #1cebb3;
}
.is-theme .nk-news-text .icon, .is-theme .nk-quick-nav-icon, .is-theme .overline-title {
    color: #abe2d3;
}
.is-theme .nk-news-text p span, .is-theme .nk-menu-heading .overline-title {
    color: rgba(171, 226, 211, 0.8);
}
.is-theme .user-name, .is-theme .nk-news-text p {
    color: #bee9dd;
}

.card-tools-nav li a:before,
.nav-tabs .nav-link:after,
.progress-bar, .dot-primary,
.alert-fill.alert-primary,
.badge-primary,
.icon-circle,
.noUi-connect,
.nk-msg-menu-item a:after,
.ui-shady .nk-msg-item.active:after, .ui-shady .nk-msg-item.current:after,
.ui-softy .nk-msg-item.active:after, .ui-softy .nk-msg-item.current:after {
    background: #0fac81;
}

.card-bordered.is-dark {
    border-color: #07523d;
}

.card.is-dark {
    background: #07523d;
}

.user-avatar, [class^=user-avatar]:not([class*=-group]) {
    background: #13dba4;
}

.nk-news-item:hover .nk-news-text .icon {
    color: #13dba4;
}

.nk-menu-link:hover,
.nk-menu-link:hover .nk-menu-icon,
.nk-menu-item.active > .nk-menu-link .nk-menu-icon,
.nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon,
.nk-menu-link:hover .count,
.nk-menu-sub .nk-menu-link:hover,
.nk-menu-sub .active > .nk-menu-link,
.nk-menu-sm .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:focus,
.nk-menu-main .nk-menu-item.active > .nk-menu-link,
.nk-menu-main .nk-menu-item.current-menu > .nk-menu-link,
.nk-menu-main .nk-menu-item:hover > .nk-menu-link,
.is-light .nk-menu-link:hover, .is-light .active > .nk-menu-link,
.active > .nk-menu-link,
.active > .nk-menu-link .count,
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.active .nav-link,
.nk-menu-footer .nk-menu-icon,
.nk-menu-footer .nk-menu-link:hover,
.nk-footer-copyright a:hover,
.page-link:hover,
.list-plain a:hover,
.link-check li a:hover,
.link-list a:hover,
.link-list-opt a:hover,
.link-list-plain a:hover,
.link-list-menu li.active > a,
.link-list-menu a.active,
.link-list-menu a:hover,
.link-list-menu li.active > a .icon,
.link-list-menu a.active .icon,
.link-list-menu a:hover .icon,
.link-list-menu li.active > a:after,
.link-list-menu a.active:after,
.link-list-menu a:hover:after,
.list-checked li:before,
.list-step li.list-step-current:before,
.accordion-s2 .accordion-head .title,
.accordion-s3 .accordion-head .title,
.bg-outline-primary,
.badge-dim.bg-primary,
.badge-dot.bg-primary,
.badge-dim.bg-outline-primary,
.alert-primary,
.form-clip,
.form-text-hint,
.search-submit:hover,
.nk-news-icon .icon,
.attach-item .icon,
.attach-download:hover span,
.nk-reply-meta-info .whom,
.nk-msg-tags li > span .icon,
.nk-msg-menu-item a:hover,
.nk-msg-menu-item.active a,
.user-balance,
.user-avatar[class*=-primary-dim],
.nk-order-ovwg-data.sell .amount,
.nk-order-ovwg-data.sell .title .icon,
.nk-wg-action-content p strong, a,
.dropzone .dz-message-text span,
.nk-switch-icon.active,
.link-list-plain a .icon,
.chat-upload-option a,
.is-unread .chat-context .status,
.add-opt:hover .sub-text, .add-opt:hover .icon,
.icon[class*=bg-primary-dim] {
    color: #0fac81;
}

a:hover {
    color: #0c8665;
}

.text-primary, .link-primary {
    color: #0fac81 !important;
}

.link-primary:hover, a.text-primary:hover, a.text-primary:focus, .chat-upload-option a:hover {
    color: #0c8665 !important;
}

.border-primary, .nk-kycfm-control:checked ~ .nk-kycfm-label {
    border-color: #0fac81 !important;
}

.bg-lighter {
    background-color: #f5f6fa !important;
}

.bg-primary {
    background-color: #0fac81 !important;
}

a.bg-primary:hover, a.bg-primary:focus, button.bg-primary:hover, button.bg-primary:focus {
    background-color: #0b7d5e !important;
}

.bg-primary-dim, .dual-listbox .dual-listbox__item:active, .dual-listbox .dual-listbox__item.dual-listbox__item--selected {
    background-color: #e7f7f2 !important;
}

.text-primary-dim {
    color: #e7f7f2 !important;
}

.alert-primary, .badge-dim.bg-outline-primary {
    background-color: #e7f7f2;
    border-color: #9fdecd;
}

.alert-pro.alert-primary, .bg-primary,
.form-control:focus,
.form-control.focus,
.custom-file-input:focus ~ .custom-file-label,
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #0fac81;
}

.nk-order-ovwg-data.sell, .bg-outline-primary {
    border-color: #9fdecd;
}

.dropdown-menu-s1 {
    border-top-color: #0fac81;
}

.nk-iv-wg2-amount.ui-v2 {
    border-bottom-color: #0fac81;
}

.page-item.active .page-link,
.custom-control-input:checked ~ .custom-control-label::before,
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
    background-color: #0fac81;
    border-color: #0fac81;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
    background-color: #6fcdb3;
}

.badge-dim.bg-primary {
    background-color: #e7f7f2;
    border-color: #e7f7f2;
}

.bg-primary.badge-dot {
    background: transparent !important;
}

.nk-error-head {
    background: -webkit-linear-gradient(#0fac81, #0b8160);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.nav-switch .nav-link.active {
    background: #0fac81;
    color: #ffffff;
}

.icon-avatar {
    color: #0fac81;
    background-color: #dbf3ec;
}

.is-theme .nk-quick-nav-icon:before {
    background-color: #03231a;
}

.btn-primary {
    background-color: #0fac81;
    border-color: #0fac81;
}
.btn-primary:hover {
    background-color: #0d9973;
    border-color: #0d906c;
}
.btn-primary:focus {
    background-color: #0d9973;
    border-color: #0d906c;
}
.btn-primary.btn-dim {
    color: #0fac81;
    background-color: #e5f6f1;
    border-color: #e5f6f1;
}

.btn-dim.btn-outline-primary {
    color: #0fac81;
    background-color: #e5f6f1;
    border-color: #9fdecd;
}
.btn-dim.btn-outline-primary.btn-white {
    background-color: #ffffff;
}

.btn-outline-primary {
    border-color: #0fac81;
    color: #0fac81;
}
.btn-outline-primary:hover {
    background-color: #0fac81;
    border-color: #0fac81;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
    background-color: #0d5f5c;
    border-color: #0d9973;
}

.btn-primary:focus,
.btn-outline-primary:focus,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary.btn-dim:focus,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active:focus,
.btn-dim.btn-outline-primary:focus,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle,
.show > .btn-outline-primary.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(15, 172, 129, 0.2);
}

.btn-outline-light:focus, .btn-outline-light.focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
    box-shadow: 0 0 0 0.2rem rgba(128, 148, 174, 0.1);
}

.btn-trigger:focus {
    box-shadow: none;
}

.form-control:focus, .form-control.focus,
.custom-control-input:focus ~ .custom-control-label::before,
.custom-file-input:focus ~ .custom-file-label,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    box-shadow: 0 0 0 3px rgba(15, 172, 129, 0.1);
}

.form-control-simple:focus {
    box-shadow: none;
}

.btn-primary.btn-dim:not(:disabled):not(.disabled):hover,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):hover,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle,
.btn-primary.disabled, .btn-primary:disabled {
    background-color: #0fac81;
    border-color: #0fac81;
}

.form-focus-none:focus {
    border-color: transparent;
    box-shadow: none;
}

@media (min-width: 992px) {
    .nk-menu-main > li > .nk-menu-link:before {
        background: #0fac81;
    }
    .is-theme .nk-menu-main > li > .nk-menu-link {
        color: #abe2d3;
    }
    .is-theme .nk-menu-main > li > .nk-menu-link.nk-menu-toggle:after {
        color: rgba(171, 226, 211, 0.7);
    }
}
.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
    background-color: #dbf3ec;
    color: #0fac81;
}

.datepicker table tr td.today:hover, .datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover, .datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:active, .datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active, .datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active, .datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active, .datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled, .datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled, .datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled], .datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled], .datepicker table tr td.today.disabled:hover[disabled] {
    background-color: #72ceb5;
}

.datepicker table tr td.active:active, .datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active, .datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td span:hover, .datepicker table tr td span.focused {
    background-color: #0fac81;
}

.code-tag {
    color: blue;
}

.alert-pro {
    background: #ffffff;
    color: #526484;
}

.nk-menu-badge {
    color: #57c5a7;
    background-color: #e7f7f2;
}

.is-theme .nk-sidebar-footer, .is-theme .nk-sidebar-profile-fixed {
    background: #0d5f5c;
    border-color: #005f5f;
}

.is-theme .nk-menu-footer .nk-menu-link, .is-theme .sub-text, .is-theme .lead-text span {
    color: #abe2d3;
}

.nav-switch-s2 .nav-link:hover, .nav-switch-s2 .nav-link:focus {
    color: #526484;
}

.nav-switch-s2 .nav-link.active {
    color: #364a63;
}

.active .nk-ibx-menu-text,
.active .nk-ibx-menu-item .icon,
.nk-reply-form-nav li a:hover,
.nk-reply-form-input .toggle-opt:hover {
    color: #0fac81;
}

.nk-ibx-menu li.active {
    background: #ecf8f5;
}

.nk-fmg-menu li.active {
    background: #ecf8f5;
}

.nk-file-name .asterisk .icon, .nk-file-name-text a.title:hover,
.nk-file-link:hover .title, .active .nk-fmg-menu-item .icon, .active .nk-fmg-menu-text {
    color: #0fac81;
}

.nk-files-view-list .nk-file.selected {
    background-color: #e2f5f0;
}

.chat.is-me .chat-msg {
    background-color: #0fac81;
}

.kanban-add-task {
    color: #0fac81;
}
.kanban-add-task:hover {
    background: #0fac81;
    border-color: #0fac81;
}

.actions ul li a {
    border-color: #0fac81;
    background: #0fac81;
}

.nk-wizard-simple .steps ul li.done h5, .nk-wizard-simple .steps ul li.done .number, .nk-wizard-simple .steps ul li.current h5, .nk-wizard-simple .steps ul li.current .number {
    color: #0fac81;
}

.nk-wizard-simple .steps ul li:after {
    background: #0fac81;
}
.js-preloader {
    position: fixed;
    inset: 0;
    background: #fff;
    z-index: 99999;
}
.page-loaded .js-preloader {
    pointer-events: none;
}

.loading-animation {
    position: absolute;
    left: 50%;
    top: 50%;
    display: block;
    transform: translate(-50%, -50%);
}

.tri-ring {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    border: 2px solid transparent;
    border-top-color: #6576ff;
    animation: spin 2s linear infinite;
}

.tri-ring:before, .tri-ring:after {
    content: "";
    position: absolute;
    border-radius: 50%;
    border: 3px solid transparent;
}

.tri-ring:before {
    top: 5px;
    left: 5px;
    right: 5px;
    bottom: 5px;
    border-top-color: #09c2de;
    animation: spin 3s linear infinite;
}

.tri-ring:after {
    top: 15px;
    left: 15px;
    right: 15px;
    bottom: 15px;
    border-top-color: #1f2b3a;
    animation: spin 1.5s linear infinite;
}

.duo-pulse {
    width: 50px;
    height: 50px;
    display: inline-block;
    transform: translateZ(0);
}

.duo-pulse::before, .duo-pulse::after {
    content: "";
    background-color: #6576ff;
    display: inline-block;
    float: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 50px;
    height: 50px;
    opacity: 0.5;
    border-radius: 50%;
    animation: ballPulseDouble 2s ease-in-out infinite;
}
.duo-pulse::after {
    animation-delay: -1s;
}

@keyframes ballPulseDouble {
    0%, 100% {
        transform: scale(0);
    }
    50% {
        transform: scale(1);
    }
}
.widget-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}
.widget-title > * {
    margin-bottom: 0;
}
.text-center .widget-title {
    justify-content: center;
}
.is-theme .widget-title a:hover {
    color: #fff;
}

.side-wg:not(:last-child) {
    margin-bottom: 2.5rem;
}
.side-wg-title {
    padding-bottom: 0.75rem;
}
.side-wg .back-to {
    font-size: 0.875rem;
    line-height: 1.1;
    font-weight: 400;
    position: relative;
    color: #8094ae;
    display: inline-flex;
    align-items: center;
}
.side-wg .back-to .icon {
    font-size: 1.25rem;
    width: 1.75rem;
    margin-top: -3px;
    display: inline-block;
}

.nk-ck {
    height: 260px;
}
.nk-ck-sm {
    height: 180px;
}

.nk-ck1 {
    height: 120px;
}

.nk-ck2 {
    height: 240px;
}

.nk-ck3 {
    height: 160px;
}

.nk-cktv {
    height: 300px;
    overflow: hidden;
    border: 1px solid #dbdfea;
    border-radius: 4px;
}
.nk-cktv .tradingview-widget-container {
    overflow: hidden;
    position: relative;
    top: 8px;
}
.nk-cktv .tradingview-widget-container > div {
    margin: -1px;
}

@media (min-width: 576px) {
    .nk-ck {
        height: 260px;
    }
    .nk-ck-sm {
        height: 180px;
    }
    .nk-ck1 {
        height: 120px;
    }
    .nk-ck2 {
        height: 240px;
    }
    .nk-ck3 {
        height: 258px;
    }
}
.nk-wg6-title:not(:first-child) {
    margin-top: 1.5rem;
}
.nk-wg6-title:not(:last-child) {
    margin-bottom: 1.5rem;
}
.nk-wg6-text:not(:last-child) {
    margin-bottom: 1.5rem;
}

.aside-wg + .aside-wg {
    padding-top: 2rem;
}

.nk-modal-title:not(:first-child) {
    margin-top: 1.5rem;
}
.nk-modal-title:not(:last-child) {
    margin-bottom: 1.5rem;
}
.nk-modal-title.title {
    font-size: 1.5rem;
}
.nk-modal-text:not(:last-child) {
    margin-bottom: 1.5rem;
}
.nk-modal-text .lead {
    font-size: 1.1rem;
    line-height: 1.5;
}
.nk-modal-text .sub-text {
    font-size: 14px;
}
.nk-modal-action {
    margin-top: 1.5rem;
}
.nk-modal-action-sm {
    margin-top: 1rem;
}
.nk-modal-action-md {
    margin-top: 2rem;
}
.nk-modal-action-lg {
    margin-top: 2.25rem;
}

.modal-body-lg .tab-content {
    margin-top: 2rem;
}

@media (min-width: 576px) {
    .nk-modal-action-md {
        margin-top: 2.5rem;
    }
    .nk-modal-action-lg {
        margin-top: 3.25rem;
    }
}
[data-bs-toggle=modal] {
    cursor: pointer;
}

.search-wrap {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0;
    background: #fff;
    transition: opacity 0.4s;
    border-radius: 6px;
    pointer-events: none;
    display: flex;
    align-items: center;
}
.search-wrap.active {
    opacity: 1;
    z-index: 9;
    pointer-events: auto;
}
.search-wrap-extend {
    margin-top: -1rem;
    left: -2px;
    right: -2px;
}
.search-toggle {
    transition: all 0.4s;
    opacity: 1;
}
.search-toggle.active {
    opacity: 0;
}
.search-content {
    position: relative;
    width: 100%;
}
.search-content .form-control, .search-content div.dataTables_wrapper div.dataTables_filter input, div.dataTables_wrapper div.dataTables_filter .search-content input, .search-content .dual-listbox .dual-listbox__search, .dual-listbox .search-content .dual-listbox__search {
    padding-left: calc(2.125rem + 2px);
    padding-right: calc(2.125rem + 2px);
}
.search-content .form-control-sm {
    padding-left: calc(1.75rem + 2px);
    padding-right: calc(1.75rem + 2px);
}
.search-back, .search-submit {
    position: absolute;
    top: 50%;
}
.search-back:focus, .search-submit:focus {
    box-shadow: none;
}
.search-back {
    left: 0;
    transform: translate(-0.25rem, -50%);
}
.search-submit {
    right: 0;
    transform: translate(0.5rem, -50%);
}
.search-submit:hover {
    color: #6576ff;
}

.nk-search-box {
    margin-top: 1.5rem;
    margin-bottom: 1.25rem;
}
.nk-search-box .form-icon {
    height: 100%;
    border: none;
    background: transparent;
    width: 3.5rem;
}
.nk-search-box .form-control, .nk-search-box div.dataTables_wrapper div.dataTables_filter input, div.dataTables_wrapper div.dataTables_filter .nk-search-box input, .nk-search-box .dual-listbox .dual-listbox__search, .dual-listbox .nk-search-box .dual-listbox__search {
    border-radius: 1.5rem;
    padding-left: 1.25rem;
    padding-right: 1.5rem;
}

.nk-block + .nk-block, .nk-block + .nk-block-head {
    padding-top: 28px;
}
.nk-block + .nk-block-lg, .nk-block + .nk-block-head-lg {
    padding-top: 2.5rem;
}
.nav-tabs + .nk-block {
    padding-top: 1.5rem;
}
.nav-tabs + .nk-block-sm {
    padding-top: 1rem;
}
.nk-block-area {
    display: flex;
}
.nk-block-area-column {
    flex-direction: column;
}
.nk-block-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.nk-block-between > .title:first-child {
    margin-bottom: 0;
}
.nk-block-middle {
    margin-top: auto;
    margin-bottom: auto;
}
.nk-block-head {
    position: relative;
    padding-bottom: 1.25rem;
}
.nk-block-head:only-child {
    padding-bottom: 0;
}
.nk-block-head h2:not(:last-child), .nk-block-head .h2:not(:last-child) {
    margin-bottom: 1rem;
}
.nk-block-head-sub {
    font-size: 1rem;
    line-height: 1.5rem;
    margin-bottom: 0.5rem;
    color: #8094ae;
    font-weight: 400;
    position: relative;
}
.nk-block-head-sub .dropdown {
    position: absolute;
    right: -8px;
    top: -6px;
}
.nk-block-head-xs {
    padding-bottom: 0.75rem;
}
.nk-block-head-sm {
    padding-bottom: 1rem;
}
.nk-block-head-lg {
    padding-bottom: 1.5rem;
}
.nk-block-head + .nav-tabs {
    margin-top: -1rem;
}
.nk-content-body > .nk-block-head:first-child {
    padding-bottom: 1.75rem;
}
.nk-block-head .nk-block-text {
    margin-top: 1.5rem;
}
.nk-block-head .nk-block-text + .btn, .nk-block-head .dual-listbox .nk-block-text + .dual-listbox__button, .dual-listbox .nk-block-head .nk-block-text + .dual-listbox__button {
    margin-top: 1.5rem;
}
.nk-block-tools {
    display: flex;
    align-items: center;
}
.nk-block-tools > * {
    display: inline-flex;
}
.nk-block-des {
    color: #526484;
}
.nk-block-des strong {
    color: #364a63;
}
.nk-block-des .icon {
    vertical-align: middle;
}
.nk-block-content + .nk-block-head {
    padding-top: 2rem;
}
.nk-block-content + .nk-block-head-sm {
    padding-top: 1.5rem;
}
.nk-block-content-head:not(:last-child) {
    margin-bottom: 1rem;
}
.nk-block-title-group {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.nk-block-title-group .title {
    margin-bottom: 0;
}
.nk-block-title-group:not(:last-child) {
    margin-bottom: 0.5rem;
}

@media (min-width: 576px) {
    .nk-block-head-md {
        padding-bottom: 1.75rem;
    }
    .nk-block-head-lg {
        padding-bottom: 2.5rem;
    }
    .nk-block-content + .nk-block-head {
        padding-top: 4rem;
    }
    .nk-block-content + .nk-block-head-sm {
        padding-top: 2.5rem;
    }
}
@media (min-width: 768px) {
    .nk-content-body > .nk-block-head:first-child {
        padding-bottom: 2.5rem;
    }
    .nk-content-body > .nk-block-head-sm:first-child {
        padding-bottom: 1.75rem;
    }
    .nav-tabs + .nk-block {
        padding-top: 2.5rem;
    }
    .nav-tabs + .nk-block-sm {
        padding-top: 2rem;
    }
    .nav-tabs + .nk-block-xs {
        padding-top: 1.25rem;
    }
    .nk-block-text h5, .nk-block-text .h5, .nk-block-text h6, .nk-block-text .h6 {
        font-size: 1rem;
    }
}
@media (max-width: 767.98px) {
    .nk-block-tools .opt-menu-md {
        order: 100;
        margin-left: auto;
    }
}
@media (min-width: 768px) {
    .nk-block-between-md {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
}
.back-to {
    color: inherit;
    display: inline-flex;
    align-items: center;
}
.back-to .icon {
    font-size: 1.5rem;
    width: 2rem;
    margin-top: -3px;
    display: inline-block;
}

.nk-block-subhead {
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #dbdfea;
}

.nk-feature-center {
    text-align: center;
}

.filter-wg label.overline-title {
    margin-bottom: 0.5rem;
}

.change {
    line-height: 1;
}
.change .sign {
    font-family: "Nioicon";
}
.change .sign:before {
    content: "";
}
.change.up {
    color: #1ee0ac !important;
}
.change.up .sign:before {
    content: "\e93c";
}
.change.down {
    color: #e85347 !important;
}
.change.down .sign:before {
    content: "\e939";
}

.nk-knob > div {
    position: relative;
    display: inline-block !important;
}

.knob {
    height: 100% !important;
    margin-top: 0 !important;
    font-size: 1.5rem !important;
}

.knob-half {
    height: 50% !important;
    font-size: 1.25rem !important;
}

[class*=knob] {
    text-align: center;
    pointer-events: none;
    width: 100% !important;
    font-weight: 400 !important;
    font-family: Roboto, sans-serif !important;
    color: #364a63 !important;
    left: 0;
    margin-left: 0 !important;
    border: none;
}
[class*=knob]:focus {
    outline: none;
}

.stats {
    padding: 0 0 1rem;
}

@media (min-width: 1540px) {
    .nk-content-sidebar .stats {
        padding: 1.25rem 1.5rem 1rem;
    }
}
.collapse-shown {
    display: inline-block !important;
}
.collapsed .collapse-shown {
    display: none !important;
}
.collapse-hidden {
    display: none !important;
}
.collapsed .collapse-hidden {
    display: inline-block !important;
}

.clipboard-init {
    cursor: pointer;
}
.clipboard-success .clipboard-init {
    color: #1bca9b;
}
.clipboard-text {
    font-size: 12px;
}
.clipboard-success .form-control:focus, .clipboard-success div.dataTables_wrapper div.dataTables_filter input:focus, div.dataTables_wrapper div.dataTables_filter .clipboard-success input:focus, .clipboard-success .dual-listbox .dual-listbox__search:focus, .dual-listbox .clipboard-success .dual-listbox__search:focus {
    border-color: #dbdfea;
    box-shadow: inset 0 1px 1px rgba(16, 25, 36, 0.075);
}

.nk-news-item {
    display: flex;
    align-items: center;
}
.nk-news-icon {
    width: 2rem;
    display: inline-flex;
    flex-shrink: 0;
}
.nk-news-icon .icon {
    font-size: 24px;
    color: #6576ff;
}
.is-theme .nk-news-icon .icon {
    color: #7f8dff;
}
.nk-news-icon img {
    width: 24px;
}
.nk-news-text {
    display: flex;
    align-items: center;
    max-width: calc(100% - 1.5rem);
}
.nk-news-text p {
    font-size: 13px;
    margin-bottom: 0;
    color: #526484;
    font-weight: 500;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
    width: calc(100% - 2rem);
}
.nk-news-text p span {
    color: #8094ae;
    font-weight: 400;
}
.is-dark .nk-news-text p {
    color: #90a1b8;
}
.is-dark .nk-news-text p span {
    color: rgba(128, 148, 174, 0.9);
}
.is-theme .nk-news-text p {
    color: #dde3fe;
}
.is-theme .nk-news-text p span {
    color: rgba(196, 206, 254, 0.8);
}
.nk-news-text .icon {
    color: #8094ae;
    margin-left: 0.25rem;
}
.is-dark .nk-news-text .icon {
    color: #8094ae;
}
.is-theme .nk-news-text .icon {
    color: #c4cefe;
}
.nk-news-item:hover .nk-news-text .icon {
    color: #6576ff;
}

.language-list li:not(:last-child) .language-item {
    border-bottom: 1px solid #ecf2ff;
}
.language-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #344357;
    transition: all 0.4s;
}
.language-item:hover {
    color: #3c4d62;
    background: #ebeef2;
}
.language-name {
    font-size: 12px;
}
.language-flag {
    width: 24px;
    margin-right: 12px;
}

.entry img, .entry .video, .entry .image-group {
    border-radius: 4px;
}
.entry img + p, .entry img + h2, .entry img + .h2, .entry img + h3, .entry img + .h3, .entry img + h4, .entry img + .h4, .entry img + h5, .entry img + .h5, .entry img + h6, .entry img + .h6, .entry img + ul, .entry img + ol, .entry .video + p, .entry .video + h2, .entry .video + .h2, .entry .video + h3, .entry .video + .h3, .entry .video + h4, .entry .video + .h4, .entry .video + h5, .entry .video + .h5, .entry .video + h6, .entry .video + .h6, .entry .video + ul, .entry .video + ol, .entry .image-group + p, .entry .image-group + h2, .entry .image-group + .h2, .entry .image-group + h3, .entry .image-group + .h3, .entry .image-group + h4, .entry .image-group + .h4, .entry .image-group + h5, .entry .image-group + .h5, .entry .image-group + h6, .entry .image-group + .h6, .entry .image-group + ul, .entry .image-group + ol {
    margin-top: 2rem;
}
.entry p + img {
    margin-top: 1rem;
}
.entry p + h2, .entry p + .h2, .entry p + h3, .entry p + .h3, .entry p + h4, .entry p + .h4, .entry p + h5, .entry p + .h5, .entry p + h6, .entry p + .h6, .entry p + .video, .entry p + .image-group {
    padding-top: 0.75rem;
}

p > span > .icon.ni {
    vertical-align: middle;
}

.btn-trigger + .dropdown-menu-end, .btn-trigger + .dropdown-menu-start {
    margin: -0.75rem 0 !important;
}

.btn-trigger + .dropdown-menu-end {
    margin-right: 16px !important;
}

.btn-trigger + .dropdown-menu-start {
    margin-left: 16px;
}

@media (max-width: 420px) {
    .btn-trigger + .dropdown-menu-xl, .btn-trigger + .dropdown-menu-lg, .btn-trigger + .dropdown-menu-md {
        margin-right: -16px !important;
        margin-top: 0 !important;
    }
}
.bq-note-item:not(:first-child) {
    margin-top: 1.75rem;
}
.bq-note-text {
    padding: 1rem 1.25rem;
    background: #f5f6fa;
    border-radius: 4px;
}
.bq-note-meta {
    font-size: 12px;
    color: #8094ae;
    margin-top: 0.75rem;
}
.bq-note-meta span > span {
    color: #526484;
}
.bq-note-meta .link {
    margin-left: 0.75rem;
}
.bq-note-sep {
    height: 0.25rem;
    display: block;
    visibility: hidden;
    padding: 0 0.25rem;
}
.bq-note-by {
    display: inline-block;
}

@media (min-width: 576px) {
    .bq-note-text {
        padding: 1.25rem 1.5rem;
    }
    .bq-note-sep {
        height: auto;
        display: inline-block;
        visibility: visible;
    }
}
@media (min-width: 1200px) and (max-width: 1359px) {
    .form-settings .col-lg-5, .form-settings .col-lg-7 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}
.ratio {
    border-radius: 4px;
}

.video {
    position: relative;
    overflow: hidden;
    border-radius: 4px;
}
.video:before {
    position: absolute;
    content: "";
    bottom: 0;
    right: 0;
    left: 0;
    height: 120px;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0));
}
.video-play {
    display: flex;
    align-items: center;
    position: absolute;
    left: 2rem;
    bottom: 2rem;
    color: #fff;
    font-size: 1.25rem;
}
.video-play:hover {
    color: #fff;
}
.video-play .icon {
    font-size: 2rem;
}

.nk-slider {
    position: relative;
}
.nk-slider-s1 {
    width: 290px;
    max-width: 100%;
}
.nk-slider-s1 .slick-dots {
    position: absolute;
    right: 1.25rem;
    top: 1.5rem;
    padding-top: 0;
}
.nk-slider-s2 .slider-arrows > div {
    padding: 0.5rem 0;
}

.project-head {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1.25rem;
    align-items: start;
}
.project-title {
    display: flex;
    align-items: center;
    margin-right: 0.75rem;
}
.project-title .user-avatar {
    margin-right: 1rem;
}
.project-title .title {
    font-size: 0.975rem;
}
.project-title .title:not(:last-child) {
    margin-bottom: 0.125rem;
}
.project-details {
    margin-bottom: 1rem;
}
.project-progress {
    margin-bottom: 1rem;
}
.project-progress-details {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}
.project-progress-task {
    display: flex;
    align-items: center;
    color: #8094ae;
}
.project-progress-task .icon {
    font-size: 1rem;
    line-height: 1.5rem;
    margin-right: 0.25rem;
}
.project-progress-percent {
    color: #526484;
    font-weight: 500;
}
.project-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.project-users {
    display: flex;
    align-items: center;
}
.project-list-progress {
    display: flex;
    align-items: center;
}
.project-list-progress .progress {
    width: 100px;
    margin-right: 0.5rem;
}

@media (min-width: 1540px) {
    .project-list-progress .progress {
        width: 140px;
        margin-right: 1rem;
    }
}
.team {
    position: relative;
}
.team-info {
    padding: 1rem 0 1.25rem;
}
.team-info li {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.9375rem;
    line-height: 1.75rem;
}
.team-info li span:first-child {
    color: #8094ae;
}
.team-info li span:last-child {
    color: #526484;
}
.team-status {
    position: absolute;
    left: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 20px;
    width: 20px;
    border-radius: 50%;
}
.team-options {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
}
.team-details {
    padding-top: 0.5rem;
    text-align: center;
    max-width: 200px;
    margin: 0 auto;
}
.team-statistics {
    display: flex;
    justify-content: space-around;
    text-align: center;
    padding: 1rem 0 1.5rem;
}
.team-statistics li span {
    display: block;
}
.team-statistics li span:first-child {
    font-size: 1.125rem;
    color: #1c2b46;
}
.team-statistics li span:last-child {
    font-size: 0.875rem;
    color: #8094ae;
}
.team-view {
    display: flex;
    justify-content: center;
    padding-bottom: 0.25rem;
}

.rating {
    display: flex;
    align-items: center;
    margin: 0 -0.125rem;
    color: #f4bd0e;
}
.rating > * {
    padding: 0 0.125rem;
    display: inline-flex;
}
.rating .icon {
    font-size: 1rem;
}
.rating-wrap {
    display: inline-flex;
    align-items: flex-start;
}
.rating-wrap .amount {
    line-height: 1.25rem;
}
.rating-wrap .rating + span, .rating-wrap span + .rating {
    margin-left: 0.75rem;
}
.rating-pill {
    padding: 0.35rem 0.875rem;
    border-radius: 3rem;
}

.rating-card-description .icon {
    color: #6576ff;
}
.rating-card-description li {
    color: #8094ae;
}
.rating-progress {
    display: flex;
    align-items: center;
}

.icon + span, span + .icon {
    margin-left: 0.25rem;
}

#overlay{
    position: fixed;
    top: 0;
    z-index: 100;
    width: 100%;
    height:100%;
    display: none;
    background: rgba(0,0,0,0.1);
}
.cv-spinner {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
.spinner {
    width: 40px;
    height: 40px;
    border: 4px #ddd solid;
    border-top: 4px #2e93e6 solid;
    border-radius: 50%;
    animation: sp-anime 0.8s infinite linear;
}
@keyframes sp-anime {
    100% {
        transform: rotate(360deg);
    }
}
.is-hide{
    display:none;
}



@media (min-width: 576px) {
    .plan-iv-actions {
        padding-top: 3.5rem;
    }
}
.invest-cc-dropdown {
    width: 100%;
}
.invest-cc-dropdown .dropdown-indicator:after {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    width: 56px;
    font-size: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #8094ae;
    opacity: 0.5;
}
.invest-cc-dropdown .dropdown-menu {
    margin-top: -78px;
}
.invest-cc-dropdown .dropdown-menu[x-placement=top-start] {
    margin-top: 0;
    margin-bottom: -78px;
}
.invest-cc-item:not(:last-child) {
    border-bottom: 1px solid #e5e9f2;
}
.invest-cc-choosen, .invest-cc-chosen {
    display: block;
    width: 100%;
    border: 1px solid #dbdfea;
    border-radius: 4px;
    background: #fff;
}
.invest-cc-opt {
    display: block;
}
.invest-pm-list {
    border: 1px solid #dbdfea;
    background: #fff;
    border-radius: 4px;
}
.invest-pm-item {
    position: relative;
}
.invest-pm-item:not(:last-child) {
    border-bottom: 1px solid #e5e9f2;
}
.invest-pm-label {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0;
    padding: 16px 20px 16px 60px;
    cursor: pointer;
}
.invest-pm-label:before, .invest-pm-label:after {
    position: absolute;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    height: 24px;
    width: 24px;
    border-radius: 50%;
}
.invest-pm-label:before {
    content: "";
    border: 2px solid #dbdfea;
}
.invest-pm-label:after {
    font-family: "Nioicon";
    content: "\e9be";
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: #0971fe;
    transition: opacity 0.3s;
    opacity: 0;
}
.invest-pm-label .pm-name {
    font-size: 14px;
    color: #364a63;
}
.invest-pm-label .pm-icon {
    display: inline-flex;
    font-size: 24px;
    color: #8094ae;
}
.invest-pm-control {
    position: absolute;
    top: 0;
    height: 1px;
    width: 1px;
    opacity: 0;
}
.invest-pm-control:checked ~ .buysell-pm-label {
    cursor: default;
}
.invest-pm-control:checked ~ .buysell-pm-label:after {
    opacity: 1;
}
.invest-field {
    margin-bottom: 1.5rem !important;
}
.invest-field .form-control-amount {
    padding: 1rem 4rem 1.25rem 1.5rem;
    height: 3rem;
    font-size: 1.25rem;
    color: #8094ae;
    border-color: #dbdfea;
}
.invest-field .form-control-amount ~ .form-range-slider {
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
}
.invest-field .form-control-amount ~ .form-range-slider.noUi-horizontal {
    height: 4px;
    border-radius: 0 0 4px 4px;
}
.invest-field .form-control-amount ~ .form-range-slider.noUi-target {
    background: rgba(219, 223, 234, 0.6);
}
.invest-field .form-control-amount ~ .form-range-slider .noUi-connects {
    border-radius: 0 0 4px 4px;
}

.invest-amount-group {
    display: flex;
    flex-wrap: wrap;
}
.invest-amount-item {
    position: relative;
    flex-grow: 1;
    width: 33.33%;
}
.invest-amount-label {
    cursor: pointer;
    border-radius: 4px;
    border: 1px solid #dbdfea;
    background: #fff;
    font-size: 14px;
    text-align: center;
    line-height: 1rem;
    padding: 1rem;
    width: 100%;
    margin-bottom: 0;
    transition: all 0.3s;
}
.invest-amount-control {
    position: absolute;
    opacity: 0;
    height: 1px;
    width: 1px;
}
.invest-amount-control:checked ~ .invest-amount-label {
    border-color: #0971fe;
    background-color: #0971fe;
    color: #fff;
}

@media (min-width: 576px) {
    .invest-amount-item {
        width: auto;
    }
}
.nk-ecwg .data {
    margin-top: 0.5rem;
}
.nk-ecwg .data:not(:last-child) {
    margin-bottom: 2rem;
}
.nk-ecwg .data-group {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.nk-ecwg .data-group .data {
    margin: 0;
    padding-right: 20px;
}
.nk-ecwg4 {
    display: flex;
    flex-direction: column;
}
.nk-ecwg4-ck {
    height: 180px;
    width: 180px;
    margin-right: 2.5rem;
}
.nk-ecwg4-legends {
    flex-grow: 1;
    padding: 1rem 0;
}
.nk-ecwg4-legends li {
    display: flex;
    justify-content: space-between;
    padding: 0.25rem 0;
}
.nk-ecwg4-legends .title {
    display: flex;
    align-items: center;
}
.nk-ecwg4-legends .title .dot {
    margin-right: 0.5rem;
}

.nk-iv-wg4 .lead-text {
    font-weight: 400;
}
.nk-iv-wg4-title {
    margin-bottom: 0.75rem;
}
.nk-iv-wg4-sub {
    padding: 1.25rem 1.5rem;
}
.nk-iv-wg4-sub:not(:last-child) {
    border-bottom: 1px solid #e5e9f2;
}
.card .nk-iv-wg4-sub:last-child {
    border-radius: 0 0 3px 3px;
}
.nk-iv-wg4-sub .btn, .nk-iv-wg4-sub .dual-listbox .dual-listbox__button, .dual-listbox .nk-iv-wg4-sub .dual-listbox__button {
    margin-top: 0.5rem;
    margin-bottom: 0.5rem;
}
.nk-iv-wg4-overview {
    display: flex;
    flex-wrap: wrap;
}
.nk-iv-wg4-overview li {
    width: 50%;
}
.nk-iv-wg4-list li {
    display: flex;
    justify-content: space-between;
}
.nk-iv-wg4-list li:not(:last-child) {
    margin-bottom: 0.25rem;
}

@media (max-width: 420px) {
    .nk-iv-wg4-overview li {
        width: 100%;
    }
}
.nk-iv-wg5 {
    display: inline-flex;
    flex-direction: column;
    margin-left: auto;
    margin-right: auto;
}
.nk-iv-wg5-head {
    margin-bottom: 1rem;
}
.nk-iv-wg5-title {
    margin-bottom: 0.25rem;
}
.nk-iv-wg5-subtitle {
    color: #8094ae;
}
.nk-iv-wg5-ck {
    margin-top: auto;
    position: relative;
    display: inline-block;
}
.nk-iv-wg5-ck-result {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
}
.nk-iv-wg5-ck-result .text-lead {
    font-size: 40px;
    font-weight: 300;
    color: #364a63;
    line-height: 1.3;
}
.nk-iv-wg5-ck-result .text-lead.sm {
    font-size: 30px;
}
.nk-iv-wg5-ck-result .text-sub {
    font-size: 12px;
    font-weight: 500;
    color: #8094ae;
}
.nk-iv-wg5-ck-minmax {
    display: flex;
    justify-content: space-between;
    color: #8094ae;
    font-size: 12px;
}

.featured-card {
    position: relative;
    overflow: hidden;
}
.featured-action {
    position: absolute;
    height: 77px;
    width: 77px;
    bottom: -1.5rem;
    right: -1.5rem;
    background: #f5f6fa;
    padding: 1rem 1.1875rem;
    border-radius: 50%;
    font-size: 1.25rem;
    color: #364a63;
}
.featured-action:hover {
    background-color: #0971fe;
    color: #fff;
}

.nk-coin-ovwg6 {
    display: flex;
}
.nk-coin-ovwg6-ck {
    flex-grow: 1;
    height: 165px;
}
.nk-coin-ovwg6-legends {
    flex-shrink: 0;
}
.nk-coin-ovwg6-legends li {
    display: flex;
    align-items: center;
    line-height: 1.25rem;
    font-size: 12px;
    padding: 0.25rem 0;
    color: #8094ae;
}
.nk-coin-ovwg6-legends li .dot {
    margin-right: 0.5rem;
}

.asterisk a {
    position: relative;
    color: #8094ae;
    display: inline-flex;
}
.asterisk a:hover, .asterisk a.active {
    color: #ffa353;
}
.asterisk a:hover .asterisk-on, .asterisk a.active .asterisk-on {
    opacity: 1;
}
.asterisk a.active:hover .asterisk-on {
    opacity: 0;
}
.asterisk-on {
    opacity: 0;
    position: absolute;
    left: 0;
    color: #ffa353;
    z-index: 1;
    transition: opacity 0.2s;
}