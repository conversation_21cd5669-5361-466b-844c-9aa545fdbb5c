/*!
 * Theme: Egyptian
 * Package: DashLite v3.2.3
 * Updated: 09.27.2023
 * Author: Soft<PERSON>
 * Author URI: http://themeforest.net/user/softnio
**/
.nk-sidebar.is-dark {
  background: #0f192a;
  border-right-color: #1c2f50;
}
.nk-sidebar.is-theme {
  background: #02274d;
  border-right-color: #03407f;
}
.is-dark .nk-sidebar-head {
  border-color: #1c2f50;
}
.is-theme .nk-sidebar-head {
  border-color: #03407f;
}
.nk-sidebar.is-dark .user-balance-alt, .nk-sidebar.is-theme .user-balance-alt {
  color: rgba(255, 255, 255, 0.8);
}

.nk-header {
  background: #f5f6fa;
  border-bottom-color: #e5e9f2;
}
.nk-header.is-dark:not([class*=bg-]) {
  background: #0f192a;
}
.nk-header.is-theme:not([class*=bg-]) {
  background: #02274d;
}

.is-dark .nk-menu-link {
  color: #8699b2;
}
.is-dark .nk-menu-icon {
  color: #9faec2;
}
.is-dark .nk-menu-badge {
  color: #5b97e0;
  background: #1c2f50;
}
.is-dark .nk-menu-sub .active > .nk-menu-link,
.is-dark .nk-menu-link:hover, .is-dark .active > .nk-menu-link,
.is-dark .nk-menu-link:hover .nk-menu-icon, .is-dark .nk-menu-item.active > .nk-menu-link .nk-menu-icon, .is-dark .nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon {
  color: #307bd8;
}
.is-dark .nk-news-icon .icon {
  color: #2263b3;
}
.is-dark .nk-news-text .icon {
  color: #8094ae;
}
.is-dark .nk-news-text p span {
  color: rgba(128, 148, 174, 0.9);
}
.is-dark .user-name, .is-dark .nk-news-text p {
  color: #90a1b8;
}
.is-dark .nk-quick-nav-icon {
  color: #9faec2;
}

.is-theme .nk-menu-link {
  color: #aac4e1;
}
.is-theme .nk-menu-icon {
  color: #c9d9ec;
}
.is-theme .nk-menu-badge {
  color: #86b2e8;
  background: #03407f;
}
.is-theme .nk-news-icon .icon {
  color: #4689dc;
}
.is-theme .nk-news-text .icon, .is-theme .nk-quick-nav-icon, .is-theme .overline-title {
  color: #b2c9e4;
}
.is-theme .nk-news-text p span, .is-theme .nk-menu-heading .overline-title {
  color: rgba(178, 201, 228, 0.8);
}
.is-theme .user-name, .is-theme .nk-news-text p {
  color: #c5d6eb;
}

.card-tools-nav li a:before,
.nav-tabs .nav-link:after,
.progress-bar, .dot-primary,
.alert-fill.alert-primary,
.badge-primary,
.icon-circle,
.noUi-connect,
.nk-msg-menu-item a:after,
.ui-shady .nk-msg-item.active:after, .ui-shady .nk-msg-item.current:after,
.ui-softy .nk-msg-item.active:after, .ui-softy .nk-msg-item.current:after {
  background: #2263b3;
}

.card-bordered.is-dark {
  border-color: #022c57;
}

.card.is-dark {
  background: #022c57;
}

.user-avatar, [class^=user-avatar]:not([class*=-group]) {
  background: #307bd8;
}

.nk-news-item:hover .nk-news-text .icon {
  color: #307bd8;
}

.nk-menu-link:hover,
.nk-menu-link:hover .nk-menu-icon,
.nk-menu-item.active > .nk-menu-link .nk-menu-icon,
.nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon,
.nk-menu-link:hover .count,
.nk-menu-sub .nk-menu-link:hover,
.nk-menu-sub .active > .nk-menu-link,
.nk-menu-sm .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:focus,
.nk-menu-main .nk-menu-item.active > .nk-menu-link,
.nk-menu-main .nk-menu-item.current-menu > .nk-menu-link,
.nk-menu-main .nk-menu-item:hover > .nk-menu-link,
.is-light .nk-menu-link:hover, .is-light .active > .nk-menu-link,
.active > .nk-menu-link,
.active > .nk-menu-link .count,
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.active .nav-link,
.nk-menu-footer .nk-menu-icon,
.nk-menu-footer .nk-menu-link:hover,
.nk-footer-copyright a:hover,
.page-link:hover,
.list-plain a:hover,
.link-check li a:hover,
.link-list a:hover,
.link-list-opt a:hover,
.link-list-plain a:hover,
.link-list-menu li.active > a,
.link-list-menu a.active,
.link-list-menu a:hover,
.link-list-menu li.active > a .icon,
.link-list-menu a.active .icon,
.link-list-menu a:hover .icon,
.link-list-menu li.active > a:after,
.link-list-menu a.active:after,
.link-list-menu a:hover:after,
.list-checked li:before,
.list-step li.list-step-current:before,
.accordion-s2 .accordion-head .title,
.accordion-s3 .accordion-head .title,
.bg-outline-primary,
.badge-dim.bg-primary,
.badge-dot.bg-primary,
.badge-dim.bg-outline-primary,
.alert-primary,
.form-clip,
.form-text-hint,
.search-submit:hover,
.nk-news-icon .icon,
.attach-item .icon,
.attach-download:hover span,
.nk-reply-meta-info .whom,
.nk-msg-tags li > span .icon,
.nk-msg-menu-item a:hover,
.nk-msg-menu-item.active a,
.user-balance,
.user-avatar[class*=-primary-dim],
.nk-order-ovwg-data.sell .amount,
.nk-order-ovwg-data.sell .title .icon,
.nk-wg-action-content p strong, a,
.dropzone .dz-message-text span,
.nk-switch-icon.active,
.link-list-plain a .icon,
.chat-upload-option a,
.is-unread .chat-context .status,
.add-opt:hover .sub-text, .add-opt:hover .icon,
.icon[class*=bg-primary-dim] {
  color: #2263b3;
}

a:hover {
  color: #1b5091;
}

.text-primary, .link-primary {
  color: #2263b3 !important;
}

.link-primary:hover, a.text-primary:hover, a.text-primary:focus, .chat-upload-option a:hover {
  color: #1b5091 !important;
}

.border-primary, .nk-kycfm-control:checked ~ .nk-kycfm-label {
  border-color: #2263b3 !important;
}

.bg-lighter {
  background-color: #f5f6fa !important;
}

.bg-primary {
  background-color: #2263b3 !important;
}

a.bg-primary:hover, a.bg-primary:focus, button.bg-primary:hover, button.bg-primary:focus {
  background-color: #1a4b88 !important;
}

.bg-primary-dim, .dual-listbox .dual-listbox__item:active, .dual-listbox .dual-listbox__item.dual-listbox__item--selected {
  background-color: #e9eff7 !important;
}

.text-primary-dim {
  color: #e9eff7 !important;
}

.alert-primary, .badge-dim.bg-outline-primary {
  background-color: #e9eff7;
  border-color: #a7c1e1;
}

.alert-pro.alert-primary, .bg-primary,
.form-control:focus,
.form-control.focus,
.custom-file-input:focus ~ .custom-file-label,
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #2263b3;
}

.nk-order-ovwg-data.sell, .bg-outline-primary {
  border-color: #a7c1e1;
}

.dropdown-menu-s1 {
  border-top-color: #2263b3;
}

.nk-iv-wg2-amount.ui-v2 {
  border-bottom-color: #2263b3;
}

.page-item.active .page-link,
.custom-control-input:checked ~ .custom-control-label::before,
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  background-color: #2263b3;
  border-color: #2263b3;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #7aa1d1;
}

.badge-dim.bg-primary {
  background-color: #e9eff7;
  border-color: #e9eff7;
}

.bg-primary.badge-dot {
  background: transparent !important;
}

.nk-error-head {
  background: -webkit-linear-gradient(#2263b3, #034589);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-switch .nav-link.active {
  background: #2263b3;
  color: #ffffff;
}

.icon-avatar {
  color: #2263b3;
  background-color: #dee8f4;
}

.is-theme .nk-quick-nav-icon:before {
  background-color: #011325;
}

.btn-primary {
  background-color: #2263b3;
  border-color: #2263b3;
}
.btn-primary:hover {
  background-color: #1f5aa2;
  border-color: #1d5599;
}
.btn-primary:focus {
  background-color: #1f5aa2;
  border-color: #1d5599;
}
.btn-primary.btn-dim {
  color: #2263b3;
  background-color: #e7eef7;
  border-color: #e7eef7;
}

.btn-dim.btn-outline-primary {
  color: #2263b3;
  background-color: #e7eef7;
  border-color: #a7c1e1;
}
.btn-dim.btn-outline-primary.btn-white {
  background-color: #ffffff;
}

.btn-outline-primary {
  border-color: #2263b3;
  color: #2263b3;
}
.btn-outline-primary:hover {
  background-color: #2263b3;
  border-color: #2263b3;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  background-color: #205ca6;
  border-color: #1f5aa2;
}

.btn-primary:focus,
.btn-outline-primary:focus,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary.btn-dim:focus,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active:focus,
.btn-dim.btn-outline-primary:focus,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(34, 99, 179, 0.2);
}

.btn-outline-light:focus, .btn-outline-light.focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(128, 148, 174, 0.1);
}

.btn-trigger:focus {
  box-shadow: none;
}

.form-control:focus, .form-control.focus,
.custom-control-input:focus ~ .custom-control-label::before,
.custom-file-input:focus ~ .custom-file-label,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  box-shadow: 0 0 0 3px rgba(34, 99, 179, 0.1);
}

.form-control-simple:focus {
  box-shadow: none;
}

.btn-primary.btn-dim:not(:disabled):not(.disabled):hover,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):hover,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle,
.btn-primary.disabled, .btn-primary:disabled {
  background-color: #2263b3;
  border-color: #2263b3;
}

.form-focus-none:focus {
  border-color: transparent;
  box-shadow: none;
}

@media (min-width: 992px) {
  .nk-menu-main > li > .nk-menu-link:before {
    background: #2263b3;
  }
  .is-theme .nk-menu-main > li > .nk-menu-link {
    color: #b2c9e4;
  }
  .is-theme .nk-menu-main > li > .nk-menu-link.nk-menu-toggle:after {
    color: rgba(178, 201, 228, 0.7);
  }
}
.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
  background-color: #dee8f4;
  color: #2263b3;
}

.datepicker table tr td.today:hover, .datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover, .datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:active, .datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active, .datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active, .datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active, .datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled, .datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled, .datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled], .datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled], .datepicker table tr td.today.disabled:hover[disabled] {
  background-color: #79a1d0;
}

.datepicker table tr td.active:active, .datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active, .datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td span:hover, .datepicker table tr td span.focused {
  background-color: #2263b3;
}

.code-tag {
  color: blue;
}

.alert-pro {
  background: #ffffff;
  color: #526484;
}

.nk-menu-badge {
  color: #6492ca;
  background-color: #e9eff7;
}

.is-theme .nk-sidebar-footer, .is-theme .nk-sidebar-profile-fixed {
  background: #02274d;
  border-color: #03407f;
}

.is-theme .nk-menu-footer .nk-menu-link, .is-theme .sub-text, .is-theme .lead-text span {
  color: #b2c9e4;
}

.nav-switch-s2 .nav-link:hover, .nav-switch-s2 .nav-link:focus {
  color: #526484;
}

.nav-switch-s2 .nav-link.active {
  color: #364a63;
}

.active .nk-ibx-menu-text,
.active .nk-ibx-menu-item .icon,
.nk-reply-form-nav li a:hover,
.nk-reply-form-input .toggle-opt:hover {
  color: #2263b3;
}

.nk-ibx-menu li.active {
  background: #edf3f9;
}

.nk-fmg-menu li.active {
  background: #edf3f9;
}

.nk-file-name .asterisk .icon, .nk-file-name-text a.title:hover,
.nk-file-link:hover .title, .active .nk-fmg-menu-item .icon, .active .nk-fmg-menu-text {
  color: #2263b3;
}

.nk-files-view-list .nk-file.selected {
  background-color: #e4ecf6;
}

.chat.is-me .chat-msg {
  background-color: #2263b3;
}

.kanban-add-task {
  color: #2263b3;
}
.kanban-add-task:hover {
  background: #2263b3;
  border-color: #2263b3;
}

.actions ul li a {
  border-color: #2263b3;
  background: #2263b3;
}

.nk-wizard-simple .steps ul li.done h5, .nk-wizard-simple .steps ul li.done .number, .nk-wizard-simple .steps ul li.current h5, .nk-wizard-simple .steps ul li.current .number {
  color: #2263b3;
}

.nk-wizard-simple .steps ul li:after {
  background: #2263b3;
}

.invoice-contact ul .icon, .invoice-desc .title, .invoice-bills .table th {
  color: #2263b3;
}

.product-gallery .slider-nav .slider-item.slick-current .thumb,
.custom-control-pro.no-control .custom-control-input:checked ~ .custom-control-label,
.custom-control-pro.no-control .custom-control-input:not(:disabled):active ~ .custom-control-label,
.custom-control.color-control .custom-control-label:before {
  border-color: #2263b3 !important;
}