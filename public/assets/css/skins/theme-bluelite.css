/*!
 * Theme: Blue Light
 * Package: DashLite v3.2.3
 * Updated: 09.27.2023
 * Author: Soft<PERSON>
 * Author URI: http://themeforest.net/user/softnio
**/
.nk-sidebar.is-dark {
  background: #0f192a;
  border-right-color: #1c2f50;
}
.nk-sidebar.is-theme {
  background: #29347a;
  border-right-color: #3644a0;
}
.is-dark .nk-sidebar-head {
  border-color: #1c2f50;
}
.is-theme .nk-sidebar-head {
  border-color: #3644a0;
}
.nk-sidebar.is-dark .user-balance-alt, .nk-sidebar.is-theme .user-balance-alt {
  color: rgba(255, 255, 255, 0.8);
}

.nk-header {
  background: #f5f6fa;
  border-bottom-color: #e5e9f2;
}
.nk-header.is-dark:not([class*=bg-]) {
  background: #0f192a;
}
.nk-header.is-theme:not([class*=bg-]) {
  background: #29347a;
}

.is-dark .nk-menu-link {
  color: #8699b2;
}
.is-dark .nk-menu-icon {
  color: #9faec2;
}
.is-dark .nk-menu-badge {
  color: #cbd1ff;
  background: #1c2f50;
}
.is-dark .nk-menu-sub .active > .nk-menu-link,
.is-dark .nk-menu-link:hover, .is-dark .active > .nk-menu-link,
.is-dark .nk-menu-link:hover .nk-menu-icon, .is-dark .nk-menu-item.active > .nk-menu-link .nk-menu-icon, .is-dark .nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon {
  color: #98a3ff;
}
.is-dark .nk-news-icon .icon {
  color: #6576ff;
}
.is-dark .nk-news-text .icon {
  color: #8094ae;
}
.is-dark .nk-news-text p span {
  color: rgba(128, 148, 174, 0.9);
}
.is-dark .user-name, .is-dark .nk-news-text p {
  color: #90a1b8;
}
.is-dark .nk-quick-nav-icon {
  color: #9faec2;
}

.is-theme .nk-menu-link {
  color: #bac6fe;
}
.is-theme .nk-menu-icon {
  color: #e2e7ff;
}
.is-theme .nk-menu-badge {
  color: #fefeff;
  background: #3644a0;
}
.is-theme .nk-news-icon .icon {
  color: #b2baff;
}
.is-theme .nk-news-text .icon, .is-theme .nk-quick-nav-icon, .is-theme .overline-title {
  color: #c4cefe;
}
.is-theme .nk-news-text p span, .is-theme .nk-menu-heading .overline-title {
  color: rgba(196, 206, 254, 0.8);
}
.is-theme .user-name, .is-theme .nk-news-text p {
  color: #dde3fe;
}

.card-tools-nav li a:before,
.nav-tabs .nav-link:after,
.progress-bar, .dot-primary,
.alert-fill.alert-primary,
.bg-primary,
.icon-circle,
.noUi-connect,
.nk-kycfm-label::after,
.buysell-pm-label:after,
.nk-msg-menu-item a:after,
.ui-shady .nk-msg-item.active:after, .ui-shady .nk-msg-item.current:after {
  background: #6576ff;
}

.card-bordered.is-dark {
  border-color: #2c3782;
}

.card.is-dark {
  background: #2c3782;
}

.is-dark .nk-wg7-title, .is-dark .nk-wg7-note {
  color: #c4cefe;
}

.user-avatar, [class^=user-avatar]:not([class*=-group]) {
  background: #98a3ff;
}

.nk-news-item:hover .nk-news-text .icon {
  color: #98a3ff;
}

.nk-menu-link:hover,
.nk-menu-link:hover .nk-menu-icon,
.nk-menu-item.active > .nk-menu-link .nk-menu-icon,
.nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon,
.nk-menu-link:hover .count,
.nk-menu-sub .nk-menu-link:hover,
.nk-menu-sub .active > .nk-menu-link,
.nk-menu-sm .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:hover,
.nk-menu-main .nk-menu-link:focus,
.nk-menu-main .nk-menu-item.active > .nk-menu-link,
.nk-menu-main .nk-menu-item.current-menu > .nk-menu-link,
.nk-menu-main .nk-menu-item:hover > .nk-menu-link,
.is-light .nk-menu-link:hover, .is-light .active > .nk-menu-link,
.active > .nk-menu-link,
.active > .nk-menu-link .count,
.nav-tabs .nav-link:focus,
.nav-tabs .nav-link.active,
.nav-tabs .nav-item.active .nav-link,
.nk-menu-footer .nk-menu-icon,
.nk-menu-footer .nk-menu-link:hover,
.nk-footer-copyright a:hover,
.page-link:hover,
.list-plain a:hover,
.link-check li a:hover,
.link-list a:hover,
.link-list-opt a:hover,
.link-list-plain a:hover,
.link-list-menu li.active > a,
.link-list-menu a.active,
.link-list-menu a:hover,
.link-list-menu li.active > a .icon,
.link-list-menu a.active .icon,
.link-list-menu a:hover .icon,
.link-list-menu li.active > a:after,
.link-list-menu a.active:after,
.link-list-menu a:hover:after,
.list-checked li:before,
.list-step li.list-step-current:before,
.accordion-s2 .accordion-head .title,
.accordion-s3 .accordion-head .title,
.bg-outline-primary,
.badge-dim.bg-primary,
.badge-dot.bg-primary,
.badge-dim.bg-outline-primary,
.alert-primary,
.form-clip,
.form-text-hint,
.search-submit:hover,
.nk-news-icon .icon,
.nk-refwg-name .title,
.nk-wg1-amount .amount,
.nk-wgw-add a .title,
.nk-wgw-actions a:hover span,
.nk-wgw-actions a:hover .icon,
.nk-wg4-note span,
.nk-kycfm-control:checked ~ .nk-kycfm-label .label-icon,
.wallet-item-add a:hover .wallet-icon,
.wallet-item-add a:hover .wallet-name,
.attach-item .icon,
.attach-download:hover span,
.nk-reply-meta-info .whom,
.nk-msg-tags li > span .icon,
.nk-msg-menu-item a:hover,
.nk-msg-menu-item.active a,
.user-balance,
.user-avatar[class*=-primary-dim],
.nk-order-ovwg-data.sell .amount,
.nk-order-ovwg-data.sell .title .icon,
.nk-wg-action-content p strong, a,
.dropzone .dz-message-text span,
.nk-switch-icon.active,
.link-list-plain a .icon,
.chat-upload-option a,
.is-unread .chat-context .status,
.add-opt:hover .sub-text, .add-opt:hover .icon,
.icon[class*=bg-primary-dim] {
  color: #6576ff;
}

a:hover {
  color: #3c52ff;
}

.text-primary, .link-primary {
  color: #6576ff !important;
}

.link-primary:hover, a.text-primary:hover, a.text-primary:focus, .chat-upload-option a:hover {
  color: #3c52ff !important;
}

.border-primary, .nk-kycfm-control:checked ~ .nk-kycfm-label {
  border-color: #6576ff !important;
}

.bg-lighter {
  background-color: #f5f6fa !important;
}

.bg-primary {
  background-color: #6576ff !important;
}

a.bg-primary:hover, a.bg-primary:focus, button.bg-primary:hover, button.bg-primary:focus {
  background-color: #3249ff !important;
}

.bg-primary-dim {
  background-color: #f0f1ff !important;
}

.text-primary-dim {
  color: #f0f1ff !important;
}

.alert-primary, .badge-dim.bg-outline-primary {
  background-color: #f0f1ff;
  border-color: #c1c8ff;
}

.alert-pro.alert-primary, .bg-primary,
.form-control:focus,
.form-control.focus,
.custom-file-input:focus ~ .custom-file-label,
.custom-control-input:focus:not(:checked) ~ .custom-control-label::before,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  border-color: #6576ff;
}

.nk-order-ovwg-data.sell, .bg-outline-primary {
  border-color: #c1c8ff;
}

.dropdown-menu-s1 {
  border-top-color: #6576ff;
}

.page-item.active .page-link,
.custom-control-input:checked ~ .custom-control-label::before,
.custom-control-input:not(:disabled):active ~ .custom-control-label::before {
  background-color: #6576ff;
  border-color: #6576ff;
}

.custom-checkbox .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-radio .custom-control-input:disabled:checked ~ .custom-control-label::before,
.custom-switch .custom-control-input:disabled:checked ~ .custom-control-label::before {
  background-color: #a3adff;
}

.badge-dim.bg-primary {
  background-color: #f0f1ff;
  border-color: #f0f1ff;
}

.bg-primary.badge-dot {
  background: transparent !important;
}

.nk-error-head {
  background: -webkit-linear-gradient(#6576ff, #3947a8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.nav-switch .nav-link.active {
  background: #6576ff;
  color: #ffffff;
}

.icon-avatar, .nk-iv-scheme-icon.is-running {
  color: #6576ff;
  background-color: #e8eaff;
}

.is-dark .nk-wgw-icon {
  background: #8491ff;
}

.is-dark .nk-wgw-balance .amount-sm, .is-dark .nk-wgw-actions a .icon, .is-dark .nk-wgw-more .btn-trigger {
  color: #c4cefe;
}

.is-dark .nk-wgw-actions a {
  color: #c4cefe;
}

.is-dark .nk-wgw-actions {
  border-color: rgba(196, 206, 254, 0.4);
}

.is-dark .nk-wgw-more .btn-trigger:before, .is-theme .nk-quick-nav-icon:before {
  background-color: #1f275c;
}

.btn-primary {
  background-color: #6576ff;
  border-color: #6576ff;
}
.btn-primary:hover {
  background-color: #5164ff;
  border-color: #465bff;
}
.btn-primary:focus {
  background-color: #5164ff;
  border-color: #465bff;
}
.btn-primary.btn-dim {
  color: #6576ff;
  background-color: #eef0ff;
  border-color: #eef0ff;
}

.btn-dim.btn-outline-primary {
  color: #6576ff;
  background-color: #eef0ff;
  border-color: #c1c8ff;
}
.btn-dim.btn-outline-primary.btn-white {
  background-color: #ffffff;
}

.btn-outline-primary {
  border-color: #6576ff;
  color: #6576ff;
}
.btn-outline-primary:hover {
  background-color: #6576ff;
  border-color: #6576ff;
}

.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled).active,
.show > .btn-primary.dropdown-toggle {
  background-color: #5668ff;
  border-color: #5164ff;
}

.btn-primary:focus,
.btn-outline-primary:focus,
.btn-primary:not(:disabled):not(.disabled):active,
.btn-primary:not(:disabled):not(.disabled):active:focus,
.btn-primary.btn-dim:focus,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active,
.btn-primary.btn-dim:not(:disabled):not(.disabled):active:focus,
.btn-dim.btn-outline-primary:focus,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.show > .btn-primary.dropdown-toggle:focus,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-primary.dropdown-toggle,
.show > .btn-outline-primary.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(101, 118, 255, 0.2);
}

.btn-outline-light:focus, .btn-outline-light.focus,
.btn-outline-light:not(:disabled):not(.disabled):active:focus,
.btn-outline-light:not(:disabled):not(.disabled).active:focus,
.show > .btn-outline-light.dropdown-toggle:focus {
  box-shadow: 0 0 0 0.2rem rgba(128, 148, 174, 0.1);
}

.btn-trigger:focus {
  box-shadow: none;
}

.form-control:focus, .form-control.focus,
.custom-control-input:focus ~ .custom-control-label::before,
.custom-file-input:focus ~ .custom-file-label,
.select2-container--default .select2-selection--single:focus,
.select2-container--default.select2-container--focus .select2-selection--multiple {
  box-shadow: 0 0 0 3px rgba(101, 118, 255, 0.1);
}

.form-control-simple:focus {
  box-shadow: none;
}

.btn-primary.btn-dim:not(:disabled):not(.disabled):hover,
.btn-dim.btn-outline-primary:not(:disabled):not(.disabled):hover,
.btn-outline-primary:not(:disabled):not(.disabled):active,
.btn-outline-primary:not(:disabled):not(.disabled).active,
.show > .btn-outline-primary.dropdown-toggle,
.btn-primary.disabled, .btn-primary:disabled {
  background-color: #6576ff;
  border-color: #6576ff;
}

.form-focus-none:focus {
  border-color: transparent;
  box-shadow: none;
}

@media (min-width: 992px) {
  .nk-menu-main > li > .nk-menu-link:before {
    background: #6576ff;
  }
  .is-theme .nk-menu-main > li > .nk-menu-link {
    color: #c4cefe;
  }
  .is-theme .nk-menu-main > li > .nk-menu-link.nk-menu-toggle:after {
    color: rgba(196, 206, 254, 0.7);
  }
}
.datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover {
  background-color: #e8eaff;
  color: #6576ff;
}

.datepicker table tr td.today:hover, .datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover, .datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:active, .datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active, .datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active, .datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active, .datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled, .datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled, .datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled], .datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled], .datepicker table tr td.today.disabled:hover[disabled] {
  background-color: #7990fd;
}

.datepicker table tr td.active:active, .datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active, .datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active, .datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active, .datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td span:hover, .datepicker table tr td span.focused {
  background-color: #6576ff;
}

.code-tag {
  color: blue;
}

.alert-pro {
  background: #ffffff;
  color: #526484;
}

.nk-menu-badge {
  color: #939fff;
  background-color: #f0f1ff;
}

.is-theme .nk-sidebar-footer, .is-theme .nk-sidebar-profile-fixed {
  background: #29347a;
  border-color: #3644a0;
}

.is-theme .wallet-name, .is-theme .wallet-icon, .is-theme .wallet-balance span, .is-theme .nk-menu-footer .nk-menu-link, .is-theme .sub-text, .is-theme .lead-text span {
  color: #c4cefe;
}

@media (min-width: 1200px) {
  .nk-sidebar.is-theme .nk-menu > li:hover > a, .nk-sidebar.is-theme .nk-menu > li.active > a, .nk-sidebar.is-theme .nk-menu > li.current-menu > a, .nk-sidebar.is-theme .nk-menu > li > a:hover, .is-theme .wallet-item a:before {
    background: #344199;
  }
}
.nav-switch-s2 .nav-link:hover, .nav-switch-s2 .nav-link:focus {
  color: #526484;
}

.nav-switch-s2 .nav-link.active {
  color: #364a63;
}

.active .nk-ibx-menu-text,
.active .nk-ibx-menu-item .icon,
.nk-reply-form-nav li a:hover,
.nk-reply-form-input .toggle-opt:hover {
  color: #6576ff;
}

.nk-ibx-menu li.active {
  background: #f3f4ff;
}

.nk-fmg-menu li.active {
  background: #f3f4ff;
}

.nk-file-name .asterisk .icon, .nk-file-name-text a.title:hover,
.nk-file-link:hover .title, .active .nk-fmg-menu-item .icon, .active .nk-fmg-menu-text {
  color: #6576ff;
}

.nk-files-view-list .nk-file.selected {
  background-color: #eff1ff;
}

.chat.is-me .chat-msg {
  background-color: #6576ff;
}

.kanban-add-task {
  color: #6576ff;
}
.kanban-add-task:hover {
  background: #6576ff;
  border-color: #6576ff;
}

.actions ul li a {
  border-color: #6576ff;
  background: #6576ff;
}

.nk-wizard-simple .steps ul li.done h5, .nk-wizard-simple .steps ul li.done .number, .nk-wizard-simple .steps ul li.current h5, .nk-wizard-simple .steps ul li.current .number {
  color: #6576ff;
}

.nk-wizard-simple .steps ul li:after {
  background: #6576ff;
}

.invoice-contact ul .icon, .invoice-desc .title, .invoice-bills .table th {
  color: #6576ff;
}

.product-gallery .slider-nav .slider-item.slick-current .thumb,
.custom-control-pro.no-control .custom-control-input:checked ~ .custom-control-label,
.custom-control-pro.no-control .custom-control-input:not(:disabled):active ~ .custom-control-label,
.custom-control.color-control .custom-control-label:before {
  border-color: #6576ff !important;
}

.has-sub.active > .nk-menu-toggle:after {
  color: #6576ff;
}

.nk-sidebar .nk-menu > li .nk-menu-sub .nk-menu-link:before {
  border-color: #eceefa;
  background: #dee2fb;
}
.nk-sidebar .nk-menu > li.active > .nk-menu-link {
  background: #dee2fb;
}
.nk-sidebar .nk-menu > li > .nk-menu-link:hover {
  background: #dee2fb;
}
.nk-sidebar .nk-menu-sub {
  background: #eceefa;
}
.nk-sidebar .nk-menu-sub:before {
  background: #dee2fb;
}
.nk-sidebar .nk-menu-sub .active > .nk-menu-link:before {
  background: #6576ff !important;
}
.nk-sidebar.is-dark .nk-menu > li .nk-menu-sub .nk-menu-link:before {
  border-color: #141f37;
  background: #1d284c;
}
.nk-sidebar.is-dark .nk-menu > li.active > .nk-menu-link {
  background: #1d284c;
}
.nk-sidebar.is-dark .nk-menu > li > .nk-menu-link:hover {
  background: #1d284c;
}
.nk-sidebar.is-dark .nk-menu-sub {
  background: #141f37;
}
.nk-sidebar.is-dark .nk-menu-sub:before {
  background: #1d284c;
}
.nk-sidebar.is-dark .nk-menu-sub .active > .nk-menu-link:before {
  background: #ffffff !important;
}
.nk-sidebar.is-dark .nk-menu .nk-menu-link:hover .nk-menu-icon, .nk-sidebar.is-dark .nk-menu .nk-menu-item.active > .nk-menu-link .nk-menu-icon, .nk-sidebar.is-dark .nk-menu .nk-menu-item.current-menu > .nk-menu-link .nk-menu-icon,
.nk-sidebar.is-dark .nk-menu .nk-menu-link:hover, .nk-sidebar.is-dark .nk-menu .active > .nk-menu-link {
  color: #ffffff;
}
.nk-sidebar.is-theme .nk-menu > li .nk-menu-sub .nk-menu-link:before {
  border-color: #2d3882;
  background: #333f8f;
}
.nk-sidebar.is-theme .nk-menu > li.active > .nk-menu-link {
  background: #333f8f;
}
.nk-sidebar.is-theme .nk-menu > li > .nk-menu-link:hover {
  background: #333f8f;
}
.nk-sidebar.is-theme .nk-menu-sub {
  background: #2d3882;
}
.nk-sidebar.is-theme .nk-menu-sub:before {
  background: #333f8f;
}
.nk-sidebar.is-theme .nk-menu-sub .active > .nk-menu-link:before {
  background: #ffffff !important;
}