!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).dragula=e()}}((function(){return function e(t,n,r){function o(u,c){if(!n[u]){if(!t[u]){var a="function"==typeof require&&require;if(!c&&a)return a(u,!0);if(i)return i(u,!0);var l=new Error("Cannot find module '"+u+"'");throw l.code="MODULE_NOT_FOUND",l}var f=n[u]={exports:{}};t[u][0].call(f.exports,(function(e){return o(t[u][1][e]||e)}),f,f.exports,e,t,n,r)}return n[u].exports}for(var i="function"==typeof require&&require,u=0;u<r.length;u++)o(r[u]);return o}({1:[function(e,t,n){"use strict";var r={};function o(e){var t=r[e];return t?t.lastIndex=0:r[e]=t=new RegExp("(?:^|\\s)"+e+"(?:\\s|$)","g"),t}t.exports={add:function(e,t){var n=e.className;n.length?o(t).test(n)||(e.className+=" "+t):e.className=t},rm:function(e,t){e.className=e.className.replace(o(t)," ").trim()}}},{}],2:[function(e,t,n){(function(n){"use strict";var r=e("contra/emitter"),o=e("crossvent"),i=e("./classes"),u=document,c=u.documentElement;function a(e,t,r,i){n.navigator.pointerEnabled?o[t](e,{mouseup:"pointerup",mousedown:"pointerdown",mousemove:"pointermove"}[r],i):n.navigator.msPointerEnabled?o[t](e,{mouseup:"MSPointerUp",mousedown:"MSPointerDown",mousemove:"MSPointerMove"}[r],i):(o[t](e,{mouseup:"touchend",mousedown:"touchstart",mousemove:"touchmove"}[r],i),o[t](e,r,i))}function l(e){if(void 0!==e.touches)return e.touches.length;if(void 0!==e.which&&0!==e.which)return e.which;if(void 0!==e.buttons)return e.buttons;var t=e.button;return void 0!==t?1&t?1:2&t?3:4&t?2:0:void 0}function f(e){var t=e.getBoundingClientRect();return{left:t.left+s("scrollLeft","pageXOffset"),top:t.top+s("scrollTop","pageYOffset")}}function s(e,t){return void 0!==n[t]?n[t]:c.clientHeight?c[e]:u.body[e]}function d(e,t,n){var r,o=(e=e||{}).className||"";return e.className+=" gu-hide",r=u.elementFromPoint(t,n),e.className=o,r}function v(){return!1}function m(){return!0}function p(e){return e.width||e.right-e.left}function h(e){return e.height||e.bottom-e.top}function g(e){return e.parentNode===u?null:e.parentNode}function y(e){return"INPUT"===e.tagName||"TEXTAREA"===e.tagName||"SELECT"===e.tagName||function e(t){if(!t)return!1;if("false"===t.contentEditable)return!1;if("true"===t.contentEditable)return!0;return e(g(t))}(e)}function w(e){return e.nextElementSibling||function(){var t=e;do{t=t.nextSibling}while(t&&1!==t.nodeType);return t}()}function b(e,t){var n=function(e){return e.targetTouches&&e.targetTouches.length?e.targetTouches[0]:e.changedTouches&&e.changedTouches.length?e.changedTouches[0]:e}(t),r={pageX:"clientX",pageY:"clientY"};return e in r&&!(e in n)&&r[e]in n&&(e=r[e]),n[e]}t.exports=function(e,t){var n,s,T,E,x,S,C,O,I,N,_,X=arguments.length;1===X&&!1===Array.isArray(e)&&(t=e,e=[]);var L,Y=null,A=t||{};void 0===A.moves&&(A.moves=m),void 0===A.accepts&&(A.accepts=m),void 0===A.invalid&&(A.invalid=V),void 0===A.containers&&(A.containers=e||[]),void 0===A.isContainer&&(A.isContainer=v),void 0===A.copy&&(A.copy=!1),void 0===A.copySortSource&&(A.copySortSource=!1),void 0===A.revertOnSpill&&(A.revertOnSpill=!1),void 0===A.removeOnSpill&&(A.removeOnSpill=!1),void 0===A.direction&&(A.direction="vertical"),void 0===A.ignoreInputTextSelection&&(A.ignoreInputTextSelection=!0),void 0===A.mirrorContainer&&(A.mirrorContainer=u.body);var B=r({containers:A.containers,start:z,end:$,cancel:Z,remove:W,destroy:k,canMove:K,dragging:!1});return!0===A.removeOnSpill&&B.on("over",oe).on("out",ie),D(),B;function P(e){return-1!==B.containers.indexOf(e)||A.isContainer(e)}function D(e){var t=e?"remove":"add";a(c,t,"mousedown",R),a(c,t,"mouseup",J)}function F(e){a(c,e?"remove":"add","mousemove",q)}function M(e){var t=e?"remove":"add";o[t](c,"selectstart",j),o[t](c,"click",j)}function k(){D(!0),J({})}function j(e){L&&e.preventDefault()}function R(e){if(S=e.clientX,C=e.clientY,!(1!==l(e)||e.metaKey||e.ctrlKey)){var t=e.target,n=U(t);n&&(L=n,F(),"mousedown"===e.type&&(y(t)?t.focus():e.preventDefault()))}}function q(e){if(L)if(0!==l(e)){if(!(void 0!==e.clientX&&Math.abs(e.clientX-S)<=(A.slideFactorX||0)&&void 0!==e.clientY&&Math.abs(e.clientY-C)<=(A.slideFactorY||0))){if(A.ignoreInputTextSelection){var t=b("clientX",e)||0,n=b("clientY",e)||0;if(y(u.elementFromPoint(t,n)))return}var r=L;F(!0),M(),$(),H(r);var o=f(T);E=b("pageX",e)-o.left,x=b("pageY",e)-o.top,i.add(N||T,"gu-transit"),ue(),re(e)}}else J({})}function U(e){if(!(B.dragging&&n||P(e))){for(var t=e;g(e)&&!1===P(g(e));){if(A.invalid(e,t))return;if(!(e=g(e)))return}var r=g(e);if(r)if(!A.invalid(e,t))if(A.moves(e,r,t,w(e)))return{item:e,source:r}}}function K(e){return!!U(e)}function z(e){var t=U(e);t&&H(t)}function H(e){fe(e.item,e.source)&&(N=e.item.cloneNode(!0),B.emit("cloned",N,e.item,"copy")),s=e.source,T=e.item,O=I=w(e.item),B.dragging=!0,B.emit("drag",T,s)}function V(){return!1}function $(){if(B.dragging){var e=N||T;Q(e,g(e))}}function G(){L=!1,F(!0),M(!0)}function J(e){if(G(),B.dragging){var t=N||T,r=b("clientX",e)||0,o=b("clientY",e)||0,i=ne(d(n,r,o),r,o);i&&(N&&A.copySortSource||!N||i!==s)?Q(t,i):A.removeOnSpill?W():Z()}}function Q(e,t){var n=g(e);N&&A.copySortSource&&t===s&&n.removeChild(T),te(t)?B.emit("cancel",e,s,s):B.emit("drop",e,t,s,I),ee()}function W(){if(B.dragging){var e=N||T,t=g(e);t&&t.removeChild(e),B.emit(N?"cancel":"remove",e,t,s),ee()}}function Z(e){if(B.dragging){var t=arguments.length>0?e:A.revertOnSpill,n=N||T,r=g(n),o=te(r);!1===o&&t&&(N?r&&r.removeChild(N):s.insertBefore(n,O)),o||t?B.emit("cancel",n,s,s):B.emit("drop",n,r,s,I),ee()}}function ee(){var e=N||T;G(),ce(),e&&i.rm(e,"gu-transit"),_&&clearTimeout(_),B.dragging=!1,Y&&B.emit("out",e,Y,s),B.emit("dragend",e),s=T=N=O=I=_=Y=null}function te(e,t){var r;return r=void 0!==t?t:n?I:w(N||T),e===s&&r===O}function ne(e,t,n){for(var r=e;r&&!o();)r=g(r);return r;function o(){if(!1===P(r))return!1;var o=ae(r,e),i=le(r,o,t,n);return!!te(r,i)||A.accepts(T,r,s,i)}}function re(e){if(n){e.preventDefault();var t=b("clientX",e)||0,r=b("clientY",e)||0,o=t-E,i=r-x;n.style.left=o+"px",n.style.top=i+"px";var u=N||T,c=d(n,t,r),a=ne(c,t,r),l=null!==a&&a!==Y;(l||null===a)&&(Y&&p("out"),Y=a,l&&p("over"));var f=g(u);if(a!==s||!N||A.copySortSource){var v,m=ae(a,c);if(null!==m)v=le(a,m,t,r);else{if(!0!==A.revertOnSpill||N)return void(N&&f&&f.removeChild(u));v=O,a=s}(null===v&&l||v!==u&&v!==w(u))&&(I=v,a.insertBefore(u,v),B.emit("shadow",u,a,s))}else f&&f.removeChild(u)}function p(e){B.emit(e,u,Y,s)}}function oe(e){i.rm(e,"gu-hide")}function ie(e){B.dragging&&i.add(e,"gu-hide")}function ue(){if(!n){var e=T.getBoundingClientRect();(n=T.cloneNode(!0)).style.width=p(e)+"px",n.style.height=h(e)+"px",i.rm(n,"gu-transit"),i.add(n,"gu-mirror"),A.mirrorContainer.appendChild(n),a(c,"add","mousemove",re),i.add(A.mirrorContainer,"gu-unselectable"),B.emit("cloned",n,T,"mirror")}}function ce(){n&&(i.rm(A.mirrorContainer,"gu-unselectable"),a(c,"remove","mousemove",re),g(n).removeChild(n),n=null)}function ae(e,t){for(var n=t;n!==e&&g(n)!==e;)n=g(n);return n===c?null:n}function le(e,t,n,r){var o="horizontal"===A.direction;return t!==e?function(){var e=t.getBoundingClientRect();if(o)return i(n>e.left+p(e)/2);return i(r>e.top+h(e)/2)}():function(){var t,i,u,c=e.children.length;for(t=0;t<c;t++){if(i=e.children[t],u=i.getBoundingClientRect(),o&&u.left+u.width/2>n)return i;if(!o&&u.top+u.height/2>r)return i}return null}();function i(e){return e?w(t):t}}function fe(e,t){return"boolean"==typeof A.copy?A.copy:A.copy(e,t)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./classes":1,"contra/emitter":5,crossvent:6}],3:[function(e,t,n){t.exports=function(e,t){return Array.prototype.slice.call(e,t)}},{}],4:[function(e,t,n){"use strict";var r=e("ticky");t.exports=function(e,t,n){e&&r((function(){e.apply(n||null,t||[])}))}},{ticky:10}],5:[function(e,t,n){"use strict";var r=e("atoa"),o=e("./debounce");t.exports=function(e,t){var n=t||{},i={};return void 0===e&&(e={}),e.on=function(t,n){return i[t]?i[t].push(n):i[t]=[n],e},e.once=function(t,n){return n._once=!0,e.on(t,n),e},e.off=function(t,n){var r=arguments.length;if(1===r)delete i[t];else if(0===r)i={};else{var o=i[t];if(!o)return e;o.splice(o.indexOf(n),1)}return e},e.emit=function(){var t=r(arguments);return e.emitterSnapshot(t.shift()).apply(this,t)},e.emitterSnapshot=function(t){var u=(i[t]||[]).slice(0);return function(){var i=r(arguments),c=this||e;if("error"===t&&!1!==n.throws&&!u.length)throw 1===i.length?i[0]:i;return u.forEach((function(r){n.async?o(r,i,c):r.apply(c,i),r._once&&e.off(t,r)})),e}},e}},{"./debounce":4,atoa:3}],6:[function(e,t,n){(function(n){"use strict";var r=e("custom-event"),o=e("./eventmap"),i=n.document,u=function(e,t,n,r){return e.addEventListener(t,n,r)},c=function(e,t,n,r){return e.removeEventListener(t,n,r)},a=[];function l(e,t,n){var r=function(e,t,n){var r,o;for(r=0;r<a.length;r++)if((o=a[r]).element===e&&o.type===t&&o.fn===n)return r}(e,t,n);if(r){var o=a[r].wrapper;return a.splice(r,1),o}}n.addEventListener||(u=function(e,t,r){return e.attachEvent("on"+t,function(e,t,r){var o=l(e,t,r)||function(e,t,r){return function(t){var o=t||n.event;o.target=o.target||o.srcElement,o.preventDefault=o.preventDefault||function(){o.returnValue=!1},o.stopPropagation=o.stopPropagation||function(){o.cancelBubble=!0},o.which=o.which||o.keyCode,r.call(e,o)}}(e,0,r);return a.push({wrapper:o,element:e,type:t,fn:r}),o}(e,t,r))},c=function(e,t,n){var r=l(e,t,n);if(r)return e.detachEvent("on"+t,r)}),t.exports={add:u,remove:c,fabricate:function(e,t,n){var u=-1===o.indexOf(t)?new r(t,{detail:n}):function(){var e;i.createEvent?(e=i.createEvent("Event")).initEvent(t,!0,!0):i.createEventObject&&(e=i.createEventObject());return e}();e.dispatchEvent?e.dispatchEvent(u):e.fireEvent("on"+t,u)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./eventmap":7,"custom-event":8}],7:[function(e,t,n){(function(e){"use strict";var n=[],r="",o=/^on/;for(r in e)o.test(r)&&n.push(r.slice(2));t.exports=n}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],8:[function(e,t,n){(function(e){var n=e.CustomEvent;t.exports=function(){try{var e=new n("cat",{detail:{foo:"bar"}});return"cat"===e.type&&"bar"===e.detail.foo}catch(e){}return!1}()?n:"undefined"!=typeof document&&"function"==typeof document.createEvent?function(e,t){var n=document.createEvent("CustomEvent");return t?n.initCustomEvent(e,t.bubbles,t.cancelable,t.detail):n.initCustomEvent(e,!1,!1,void 0),n}:function(e,t){var n=document.createEventObject();return n.type=e,t?(n.bubbles=Boolean(t.bubbles),n.cancelable=Boolean(t.cancelable),n.detail=t.detail):(n.bubbles=!1,n.cancelable=!1,n.detail=void 0),n}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],9:[function(e,t,n){var r,o,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}function a(e){if(r===setTimeout)return setTimeout(e,0);if((r===u||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}!function(){try{r="function"==typeof setTimeout?setTimeout:u}catch(e){r=u}try{o="function"==typeof clearTimeout?clearTimeout:c}catch(e){o=c}}();var l,f=[],s=!1,d=-1;function v(){s&&l&&(s=!1,l.length?f=l.concat(f):d=-1,f.length&&m())}function m(){if(!s){var e=a(v);s=!0;for(var t=f.length;t;){for(l=f,f=[];++d<t;)l&&l[d].run();d=-1,t=f.length}l=null,s=!1,function(e){if(o===clearTimeout)return clearTimeout(e);if((o===c||!o)&&clearTimeout)return o=clearTimeout,clearTimeout(e);try{o(e)}catch(t){try{return o.call(null,e)}catch(t){return o.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function h(){}i.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];f.push(new p(e,t)),1!==f.length||s||a(m)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(e){return[]},i.binding=function(e){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],10:[function(e,t,n){(function(e){var n;n="function"==typeof e?function(t){e(t)}:function(e){setTimeout(e,0)},t.exports=n}).call(this,e("timers").setImmediate)},{timers:11}],11:[function(e,t,n){(function(t,r){var o=e("process/browser.js").nextTick,i=Function.prototype.apply,u=Array.prototype.slice,c={},a=0;function l(e,t){this._id=e,this._clearFn=t}n.setTimeout=function(){return new l(i.call(setTimeout,window,arguments),clearTimeout)},n.setInterval=function(){return new l(i.call(setInterval,window,arguments),clearInterval)},n.clearTimeout=n.clearInterval=function(e){e.close()},l.prototype.unref=l.prototype.ref=function(){},l.prototype.close=function(){this._clearFn.call(window,this._id)},n.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},n.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},n._unrefActive=n.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout((function(){e._onTimeout&&e._onTimeout()}),t))},n.setImmediate="function"==typeof t?t:function(e){var t=a++,r=!(arguments.length<2)&&u.call(arguments,1);return c[t]=!0,o((function(){c[t]&&(r?e.apply(null,r):e.call(null),n.clearImmediate(t))})),t},n.clearImmediate="function"==typeof r?r:function(e){delete c[e]}}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":9,timers:11}]},{},[2])(2)}));