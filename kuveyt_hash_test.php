<?php

/**
 * KuveytTurk POS Hash Test
 * 
 * Bu script, KuveytTurk POS için hash hesaplamasını test eder.
 */

echo "=== KuveytTurk POS Hash Test ===\n\n";

// Test 1: Hash Calculation
echo "Test 1: Hash Calculation\n";
echo "-------------------------\n";

function calculateKuveytPosHash(array $data): string
{
    // KuveytTurk hash formatı: MerchantId + MerchantOrderId + Amount + OkUrl + FailUrl + UserName + Password
    $hashString = $data['MerchantId'] . 
                 $data['MerchantOrderId'] . 
                 $data['Amount'] . 
                 '' . // OkUrl (boş)
                 '' . // FailUrl (boş)
                 $data['UserName'] . 
                 $data['Password'];
    
    // SHA-1 hash hesapla ve Base64 encode et
    $hash = base64_encode(sha1($hashString, true));
    
    echo "Hash String: $hashString\n";
    echo "SHA-1 (binary): " . bin2hex(sha1($hashString, true)) . "\n";
    echo "Base64 Hash: $hash\n";
    
    return $hash;
}

// Test verileri
$testData = [
    'MerchantId' => '588693',
    'MerchantOrderId' => 'P598',
    'Amount' => '1000',
    'UserName' => 'kwebservis',
    'Password' => '3GgejhjaHCSX55k'
];

echo "Test Verileri:\n";
foreach ($testData as $key => $value) {
    echo "  $key: $value\n";
}
echo "\n";

$hash = calculateKuveytPosHash($testData);
echo "\n✓ Hash hesaplandı: $hash\n";
echo "✓ Hash uzunluğu: " . strlen($hash) . " karakter\n\n";

// Test 2: XML Builder with Hash
echo "Test 2: XML Builder with Hash\n";
echo "------------------------------\n";

function buildKuveytPosProvisionXml(array $data): string
{
    $hashData = calculateKuveytPosHash($data);
    
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>1.0.0</APIVersion>';
    $xml .= '<HashData>' . htmlspecialchars($hashData) . '</HashData>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<CardNumber></CardNumber>';
    $xml .= '<CardExpireDateYear></CardExpireDateYear>';
    $xml .= '<CardExpireDateMonth></CardExpireDateMonth>';
    $xml .= '<CardCVV2></CardCVV2>';
    $xml .= '<CardHolderName></CardHolderName>';
    $xml .= '<CardType></CardType>';
    $xml .= '<BatchID>0</BatchID>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<DisplayAmount>' . htmlspecialchars($data['Amount']) . '</DisplayAmount>';
    $xml .= '<CurrencyCode>0949</CurrencyCode>';
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>';
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

$fullTestData = array_merge($testData, [
    'CustomerId' => '98267952',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'BusinessKey' => '202508159555000000000014567'
]);

$xmlRequest = buildKuveytPosProvisionXml($fullTestData);
echo "✓ XML Request oluşturuldu (" . strlen($xmlRequest) . " karakter)\n";

// XML'de HashData'nın doğru olup olmadığını kontrol et
if (strpos($xmlRequest, '<HashData></HashData>') !== false) {
    echo "✗ HashData hala boş!\n";
} else {
    echo "✓ HashData dolu\n";
}

// Test 3: Base64 Validation
echo "\nTest 3: Base64 Validation\n";
echo "--------------------------\n";

function isValidBase64($string) {
    return base64_encode(base64_decode($string, true)) === $string;
}

if (isValidBase64($hash)) {
    echo "✓ Hash geçerli Base64 formatında\n";
} else {
    echo "✗ Hash geçersiz Base64 formatında\n";
}

// Test 4: Different Hash Algorithms
echo "\nTest 4: Different Hash Algorithms\n";
echo "----------------------------------\n";

$hashString = $testData['MerchantId'] . $testData['MerchantOrderId'] . $testData['Amount'] . $testData['UserName'] . $testData['Password'];

echo "MD5: " . md5($hashString) . "\n";
echo "SHA-1 (hex): " . sha1($hashString) . "\n";
echo "SHA-1 (base64): " . base64_encode(sha1($hashString, true)) . "\n";
echo "SHA-256 (base64): " . base64_encode(hash('sha256', $hashString, true)) . "\n";

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile ödeme yapın\n";
echo "2. Logları kontrol edin:\n";
echo "   tail -f storage/logs/laravel.log | grep -i hash\n";
echo "\n3. Aranacak log mesajları:\n";
echo "   - 'KuveytPos hash calculated'\n";
echo "   - 'KuveytPos provision XML built'\n";
echo "   - Hash değerinin boş olmaması\n";
echo "\n4. Artık '500 Internal Server Error' almamalısınız\n";
echo "5. Başarılı olursa para karttan çekilmeli!\n";
