<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

/*
|--------------------------------------------------------------------------
| Console Routes
|--------------------------------------------------------------------------
|
| This file is where you may define all of your Closure based console
| commands. Each Closure is bound to a command instance allowing a
| simple approach to interacting with each command's IO methods.
|
*/

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

//Schedule::command('app:test')->everySecond();
Schedule::command('app:api-key-refresh')->hourly();

Schedule::command('backup:clean')->daily()->at('01:00');

Schedule::command('get:banks')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:30');
Schedule::command('get:bank-branches')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:31');
Schedule::command('get:countries')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:01');
Schedule::command('get:cities')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:02');
Schedule::command('get:towns')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:03');
Schedule::command('get:units')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:06');
Schedule::command('get:warehouses')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:07');
Schedule::command('get:categories')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:payment-methods')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:05');
Schedule::command('get:sales-persons')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:11');
Schedule::command('get:daily-cur-rates')->weekdays()->everyTenMinutes()->timezone('Europe/Istanbul')->between('07:00', '18:00')->runInBackground();

Schedule::command('get:entities')->weekdays()->everyMinute();
Schedule::command('get:discounts')->weekdays()->everyFiveMinutes();
Schedule::command('get:contract-discounts')->weekdays()->everyFiveMinutes();
Schedule::command('get:company-entity')->weekdays()->everyMinute();

// Schedule::command('get:products')->weekdays()->timezone('Europe/Istanbul')->hourly()->between('07:00', '19:00');
Schedule::command('get:products')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');

Schedule::command('get:product-branch')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:32');

Schedule::command('get:letter-credits')->weekdays()->timezone('Europe/Istanbul')->dailyAt('07:40');

Schedule::command('get:offers')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:waybills')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:invoices')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:invoices-d')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:orders')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:orders-d')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:dbs')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('app:get-prices')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00')->runInBackground();
Schedule::command('get:prices-sync')->weekdays()->everyTenMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00')->runInBackground();
Schedule::command('get:price-rules')->weekdays()->everyTenMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00')->runInBackground();
Schedule::command('get:price-lists')->weekdays()->everyTenMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:contract-price-lists')->weekdays()->everyFiveMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00')->runInBackground();
Schedule::command('get:contracts')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:contract-expenses')->weekdays()->everyMinute()->timezone('Europe/Istanbul')->between('08:00', '19:00');
Schedule::command('get:contract-amounts')->weekdays()->everyTenMinutes()->timezone('Europe/Istanbul')->between('08:00', '19:00')->runInBackground();

Schedule::command('get:shipments')->everyThirtyMinutes()->timezone('Europe/Istanbul')->between('07:00', '19:00');

Schedule::command('get:daily-cur-rates')->weekdays()->hourly()->timezone('Europe/Istanbul')->between('07:00', '19:00');
Schedule::command('get:item-units')->daily()->at('05:32');

Schedule::command('auth:clear-resets')->everyFifteenMinutes();

// Ana senkronizasyon - saatte bir
Schedule::command('get:orders --chunk=500')
    ->hourly()
    ->withoutOverlapping()
    ->runInBackground();

// Sadece durum güncellemesi - 15 dakikada bir
Schedule::command('get:orders:status')
    ->everyFifteenMinutes()
    ->withoutOverlapping();