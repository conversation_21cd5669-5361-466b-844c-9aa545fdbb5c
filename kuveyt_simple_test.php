<?php

/**
 * KuveytTurk POS Simple Test
 * 
 * Bu script, basit provision request test eder.
 */

echo "=== KuveytTurk POS Simple Test ===\n\n";

// Test 1: Simple XML without Hash
echo "Test 1: Simple XML without Hash\n";
echo "--------------------------------\n";

function buildSimpleProvisionXml(array $data): string
{
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>1.0.0</APIVersion>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<Password>' . htmlspecialchars($data['Password']) . '</Password>';
    $xml .= '<CardNumber></CardNumber>';
    $xml .= '<CardExpireDateYear></CardExpireDateYear>';
    $xml .= '<CardExpireDateMonth></CardExpireDateMonth>';
    $xml .= '<CardCVV2></CardCVV2>';
    $xml .= '<CardHolderName></CardHolderName>';
    $xml .= '<CardType></CardType>';
    $xml .= '<BatchID>0</BatchID>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<DisplayAmount>' . htmlspecialchars($data['Amount']) . '</DisplayAmount>';
    $xml .= '<CurrencyCode>0949</CurrencyCode>';
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>';
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

$testData = [
    'MerchantId' => '588693',
    'CustomerId' => '98267952',
    'UserName' => 'kwebservis',
    'Password' => '3GgejhjaHCSX55k',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'Amount' => '2000',
    'MerchantOrderId' => 'P598',
    'BusinessKey' => '202508159551000000000024721'
];

$xmlRequest = buildSimpleProvisionXml($testData);
echo "✓ XML Request oluşturuldu (" . strlen($xmlRequest) . " karakter)\n";
echo "✓ Password dahil edildi\n";
echo "✓ HashData yok\n";
echo "✓ BusinessKey (MD): " . $testData['BusinessKey'] . "\n\n";

// Test 2: XML Validation
echo "Test 2: XML Validation\n";
echo "-----------------------\n";

$dom = new DOMDocument();
if ($dom->loadXML($xmlRequest)) {
    echo "✓ XML formatı geçerli\n";
    
    // Required fields check
    $requiredFields = [
        'MerchantId' => $testData['MerchantId'],
        'CustomerId' => $testData['CustomerId'],
        'UserName' => $testData['UserName'],
        'Password' => $testData['Password'],
        'Amount' => $testData['Amount'],
        'MerchantOrderId' => $testData['MerchantOrderId'],
        'TransactionType' => $testData['TransactionType']
    ];
    
    foreach ($requiredFields as $field => $expectedValue) {
        $nodes = $dom->getElementsByTagName($field);
        if ($nodes->length > 0) {
            $actualValue = $nodes->item(0)->textContent;
            if ($actualValue === $expectedValue) {
                echo "✓ $field: $actualValue\n";
            } else {
                echo "✗ $field mismatch: expected '$expectedValue', got '$actualValue'\n";
            }
        } else {
            echo "✗ $field eksik\n";
        }
    }
    
    // BusinessKey check
    $mdNodes = $dom->getElementsByTagName('Data');
    if ($mdNodes->length > 0) {
        $businessKey = $mdNodes->item(0)->textContent;
        if ($businessKey === $testData['BusinessKey']) {
            echo "✓ BusinessKey (MD): $businessKey\n";
        } else {
            echo "✗ BusinessKey mismatch\n";
        }
    } else {
        echo "✗ BusinessKey (MD) eksik\n";
    }
    
} else {
    echo "✗ XML formatı geçersiz\n";
}

echo "\n";

// Test 3: Endpoint Check
echo "Test 3: Endpoint Check\n";
echo "-----------------------\n";

$provisionUrl = 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelProvisionGate';
echo "Provision URL: $provisionUrl\n";

// Simple connectivity check
$context = stream_context_create([
    'http' => [
        'timeout' => 5,
        'method' => 'HEAD'
    ]
]);

$headers = @get_headers($provisionUrl, 1, $context);
if ($headers) {
    echo "✓ Endpoint erişilebilir\n";
    echo "✓ Response: " . $headers[0] . "\n";
} else {
    echo "? Endpoint kontrolü yapılamadı (normal)\n";
}

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile ödeme yapın\n";
echo "2. Logları kontrol edin:\n";
echo "   tail -f storage/logs/laravel.log | grep -i provision\n";
echo "\n3. Aranacak log mesajları:\n";
echo "   - 'KuveytPos provision XML built (no hash)'\n";
echo "   - 'KuveytPos sending provision request'\n";
echo "   - 'KuveytPos provision response received'\n";
echo "\n4. Artık 500 hatası almamalısınız\n";
echo "5. Başarılı response almalısınız\n";
echo "6. Para karttan çekilmeli!\n";
echo "\nÖnemli: Password alanı XML'e eklendi\n";
echo "Bu, KuveytTurk'ün provision request'inde beklediği format olabilir.\n";
