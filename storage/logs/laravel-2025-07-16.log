[2025-07-16 17:01:52] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:01:53] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:04:42] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:04:43] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:04:58] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:04:59] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:05:21] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:05:21] local.ERROR: Please provide a valid cache path. {"exception":"[object] (InvalidArgumentException(code: 0): Please provide a valid cache path. at D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Compilers\\Compiler.php:67)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\ViewServiceProvider.php(97): Illuminate\\View\\Compilers\\Compiler->__construct(Object(Illuminate\\Filesystem\\Filesystem), false, '', true, 'php')
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(952): Illuminate\\View\\ViewServiceProvider->Illuminate\\View\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(832): Illuminate\\Container\\Container->build(Object(Closure))
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1078): Illuminate\\Container\\Container->resolve('blade.compiler', Array, true)
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(763): Illuminate\\Foundation\\Application->resolve('blade.compiler', Array)
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1058): Illuminate\\Container\\Container->make('blade.compiler', Array)
#6 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1580): Illuminate\\Foundation\\Application->make('blade.compiler')
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(239): Illuminate\\Container\\Container->offsetGet('blade.compiler')
#8 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(210): Illuminate\\Support\\Facades\\Facade::resolveFacadeInstance('blade.compiler')
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(355): Illuminate\\Support\\Facades\\Facade::getFacadeRoot()
#10 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(70): Illuminate\\Support\\Facades\\Facade::__callStatic('directive', Array)
#11 D:\\Laragon\\www\\akdag\\vendor\\lavary\\laravel-menu\\src\\Lavary\\Menu\\ServiceProvider.php(94): Lavary\\Menu\\ServiceProvider->bladeDirectives()
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Lavary\\Menu\\ServiceProvider->boot()
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(95): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(696): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1151): Illuminate\\Container\\Container->call(Array)
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1132): Illuminate\\Foundation\\Application->bootProvider(Object(Lavary\\Menu\\ServiceProvider))
#19 [internal function]: Illuminate\\Foundation\\Application->Illuminate\\Foundation\\{closure}(Object(Lavary\\Menu\\ServiceProvider), 'Lavary\\\\Menu\\\\Ser...')
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1131): array_walk(Array, Object(Closure))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\BootProviders.php(17): Illuminate\\Foundation\\Application->boot()
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(342): Illuminate\\Foundation\\Bootstrap\\BootProviders->bootstrap(Object(Illuminate\\Foundation\\Application))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(187): Illuminate\\Foundation\\Application->bootstrapWith(Array)
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(171): Illuminate\\Foundation\\Http\\Kernel->bootstrap()
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#27 {main}
"} 
[2025-07-16 17:07:28] local.DEBUG: Kullanıcı Oturum Açma Başarılı. Ad: Hasan Cagrici | Id: 1 | Email: <EMAIL> | Kullanıcı adı: 100001 IP:127.0.0.1 | UpdateProfileLoginData  
[2025-07-16 17:07:28] local.DEBUG: Kullanıcı Oturum Açma Başarılı. Ad: Hasan Cagrici | Id: 1 | Email: <EMAIL> | Kullanıcı adı: 100001 IP:127.0.0.1 | UpdateProfileLoginData  
[2025-07-16 17:47:46] local.INFO: Bildirimler List | Kullanıcı:Hasan Cagrici(ID:1)  
[2025-07-16 17:57:09] local.INFO: Popup Ayarları Güncellendi | Kullanıcı:Hasan Cagrici(ID:1)  
