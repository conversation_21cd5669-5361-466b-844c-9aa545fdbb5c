[2025-07-22 13:23:45] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:23:45] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:23:49] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:23:49] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:23:53] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:23:53] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:24:20] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:24:20] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:24:28] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:24:28] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:24:45] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:24:45] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:24:48] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":19800.0} 
[2025-07-22 13:24:48] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:27:23] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":********.0,"amt_vat_cleaned":********.0,"unit_price_tra":198.0} 
[2025-07-22 13:27:23] local.INFO: Cart Debug - Calculated values {"amt_tra":********.0,"amt":********.0,"amt_vat_tra":********.0,"amt_vat":********.0,"cur_rate_tra":1} 
[2025-07-22 13:28:01] local.ERROR: Unsupported operand types: string * string {"userId":1,"exception":"[object] (TypeError(code: 0): Unsupported operand types: string * string at D:\\Laragon\\www\\akdag\\app\\Http\\Controllers\\Frontend\\AjaxController.php:316)
[stacktrace]
#0 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Frontend\\AjaxController->get_product_price(Object(Illuminate\\Http\\Request))
#1 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(44): Illuminate\\Routing\\Controller->callAction('get_product_pri...', Array)
#2 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Frontend\\AjaxController), 'get_product_pri...')
#3 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\Route->runController()
#4 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#5 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\Laragon\\www\\akdag\\app\\Http\\Middleware\\CheckPasswordChange.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\CheckPasswordChange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\Laragon\\www\\akdag\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\Laragon\\www\\akdag\\app\\Http\\Middleware\\SetLocale.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): App\\Http\\Middleware\\SetLocale->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(64): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(88): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#22 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(75): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#29 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#30 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#31 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#32 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(201): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#33 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Laragon\\www\\akdag\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Laragon\\www\\akdag\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 D:\\Laragon\\www\\akdag\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 D:\\Laragon\\www\\akdag\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-22 13:29:02] local.INFO: Cart Debug - Raw values {"amt_raw":"250905.6","amt_vat_raw":"50181.12","amt_cleaned":2509056.0,"amt_vat_cleaned":5018112.0,"unit_price_tra":99.0} 
[2025-07-22 13:29:02] local.INFO: Cart Debug - Calculated values {"amt_tra":2509056.0,"amt":2509056.0,"amt_vat_tra":5018112.0,"amt_vat":5018112.0,"cur_rate_tra":1} 
[2025-07-22 13:29:46] local.INFO: Cart Debug - Raw values {"amt_raw":"250905.6","amt_vat_raw":"50181.12","amt_cleaned":2509056.0,"amt_vat_cleaned":5018112.0,"unit_price_tra":99.0} 
[2025-07-22 13:29:46] local.INFO: Cart Debug - Calculated values {"amt_tra":2509056.0,"amt":2509056.0,"amt_vat_tra":5018112.0,"amt_vat":5018112.0,"cur_rate_tra":1} 
[2025-07-22 13:29:57] local.INFO: Cart Debug - Raw values {"amt_raw":"250905.6","amt_vat_raw":"50181.12","amt_cleaned":2509056.0,"amt_vat_cleaned":5018112.0,"unit_price_tra":99.0} 
[2025-07-22 13:29:57] local.INFO: Cart Debug - Calculated values {"amt_tra":2509056.0,"amt":2509056.0,"amt_vat_tra":5018112.0,"amt_vat":5018112.0,"cur_rate_tra":1} 
[2025-07-22 13:31:16] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:16] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:31:20] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:20] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:31:24] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:24] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:31:31] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:31] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:31:36] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:36] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:31:54] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":20072448.0,"amt_vat_cleaned":401449.0,"unit_price_tra":79.2} 
[2025-07-22 13:31:54] local.INFO: Cart Debug - Calculated values {"amt_tra":20072448.0,"amt":20072448.0,"amt_vat_tra":401449.0,"amt_vat":401449.0,"cur_rate_tra":1} 
[2025-07-22 13:33:31] local.INFO: Cart Debug - Raw values {"amt_raw":"401448.96","amt_vat_raw":"80289.79","amt_cleaned":40144896.0,"amt_vat_cleaned":8028979.0,"unit_price_tra":132.0} 
[2025-07-22 13:33:31] local.INFO: Cart Debug - Calculated values {"amt_tra":40144896.0,"amt":40144896.0,"amt_vat_tra":8028979.0,"amt_vat":8028979.0,"cur_rate_tra":1} 
[2025-07-22 13:33:35] local.INFO: Cart Debug - Raw values {"amt_raw":"401448.96","amt_vat_raw":"80289.79","amt_cleaned":40144896.0,"amt_vat_cleaned":8028979.0,"unit_price_tra":132.0} 
[2025-07-22 13:33:35] local.INFO: Cart Debug - Calculated values {"amt_tra":40144896.0,"amt":40144896.0,"amt_vat_tra":8028979.0,"amt_vat":8028979.0,"cur_rate_tra":1} 
[2025-07-22 13:34:11] local.INFO: Cart Debug - Raw values {"amt_raw":"401448.96","amt_vat_raw":"80289.79","amt_cleaned":40144896.0,"amt_vat_cleaned":8028979.0,"unit_price_tra":132.0} 
[2025-07-22 13:34:11] local.INFO: Cart Debug - Calculated values {"amt_tra":40144896.0,"amt":40144896.0,"amt_vat_tra":8028979.0,"amt_vat":8028979.0,"cur_rate_tra":1} 
[2025-07-22 13:35:39] local.INFO: Cart Debug - Raw values {"amt_raw":"421521.41","amt_vat_raw":"84304.28","amt_cleaned":421521.41,"amt_vat_cleaned":84304.28,"unit_price_tra":184.8} 
[2025-07-22 13:35:39] local.INFO: Cart Debug - Calculated values {"amt_tra":421521.41,"amt":421521.41,"amt_vat_tra":84304.28,"amt_vat":84304.28,"cur_rate_tra":1} 
[2025-07-22 13:35:55] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":602173.44,"amt_vat_cleaned":120434.69,"unit_price_tra":132.0} 
[2025-07-22 13:35:55] local.INFO: Cart Debug - Calculated values {"amt_tra":602173.44,"amt":602173.44,"amt_vat_tra":120434.69,"amt_vat":120434.69,"cur_rate_tra":1} 
[2025-07-22 13:36:19] local.INFO: Cart Debug - Raw values {"amt_raw":"602173.44","amt_vat_raw":"120434.69","amt_cleaned":602173.44,"amt_vat_cleaned":120434.69,"unit_price_tra":132.0} 
[2025-07-22 13:36:19] local.INFO: Cart Debug - Calculated values {"amt_tra":602173.44,"amt":602173.44,"amt_vat_tra":120434.69,"amt_vat":120434.69,"cur_rate_tra":1} 
[2025-07-22 14:49:05] local.INFO: Cart Debug - Raw values {"amt_raw":"802897.92","amt_vat_raw":"160579.58","amt_cleaned":802897.92,"amt_vat_cleaned":160579.58,"unit_price_tra":158.4} 
[2025-07-22 14:49:05] local.INFO: Cart Debug - Calculated values {"amt_tra":802897.92,"amt":802897.92,"amt_vat_tra":160579.58,"amt_vat":160579.58,"cur_rate_tra":1} 
[2025-07-22 14:49:15] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":200724.48,"amt_vat_cleaned":40144.9,"unit_price_tra":79.2} 
[2025-07-22 14:49:15] local.INFO: Cart Debug - Calculated values {"amt_tra":200724.48,"amt":200724.48,"amt_vat_tra":40144.9,"amt_vat":40144.9,"cur_rate_tra":1} 
[2025-07-22 14:49:21] local.INFO: Cart Debug - Raw values {"amt_raw":"200724.48","amt_vat_raw":"40144.9","amt_cleaned":200724.48,"amt_vat_cleaned":40144.9,"unit_price_tra":79.2} 
[2025-07-22 14:49:21] local.INFO: Cart Debug - Calculated values {"amt_tra":200724.48,"amt":200724.48,"amt_vat_tra":40144.9,"amt_vat":40144.9,"cur_rate_tra":1} 
[2025-07-22 20:10:02] local.DEBUG: Kullanıcı Oturum Açma Başarılı. Ad: Hasan Cagrici | Id: 1 | Email: <EMAIL> | Kullanıcı adı: 100001 IP:127.0.0.1 | UpdateProfileLoginData  
[2025-07-22 20:10:02] local.DEBUG: Kullanıcı Oturum Açma Başarılı. Ad: Hasan Cagrici | Id: 1 | Email: <EMAIL> | Kullanıcı adı: 100001 IP:127.0.0.1 | UpdateProfileLoginData  
[2025-07-22 20:10:14] local.DEBUG: switching mode {"is_test_mode":false} 
