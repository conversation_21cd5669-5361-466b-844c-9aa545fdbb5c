<?php

/**
 * KuveytTurk POS Hata Düzeltmesi Test Scripti
 * 
 * Bu script, yapılan değişikliklerin doğru çalışıp çalışmadığını test etmek için kullanılabilir.
 */

// Test 1: XML Parsing Güvenliği
echo "=== Test 1: XML Parsing Güvenliği ===\n";

// Boş XML test
$emptyXml = "";
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$result = $dom->loadXML($emptyXml);
if (!$result) {
    echo "✓ Boş XML güvenli şekilde yakalandı\n";
} else {
    echo "✗ Boş XML testi başarısız\n";
}

// Geçersiz XML test
$invalidXml = "<invalid>xml<content>";
$result = $dom->loadXML($invalidXml);
if (!$result) {
    echo "✓ Geçersiz XML güvenli şekilde yakalandı\n";
} else {
    echo "✗ Geçersiz XML testi başarısız\n";
}

libxml_clear_errors();

// Test 2: Null Pointer Koruması
echo "\n=== Test 2: Null Pointer Koruması ===\n";

function testGetXmlValue($dom, $tagName) {
    try {
        $nodes = $dom->getElementsByTagName($tagName);
        if ($nodes && $nodes->length > 0) {
            $node = $nodes->item(0);
            if ($node && $node->textContent !== null) {
                return trim($node->textContent);
            }
        }
    } catch (Exception $e) {
        echo "Hata yakalandı: " . $e->getMessage() . "\n";
    }
    return null;
}

$validXml = "<root><test>value</test></root>";
$dom->loadXML($validXml);
$value = testGetXmlValue($dom, 'test');
if ($value === 'value') {
    echo "✓ Geçerli XML değeri başarıyla alındı\n";
} else {
    echo "✗ Geçerli XML değeri alınamadı\n";
}

$nonExistentValue = testGetXmlValue($dom, 'nonexistent');
if ($nonExistentValue === null) {
    echo "✓ Olmayan tag güvenli şekilde null döndü\n";
} else {
    echo "✗ Olmayan tag testi başarısız\n";
}

// Test 3: Error Response Format
echo "\n=== Test 3: Error Response Format ===\n";

$expectedErrorResponse = [
    'status' => 'declined',
    'error_message' => 'Payment declined by bank',
    'md_error_message' => 'Ödeme bankaca reddedildi veya geçersiz yanıt alındı. Kart bilgilerinizi kontrol ederek tekrar deneyiniz veya farklı bir kart kullanınız.',
    'installment_count' => 0,
    'ref_ret_num' => null,
];

$requiredKeys = ['status', 'error_message', 'md_error_message', 'installment_count', 'ref_ret_num'];
$allKeysPresent = true;

foreach ($requiredKeys as $key) {
    if (!array_key_exists($key, $expectedErrorResponse)) {
        $allKeysPresent = false;
        echo "✗ Eksik key: $key\n";
    }
}

if ($allKeysPresent) {
    echo "✓ Error response formatı doğru\n";
}

echo "\n=== Test Tamamlandı ===\n";
echo "Tüm testler başarılı ise, KuveytTurk POS hata düzeltmesi doğru çalışıyor demektir.\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile bir test ödemesi yapın\n";
echo "2. 3D Secure doğrulamasını tamamlayın\n";
echo "3. Hata loglarını kontrol edin: storage/logs/laravel.log\n";
echo "4. Artık 'attributes on null' hatası yerine anlamlı hata mesajları görmelisiniz\n";
