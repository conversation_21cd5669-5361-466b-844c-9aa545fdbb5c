# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Laravel 11 modular B2B e-commerce application based on the "nasirkhan/laravel-starter" template. The project serves as a business-to-business portal for stone/aggregate products, featuring a comprehensive ERP integration with order management, pricing, and payment processing.

## Common Commands

### Development Commands
- `composer install` - Install PHP dependencies
- `composer clear-all` - Clear all Laravel caches (custom script)
- `composer pint` - Run Laravel Pint for code formatting
- `php artisan migrate` - Run database migrations
- `php artisan db:seed` - Run database seeders
- `php artisan serve` - Start development server

### Module Commands
- `php artisan module:list` - List all modules
- `php artisan module:make {name}` - Create a new module
- `php artisan module:enable {name}` - Enable a module
- `php artisan module:disable {name}` - Disable a module
- `php artisan module:migrate` - Run migrations for all modules
- `php artisan module:test {name}` - Run tests for a specific module

### Testing
- `php artisan test` - Run PHPUnit tests
- `php artisan test --filter {TestName}` - Run specific test
- Tests are configured in each module's `Tests/` directory

### Custom Commands
- `php artisan get:contract-amounts` - Sync contract amounts
- `php artisan app:api-key-refresh` - Refresh API authentication keys
- Various sync commands for ERP integration (see `app/Console/Commands/`)

## Architecture

### Module Structure
The application uses `nwidart/laravel-modules` package with these active modules:
- **Article** - Blog/news functionality
- **Category** - Product categorization
- **Comment** - User comments system
- **Tag** - Content tagging

Each module follows this structure:
- `Http/Controllers/` - Backend and Frontend controllers
- `Models/` - Eloquent models
- `Database/` - Migrations, seeders, factories
- `Resources/views/` - Blade templates
- `Routes/` - Web and API routes
- `Tests/` - Unit and feature tests

### Key Models & Relationships
- **User** extends `BaseModel` with roles/permissions (Spatie)
- **BaseModel** - Common model with audit fields (created_by, updated_by, deleted_by)
- **Entity** - Core business entities (companies/customers)
- **Product** - Inventory items with pricing
- **Order/Cart** - Shopping cart and order management
- **Contract** - B2B pricing contracts
- **Payment** - Payment processing and transactions

### Core Features
- **ERP Integration** - Via `uyumapi()` helper function for external API calls
- **Multi-language** - Turkish/English support
- **Role-based permissions** - Using Spatie Permission package
- **Media management** - File uploads with Spatie Media Library
- **Activity logging** - User action tracking
- **Payment processing** - Credit card and bank integration

### Authentication & Authorization
- Laravel Breeze for authentication
- Social login support (Laravel Socialite)
- Custom user profiles with company associations
- Role-based access control with permissions

### Frontend Structure
- Blade templates with Bootstrap-based admin theme
- Livewire components for dynamic interactions
- Multi-layout support (frontend/backend)
- RTL language support

### API Integration
- External ERP system integration via `uyumapi()` function
- API authentication with token refresh mechanism
- Background job processing for API calls
- Caching for performance optimization

## Development Notes

### Key Helpers (app/helpers.php)
- `setting()` - Get/set application settings
- `encode_id()`/`decode_id()` - ID obfuscation with Hashids
- `uyumapi()` - Main ERP API integration function
- `post_order_to_uyum()` - Order synchronization
- Various Turkish localization helpers

### Database Considerations
- Soft deletes enabled on BaseModel
- Audit fields automatically populated
- Multiple currency support
- Complex pricing and contract structures

### Security Features
- CSRF protection
- Input validation and sanitization
- Role-based access control
- API key management with refresh tokens

### Performance Optimizations
- Query optimization for large datasets
- Background job processing
- Caching strategies
- Database indexing for search operations

## Code Style
- PSR-12 code standards via Laravel Pint
- Turkish language comments and variables in domain logic
- Consistent naming conventions
- Proper error handling and validation