<?php

/**
 * KuveytTurk POS Provision Test Scripti
 * 
 * Bu script, KuveytTurk POS için ikinci istek (provision) akışını test eder.
 */

echo "=== KuveytTurk POS Provision Test ===\n\n";

// Test 1: XML Request Builder
echo "Test 1: XML Request Builder\n";
echo "----------------------------\n";

function buildKuveytPosProvisionXml(array $data): string
{
    $xml = '<?xml version="1.0" encoding="utf-8"?>';
    $xml .= '<KuveytTurkVPosMessage xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">';
    $xml .= '<APIVersion>1.0.0</APIVersion>';
    $xml .= '<OkUrl></OkUrl>';
    $xml .= '<FailUrl></FailUrl>';
    $xml .= '<HashData></HashData>';
    $xml .= '<MerchantId>' . htmlspecialchars($data['MerchantId']) . '</MerchantId>';
    $xml .= '<CustomerId>' . htmlspecialchars($data['CustomerId']) . '</CustomerId>';
    $xml .= '<UserName>' . htmlspecialchars($data['UserName']) . '</UserName>';
    $xml .= '<CardNumber></CardNumber>';
    $xml .= '<CardExpireDateYear></CardExpireDateYear>';
    $xml .= '<CardExpireDateMonth></CardExpireDateMonth>';
    $xml .= '<CardCVV2></CardCVV2>';
    $xml .= '<CardHolderName></CardHolderName>';
    $xml .= '<CardType></CardType>';
    $xml .= '<BatchID>0</BatchID>';
    $xml .= '<TransactionType>' . htmlspecialchars($data['TransactionType']) . '</TransactionType>';
    $xml .= '<InstallmentCount>' . htmlspecialchars($data['InstallmentCount']) . '</InstallmentCount>';
    $xml .= '<Amount>' . htmlspecialchars($data['Amount']) . '</Amount>';
    $xml .= '<DisplayAmount>' . htmlspecialchars($data['Amount']) . '</DisplayAmount>';
    $xml .= '<CurrencyCode>0949</CurrencyCode>'; // TRY
    $xml .= '<MerchantOrderId>' . htmlspecialchars($data['MerchantOrderId']) . '</MerchantOrderId>';
    $xml .= '<TransactionSecurity>3</TransactionSecurity>'; // 3D Secure
    $xml .= '<KuveytTurkVPosAdditionalData>';
    $xml .= '<AdditionalData>';
    $xml .= '<Key>MD</Key>';
    $xml .= '<Data>' . htmlspecialchars($data['BusinessKey']) . '</Data>';
    $xml .= '</AdditionalData>';
    $xml .= '</KuveytTurkVPosAdditionalData>';
    $xml .= '</KuveytTurkVPosMessage>';

    return $xml;
}

$testData = [
    'MerchantId' => '588693',
    'CustomerId' => '98267952',
    'UserName' => 'kwebservis',
    'TransactionType' => 'Sale',
    'InstallmentCount' => '0',
    'Amount' => '1000',
    'MerchantOrderId' => 'P608',
    'BusinessKey' => '202508149555000000000019243'
];

$xmlRequest = buildKuveytPosProvisionXml($testData);
echo "✓ XML Request oluşturuldu:\n";
echo $xmlRequest . "\n\n";

// Test 2: XML Response Parser
echo "Test 2: XML Response Parser\n";
echo "----------------------------\n";

function parseKuveytPosProvisionResponse(string $xmlResponse): ?array
{
    try {
        if (empty(trim($xmlResponse))) {
            echo "✗ XML response boş\n";
            return null;
        }

        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        
        if (!$dom->loadXML($xmlResponse)) {
            echo "✗ XML parse edilemedi\n";
            return null;
        }

        // Test için basit değer çıkarma
        $responseCode = getXmlValue($dom, 'ResponseCode');
        $responseMessage = getXmlValue($dom, 'ResponseMessage');
        
        echo "✓ Response Code: " . ($responseCode ?: 'N/A') . "\n";
        echo "✓ Response Message: " . ($responseMessage ?: 'N/A') . "\n";

        return [
            'response_code' => $responseCode,
            'response_message' => $responseMessage
        ];

    } catch (Exception $e) {
        echo "✗ Parse hatası: " . $e->getMessage() . "\n";
        return null;
    }
}

function getXmlValue(DOMDocument $dom, string $tagName): ?string
{
    try {
        $nodes = $dom->getElementsByTagName($tagName);
        if ($nodes && $nodes->length > 0) {
            $node = $nodes->item(0);
            if ($node && $node->textContent !== null) {
                return trim($node->textContent);
            }
        }
    } catch (Exception $e) {
        // Hata yakalandı
    }
    return null;
}

// Başarılı response testi
$successResponse = '<?xml version="1.0" encoding="utf-8"?>
<KuveytTurkVPosMessage>
    <ResponseCode>00</ResponseCode>
    <ResponseMessage>İşlem başarılı</ResponseMessage>
    <OrderId>283961617</OrderId>
    <MerchantOrderId>P608</MerchantOrderId>
    <Amount>1000</Amount>
    <ProvisionNumber>123456</ProvisionNumber>
    <RRN>789012</RRN>
</KuveytTurkVPosMessage>';

echo "Başarılı response testi:\n";
$result = parseKuveytPosProvisionResponse($successResponse);
if ($result && $result['response_code'] === '00') {
    echo "✓ Başarılı response doğru parse edildi\n";
} else {
    echo "✗ Başarılı response parse edilemedi\n";
}

// Başarısız response testi
$failResponse = '<?xml version="1.0" encoding="utf-8"?>
<KuveytTurkVPosMessage>
    <ResponseCode>99</ResponseCode>
    <ResponseMessage>İşlem başarısız</ResponseMessage>
</KuveytTurkVPosMessage>';

echo "\nBaşarısız response testi:\n";
$result = parseKuveytPosProvisionResponse($failResponse);
if ($result && $result['response_code'] === '99') {
    echo "✓ Başarısız response doğru parse edildi\n";
} else {
    echo "✗ Başarısız response parse edilemedi\n";
}

echo "\n=== Test Tamamlandı ===\n";
echo "\nGerçek test için:\n";
echo "1. KuveytTurk POS ile test ödemesi yapın\n";
echo "2. 3D Secure doğrulamasını tamamlayın\n";
echo "3. Logları kontrol edin - artık provision request gönderilmeli\n";
echo "4. Başarılı olursa para karttan çekilmeli\n";
echo "\nÖnemli: Provision request'in başarılı olması için:\n";
echo "- BusinessKey (MD) değeri doğru olmalı\n";
echo "- MerchantId, CustomerId, UserName doğru olmalı\n";
echo "- Provision endpoint'i doğru olmalı\n";
