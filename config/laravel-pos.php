<?php

return [
    'banks' => [
//        'akbank' => [
//            'name'  => 'Akbank',
//            'gateway_class'     => Mews\Pos\Gateways\EstV3Pos::class,
//            'test_mode'         => true,
//            'lang'              => Mews\Pos\PosInterface::LANG_TR,
//            'credentials'       => [
//                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_PAY,
//                'merchant_id'   => '*********',
//                'user_name'     => 'Akaser',
//                'user_password' => 'Akass741',
//                'enc_key'       => '123456',
//            ],
//            'gateway_endpoints' => [
//                'payment_api' => 'https://entegrasyon.asseco-see.com.tr/fim/api',
//                'gateway_3d'  => 'https://entegrasyon.asseco-see.com.tr/fim/est3Dgate',
//            ],
//        ],
        'akbank' => [
            'name'  => 'Akbank',
            'gateway_class'     => Mews\Pos\Gateways\AkbankPos::class,
            'test_mode'         => false,
            'lang'              => Mews\Pos\PosInterface::LANG_TR,
            'credentials'       => [
                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
                'merchant_id'   => '2025021417310018673FAAAED118E87F',
                'terminal_id'     => '20250214173100201EC9B6D9B7EC3EE5',
                    'enc_key'       => '32303235303231343137333130303137367676673337677474376731355f38373231313776385f745f375f31767337333376327473723267386735353335335f',
            ],
            'gateway_endpoints' => [
                'payment_api'     => 'https://api.akbank.com/api/v1/payment/virtualpos',
                'gateway_3d'      => 'https://virtualpospaymentgateway.akbank.com/securepay',
                'gateway_3d_host' => 'https://virtualpospaymentgateway.akbank.com/payhosting',
            ],

        ],

//        'garanti' => [
//            'name'  => 'Garanti Bankası',
//            'gateway_class'     => Mews\Pos\Gateways\GarantiPos::class,
//            'test_mode'         => true,
//            'lang'              => Mews\Pos\PosInterface::LANG_TR,
//            'credentials'       => [
//                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
//                'merchant_id'   => '7000679',
//                'user_name'     => 'PROVAUT',
//                'user_password' => '123qweASD/',
//                'terminal_id'   => '********',
//                'enc_key'       => '********',
//            ],
//            'gateway_endpoints' => [
//                'payment_api'     => 'https://sanalposprov.garanti.com.tr/VPServlet',
//                'gateway_3d'      => 'https://sanalposprovtest.garantibbva.com.tr/servlet/gt3dengine',
//
//            ],
//        ],


        'garanti' => [
            'name'  => 'Garanti Bankası',
            'gateway_class'     => Mews\Pos\Gateways\GarantiPos::class,
            'test_mode'         => false,
            'lang'              => Mews\Pos\PosInterface::LANG_TR,
            'credentials'       => [
                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
                'merchant_id'   => '1491004',
                'user_name'     => 'PROVAUT',
                'user_password' => '1ed234/5Aw',
                'terminal_id'   => '********',
                'enc_key'       => '313235386166677468466b6c6e6746313234353837343538',
            ],

            'gateway_endpoints' => [
                'payment_api'     => 'https://sanalposprov.garanti.com.tr/VPServlet',
                'gateway_3d'      => 'https://sanalposprov.garanti.com.tr/servlet/gt3dengine',

            ],
        ],

        'halkbank' => [
            'name'  => 'Halkbank',
            'gateway_class'     => Mews\Pos\Gateways\EstV3Pos::class,
            'test_mode'         => false,
            'lang'              => Mews\Pos\PosInterface::LANG_TR,
            'credentials'       => [
                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
                'merchant_id'   => *********,
                'user_name'     => 'halkApiUsr',
                'user_password' => '123dfTy!_A',
                'enc_key'       => 'RNA9ke6yVU4zXkc',
            ],
            'gateway_endpoints' => [
                'payment_api'     => 'https://sanalpos.halkbank.com.tr/fim/api',
                'gateway_3d'      => 'https://sanalpos.halkbank.com.tr/fim/est3dgate',
            ],
        ],

        'isbank' => [
            'name'  => 'İş Bankası',
            'gateway_class'     => \Mews\Pos\Gateways\EstV3Pos::class,
            'test_mode'         => false,
            'lang'              => Mews\Pos\PosInterface::LANG_TR,
            'credentials'       => [
                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
                'merchant_id'   => '************',
                'user_name'     => 'isapiuser',
                'user_password' => 'isApiUser4758',
                'enc_key'       => 'akdAG785!4Ct',
            ],
            // "Undefined array key "storetype""
            'gateway_endpoints' => [
                'payment_api'     => 'https://sanalpos.isbank.com.tr/fim/api',
                'gateway_3d'      => 'https://sanalpos.isbank.com.tr/fim/est3Dgate',
            ],
        ],


        'yapikredi' => [
            'name'  => 'Yapı Kredi Bankası',
            'gateway_class'     => Mews\Pos\Gateways\PosNet::class, // Required
            'lang'              => 'tr',
            'credentials'       => [
                'payment_model'        => Mews\Pos\PosInterface::MODEL_3D_SECURE, // Required
                'merchant_id'          => **********,
                'user_name'            => '****************',
                'terminal_id'          => ********,
                'enc_key'              => '10,10,10,10,10,10,10,10',
            ],
            'gateway_endpoints' => [
                'payment_api'     => 'https://posnet.yapikredi.com.tr/PosnetWebService/XML',
                'gateway_3d'      => 'https://posnet.yapikredi.com.tr/3DSWebService/YKBPaymentService',
            ],
            'test_mode'         => false,
        ],

        'ziraat-estpos' => [
            'name'  => 'Ziraat Bankası',
            'gateway_class' => Mews\Pos\Gateways\EstV3Pos::class,
            'credentials'       => [
                'payment_model'        => Mews\Pos\PosInterface::MODEL_3D_SECURE, // Required
                'merchant_id'          => '*********',
                'user_name'            => 'ziraatApiU',
                'user_password'        => '789789Au',
                'enc_key'              => '739AwsZbXqAXxx',
            ],
            'test_mode'         => false,
            'gateway_endpoints'  => [
                'payment_api'     => 'https://sanalpos2.ziraatbank.com.tr/fim/api',
                'gateway_3d'      => 'https://sanalpos2.ziraatbank.com.tr/fim/est3Dgate',
            ],
        ],


//        'teb' => [
//            'name'  => 'TEB pos',
//            'gateway_class'     => Mews\Pos\Gateways\EstV3Pos::class, // Required
//            'lang'              => 'tr',
//            'credentials'       => [  // Required, sanal pos hesap bilgileri
//                'payment_model'        => Mews\Pos\PosInterface::MODEL_3D_SECURE, // Required
//                'merchant_id'          => *********,
//                'user_name'            => 'buukapi',
//                'user_password'        => 'hntZSUzGny483PF',
//                'enc_key'              => 'nCQfGEx4eq39Y6',
//            ],
//            'gateway_endpoints' => [ // Required
//                'payment_api'     => 'https://sanalpos.teb.com.tr/fim', // Required
//                'gateway_3d'      => 'https://sanalpos.teb.com.tr/fim/est3Dgate', // Required
//            ],
//            'test_mode'         => false,
//        ],

        'kuveytpos' => [
            'name'  => 'Kuveyt Türk',
            'gateway_class'     => Mews\Pos\Gateways\KuveytPos::class,
            'lang'              => Mews\Pos\PosInterface::LANG_TR,
            'credentials'       => [
                'payment_model' => Mews\Pos\PosInterface::MODEL_3D_SECURE,
                'merchant_id'   => '588693',  // MerchantId - Kuveyt Türk Mağaza Numarası
            //    'customer_id'   => '98267952',  // CustomerId - Kuveyt Türk Müşteri Numarası
                'user_name'     => 'kwebservis',  // UserName - Kuveyt Türk Kullanıcı Adı
                'enc_key'       => '3GgejhjaHCSX55k',  // Password - API Şifresi
                'terminal_id'   => '98267952',  // CustomerId - Kuveyt Türk Müşteri Numarası

            ],
            'gateway_endpoints' => [
                'payment_api'     => 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelPayGate', // Updated URL based on PDF
                'gateway_3d'      => 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelPayGate', // Same URL for 3D secure
                'query_api'       => 'https://sanalpos.kuveytturk.com.tr/ServiceGateWay/Home/ThreeDModelProvisionGate', // Provision URL
            ],
            'test_mode'         => false,
        ]

    ],
];
