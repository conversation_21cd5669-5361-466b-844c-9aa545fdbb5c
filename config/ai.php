<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI API Anahtarı
    |--------------------------------------------------------------------------
    |
    | Bu API anahtarını kullanarak OpenAI API'sine bağlanabilirsiniz.
    | Bu değeri .env dosyanızda OPENAI_API_KEY olarak ayarlayabilirsiniz.
    |
    */
    'api_key' => env('OPENAI_API_KEY'),

    /*
    |--------------------------------------------------------------------------
    | Varsayılan AI Modeli
    |--------------------------------------------------------------------------
    |
    | AI sorguları için kullanılacak varsayılan model.
    | Önerilen: gpt-4-turbo veya gpt-3.5-turbo
    |
    */
    'default_model' => env('OPENAI_MODEL', 'gpt-4o-mini-2024-07-18'),

    /*
    |--------------------------------------------------------------------------
    | Temperature (Yaratıcılık Seviyesi)
    |--------------------------------------------------------------------------
    |
    | Daha düşük değerler daha kararlı ve tekrarlanabilir yanıtlar üretir.
    | Daha yüksek değerler daha yaratıcı yanıtlar üretir.
    | İlk SQL sorgusu oluşturmak için 0.3 gibi düşük bir değer,
    | Rapor özeti için 0.7 gibi daha yüksek bir değer önerilir.
    |
    */
    'temperature' => env('OPENAI_TEMPERATURE', 0.3),

    /*
    |--------------------------------------------------------------------------
    | Maksimum Token Sayısı
    |--------------------------------------------------------------------------
    |
    | Bir istekte kullanılacak maksimum token sayısı.
    | Rapor yanıtları için en az 1000-2000 arası önerilir.
    |
    */
    'max_tokens' => env('OPENAI_MAX_TOKENS', 2000),

    /*
    |--------------------------------------------------------------------------
    | Sistem Promptu
    |--------------------------------------------------------------------------
    |
    | AI'a verilen base system prompt. Bu, AI'ın rolünü ve
    | görevini tanımlar.
    |
    */
    'system_prompt' => env('OPENAI_SYSTEM_PROMPT', 'Sen bir B2B web sitesi için veri analiz asistanısın. Kullanıcının sorularına veritabanından sağlanan veriler doğrultusunda yanıt ver.'),

    /*
    |--------------------------------------------------------------------------
    | Önbellek Ayarları
    |--------------------------------------------------------------------------
    |
    | AI sorgu sonuçlarını önbelleğe almak için ayarlar.
    | enable_cache: Önbelleğe almayı etkinleştirir/devre dışı bırakır.
    | cache_ttl: Önbellek süresini dakika cinsinden belirtir (varsayılan: 1 gün).
    |
    */
    'enable_cache' => env('AI_ENABLE_CACHE', false),
    'cache_ttl' => env('AI_CACHE_TTL', 60 * 24), // 1 gün

    /*
    |--------------------------------------------------------------------------
    | SQL Aşaması Ayarları
    |--------------------------------------------------------------------------
    |
    | İlk aşama SQL sorgusu oluşturma için ayarlar.
    | sql_model: SQL sorguları oluşturmak için kullanılacak model.
    | sql_temperature: SQL oluşturma için temperature değeri (düşük önerilir).
    |
    */
    'sql_model' => env('OPENAI_SQL_MODEL', 'gpt-4o-mini-2024-07-18'),
    'sql_temperature' => env('OPENAI_SQL_TEMPERATURE', 0.3),

    /*
    |--------------------------------------------------------------------------
    | Rapor Aşaması Ayarları
    |--------------------------------------------------------------------------
    |
    | İkinci aşama rapor oluşturma için ayarlar.
    | report_model: Rapor oluşturmak için kullanılacak model.
    | report_temperature: Rapor oluşturma için temperature değeri.
    |
    */
    'report_model' => env('OPENAI_REPORT_MODEL', 'gpt-4'),
    'report_temperature' => env('OPENAI_REPORT_TEMPERATURE', 0.7),
];