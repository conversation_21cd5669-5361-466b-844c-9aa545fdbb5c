{"The :attribute must contain at least one letter.": "The :attribute must contain at least one letter.", "The :attribute must contain at least one number.": "The :attribute must contain at least one number.", "The :attribute must contain at least one symbol.": "The :attribute must contain at least one symbol.", "The :attribute must contain at least one uppercase and one lowercase letter.": "The :attribute must contain at least one uppercase and one lowercase letter.", "The given :attribute has appeared in a data leak. Please choose a different :attribute.": "The given :attribute has appeared in a data leak. Please choose a different :attribute.", "Welcome to": "Welcome to :name <PERSON><PERSON> Dashboard.", "Dashboard": "Dashboard", "Admin Dashboard": "Admin Dashboard", "Name": "Name", "Email": "Email", "Password": "Password", "Password Confirmation": "Password Confirmation", "Value": "Value", "Index": "Index", "List": "List", "Show": "Show", "Edit": "Edit", "Action": "Action", "Back": "Back", "Return back": "Return back", "Save": "Save", "Cancel": "Cancel", "Create": "Create", "Created": "Created", "Created at": "Created at", "Update": "Update", "Updated": "Updated", "Updated at": "Updated at", "Delete": "Delete", "Deleted": "Deleted", "Deleted at": "Deleted at", "Deleted List": "Deleted List", "Trash": "Trash", "Download": "Download", "Activity Log": "Activity Log", "Log": "Log", "Current": "Current", "Old": "Old", "At": "At", "Type": "Type", "User": "User", "Change Password": "Change Password", "Roles": "Roles", "Permissions": "Permissions", "All Permissions": "All Permissions", "Please fix the following errors & try again!": "Please fix the following errors & try again!", "Copyright": "Copyright", "Change language": "Change language", "Notifications": "Notifications", "You have :count notifications": "You have :count notifications", ":count unread": ":count unread", "Account": "Account", "Settings": "Settings", "Login": "<PERSON><PERSON>", "Register": "Register", "Create an account": "Create an account", "Logout": "Sign out", "All values of :module_name (Id: :id)": "All values of <b>:module_name (Id: :id)</b>.", "Are you sure?": "Are you sure?", "Send Confirmation Email": "Send Confirmation Email", ":module_name Management Dashboard": ":module_name Management Dashboard", "Create new :module_name": "Create new :module_name", "Backup": "Backup", "Backups": "Backups", "File": "File", "Size": "Size", "Date": "Date", "Age": "Age", "Download File": "Download File", "Delete ": "Delete ", "Delete File": "Delete File", "There are no backups": "There are no backups", "Mark All As Read": "<PERSON> As Read", "Delete All Notifications": "Delete All Notifications", "Text": "Text", "Module": "<PERSON><PERSON><PERSON>", "Total": "Total", "Browse": "Browse", "Management": "Management", "Log Viewer": "Log Viewer", "Log Viewer Module": "Log Viewer Module", "Logs by Date": "Logs by Date", "Log Viewer Dashboard": "Log Viewer Dashboard", "Delete Log File": "Delete Log File", "Details": "Details", "Levels": "Levels", "Log Info": "Log Info", "Select an option": "-- Select an option --", "URL": "URL", "Username": "Username", "Visit": "Visit", "Active": "Active", "Email Confirmed": "Email Confirmed", "Email Credentials": "Send account credentials to the user", "Open main menu": "Open main menu", "Posts": "Posts", "Categories": "Categories", "Tags": "Tags", "Comments": "Comments", "Companies": "Companies", "Draft": "Draft", "Published": "Published", "Unpublished": "Unpublished"}