@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

@import '/node_modules/@fortawesome/fontawesome-free/css/all.min.css';

@layer base {
    html {
        @apply text-gray-600;
    }

    h1,
    h2,
    h3,
    h4 {
        @apply mb-4;
    }

    p {
        @apply mb-4;
    }

    hr {
        @apply my-4;
    }

    ul {
        @apply list-inside list-disc;
    }

    ol {
        @apply list-inside list-decimal;
    }
}

@layer components {
    .btn-primary {
        @apply py-2 px-4 bg-blue-500 text-white font-semibold rounded-lg shadow-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-opacity-75;
    }

    .text-danger {
        @apply text-red-500;
    }
}