@extends('frontend.layouts.app')

@section('title') {{$user->name}} Profil <PERSON> @endsection

@section('content')

<div class="container mx-auto flex justify-center">

    @include('frontend.includes.messages')

</div>

<div class="nk-content ">
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="card card-bordered">
                    <div class="card-aside-wrap">
                        <div class="card-inner card-inner-lg">

                            {{ html()->modelForm($userprofile, 'PATCH', route('frontend.users.profileUpdate', encode_id($user->id)))->acceptsFiles()->open() }}
                            <div class="row">
                                <div class="col-md-7">    <div class="mb-8 p-6 bg-white border rounded-lg">
                                        <div class="card-inner">
                                            <div class="nk-block-head nk-block-head-lg">
                                                <div class="nk-block-between">
                                                    <div class="nk-block-head-content"><h4 class="nk-block-title">{{ __('Personal Info') }}</h4>
                                                        <div class="nk-block-des"><p> Bu bloğun bilgileri herkese açık olarak gösterilmeyecektir.</p></div>
                                                    </div>
                                                    <div class="nk-block-head-content align-self-start d-lg-none"><a href="#"
                                                                                                                     class="toggle btn btn-icon btn-trigger mt-n1"
                                                                                                                     data-target="userAside"><em
                                                                    class="icon ni ni-menu-alt-r"></em></a></div>
                                                </div>
                                            </div>


                                            <div class="col-span-6 sm:col-span-3">

                                                {{ html()->label(__('First Name'), 'first_name')->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required('required') !!}
                                                {{ html()->text('first_name')->placeholder(__('First Name'))->class('form-control')->attributes(["required"]) }}
                                            </div>

                                            <div class="col-span-6 sm:col-span-3">

                                                {{ html()->label(__('Last Name'), 'last_name')->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required('required') !!}
                                                {{ html()->text('last_name')->placeholder(__('Last Name'))->class('form-control')->attributes(["required"]) }}
                                            </div>

                                            <div class="col-span-6">
                                                {{ html()->label(__('Address'), 'address')->class('block-inline text-sm font-medium text-gray-700') }}
                                                {{ html()->text('address')->placeholder(__('Address'))->class('form-control') }}
                                            </div>

                                            <div class="col-span-6">

                                                {{ html()->label(__('Web Site'), 'url_website')->class('block-inline text-sm font-medium text-gray-700') }}
                                                {{ html()->text('url_website')->placeholder(__('Web Site'))->class('form-control')->attributes([""]) }}
                                            </div>

                                            <div class="col-span-6 sm:col-span-3">
                                                <?php
                                                $field_name = 'url_facebook';
                                                $field_label = label_case($field_name);
                                                $field_placeholder = $field_label;
                                                $required = "";
                                                ?>
                                                {{ html()->label($field_label, $field_name)->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required($required) !!}
                                                {{ html()->text($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                            </div>

                                            <div class="col-span-6 sm:col-span-3">
                                                <?php
                                                $field_name = 'url_twitter';
                                                $field_label = label_case($field_name);
                                                $field_placeholder = $field_label;
                                                $required = "";
                                                ?>
                                                {{ html()->label($field_label, $field_name)->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required($required) !!}
                                                {{ html()->text($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                            </div>

                                            <div class="col-span-6 sm:col-span-3">
                                                <?php
                                                $field_name = 'url_linkedin';
                                                $field_label = label_case($field_name);
                                                $field_placeholder = $field_label;
                                                $required = "";
                                                ?>
                                                {{ html()->label($field_label, $field_name)->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required($required) !!}
                                                {{ html()->text($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                            </div>

                                            <div class="col-span-6 sm:col-span-3">
                                                <?php
                                                $field_name = 'url_instagram';
                                                $field_label = label_case($field_name);
                                                $field_placeholder = $field_label;
                                                $required = "";
                                                ?>
                                                {{ html()->label($field_label, $field_name)->class('block-inline text-sm font-medium text-gray-700') }} {!! fielf_required($required) !!}
                                                {{ html()->text($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                            </div>
                                            <div class="col-span-6">

                                                {{ html()->label(__('About'), 'bio')->class('block-inline text-sm font-medium text-gray-700') }}
                                                {{ html()->textarea('bio')->placeholder(__('About'))->class('form-control')->attributes(['rows'=> 5]) }}
                                            </div>

                                        </div>
                                        <div class="card-inner mt-4">



                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-5"><div class="mb-4 p-6">


                                        <div class="sm:mt-0 sm:col-span-2">
                                            <div class="mb-8 p-6 bg-white border rounded-lg">
                                                <div class="card-inner">
                                                    <div class="col-span-6 sm:col-span-2">
                                                        <label class="form-label">Profil Resmi</label>
                                                        <div class="upload-zone" data-accepted-files="image/*" data-max-files="1">
                                                            <div class="dz-message" data-dz-message>
                                                                <div class="dz-image">
                                                                    {{-- Check if the user has an avatar and display it; otherwise, show a placeholder --}}
                                                                    @if($user->avatar)
                                                                        <img data-dz-thumbnail src="{{ asset($user->avatar) }}" alt="{{ __('Profile Image') }}">
                                                                    @else
                                                                        <img data-dz-thumbnail src="/images/default-avatar.png" alt="{{ __('Default Image') }}">
                                                                    @endif
                                                                </div>
                                                                <input type="hidden" id="type" value="avatar">
                                                                <span class="dz-message-text">Buraya dosya sürükle</span>
                                                                <span class="dz-message-or">veya</span>
                                                                <a class="btn btn-primary">DOSYA SEÇ</a>
                                                            </div>
                                                        </div>
                                                        <label class="block text-sm font-medium text-gray-700">
                                                            {{ __('Photo') }}
                                                        </label>

                                                    </div>

                                                    <div class="col-span-6">
                                                        <label class="block-inline text-sm font-medium text-gray-700" for="first_name">Email</label> <span class="text-danger text-red-600">*</span>
                                                        <input class="form-control" type="email" id="email" value="{{$user->email}}" disabled>
                                                    </div>
                                                    <div class="col-span-6">
                                                        {{ html()->label(__('Mobile'), 'mobile')->class('block-inline text-sm font-medium text-gray-700') }}
                                                        {{ html()->text('mobile')->placeholder(__('Mobile'))->class('form-control') }}
                                                    </div>
                                                    <div class="col-span-6 sm:col-span-3">
                                                        <?php

                                                        $value = ($user->date_of_birth == "") ? "" : \Carbon\Carbon::parse($user->date_of_birth)->toDateString();
                                                        ?>
                                                        {{ html()->label(__('Date of Birth'), 'date_of_birth')->class('block-inline text-sm font-medium text-gray-700') }}
                                                        {{ html()->text('date_of_birth')->type('date')->value($value)->placeholder(__('Date of Birth'))->class('form-control') }}
                                                    </div>
                                                    <div class="col-span-6 sm:col-span-3">
                                                        {{ html()->label(__('Gender'), 'gender')->class('block text-sm font-medium text-gray-700') }}
                                                        {{ html()->select('gender', [
                                                            'Female' => __('Female'),
                                                            'Male' => __('Male'),
                                                            'Other' => __('Other'),
                                                        ])->placeholder(__('Gender'))->class('form-select') }}
                                                    </div>
                                                </div>
                                                <div class="mt-4 px-4 bg-gray-50 text-end sm:px-6">
                                                    <button type="submit" class="btn btn-primary">
                                                        {{ __('Save') }}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div></div>
                            </div>



                            {{ html()->closeModelForm() }}
                            <div>


                        </div>
                    </div>







                        @include('frontend.users.menu')





                    </div><!-- .card -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>
</div>





@endsection