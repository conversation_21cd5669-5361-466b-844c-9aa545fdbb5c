<div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg"
     data-toggle-body="true" data-content="userAside" data-toggle-screen="lg"
     data-toggle-overlay="true">
    <div class="card-inner-group" data-simplebar>
        <div class="card-inner">
            <div class="user-card">
                <div class="user-avatar bg-primary">
                    <div class="user-avatar sq bg-transparent">
                        <img class="w-48 h-48 object-cover rounded-lg mx-auto -mb-24"
                             src="{{asset($user->avatar)}}"
                             alt="{{$user->name}}"/>
                    </div>
                </div>
                <div class="user-info">
                    <span class="lead-text">{{ $user->name }}</span>
                    <span class="sub-text">{{ $user->email }}</span>
                </div>
                <div class="user-action">
                    <div class="dropdown">
                        <a class="btn btn-icon btn-trigger me-n2"
                           data-bs-toggle="dropdown" href="#"><em
                                    class="icon ni ni-more-v"></em></a>
                        <div class="dropdown-menu dropdown-menu-end">
                            <ul class="link-list-opt no-bdr">
                                <li><a href="#"><em class="icon ni ni-camera-fill"></em><span>Resmi Değiştir</span></a>
                                </li>
                                <li>
                                    <a href="{{ route("frontend.users.profileEdit", encode_id($user->id)) }}"><em
                                                class="icon ni ni-edit-fill"></em><span>{{ __('Edit Profile') }}</span></a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div><!-- .user-card -->
        </div><!-- .card-inner -->
        <div class="card-inner">
            <div class="user-account-info py-0">
                <h6 class="overline-title-alt">{{ auth()->user()->active_entity->entity_name }}</h6>

            </div>
        </div><!-- .card-inner -->
        <div class="card-inner p-0">
            <ul class="link-list-menu">
                <li><a href="{{ route("frontend.users.profile", encode_id($user->id)) }}"><em class="icon ni ni-user-fill-c"></em><span>Kişisel Bilgiler</span></a>
                </li>
                @if (auth()->user()->id == $user->id)
                    <li>
                    <a href='{{ route("frontend.users.profileEdit", encode_id($user->id)) }}'>
                        <em class="icon ni ni-pen-fill"></em><span> {{ __('Edit Profile') }}</span>
                    </a>
                    </li>
                @endif
                <li><a href="{{ route("frontend.users.notification", encode_id($user->id)) }}"><em
                                class="icon ni ni-bell-fill"></em><span>Bildirimler</span></a>
                </li>
                <li><a href="{{ route("frontend.users.activity", encode_id($user->id)) }}"><em class="icon ni ni-activity-round-fill"></em><span>Hesap Hareketleri</span></a>
                </li>
                @if (auth()->user()->username == $user->username)
                    <li>
                        <a href="{{ route('frontend.users.changePassword', encode_id($user->id)) }}"><em
                                    class="icon ni ni-lock-alt-fill"></em><span>Güvenlik Ayarları</span></a>
                    </li>
                @endif
                <li><a class="active" href="#"><em
                                class="icon ni ni-grid-add-fill-c"></em><span>Diğer</span></a>
                </li>
            </ul>
        </div><!-- .card-inner -->
    </div><!-- .card-inner-group -->
</div><!-- card-aside -->