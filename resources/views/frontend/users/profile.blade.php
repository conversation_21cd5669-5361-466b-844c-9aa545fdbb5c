@extends('frontend.layouts.app')

@section('title') {{$user->name}} Profili @endsection

@section('content')

    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block">
                        <div class="card card-bordered">
                            <div class="card-aside-wrap">
                                <div class="card-inner card-inner-lg">
                                    <div class="nk-block-head nk-block-head-lg">
                                        <div class="nk-block-between">
                                            <div class="nk-block-head-content"><h4 class="nk-block-title">Kişisel Bilgiler</h4>
                                                <div class="nk-block-des"><p>B2B sitesi hesabınız ile ilgili işlemleri buradan görebilir ve gerek ayarlamaları yabilirsiniz
                                                    @if (auth()->user()->id == $user->id)
                                                            <a href='{{ route("frontend.users.profileEdit", encode_id($user->id)) }}'>
                                                                {{ __('Edit Profile') }}
                                                            </a>
                                                    @endif
                                                        .</p></div>
                                            </div>
                                            <div class="nk-block-head-content align-self-start d-lg-none">
                                                <a href="#" class="toggle btn btn-icon btn-trigger mt-n1" data-target="userAside"><em class="icon ni ni-menu-alt-r"></em></a></div>
                                        </div>
                                    </div>
                                    <div class="nk-block">
                                        <div class="nk-data data-list">
                                            <div class="data-head"><h6 class="overline-title">Temel</h6></div>
                                            <div class="data-item" data-bs-toggle="modal"
                                                 data-bs-target="#profile-edit">
                                                <div class="data-col"><span class="data-label">Ad Soyad</span><span
                                                            class="data-value">{{ $user->name }}</span></div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>
                                            <div class="data-item" data-bs-toggle="modal"
                                                 data-bs-target="#profile-edit">
                                                <div class="data-col"><span class="data-label">Görünen Ad</span><span
                                                            class="data-value">{{ $user->username }}</span></div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>
                                            <div class="data-item">
                                                <div class="data-col"><span class="data-label">Email</span><span
                                                            class="data-value">{{ $user->email }}</span></div>
                                                <div class="data-col data-col-end"><span class="data-more disable"><em
                                                                class="icon ni ni-lock-alt"></em></span></div>
                                            </div>
                                            @if (auth()->user()->id == $user->id)
                                            <div class="data-item" data-bs-toggle="modal"
                                                 data-bs-target="#profile-edit">
                                                <div class="data-col"><span class="data-label">{{ __('Mobile') }}</span><span
                                                            class="data-value text-soft">{{ $user->mobile }}</span></div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>
                                            <div class="data-item" data-bs-toggle="modal"
                                                 data-bs-target="#profile-edit">
                                                <div class="data-col"><span class="data-label">{{ __('Date of Birth') }}</span><span
                                                            class="data-value">@if($user->date_of_birth){{ $user->date_of_birth->toFormattedDateString() }}@endif</span></div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>
                                            <div class="data-item" data-bs-toggle="modal" data-bs-target="#profile-edit"
                                                 data-tab-target="#address">
                                                <div class="data-col"><span class="data-label">{{ __('Gender') }}</span><span
                                                            class="data-value">{{ $user->gender }}
                                                </div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>

                                                <div class="data-item" data-bs-toggle="modal" data-bs-target="#profile-edit"
                                                 data-tab-target="#address">
                                                <div class="data-col"><span class="data-label">{{ __('Address') }}</span><span
                                                            class="data-value">{{ $user->address }}
                                                </div>
                                                <div class="data-col data-col-end"><span class="data-more"><em
                                                                class="icon ni ni-forward-ios"></em></span></div>
                                            </div>
                                                @endif
                                        </div>
                                        <div class="nk-data data-list">
                                            <div class="data-head"><h6 class="overline-title">Ayarlar</h6></div>
                                            <div class="data-item">
                                                <div class="data-col"><span class="data-label">Dil</span><span
                                                            class="data-value">Türkçe</span></div>
                                                <div class="data-col data-col-end"><a href="#"
                                                                                      class="link link-primary">Dil Değiştir</a></div>
                                            </div>
                                            <div class="data-item">
                                                <div class="data-col"><span class="data-label">Tarih Formatı</span><span
                                                            class="data-value">A g, YYYY</span></div>
                                                <div class="data-col data-col-end"><a href="#"
                                                                                      class="link link-primary">Değiştir</a>
                                                </div>
                                            </div>


                                        </div>
                                    </div>
                                </div>


                                @include('frontend.users.menu')

                            </div><!-- .card-aside-wrap -->
                        </div><!-- .card -->
                    </div><!-- .nk-block -->
                </div>
            </div>
        </div>
    </div>






@endsection

@push ("after-scripts")
<script type="module" src="https://cdn.jsdelivr.net/npm/sharer.js@latest/sharer.min.js"></script>
@endpush