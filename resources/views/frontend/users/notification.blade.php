@extends('frontend.layouts.app')

@section('title') {{$$module_name_singular->name}} B<PERSON><PERSON><PERSON><PERSON> @endsection

@section('content')

    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="card card-bordered">
                        <div class="card-aside-wrap">
                            <div class="card-inner card-inner-lg">
                                <div class="nk-block-head nk-block-head-lg">
                                    <div class="nk-block-between">
                                        <div class="nk-block-head-content"><h4 class="nk-block-title">{{ __('Notification Settings') }}</h4>
                                            <div class="nk-block-des"><p>{{ __('You will get only notification what have enabled.') }}</p></div>
                                        </div>
                                        <div class="nk-block-head-content align-self-start d-lg-none"><a href="#"
                                                                                                         class="toggle btn btn-icon btn-trigger mt-n1"
                                                                                                         data-target="userAside"><em
                                                        class="icon ni ni-menu-alt-r"></em></a></div>
                                    </div>
                                </div>
                                <div class="nk-block-head nk-block-head-sm">
                                    <div class="nk-block-head-content"><h6>{{ __('Security Alerts') }}</h6>
                                        <p>{{ __('You will get only those email notification what you want.') }}</p></div>
                                </div>
                                <div class="nk-block-content">
                                    <div class="gy-3">
                                        <div class="g-item">
                                            <div class="custom-control custom-switch checked"><input type="checkbox"
                                                                                                     class="custom-control-input"
                                                                                                     checked=""
                                                                                                     id="unusual-activity"><label
                                                        class="custom-control-label" for="unusual-activity">{{ __('Notification') }}</label></div>
                                        </div>
                                        <div class="g-item">
                                            <div class="custom-control custom-switch"><input type="checkbox"
                                                                                             class="custom-control-input"
                                                                                             id="new-browser"><label
                                                        class="custom-control-label" for="new-browser">{{ __('Email me if new browser is used to sign in') }}</label></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="nk-block-head nk-block-head-sm">
                                    <div class="nk-block-head-content"><h6>{{ __('News') }}</h6>
                                        <p>{{ __('You will get only those email notification what you want.') }}</p></div>
                                </div>
                                <div class="nk-block-content">
                                    <div class="gy-3">
                                        <div class="g-item">
                                            <div class="custom-control custom-switch checked"><input type="checkbox"
                                                                                                     class="custom-control-input"
                                                                                                     checked=""
                                                                                                     id="latest-sale"><label
                                                        class="custom-control-label" for="latest-sale">{{ __('Notify me by email about sales and latest news') }}</label></div>
                                        </div>
                                        <div class="g-item">
                                            <div class="custom-control custom-switch"><input type="checkbox"
                                                                                             class="custom-control-input"
                                                                                             id="feature-update"><label
                                                        class="custom-control-label" for="feature-update">{{ __('Email me about new features and updates') }}</label></div>
                                        </div>
                                        <div class="g-item">
                                            <div class="custom-control custom-switch checked"><input type="checkbox"
                                                                                                     class="custom-control-input"
                                                                                                     checked=""
                                                                                                     id="account-tips"><label
                                                        class="custom-control-label" for="account-tips">{{ __('Email me about tips on using account') }}</label></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @include('frontend.users.menu')





                        </div><!-- .card -->
                    </div><!-- .nk-block -->
                </div>
            </div>
        </div>
    </div>






@endsection

@push ("after-scripts")
<script type="module" src="https://cdn.jsdelivr.net/npm/sharer.js@latest/sharer.min.js"></script>
@endpush