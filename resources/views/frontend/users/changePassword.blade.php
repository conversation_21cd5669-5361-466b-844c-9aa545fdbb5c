@extends('frontend.layouts.app')

@section('title')
    @lang("Change Password"): {{$$module_name_singular->name}}
@endsection

@section('content')

    <div class="container mx-auto flex justify-center">

        @include('frontend.includes.messages')

    </div>



    <div class="nk-content ">
        <div class="container-fluid">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block">
                        <div class="card card-bordered">
                            <div class="card-aside-wrap">

                                <div class="card-inner card-inner-lg">
                                    <div class="nk-block-head nk-block-head-lg">
                                        <div class="nk-block-between">
                                            <div class="nk-block-head-content"><h4 class="nk-block-title"><PERSON><PERSON><PERSON></h4>
                                                <div class="nk-block-des"><p> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>zın şifresini değiştirmek için aşağ<PERSON>daki formu kullanın!


                                                        </p></div>
                                            </div>
                                            <div class="nk-block-head-content align-self-start d-lg-none"><a href="#"
                                                                                                             class="toggle btn btn-icon btn-trigger mt-n1"
                                                                                                             data-target="userAside"><em
                                                            class="icon ni ni-menu-alt-r"></em></a></div>
                                        </div>
                                    </div>
                                    <div class="nk-block">
                                        @if (\Session::has('error'))
                                            <div class="alert alert-warning">
                                                <ul>
                                                    <li>{!! \Session::get('error') !!}</li>
                                                </ul>
                                            </div>
                                        @endif


                                        {{ html()->form('PATCH', route('frontend.users.changePasswordUpdate', encode_id($$module_name_singular->id)))->class('form-horizontal')->open() }}
                                        <div class="card mb-8 p-6 bg-white border rounded-lg">
                                            <div class="card-inner grid-cols-6 ">
                                                <div class="col-span-6 sm:col-span-3">
                                                    <?php
                                                    $required = "required";
                                                    ?>
                                                    {{ html()->label(__('Old Password'), 'current_password')->class('form-label') }} {!! fielf_required($required) !!}
                                                    {{ html()->password('current_password')->placeholder('Şimdiki şifreniz')->class('form-control')->attributes(["$required"]) }}
                                                </div>
                                                <hr>

                                                <div class="col-span-6 sm:col-span-3">
                                                    <?php
                                                    $field_name = 'password';
                                                    $field_lable = __('labels.backend.users.fields.' . $field_name);
                                                    $field_placeholder = $field_lable;
                                                    $required = "required";
                                                    ?>
                                                    {{ html()->label($field_lable, $field_name)->class('form-label') }} {!! fielf_required($required) !!}
                                                    {{ html()->password($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                                </div>
                                                <div class="col-span-6 sm:col-span-3">
                                                    <?php
                                                    $field_name = 'password_confirmation';
                                                    $field_lable = __('labels.backend.users.fields.' . $field_name);
                                                    $field_placeholder = $field_lable;
                                                    $required = "required";
                                                    ?>
                                                    {{ html()->label($field_lable, $field_name)->class('form-label') }} {!! fielf_required($required) !!}
                                                    {{ html()->password($field_name)->placeholder($field_placeholder)->class('form-control')->attributes(["$required"]) }}
                                                </div>
                                                <div class="col-span-6 px-4 py-3 bg-gray-50 text-end sm:px-6">
                                                    <button type="submit" class="btn btn-primary">
                                                        @lang('Update Password')
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        {{ html()->closeModelForm() }}
                                    </div>
                                </div>


                                @include('frontend.users.menu')



                            </div><!-- .card-aside-wrap -->
                        </div><!-- .card -->
                    </div><!-- .nk-block -->
                </div>
            </div>
        </div>
    </div>


@endsection