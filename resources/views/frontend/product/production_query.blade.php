@extends('frontend.layouts.app')

@section('title') {{ __('Production Query') }} {{app_name()}} @endsection
@section('css')
    <link rel="stylesheet" href="/assets/css/investment.css?v={{ time() }}">


<style>


</style>
    @endsection
@section('content')
    <div class="nk-content nk-content-lg nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-inner">
                <div class="nk-content-body">
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-head-content">
                            <div class="nk-block-head-sub"><a href="/stok-karti" class="back-to"><em class="icon ni ni-arrow-left"></em><span>{{ __('Go back') }}</span></a></div>
                            <div class="nk-block-head-content">
                                <h2 class="nk-block-title fw-normal">{{ __('Production Query') }}</h2>
                            </div>
                        </div>
                    </div><!-- nk-block-head -->
                    <div class="nk-block invest-block">
                        <form action="/uretim-sorgu" class="invest-form" method="POST">
                            @csrf
                            <div class="row g-gs">
                                <div class="col-lg-5">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">{{ __('DENSITY') }}</label>
                                                </div>
                                                <div class="form-control-wrap g-2">
                                                    <select class="form-select js-select2 form-select-lg" name="density" id="select_density" data-search="on">
                                                        <option value="0">{{ __('Select') }}</option>
                                                        @foreach($densities as $density)
                                                            <option value="{{$density->id}}" @if($density->id == $density_active){{ 'selected' }}@endif>{{ $density->description}}</option>
                                                        @endforeach
                                                    </select>

                                                </div>
                                            </div><!-- .invest-field -->

                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">{{ __('THICKNESS') }}</label>

                                                </div>
                                                <div class="form-control-wrap g-2">
                                                    <select class="form-select js-select2 form-select-lg" name="depth" id="select_depth"  data-search="on">
                                                        <option value="0">{{ __('Select') }}</option>
                                                        @foreach($depths as $depth)
                                                            <option value="{{$depth->id}}" @if($depth->id == $depth_active){{ 'selected' }}@endif>{{ $depth->description}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>


                                            </div><!-- .invest-field -->

                                            <div class="form-group">
                                                <div class="form-label-group">
                                                    <label class="form-label">{{ __('WIDTH X LENGTH') }}</label>

                                                </div>
                                                <div class="form-control-wrap g-2">
                                                    <select class="form-select js-select2 form-select-lg" name="wh" id="select_wh" data-search="on">
                                                        <option value="0">{{ __('Select') }}</option>
                                                        @foreach($whs as $wh)
                                                            <option value="{{$wh->id}}" @if($wh->id == $wh_active){{ 'selected' }}@endif>{{ $wh->description}} MM</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                            </div><!-- .invest-field -->

                                            <div class="text-center bg-lighter mt-5">
                                                <button type="submit" class="btn btn-primary btn-block ttu">{{ __('Query') }}</button>
                                            </div>

                                        </div>
                                    </div>

                                </div><!-- .col -->
                                <div class="col-lg-7 offset-xl-">
                                    <div class="card card-bordered ms-lg-4 ms-xl-0">


                                        <div class="nk-iv-wg4">
                                            @isset($products)
                                                @if($products->count()>0)
                                                    <div class="card card-bordered card-full">
                                                        <div class="nk-tb-list">
                                                            <div class="nk-tb-item nk-tb-head">
                                                                <div class="nk-tb-col"><span>{{ __('STOCK CODE') }}</span></div>
                                                                <div class="nk-tb-col"><span>{{ __('DENSITY') }}</span></div>
                                                                <div class="nk-tb-col"><span>{{ __('THICKNESS') }}</span></div>
                                                                <div class="nk-tb-col"><span>{{ __('WIDTH X LENGTH') }}</span></div>
                                                                <div class="nk-tb-col"><span>{{ __('Action') }}</span></div>
                                                            </div>

                                                            @foreach($products as $product)
                                                                @if($product->category5)
                                                                    <div class="nk-tb-item">
                                                                        <div class="nk-tb-col">
                                                                            {{ $product->item_code ?? '' }}
                                                                        </div>
                                                                        <div class="nk-tb-col">
                                                                            <a href="{{ request()->fullUrlWithQuery(['pid' => $product->id])  }}#item_units"> {{ $product->category5->categories_code ?? '' }}</a>
                                                                        </div>

                                                                        <div class="nk-tb-col tb-col-lg"><span
                                                                                    class="tb-sub">{{ $product->category6->description ?? '' }}</span></div>
                                                                        <div class="nk-tb-col"><span
                                                                                    class="tb-sub tb-amount">{{ $product->category7->description ?? '' }}</span>
                                                                        </div>

                                                                        <div class="nk-tb-col nk-tb-col-action">
                                                                            <div class="dropdown"><a
                                                                                        class="text-soft dropdown-toggle btn btn-sm btn-icon btn-trigger"
                                                                                        data-bs-toggle="dropdown"><em
                                                                                            class="icon ni ni-doticon ni ni-more-h"></em></a>
                                                                                <div class="dropdown-menu dropdown-menu-end dropdown-menu-xs">
                                                                                    <ul class="link-list-plain">
                                                                                        <li><a href="{{ request()->fullUrlWithQuery(['pid' => $product->id])  }}#item_units">{{ __('Unit Conversion') }}</a></li>

                                                                                    </ul>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                @endif
                                                            @endforeach

                                                        </div>
                                                    </div>
                                                @endif
                                                <div class="card-inner border-bottom">
                                                    <div class="card-title-group">
                                                        <div class="card-title"><h6 class="title">{{ __('Stock Unit Conversion') }}</h6></div>

                                                    </div>
                                                </div>
                                                @if($item_units->count()>0)
                                                    <table class="table table-iv-tnx" id="item_units">
                                                        <thead class="table-light">
                                                        <tr>
                                                            <th class="tb-col-type"><span class="overline-title">{{ __('Line No') }}</span>
                                                            </th>
                                                            <th class="tb-col-date"><span class="overline-title">{{ __('Unit Factor 1') }}</span>
                                                            </th>
                                                            <th class="tb-col-time tb-col-end"><span class="overline-title">{{ __('Unit Factor 2') }}</span>
                                                            </th>
                                                        </tr>
                                                        </thead>
                                                        <tbody>

                                                        @foreach($item_units as $item_unit)
                                                            <tr>
                                                                <td class="tb-col-type"><span class="sub-text">{{ $item_unit->line_no }}</span>
                                                                </td>
                                                                <td class="tb-col-date"><span class="sub-text">{{ $item_unit->rate }} {{ $item_unit->unit->unit_code }}</span>
                                                                </td>
                                                                <td class="tb-col-time tb-col-end"><span
                                                                            class="lead-text text-danger">{{ $item_unit->rate2 }} {{ $item_unit->unit2->unit_code }}</span></td>
                                                            </tr>
                                                        @endforeach

                                                        </tbody>
                                                    </table>
                                                @else
                                                    <p class="lead p-3">@isset($products)@if($products->count()>0) {{ __('Select product from above') }} @else {{ __('No products found for selected units. Click the button below to create a production query.') }} @endif @endisset</p>

                                                @endif

                                                @if($products->count() === 0)
                                                    <div class="nk-iv-wg4-sub text-center bg-lighter">
                                                        <a class="btn btn-lg btn-primary"  id="uretimSorgu">{{ __('Create Production Query') }}</a>
                                                    </div><!-- .nk-iv-wg4-sub -->
                                                @endif
                                            @endisset

                                        </div><!-- .nk-iv-wg4 -->
                                    </div><!-- .card -->
                                </div><!-- .col -->



                            </div><!-- .row -->
                        </form>
                    </div><!-- .nk-block -->
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script src="/assets/js/libs/datatable-btns.js?ver=3.2.3"></script>
    <script>
$(document).ready(function() {
    $(document).on('click', '#uretimSorgu', function(e) {
        e.preventDefault();
        var url = '/uretim-sorgu-olustur';
        var warning = $(this).data('warning');


        Swal.fire({
            title: '{{ __('Are you sure you want to create a production query?') }}',
            showDenyButton: true,

            showCancelButton: true,
            confirmButtonText: 'Evet',
            denyButtonText: 'Hayır',
            cancelButtonText: '<p style=> {{ __('Cancel') }} </p>',
            customClass: {
                actions: 'my-actions',
                cancelButton: 'order-1 right-gap',
                confirmButton: 'order-2',
                denyButton: 'order-3',
            },
        }).then((result) => {
            if (result.isConfirmed) {
                $("#overlay").fadeOut(200); $("#overlay").fadeOut(100);
                $.ajax({
                    method: 'POST',
                    url: url,
                    data: {
                        select_density: $('#select_density option:selected').text(),
                        select_depth: $('#select_depth option:selected').text(),
                        select_wh: $('#select_wh option:selected').text(),


                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    dataType: 'json',
                    success: function(result) {
                    $("#overlay").fadeOut(100);
                        if (result.success) {
                            Swal.fire(result.message, '', 'success')
                            data_table.ajax.reload(null, false);
                        } else {
                            Swal.fire(result.message, '', 'error')
                            data_table.ajax.reload(null, false);
                        }
                    }
                });
            } else if (result.isDenied) {
                Swal.fire('Değişiklik uygulanamadı', '', 'info')
            }
        })

    });
});</script>
@endsection