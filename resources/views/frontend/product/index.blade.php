@extends('frontend.layouts.app')

@section('title') {{ __('Stock Cards') }} {{app_name()}} @endsection
@section('css')

    @endsection
@section('content')
    <div class="page-content">
        <div class="container-fluid">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h3 class="nk-block-title page-title">{{ __('Stock Cards') }}</h3>
                    </div><!-- .nk-block-head-content -->
                    <div class="nk-block-head-content">
                        <div class="toggle-wrap nk-block-tools-toggle">
                            <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="more-options"><em class="icon ni ni-more-v"></em></a>
                            <div class="toggle-expand-content" data-content="more-options">

                            </div>
                        </div>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div><!-- .nk-block-head -->

            <div class="row">


                <div class="col-xl-12">
                    <div class="card">


                        <!-- end card header -->
                        <div class="card-body">

                            <div class="tab-content text-muted">
                                <div class="tab-pane active" id="productnav-all" role="tabpanel">
                                    <table class="datatable-init nk-tb-list nk-tb-ulist dataTable no-footer"
                                           data-auto-responsive="false" id="DataTables_Table_1"
                                           aria-describedby="DataTables_Table_1_info">
                                        <thead>
                                        <tr class="nk-tb-item nk-tb-head">
                                            <th class="nk-tb-col nk-tb-col-check sorting sorting_asc" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-sort="ascending"
                                                aria-label=": activate to sort column descending">
                                                <div class="custom-control custom-control-sm custom-checkbox notext">
                                                    <input type="checkbox" class="custom-control-input"
                                                           id="uid"><label class="custom-control-label"
                                                                           for="uid"></label></div>
                                            </th>
                                            <th class="nk-tb-col sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="User: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Product') }}</span></th>
                                            <th class="nk-tb-col tb-col-mb sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="Balance: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Price') }}</span></th>
                                            <th class="nk-tb-col tb-col-md sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="Phone: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Unit') }}</span></th>
                                            <th class="nk-tb-col tb-col-lg sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="Verified: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Stock Status') }}</span></th>
                                            <th class="nk-tb-col tb-col-lg sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="Last Login: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Last Order') }}</span></th>
                                            <th class="nk-tb-col tb-col-md sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label="Status: activate to sort column ascending"><span
                                                        class="sub-text">{{ __('Status') }}</span></th>
                                            <th class="nk-tb-col nk-tb-col-tools text-end sorting" tabindex="0"
                                                aria-controls="DataTables_Table_1" rowspan="1" colspan="1"
                                                aria-label=": activate to sort column ascending"></th>
                                        </tr>
                                        </thead>

                                        <tbody>
                                        @foreach($products as $product)
                                            <tr class="nk-tb-item odd">
                                                <td class="nk-tb-col nk-tb-col-check sorting_1">
                                                    <div class="custom-control custom-control-sm custom-checkbox notext">
                                                        <input type="checkbox" class="custom-control-input"
                                                               id="uid1"><label class="custom-control-label"
                                                                                for="uid1"></label></div>
                                                </td>
                                                <td class="nk-tb-col">
                                                    <div class="user-card">
                                                        <div class="user-avatar bg-dim-primary d-none d-sm-flex"><span>AB</span>
                                                        </div>
                                                        <div class="user-info"><span
                                                                    class="tb-lead">{{ $product->item_code }} <span
                                                                        class="dot dot-success d-md-none ms-1"></span></span><span>{{ $product->item_name }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="nk-tb-col tb-col-mb" data-order="35040.34"><span class="tb-amount">0 <span class="currency">TL</span></span>
                                                </td>
                                                <td class="nk-tb-col tb-col-md"><span>{{ $product->unit->unit_name }}</span></td>
                                                <td class="nk-tb-col tb-col-lg"
                                                    data-order="Email Verified - Kyc Unverified">
                                                    <ul class="list-status">
                                                        <li><em class="icon text-success ni ni-check-circle"></em>
                                                            <span>{{ __('Available') }}</span></li>
                                                        <li><em class="icon ni ni-alert-circle"></em> <span>{{ __('Warehouse') }}</span>
                                                        </li>
                                                    </ul>
                                                </td>
                                                <td class="nk-tb-col tb-col-lg"><span> </span></td>
                                                <td class="nk-tb-col tb-col-md"><span class="tb-status text-success">{{ __('Active') }}</span>
                                                </td>
                                                <td class="nk-tb-col nk-tb-col-tools">
                                                    <ul class="nk-tb-actions gx-1">

                                                        <div class="dropdown"><a href="#"
                                                                                 class="dropdown-toggle btn btn-icon btn-trigger"
                                                                                 data-bs-toggle="dropdown"><em
                                                                        class="icon ni ni-more-h"></em></a>
                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                <ul class="link-list-opt no-bdr">
                                                                    <li><a href="#" data-bs-target="#modalDefault" data-bs-toggle="modal"><em
                                                                                    class="icon ni ni-focus"></em><span>{{ __('Quick View') }}</span></a>
                                                                    </li>
                                                                    <li><a href="#"><em class="icon ni ni-eye"></em><span>{{ __('Product Details') }}</span></a>
                                                                    </li>
                                                                    <li><a href="#"><em
                                                                                    class="icon ni ni-repeat"></em><span>{{ __('Operations') }}</span></a>
                                                                    </li>

                                                                </ul>
                                                            </div>
                                                        </div>
                                                        </li>
                                                    </ul>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <!-- end tab pane -->

                                <div class="modal fade" tabindex="-1" id="modalDefault">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content"><a href="#" class="close" data-dismiss="modal"
                                                                      aria-label="Close"> <em class="icon ni ni-cross"></em>
                                            </a>
                                            <div class="modal-header"><h5 class="modal-title">{{ __('Product Information') }}</h5></div>
                                            <div class="modal-body"><p>{{ __('Information about this product Lorem ipsum dolor sit amet consectetur adipisicing elit. Voluptatem similique earum necessitatibus nesciunt! Quia id expedita asperiores voluptatem odit quis fugit sapiente assumenda sunt voluptatibus atque facere autem, omnis explicabo.') }}</p></div>
                                            <div class="modal-footer bg-light"><span
                                                        class="sub-text">{{ __('Last update') }}</span></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="modal fade" tabindex="-1" id="modalAlert">
                                    <div class="modal-dialog" role="document">
                                        <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"><em class="icon ni ni-cross"></em></a>
                                            <div class="modal-body modal-body-lg text-center">
                                                <div class="nk-modal"><em
                                                            class="nk-modal-icon icon icon-circle icon-circle-xxl ni ni-check bg-success"></em><h4
                                                            class="nk-modal-title">{{ __('Added to Cart!') }}</h4>
                                                    <div class="nk-modal-text">
                                                        <div class="caption-text">{{ __('Added') }} <strong>0.5968</strong> {{ __('tons of this product to cart. Ton price') }} <strong>200.00</strong>
                                                            USD
                                                        </div>
                                                        <span class="sub-text-sm">{{ __('Click') }} <a href="#"> {{ __('here') }}</a> {{ __('to view cart') }}</span>
                                                    </div>
                                                    <div class="nk-modal-action"><a href="#" class="btn btn-lg btn-mw btn-primary"
                                                                                    data-bs-dismiss="modal">{{ __('OK') }}</a></div>
                                                </div>
                                            </div>

                                        </div>
                                    </div>
                                </div>

                                <div class="tab-pane" id="productnav-published" role="tabpanel">
                                    <div id="table-product-list-published" class="table-card gridjs-border-none"></div>
                                </div>
                                <!-- end tab pane -->

                                <div class="tab-pane" id="productnav-draft" role="tabpanel">
                                    <div class="py-4 text-center">
                                        <lord-icon src="https://cdn.lordicon.com/msoeawqm.json" trigger="loop" colors="primary:#405189,secondary:#0ab39c" style="width:72px;height:72px">
                                        </lord-icon>
                                        <h5 class="mt-4">{{ __('No Result Found') }}</h5>
                                    </div>
                                </div>
                                <!-- end tab pane -->
                            </div>
                            <!-- end tab content -->

                        </div>
                        <!-- end card body -->
                    </div>
                </div>
                <!-- end col -->
            </div>
            <!-- end row -->

        </div>
        <!-- container-fluid -->
    </div>
@endsection

@section('js')
    <script src="/assets/js/libs/datatable-btns.js?ver=3.2.3"></script>

@endsection