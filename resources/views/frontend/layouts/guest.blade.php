
<!DOCTYPE html>
<html lang="tr" class="js">

<head>
    <base href="../../">
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <!-- Fav Icon  -->
    <link rel="shortcut icon" href="/images/favicon.png">
    <!-- Page Title  -->
    <title>@yield('title')</title>
    <!-- StyleSheets  -->
    <link rel="stylesheet" href="/assets/css/main.min.css">
    <link id="skin-default" rel="stylesheet" href="/assets/css/theme.css">
    @yield('css')
</head>

<body class="nk-body ui-rounder no-touch nk-nio-theme">
<div class="nk-app-root">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- wrap @s -->
        <div class="nk-wrap ">
            @auth
          @include('frontend.includes.header')
            @endauth
            <!-- main header @e -->
            <!-- content @s -->
            <div class="nk-content ">
                <div class="container">
                    <div class="nk-content-inner">

                        <div class="nk-content-body">
                            @yield('content')

                        </div>
                    </div>

                </div>
            </div>

            <!-- content @e -->
        </div>
        <!-- wrap @e -->

        <!-- footer @s -->
        <div class="nk-footer nk-footer-fluid bg-lighter">
            <div class="container wide-xl">
                <div class="nk-footer-wrap g-2">
                    <div class="nk-footer-copyright"> &copy; {{ date('Y') }} Akdağ Yalıtım A.Ş. Tüm hakları saklıdır.
                    </div>
                    <div class="nk-footer-links">
                        <ul class="nav nav-sm">
                            <li class="nav-item">
                                <a href="/" class="nav-link">Ana Sayfa</a>
                            </li>
                            <li class="nav-item dropup">
                                <a href="#" class="dropdown-toggle dropdown-indicator has-indicator nav-link text-base" data-bs-toggle="dropdown" data-offset="0,10"><span>  @if ( Config::get('app.locale') == 'en')
                                            {{ 'English' }}
                                        @elseif ( Config::get('app.locale') == 'tr' )
                                            {{ 'Türkçe' }}
                                        @endif</span></a>
                                <div class="dropdown-menu dropdown-menu-sm dropdown-menu-end">
                                    <ul class="language-list">
                                        @foreach(config('app.available_locales') as $locale_code => $locale_name)    <li>
                                            <a href="{{route('language.switch', $locale_code)}}" class="language-item">
                                                <span class="language-name">{{ $locale_name }}</span>
                                            </a>
                                        </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <!-- footer @e -->
    </div>
    <!-- main @e -->
</div>
<!-- app-root @e -->
<!-- select region modal -->
 <!-- .modal -->
<!-- JavaScript -->
<script src="/assets/js/bundle.js?ver=3.2.3"></script>
<script src="/assets/js/scripts.js?ver=3.2.3"></script>
</body>

</html>