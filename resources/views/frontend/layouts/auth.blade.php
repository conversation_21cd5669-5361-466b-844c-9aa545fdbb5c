<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}"  class="js">

<head>
    <meta charset="utf-8" />
    <link rel="apple-touch-icon" sizes="76x76" href="{{asset('img/favicon.png')}}">
    <link rel="icon" type="image/png" href="{{asset('img/favicon.png')}}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>@yield('title') | {{ config('app.name') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{{ setting('meta_description') }}">
    <meta name="keyword" content="{{ setting('meta_keyword') }}">
    @include('frontend.includes.meta')

    <!-- Shortcut Icon -->
    <link rel="shortcut icon" href="{{url('images/favicon.png')}}">
    <link rel="icon" type="image/ico" href="{{url('images/favicon.png')}}" />

    <!-- CSRF Token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <link rel="stylesheet" href="/assets/css/main.css">
    <link id="skin-theme" rel="stylesheet" href="/assets/css/skins/theme-green.css?v=3">

    <style>
        body {

            overflow: hidden;

            background-image:url(images/login-bg-3.jpg);
            background-position: top;
            background-size: 1920px 1200px;
            background-repeat:no-repeat;

            background-size:cover;
        }
        .login-bg1::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            /*background: rgba(6, 10, 48, 0.85);*/
        }

        .page-header .breadcrumb-icon {
            margin-top: 3px;
            margin-left: 4px;
        }
        .page-header .breadcrumb-item {
            margin-top: 3px;
        }
        .page-header .breadcrumb-item .svg-icon {
            width: 20px;
        }
        .page-header .breadcrumb-item:first-child {
            margin-top: 0;
        }


        .page-subtitle a {
            color: inherit;
        }

        .form-label {
            color: azure;
        }
        .page-description a {
            color: inherit;
        }

        .page-single, .page-single-content {
            flex: 1 1 auto;
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>

<body class="nk-body bg-white npc-default pg-auth">
<div class="nk-app-root page login-bg1">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- wrap @s -->
        <div class="nk-wrap nk-wrap-nosidebar">
            <!-- content @s -->
            <div class="nk-content ">
                <div class="nk-split nk-split-page nk-split-lg">
                    @yield('content')

                </div><!-- .nk-split -->
            </div>
            <!-- wrap @e -->
        </div>
        <!-- content @e -->
    </div>
    <!-- main @e -->
</div>
<!-- JavaScript -->
<script src="/assets/js/bundle.js"></script>
<script src="/assets/js/scripts.js?v={{ time() }}"></script>


</body>

</html>