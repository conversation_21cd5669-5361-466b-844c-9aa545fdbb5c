<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" class="js">
<head>
    <meta charset="utf-8"/>
    <link rel="apple-touch-icon" sizes="76x76" href="{{asset('img/favicon.png')}}">
    <link rel="icon" type="image/png" href="{{asset('img/favicon.png')}}">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/>
    <title>@yield('title') | {{ config('app.name') }}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="{{ setting('meta_description') }}">
    <meta name="keyword" content="{{ setting('meta_keyword') }}">
    <link rel="shortcut icon" href="{{url('images/favicon.png')}}">
    <link rel="icon" type="image/ico" href="{{url('images/favicon.png')}}"/>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="stylesheet" href="/assets/css/main.css">
    <link id="skin-theme" rel="stylesheet" href="/assets/css/theme.css?v={{ time() }}">
    @stack('css')
    @yield('css')
</head>

<body class="nk-body bg-lighter ui-rounder npc-general has-sidebar no-touch nk-nio-theme ui-shady @isset(Auth::user()->dark_mode){{ 'dark-mode' }}@endisset">
<div id="overlay">
    <div class="cv-spinner">
        <span class="spinner"></span>
    </div>
</div>
<div class="nk-app-root">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- wrap @s -->
        @include('frontend.includes.sidebar')
        <div class="nk-wrap ">
            @include('frontend.includes.header')
            <!-- main header @e -->
            <!-- content @s -->
            <div class="nk-content ">
                @yield('content')
            </div>
            <!-- content @e -->
            <!-- footer @s -->
            @include('frontend.includes.footer')
            <!-- footer @e -->
        </div>
        <!-- wrap @e -->
    </div>
    <!-- main @e -->
</div>
<div class="modal fade" id="modal_container"></div>

<script src="/assets/js/bundle.js?v=14"></script>
<script src="/assets/js/scripts.js?v={{ time() }}"></script>
@yield('js')
<script>

    $(document).on('click', '.btn-modal', function (e) {
        e.preventDefault();
        var container = $(this).data('container');
        $("#overlay").fadeIn(150);
        $.ajax({
            url: $(this).data('href'),
            dataType: 'html',
            success: function (result) {
                $("#overlay").fadeOut(200);
                $(container)
                    .html(result)
                    .modal('show');
              //  $('[data-bs-toggle="tooltip"]').tooltip()
                // NioApp.DataTable('.datatable-init', {
                //     responsive: {
                //         details: false
                //     },
                //
                // });
            },
        });
    });

    // $('#modal_container').on('shown.bs.modal', function (e) {
    //     var active_entity_id = $('#active_entity_id');
    //     NioApp.Select2(active_entity_id);
    //     active_entity_id.select2({dropdownParent: $(this)});
    //     })


    $(document).on('submit', 'form#change_active_entity_form', function (e) {
        e.preventDefault();
        var form = $("form#change_active_entity_form")[0];
        var data = new FormData(form);
        var url = $(this).attr("action");
        var method = $(this).attr("method");
        $.ajax({
            method: method,
            url: url,
            data: data,
            dataType: "json",
            processData: false,
            contentType: false,
            success: function (result) {
                if (result.success == true) {
                    $('div#modal_container').modal('hide');
                    toastr.clear();
                    NioApp.Toast(result.message, 'info', {position: 'top-center'});
                    $('#active_entity').html(result.entity_name);
                    if (result.redirect) {
                        setTimeout(function () {
                            window.location.href = result.redirect;
                        }, 2000);
                    }
                } else {
                    toastr.clear();
                    NioApp.Toast(result.msg, 'error', {position: 'top-center'});
                }
            }
        });
    });


    @if (\Session::has('error'))
    $(document).ready(function () {
    NioApp.Toast({!! \Session::get('error') !!}, 'warning', {position: 'top-center'});
    });
    @endif

    @if (\Session::has('success'))
    $(document).ready(function () {
        NioApp.Toast({!! \Session::get('success') !!}, 'info', {position: 'top-center'});
    });
    @endif


</script>
</body>
</html>