<div class="nk-footer nk-footer-fluid bg-lighter">
    <div class="container-xl">
        <div class="nk-footer-wrap">
            <div class="nk-footer-copyright"> &copy;2024 Akdağ Yalıtım A.Ş. {{ __('All rights reserved.') }}
            </div>
            <div class="nk-footer-links">
                <ul class="nav nav-sm">

                    <li class="nav-item">
                        <a href="/iletisim" class="nav-link">{{ __('Contact') }}</a>
                    </li>

                    <li class="nav-item dropup">
                        <a href="#" class="dropdown-toggle dropdown-indicator has-indicator nav-link text-base" data-bs-toggle="dropdown" data-offset="0,10"><span>  @if ( App::isLocale('en'))
                                    {{ 'English' }}
                                @else
                                    {{ 'Türkçe' }}
                                @endif</span></a>
                        <div class="dropdown-menu dropdown-menu-sm dropdown-menu-end">
                            <ul class="language-list">
                                @foreach(config('app.available_locales') as $locale_code => $locale_name)    <li>
                                    <a href="{{route('language.switch', $locale_code)}}" class="language-item">
                                        <span class="language-name">{{ $locale_name }}</span>
                                    </a>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                    </li>

                </ul>
            </div>
        </div>
    </div>
</div>
