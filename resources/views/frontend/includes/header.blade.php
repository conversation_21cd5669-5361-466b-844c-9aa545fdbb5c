<?php
$notifications = optional(auth()->user())->unreadNotifications;
$notifications_count = optional($notifications)->count();
$notifications_latest = optional($notifications)->take(5);
?>
<div class="nk-header nk-header-fixed is-light">
    <div class="container-fluid">
        <div class="nk-header-wrap">
            <div class="nk-menu-trigger d-xl-none ms-n1">
                <a href="#" class="nk-nav-toggle nk-quick-nav-icon" data-target="sidebarMenu"><em class="icon ni ni-menu"></em></a>
            </div>
            <div class="nk-header-brand d-xl-none">
                <a href="/" class="logo-link">
                    <img class="logo-light logo-img" src="/images/logo.png" srcset="/images/<EMAIL> 2x" alt="logo">
                    <img class="logo-dark logo-img" src="/images/logo-dark.png" srcset="/images/logo-dark2x.png 2x" alt="logo-dark">
                </a>
            </div><!-- .nk-header-brand -->
            <div class="nk-header-news d-none d-xl-block">
                <div class="nk-news-list">
{{--                    @isset($latest_announcement)--}}
{{--                    <a class="nk-news-item" href="{{'/duyuru'}}">--}}
{{--                        <div class="nk-news-icon">--}}
{{--                            <em class="icon ni ni-card-view"></em>--}}
{{--                        </div>--}}
{{--                        <div class="nk-news-text">--}}
{{--                            <p>{{ \Str::limit($latest_announcement->content, 33, ' (...)') }}</p>--}}
{{--                            <em class="icon ni ni-external"></em>--}}
{{--                        </div>--}}
{{--                    </a>--}}
{{--                    @endisset--}}
                </div>
            </div><!-- .nk-header-news -->
            <div class="nk-header-tools">
                <ul class="nk-quick-nav">
                    <li class="language-dropdown d-none d-sm-block me-n1">
                        <a href="/takvim" class="nk-quick-nav-icon">
                            <div class="icon-status icon-status-warning"><em class="icon ni ni-calendar"></em></div>
                        </a>
                    </li>
                    <li class="language-dropdown d-none d-sm-block me-n1">
                        <a href="/siparis/olustur?action=sepet" class="nk-quick-nav-icon">
                            <div class="icon-status icon-status-na"><em class="icon ni ni-cart-fill"></em></div>
                        </a>
                    </li>
                    @auth

                        <li class="dropdown notification-dropdown">
                            <a href="#" class="dropdown-toggle nk-quick-nav-icon" data-bs-toggle="dropdown">
                                <div class="icon-status icon-status-success"><em class="icon ni ni-bell"></em></div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-xl dropdown-menu-end dropdown-menu-s1">
                                <div class="dropdown-head">
                                    <span class="sub-title nk-dropdown-title">@lang("You have :count notifications", ['count'=>$notifications_count])</span>
                                    <a href="#">{{ __('Mark All as Read') }}</a>
                                </div>
                                <div class="dropdown-body">
                                    <div class="nk-notification">


                                        @if($notifications_latest)
                                            @foreach($notifications_latest as $notification)
                                                @php
                                                    $notification_text = isset($notification->data['title'])? $notification->data['title'] : $notification->data['module'];
                                                @endphp

                                                <div class="nk-notification-item dropdown-inner">
                                                    <div class="nk-notification-icon">
                                                        <em class="icon icon-circle bg-warning-dim ni {{isset($notification->data['icon'])? $notification->data['icon'] : 'ni-curve-down-right'}}"></em>
                                                    </div>
                                                    <div class="nk-notification-content">
                                                        <div class="nk-notification-text">{{ $notification_text }}</div>
                                                        <div class="nk-notification-time">2 saat önce</div>
                                                    </div>
                                                </div>
                                            @endforeach
                                        @endif

                                        <div class="nk-notification-item dropdown-inner">
                                            <div class="nk-notification-icon">
                                                <em class="icon icon-circle bg-success-dim ni ni-curve-up-left"></em>
                                            </div>
                                            <div class="nk-notification-content">

                                            </div>
                                        </div>

                                    </div><!-- .nk-notification -->
                                </div><!-- .nk-dropdown-body -->
                                <div class="dropdown-foot center">
                                    <a href="#">{{ __('View All') }}</a>
                                </div>
                            </div>
                        </li><!-- .dropdown -->

                        <li class="dropdown user-dropdown order-sm-last">
                            <a href="#" class="dropdown-toggle" data-bs-toggle="dropdown">
                                <div class="user-toggle">
                                    <div class="user-avatar sm">
                                        @empty(auth()->user()->avatar)
                                            <em class="icon ni ni-user-alt"></em>
                                        @else
                                            <img src="{{ asset(auth()->user()->avatar) }}" width="50">
                                            <div class="status dot dot-lg dot-success"></div>
                                        @endempty

                                    </div>
                                    <div class="user-info d-none d-xl-block">
                                        <div class="user-status" id="active_entity">@empty(auth()->user()->active_entity){{ 'Firma seç' }}@else{{ limit_words(auth()->user()->active_entity->entity_name,3) }}@endempty</div>
                                        <div class="user-name dropdown-indicator">{{ auth()->user()->name }}</div>
                                    </div>
                                </div>
                            </a>
                            <div class="dropdown-menu dropdown-menu-md dropdown-menu-end dropdown-menu-s1 is-light">
                                <div class="dropdown-inner user-card-wrap bg-lighter d-none d-md-block">
                                    <div class="user-card">
                                        <div class="user-avatar sq">
                                            @empty(auth()->user()->avatar)
                                                <span>HC</span>
                                            @else
                                                <img src="{{ asset(auth()->user()->avatar) }}" width="50">
                                            @endempty
                                        </div>
                                        <div class="user-info">
                                            <span class="lead-text">{{ Auth::user()->name }}</span>
                                            <span class="sub-text">{{ Auth::user()->email }}</span>
                                        </div>
                                        <div class="user-action">
                                            <a class="btn btn-icon me-n2 toggle" data-target="settingPanel" href="#"><em class="icon ni ni-setting"></em></a>
                                        </div>

                                    </div>
                                </div>

                                <div class="dropdown-inner user-account-info">
                                    <h6 class="overline-title-alt">Sözleşmeler Kalan Tutarı</h6>
                                    <div class="user-balance">{{ format_money(auth()->user()->active_entity->active_contracts->sum('remaining_amount')) }} <small class="currency currency-usd">TRY</small></div>
                                    <div class="user-balance-sub">{{ __('Used Amount') }} <span>{{ format_money(auth()->user()->active_entity->active_contracts->sum('used_amount')) }} <span class="currency currency-usd">TRY</span></span></div>
                                    <a href="#" data-href="{{ action('App\Http\Controllers\Frontend\EntityController@change_entity') }}" data-container="#modal_container" class="btn-modal link"><span>Aktif Cari Değiştir</span> <em class="icon ni ni-wallet-out"></em></a>
                                </div>
                                <div class="dropdown-inner">
                                    <ul class="link-list">
                                        @can('view_backend')
                                            <li> <a href='{{ route("backend.dashboard") }}' role="menuitem">
                                                    <i class="icon ni ni-setting-alt"></i>&nbsp;{{__('Admin Dashboard')}}
                                                </a></li>
                                        @endif
                                        <li><a href="{{ route('frontend.users.profile', encode_id(auth()->user()->id)) }}"><em class="icon ni ni-user-alt"></em><span>{{ __('View Profile') }}</span></a></li>
                                        <li><a href="{{ route('frontend.users.profileEdit', encode_id(auth()->user()->id)) }}"><em class="icon ni ni-setting-alt"></em><span>{{__('Settings')}}</span></a></li>
                                        <li><a class="dark-switch" href="#"><em class="icon ni ni-moon"></em><span>{{ __('Dark Mode') }}</span></a></li>
                                    </ul>
                                </div>
                                <div class="dropdown-inner">
                                    <ul class="link-list">
                                        <li><a href="{{ route('logout') }}" onclick="event.preventDefault(); document.getElementById('logout-form').submit();" ><em class="icon ni ni-signout"></em><span>{{__('Logout')}}</span></a></li>
                                    </ul>
                                    <form id="logout-form" action="{{ route('logout') }}" method="POST" style="display: none;">
                                        {{ csrf_field() }}
                                    </form>
                                </div>
                            </div>
                        </li><!-- .dropdown -->
                    @else
                        <li class=" order-sm-last">
                            <a href="/login" >
                                <div class="user-toggle">
                                    <div class="user-avatar sm">
                                        <em class="icon ni ni-user-alt"></em>
                                    </div>
                                    <div class="user-info d-none d-xl-block">
                                        <div class="user-status">{{__('Guest')}}</div>
                                        <div class="user-name">{{__('Sign in')}}</div>
                                    </div>
                                </div>
                            </a>

                        </li><!-- .dropdown -->
                    @endauth
                </ul><!-- .nk-quick-nav -->
            </div><!-- .nk-header-tools -->
        </div><!-- .nk-header-wrap -->
    </div><!-- .container-fliud -->
</div>

