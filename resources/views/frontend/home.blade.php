@extends('frontend.layouts.app')

@section('title') {{app_name()}} @endsection
@section('css')
    <style>
        .modal-dialog {
            max-height: 80vh; overflow-y: auto;
        }</style>
@endsection

@section('content')

    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">{{ __('Dealer Portal') }}</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ __('ACTIVE ACCOUNT') }}: <b>{{ auth()->user()->active_entity->entity_name }}</b></p>
                            </div>
                        </div><!-- .nk-block-head-content -->
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-more-v"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li class="nk-block-tools-opt"><a href="/siparis/rapor" class="btn btn-white btn-dim btn-outline-light"><em class="icon ni ni-reports"></em><span>{{ __('Order Report') }}</span></a></li>
                                        <li>
                                            <a href="{{ route('order.shipment_report')}}" class="btn btn-icon btn-dim btn-secondary d-md-none">
                                                <em class="icon ni ni-truck"></em></a>
                                            <a href="{{ route('order.shipment_report')}}" class="btn btn-dim btn-secondary d-none d-md-inline-flex"><em class="icon ni ni-truck"></em><span>{{ __('Order Shipment Report') }}</span>
                                            </a>
                                        </li>
                                        <li>
                                            <a href="/siparis/olustur" class="dropdown-toggle btn btn-primary"><em class="d-none d-sm-inline icon ni ni-plus-circle"></em><span><span class="d-none d-md-inline">{{ __('Order') }}</span> {{ __('Add') }}</span><em class="dd-indc icon ni ni-chevron-right"></em></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->
                <div class="nk-block">
                    <div class="row g-gs">
                        <div class="col-xxl-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner border-bottom">
                                    <div class="card-title-group">
                                        <div class="card-title"><h6 class="title">{{ __('Contract Information') }}</h6></div>
                                        <div class="card-tools"><a href="/sozlesme" class="link">{{ __('View All') }}</a></div>
                                    </div>
                                </div>
                                <div class="card-inner p-0">
                                    <table class="table table-tranx">
                                        <thead>
                                        <tr class="tb-tnx-head">
                                            <th class="tb-tnx-id">
                                                <span>{{ __('Contract Number') }}</span>
                                            </th>
                                            <th>
                                                <span class=" d-none d-sm-inline-block">{{ __('Ton Price') }}</span>
                                            </th>
                                            <th>
                                                <span>{{ __('Date') }}</span>
                                            </th>
                                            <th class="is-alt">
                                                <span>{{ __('Shipment') }}</span>
                                            </th>
                                            <th class="is-alt">
                                                <span>{{ __('Remaining Amount') }}</span>
                                            </th>
                                            <th>
                                                <span class=" d-none d-md-inline-block">{{ __('Status') }}</span>
                                            </th>
                                            <th class="tb-tnx-action">
                                                <span>&nbsp;</span>
                                            </th>
                                        </tr><!-- tb-tnx-item -->
                                        </thead>
                                        <tbody>
                                        @foreach($contracts as $contract)
                                            <tr class="tb-tnx-item">
                                                <td class="tb-tnx-id"><a href="#" data-href="sozlesme/{{ encode_id($contract->id) }}?action=contract_details" data-container="#modal_container" class="btn-modal"><span data-toggle="tooltip" title="{{ $contract->note_large }}">{{ $contract->doc_no }}</span></a></td>
                                                <td><div><span class="title">{{ Number::currency($contract->zz_ton_price,'TRY','tr') }}</span></div></td>
                                                <td><div><span class="date">{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</span></div></td>
                                                <td class="is-alt"><span>{{ Number::currency($contract->shipping_amount,'TRY','tr') }}</span></td>
                                                <td class="is-alt"><span class="badge badge-dim bg-danger" data-bs-toggle="tooltip" data-bs-placement="top" data-bs-title="{{ __('Contract amount') }}: {{ Number::currency($contract->amt,'TRY','tr') }}">{{ Number::currency($contract->remaining_amount,'TRY','tr') }}</span></td>
                                                <td data-bs-toggle="tooltip" data-bs-placement="left" data-bs-title="%{{ number_format((($contract->amt-$contract->remaining_amount)/$contract->amt)*100),1 }} {{ __('used') }}">
                                                    @if((($contract->amt - $contract->remaining_amount)/$contract->amt)*100 == 100)
                                                        <span class="badge badge-dot text-success">{{ __('Completed') }}</span>
                                                    @else
                                                        <div class="progress progress-sm w-80px">
                                                            <div class="progress-bar" data-progress="{{ (($contract->amt - $contract->remaining_amount)/$contract->amt)*100,1 }}"></div>
                                                        </div>
                                                    @endif
                                                </td>
                                                <td class="tb-tnx-action">
                                                    <div class="dropdown"><a class="text-soft dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown" aria-expanded="false"><em class="icon ni ni-more-h"></em></a>
                                                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-xs"
                                                             style="">
                                                            <ul class="link-list-plain">
                                                                <li><a href="#" data-href="sozlesme/{{ encode_id($contract->id) }}?action=contract_details" data-container="#modal_container" class="btn-modal">{{ __('View') }}</a></li>
                                                                <li><a href="#" data-href="sozlesme/{{ encode_id($contract->id) }}?action=orders" data-container="#modal_container" class="btn-modal">{{ __('View Orders') }}</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div><!-- .col -->
                        <div class="col-xxl-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start gx-3 mb-3">
                                        <div class="card-title">
                                            <h6 class="title">{{ __('Monthly Orders') }}</h6>
                                            <p>{{ __('Last 30 days sales report') }}. <a href="#">{{ __('View details') }}</a></p>
                                        </div>
                                        <div class="card-tools shrink-0 d-none d-sm-block">
                                            <ul class="nav nav-switch-s2 nav-tabs bg-white">
                                                <li class="nav-item"><a href="{{ url('panel?day=7') }}" class="nav-link @if($active_day == 7){{ 'active' }}@endif">7 {{ __('D') }}</a></li>
                                                <li class="nav-item"><a href="{{ url('panel?day=30') }}" class="nav-link @if($active_day == 30){{ 'active' }}@endif">1 {{ __('M') }}</a></li>
                                                <li class="nav-item"><a href="{{ url('panel?day=90') }}" class="nav-link @if($active_day == 90){{ 'active' }}@endif">3 {{ __('M') }}</a></li>
                                            </ul>
                                        </div>
                                    </div>
                                    <div class="nk-sale-data-group align-center justify-between gy-3 gx-5">
                                        <div class="nk-sale-data">
                                            <span class="amount">{{ Number::currency($last30DayOrdersTotal, 'TRY', app()->getLocale()) }}</span>
                                        </div>
                                        <div class="nk-sale-data">
                                            <span class="amount sm">{{ $last30DayOrdersCount }} <small>{{ __('Transaction') }}</small></span>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck large pt-4">
                                        <canvas class="sales-overview-chart" id="salesOverview"></canvas>
                                    </div>
                                </div>
                            </div><!-- .card -->
                        </div><!-- .col -->
                        <div class="col-xxl-8">
                            <div class="card card-bordered card-full">
                                <div class="card-inner">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title"><span class="me-2">{{ __('Recent Orders') }}</span> <a href="/siparis" class="link d-none d-sm-inline">{{ __('View All') }}</a></h6>
                                        </div>

                                    </div>
                                </div>
                                <div class="card-inner p-0 border-top">
                                    <div class="nk-tb-list nk-tb-orders">
                                        <div class="nk-tb-item nk-tb-head">
                                            <div class="nk-tb-col"><span>{{ __('Order No.') }}</span></div>
                                            <div class="nk-tb-col tb-col-sm"><span>{{ __('Contract') }}</span></div>
                                            <div class="nk-tb-col tb-col-md"><span>{{ __('Date') }}</span></div>
                                            <div class="nk-tb-col tb-col-lg"><span>{{ __('Document No') }}</span></div>
                                            <div class="nk-tb-col"><span>{{ __('Amount') }}</span></div>
                                            <div class="nk-tb-col"><span class="d-none d-sm-inline">{{ __('Status') }}</span></div>
                                            <div class="nk-tb-col"><span>&nbsp;</span></div>
                                        </div>
                                        @foreach($orders as $order)
                                            <div class="nk-tb-item">
                                                <div class="nk-tb-col">
                                                    <span class="tb-lead"><a href="#" data-href="{{ 'siparis/'.encode_id($order->id) }}" data-container="#modal_container" class="btn-modal">#{{ $order->id }}</a></span>
                                                </div>
                                                <div class="nk-tb-col tb-col-sm">
                                                    <div class="user-card">
                                                        <div class="user-name">
                                                            <span class="tb-lead">{{ $order->contract->doc_no ?? '-'}}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="nk-tb-col tb-col-md">
                                                    <span class="tb-sub">{{ date('d.m.Y', strtotime($order->doc_date)) }}</span>
                                                </div>
                                                <div class="nk-tb-col tb-col-lg">
                                                    <span class="tb-sub text-primary">{{ $order->doc_no }}</span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="tb-sub tb-amount">{{ Number::currency($order->amt,$order->currency->cur_code,app()->getLocale()) }}</span>
                                                </div>
                                                <div class="nk-tb-col">
                                                    <span class="badge badge-dot badge-dot-xs bg-success">{{ __('Paid') }}</span>
                                                </div>
                                                <div class="nk-tb-col nk-tb-col-action">
                                                    <div class="dropdown">
                                                        <a class="text-soft dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-xs">
                                                            <ul class="link-list-plain">
                                                                <li><a data-href="/siparis/{{ encode_id($order->id) }}" data-container="#modal_container" class="btn-modal">{{ __('View Details') }}</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>


                                <div class="card-inner-sm border-top text-center d-sm-none">
                                    <a href="#" class="btn btn-link btn-block">{{ __('Transaction History') }}</a>
                                </div>
                            </div><!-- .card -->


                        </div><!-- .col -->
                        <div class="col-md-6 col-xxl-4">
                            @if($contracts_end_date->count() > 0)
                                <div class="alert alert-pro alert-danger">
                                    <div class="alert-text"><h6>{{ __('WARNING') }}</h6>
                                        @foreach($contracts_end_date as $contract_end_date)
                                            <p>{{ __('Your contract with number') }} {{ $contract_end_date->doc_no }} {{ __('will expire on') }} {{ date('d.m.Y', strtotime($contract_end_date->contract_end_date)) }}.</p>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            @isset($letter_credit_due_day_count)
                                @if($letter_credit_due_day_count > 0)
                                    <div class="alert alert-success alert-icon"><em class="icon ni ni-alert-circle"></em> {{ __('There are') }} {{ $letter_credit_due_day_count }} {{ __('days left until your letter of credit expires') }}.</div>
                                @else
                                    <div class="alert alert-warning alert-icon"><em class="icon ni ni-alert-circle"></em> {{ __('Your letter of credit expired') }} {{ abs($letter_credit_due_day_count) }} {{ __('days ago') }}.</div>
                                @endif
                            @endisset

                            @foreach($shipments as $shipment)
                                <div class="alert alert-info alert-icon"><em class="icon ni ni-alert-circle"></em> <a href="#" data-href="{{ 'siparis/'.encode_id($shipment->source_m_id) }}" data-container="#modal_container" class="btn-modal alert-link">{{ $shipment->order_doc_no }}</a> {{ __('numbered sales order has') }} {{ $shipment->doc_date->diffForHumans() }} {{ __('left until the last shipment date') }}.</div>
                                @break
                            @endforeach

                            <div class="card">
                                <div class="card-inner border-bottom bg-primary">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title text-light">{{ __('Exchange Rate Information') }}</h6>
                                        </div>
                                        <div class="card-tools">
                                            <a href="#" data-bs-toggle="modal" data-bs-target="#ratesModal" class="link text-white">{{ __('All') }}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-inner">
                                    <table class="table">
                                        <thead class="bg-primary">
                                        <tr>
                                            <th>{{ __('Currency type') }}</th>
                                            <th>{{ __('TCMB 11:00') }}</th>
                                            <th>{{ __('TCBM Buying') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($currencies as $currency)
                                            <tr>
                                                <td>{{ $currency->cur_code }}</td>
                                                <td>{{ $currency->buying_rate_11->cur_rate_tra }} ₺</td>
                                                <td>{{ $currency->buying_rate->cur_rate_tra }} ₺</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                                <small class="card-footer bg-primary-dim text-end">{{ __('Last update date') }}: {{ $currency_last_update_at->format('d.m.Y H:i') }}</small>
                            </div>


                            <div class="card">
                                <div class="card-inner border-bottom">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title">{{ __('Today') }}</h6>
                                        </div>
                                        <div class="card-tools">
                                            <a href="#" class="link">{{ __('All') }}</a>
                                        </div>
                                    </div>
                                </div>
                                <table class="table">
                                    <tbody>
                                    <tr>
                                        <th>{{ __('ORDER QUANTITY') }} (M<SUP>2</SUP>)</th>
                                        <td>{{ number_format($todayTotal->qty,2) }} m<sup>2</sup></td>
                                    </tr>
                                    <tr>
                                        <th>{{ __('ORDER QUANTITY') }} ({{ __('TON') }})</th>
                                        <td>0,00</td>
                                    </tr>
                                    <tr>
                                        <th>{{ __('ORDER QUANTITY') }} ({{ __('PALLET') }})</th>
                                        <td>{{ number_format($todayTotal->quantity_pallet,2) }}</td>
                                    </tr>
                                    <tr>
                                        <th>{{ __('ORDER AMOUNT') }}</th>
                                        <td>{{ number_format($todayTotal->amt_tra,2) }} ₺</td>
                                    </tr>
                                    <tr>
                                        <th>{{ __('POS COLLECTION') }}</th>
                                        <td>0,00 ₺</td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>

                            <div class="card bg-secondary-dim">
                                <div class="card-inner border-bottom">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title">{{ __('Letter of Credit') }}</h6>
                                        </div>
                                        @if($letter_credit) <div class="card-tools">
                                            <a href="#" class="link">{{ __('All') }}</a>
                                        </div>@endif
                                    </div>
                                </div>
                                @if($letter_credit)
                                    <table class="table table-condensed ">
                                        <tbody>
                                        <tr>
                                            <th>{{ __('AMOUNT') }}</th>
                                            <td>{{ Number::currency($letter_credit->amt,'TRY','tr') }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('NUMBER') }}</th>
                                            <td>{{ $letter_credit->letter_credit_no }}</td>
                                        </tr>
                                        <tr>
                                            <th>{{ __('EXPIRY DATE') }}</th>
                                            <td>{{ date('d.m.Y', strtotime($letter_credit->due_date)) }}</td>
                                        </tr>

                                        </tbody>
                                    </table>
                                @else
                                    <div class="card-footer">{{ __('No record') }}</div>

                                @endif
                            </div>
                        </div><!-- .col -->
                        <div class="col-md-6 col-xxl-4">
                            <div class="card card-bordered card-full">
                                <div class="card-inner-group">
                                    <div class="card-inner">
                                        <div class="card-title-group">
                                            <div class="card-title">
                                                <h6 class="title">{{ __('Offers') }}</h6>
                                            </div>

                                            <div class="card-tools">
                                                <a href="/teklif" class="link">{{ __('View All') }}</a>
                                            </div>
                                        </div>
                                    </div>

                                    <table class="table table-iv-tnx">
                                        <thead class="table-light">
                                        <tr>
                                            <th class="tb-col-type"><span class="overline-title">{{ __('Offer No') }}</span></th>
                                            <th class="tb-col-date"><span class="overline-title">{{ __('Offer Date') }}</span></th>
                                            <th class="tb-col-time tb-col-end"><span
                                                        class="overline-title">{{ __('Offer Amount') }}</span></th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @if(count($offers) == 0)
                                            <tr><td class="text-center text-warning">{{ __('No offers yet') }}<td></tr>
                                        @else
                                            @foreach($offers as $offer)
                                                <tr>
                                                    <td class="tb-col-type"><span class="sub-text">{{ $offer->doc_no }}</span></td>
                                                    <td class="tb-col-date"><span class="sub-text">{{ date('d.m.Y', strtotime($offer->doc_date)) }}</span></td>
                                                    <td class="tb-col-time tb-col-end"><span class="lead-text @if($offer->amt<100000){{'text-danger'}}@endif">{{ number_format($offer->amt) }} ₺</span>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @endempty

                                        </tbody>
                                    </table>



                                </div>
                            </div><!-- .card -->
                        </div><!-- .col -->
                        <div class="col-lg-6 col-xxl-4">
                            <div class="card card-bordered h-100">
                                <div class="card-inner border-bottom">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title">{{ __('Shipments') }}</h6>
                                        </div>
                                        <div class="card-tools">
                                            <a href="/siparis/sevk-raporu" class="link">{{ __('View All') }}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-inner">
                                    <div class="timeline">
                                        <h6 class="timeline-head">{{ current_month_name('short'). ' ' . date('Y') }}</h6>
                                        <ul class="timeline-list">
                                            @foreach($shipments as $shipment)
                                                <li class="timeline-item">
                                                    <div class="timeline-status bg-primary is-outline"></div>
                                                    <div class="timeline-date">{{ $shipment->doc_date->diffForHumans() }} <em class="icon ni ni-alarm-alt"></em></div>
                                                    <div class="timeline-data">
                                                        <h6 class="timeline-title">{{ __('Shipment Planning') }}</h6>
                                                        <div class="timeline-des">
                                                            <p>{{ __('Order number') }} {{ $shipment->order_doc_no }}.</p>
                                                            <span class="time">{{ $shipment->created_at->format('H:i') }}</span>
                                                        </div>
                                                    </div>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                            </div><!-- .card -->
                        </div><!-- .col -->


                    </div><!-- .row -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>


    <!-- Modal -->
    <div class="modal fade" id="ratesModal" tabindex="-1" aria-labelledby="ratesModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="exampleModalLabel">{{ __('TCMB Indicative Exchange Rates') }}</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>

                <table class="table">
                    <thead class="bg-primary">
                    <tr>
                        <th>{{ __('Currency type') }}</th>
                        <th>{{ __('TCMB 11:00') }}</th>
                        <th>{{ __('TCBM Buying') }}</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach($currencies as $currency)
                        <tr>
                            <td>{{ $currency->cur_code }}</td>
                            <td>{{ $currency->rate->buying_rate }} ₺</td>
                            <td>{{ $currency->rate->selling_rate }} ₺</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
                <div class="modal-footer">
                    <div class="card-footer">{{ __('Last update date') }}: {{ date('d.m.Y',strtotime($currencies[0]->rate->date)) }}</div>
                </div>
            </div>
        </div>
    </div>

    @isset($popup)
        <div class="modal fade" id="onload" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true"> <!-- Add this line to your code -->
            <div class="modal-dialog modal-dialog-centered">
                <img src="{{ $popup->featured_image }}" alt="..." class="img-fluid">
            </div>
        </div> <!-- And the relavant closing div tag -->
    @endisset
@endsection
@section('js')

<script>
{{--    @if (Cookie::get('popup') == null)--}}
    window.onload = () => {
        $('#onload').modal('show');
    }
{{--@endif--}}

    var salesOverview = {
        labels: {!! $last30labels !!},
        dataUnit: 'TRY',
        lineTension: 0.1,
        datasets: [{
            label: "Satış",
            color: "#798bff",
            background: NioApp.hexRGB('#798bff', .3),
            data: {!! $last30data !!}
        }]
    };
    function lineSalesOverview(selector, set_data) {
        var $selector = selector ? $(selector) : $('.sales-overview-chart');
        $selector.each(function () {
            var $self = $(this),
                _self_id = $self.attr('id'),
                _get_data = typeof set_data === 'undefined' ? eval(_self_id) : set_data;
            var selectCanvas = document.getElementById(_self_id).getContext("2d");
            var chart_data = [];
            for (var i = 0; i < _get_data.datasets.length; i++) {
                chart_data.push({
                    label: _get_data.datasets[i].label,
                    tension: _get_data.lineTension,
                    backgroundColor: _get_data.datasets[i].background,
                    fill: true,
                    borderWidth: 2,
                    borderColor: _get_data.datasets[i].color,
                    pointBorderColor: "transparent",
                    pointBackgroundColor: "transparent",
                    pointHoverBackgroundColor: "#fff",
                    pointHoverBorderColor: _get_data.datasets[i].color,
                    pointBorderWidth: 2,
                    pointHoverRadius: 3,
                    pointHoverBorderWidth: 2,
                    pointRadius: 3,
                    pointHitRadius: 3,
                    data: _get_data.datasets[i].data
                });
            }
            var chart = new Chart(selectCanvas, {
                type: 'line',
                data: {
                    labels: _get_data.labels,
                    datasets: chart_data
                },
                options: {
                    plugins: {
                        legend: {
                            display: _get_data.legend ? _get_data.legend : false,
                            labels: {
                                boxWidth: 30,
                                padding: 20,
                                color: '#6783b8'
                            }
                        },
                        tooltip: {
                            enabled: true,
                            rtl: NioApp.State.isRTL,
                            callbacks: {
                                label: function label(context) {
                                    return "".concat(context.parsed.y, " ").concat(_get_data.dataUnit);
                                }
                            },
                            backgroundColor: '#eff6ff',
                            titleFont: {
                                size: 13
                            },
                            titleColor: '#6783b8',
                            titleMarginBottom: 6,
                            bodyColor: '#9eaecf',
                            bodyFont: {
                                size: 12
                            },
                            bodySpacing: 4,
                            padding: 10,
                            footerMarginTop: 0,
                            displayColors: false
                        }
                    },
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            display: true,
                            stacked: _get_data.stacked ? _get_data.stacked : false,
                            position: NioApp.State.isRTL ? "right" : "left",
                            ticks: {
                                beginAtZero: true,
                                font: {
                                    size: 11
                                },
                                color: '#9eaecf',
                                padding: 10,
                                callback: function callback(value, index, values) {
                                    return '₺ ' + value;
                                },
                                min: 100,
                                stepSize: 3000
                            },
                            grid: {
                                color: NioApp.hexRGB("#526484", .2),
                                tickLength: 0,
                                zeroLineColor: NioApp.hexRGB("#526484", .2),
                                drawTicks: false
                            }
                        },
                        x: {
                            display: true,
                            stacked: _get_data.stacked ? _get_data.stacked : false,
                            ticks: {
                                font: {
                                    size: 9
                                },
                                color: '#9eaecf',
                                source: 'auto',
                                padding: 10,
                                reverse: NioApp.State.isRTL
                            },
                            grid: {
                                color: "transparent",
                                tickLength: 0,
                                zeroLineColor: 'transparent',
                                drawTicks: false
                            }
                        }
                    }
                }
            });
        });
    }

    // init chart
    NioApp.coms.docReady.push(function () {
        lineSalesOverview();
    });

    $(document).ready(function() {

    });


</script>
@endsection