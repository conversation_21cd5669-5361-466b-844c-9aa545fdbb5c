<div class="table-responsive">
    <table class="table table-bordered">
        <thead>
        <tr>
            <th>Müş<PERSON><PERSON></th>
            <th>Müşteri Adı</th>
            <th>Satış Temsilcisi</th>
            <th class="text-center">Sipariş Sayısı</th>
            <th class="text-center">Toplam Tutar</th>
            <th class="text-center">İlk Sipariş</th>
            <th class="text-center">Son Sipariş</th>
            <th class="text-center">Ort. Sipariş Aralığı</th>
            <th class="text-center">Aylık Sipariş</th>
            <th class="text-center">Son Siparişten Beri</th>
        </tr>
        </thead>
        <tbody>
        @if(count($customers) > 0)
            @foreach($customers as $customer)
                <tr>
                    <td>{{ $customer['entity_code'] ?? '-' }}</td>
                    <td>{{ $customer['entity_name'] }}</td>
                    <td>{{ $customer['sales_rep_name'] ?? '-' }}</td>
                    <td class="text-center">{{ number_format($customer['order_count']) }}</td>
                    <td class="text-end">{{ number_format($customer['total_amount'], 2) }} ₺</td>
                    <td class="text-center">{{ \Carbon\Carbon::parse($customer['first_order_date'])->format('d.m.Y') }}</td>
                    <td class="text-center">{{ \Carbon\Carbon::parse($customer['last_order_date'])->format('d.m.Y') }}</td>
                    <td class="text-center">
                            <span class="badge bg-{{ $customer['avg_days_between_orders'] <= 7 ? 'success' :
                                ($customer['avg_days_between_orders'] <= 30 ? 'primary' :
                                ($customer['avg_days_between_orders'] <= 90 ? 'info' :
                                ($customer['avg_days_between_orders'] <= 180 ? 'warning' :
                                ($customer['avg_days_between_orders'] <= 365 ? 'danger' : 'secondary')))) }}">
                                {{ $customer['avg_days_between_orders'] }} gün
                            </span>
                    </td>
                    <td class="text-center">{{ number_format($customer['orders_per_month'], 2) }}</td>
                    <td class="text-center">
                            <span class="badge bg-{{ $customer['days_since_last_order'] <= 30 ? 'success' :
                                ($customer['days_since_last_order'] <= 90 ? 'info' :
                                ($customer['days_since_last_order'] <= 180 ? 'warning' : 'danger')) }}">
                                {{ $customer['days_since_last_order'] }} gün
                            </span>
                    </td>
                </tr>
            @endforeach
        @else
            <tr>
                <td colspan="10" class="text-center">Bu kategoride müşteri bulunamadı.</td>
            </tr>
        @endif
        </tbody>
    </table>
</div>
