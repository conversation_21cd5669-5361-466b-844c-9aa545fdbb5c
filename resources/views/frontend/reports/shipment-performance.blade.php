@extends('frontend.layouts.app')

@section('title', 'Sevkiyat Performans Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Sevkiyat Performans Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Sevkiyat Performans Analizi</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.shipment-performance', ['year' => $year, 'month' => $month, 'transport_type_id' => $transportTypeId, 'entity_id' => $entityId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.shipment-performance', ['year' => $year, 'month' => $month, 'transport_type_id' => $transportTypeId, 'entity_id' => $entityId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.shipment-performance') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            @for($m = 1; $m <= 12; $m++)
                                                <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>{{ date('F', mktime(0, 0, 0, $m, 1)) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="transport_type_id">Taşıma Tipi</label>
                                        <select class="form-select" id="transport_type_id" name="transport_type_id" onchange="this.form.submit()">
                                            <option value="">Tüm Taşıma Tipleri</option>
                                            @foreach($transportTypes as $type)
                                                <option value="{{ $type->id }}" {{ $transportTypeId == $type->id ? 'selected' : '' }}>{{ $type->description }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Performance Summary Cards -->
                        <div class="card-inner">
                            <div class="row g-gs">
                                <div class="col-md-6 col-lg-3">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Toplam Sevkiyat</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['order_count']) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-3">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Zamanında Teslimat</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount text-success">{{ number_format($totals['on_time_count']) }}</span>
                                                    <span class="smaller text-success">{{ $totals['on_time_percentage'] }}%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-3">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Geç Teslimat</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount text-danger">{{ number_format($totals['late_count']) }}</span>
                                                    <span class="smaller text-danger">{{ $totals['late_percentage'] }}%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6 col-lg-3">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Ort. Teslimat Süresi</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['avg_delivery_days'], 1) }}</span>
                                                    <span class="smaller">gün</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Müşteri</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Taşıma Tipi</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Ort. Teslimat Süresi (Gün)</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Zamanında Teslimat</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Geç Teslimat</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Zamanında Teslimat %</span></div>
                                </div>

                                @foreach($shipmentData as $item)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $item->entity_name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $item->transport_type ?? 'Belirtilmemiş' }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ number_format($item->order_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ number_format($item->avg_delivery_days, 1) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-success">{{ number_format($item->on_time_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="text-danger">{{ number_format($item->late_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span class="badge {{ $item->on_time_percentage >= 90 ? 'bg-success' : ($item->on_time_percentage >= 75 ? 'bg-warning' : 'bg-danger') }}">
                                                {{ $item->on_time_percentage }}%
                                            </span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col">
                                        <span></span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($totals['avg_delivery_days'], 1) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold text-success">{{ number_format($totals['on_time_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold text-danger">{{ number_format($totals['late_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold badge {{ $totals['on_time_percentage'] >= 90 ? 'bg-success' : ($totals['on_time_percentage'] >= 75 ? 'bg-warning' : 'bg-danger') }}">
                                            {{ $totals['on_time_percentage'] }}%
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Monthly Performance Trend -->
                        <div class="col-lg-8">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Aylık Teslimat Performansı</h6>
                                            <p class="text-soft">{{ $year }} yılı aylık teslimat performansı trendi</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="line-chart" id="monthlyPerformanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Transport Type Performance -->
                        <div class="col-lg-4">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Taşıma Tipine Göre Performans</h6>
                                            <p class="text-soft">Taşıma tipine göre zamanında teslimat oranları</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="bar-chart" id="transportPerformanceChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Monthly Performance Chart
            var ctxMonthly = document.getElementById('monthlyPerformanceChart').getContext('2d');
            var monthlyPerformanceChart = new Chart(ctxMonthly, {
                type: 'line',
                data: {
                    labels: [
                        @foreach($monthlyPerformance as $item)
                            '{{ $item['month'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Zamanında Teslimat %',
                        data: [
                            @foreach($monthlyPerformance as $item)
                                    {{ $item['on_time_percentage'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(30, 224, 172, 0.2)',
                        borderColor: '#1ee0ac',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Geç Teslimat %',
                        data: [
                            @foreach($monthlyPerformance as $item)
                                    {{ $item['late_percentage'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(232, 83, 71, 0.2)',
                        borderColor: '#e85347',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Transport Type Performance Chart
            var ctxTransport = document.getElementById('transportPerformanceChart').getContext('2d');
            var transportPerformanceChart = new Chart(ctxTransport, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($transportBreakdown as $item)
                            '{{ $item->description ?? "Belirtilmemiş" }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Zamanında Teslimat %',
                        data: [
                            @foreach($transportBreakdown as $item)
                                    {{ $item->on_time_percentage }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#1ee0ac',
                            '#816bff',
                            '#f4bd0e',
                            '#09c2de',
                            '#6576ff'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection