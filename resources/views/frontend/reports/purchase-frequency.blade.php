@extends('frontend.layouts.app')

@section('title', 'Müşteri Satınalma Sıklığı Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Müşteri Satınalma Sıklığı Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $periodText }} Müşteri Satınalma Davranışı Analizi</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.purchase-frequency', ['year' => $year, 'period' => $period, 'min_orders' => $minOrders, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.purchase-frequency', ['year' => $year, 'period' => $period, 'min_orders' => $minOrders, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.purchase-frequency') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" {{ $period == 'all' ? 'disabled' : '' }} onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="period">Dönem</label>
                                        <select class="form-select" id="period" name="period" onchange="this.form.submit()">
                                            <option value="yearly" {{ $period == 'yearly' ? 'selected' : '' }}>Yıllık</option>
                                            <option value="all" {{ $period == 'all' ? 'selected' : '' }}>Tüm Zamanlar</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="min_orders">Minimum Sipariş Sayısı</label>
                                        <select class="form-select" id="min_orders" name="min_orders" onchange="this.form.submit()">
                                            @for($i = 2; $i <= 10; $i++)
                                                <option value="{{ $i }}" {{ $minOrders == $i ? 'selected' : '' }}>{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Summary Cards -->
                        <div class="card-inner">
                            <div class="row g-gs">
                                <div class="col-md-4">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-0">
                                                <div class="card-title">
                                                    <h6 class="title">Toplam Müşteri</h6>
                                                </div>
                                                <div class="card-tools">
                                                    <em class="card-hint icon ni ni-users text-primary"></em>
                                                </div>
                                            </div>
                                            <div class="card-amount">
                                                <span class="amount">{{ number_format($totals['customer_count']) }}</span>
                                            </div>
                                            <div class="card-subtitle">Analiz edilen müşteri sayısı</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-0">
                                                <div class="card-title">
                                                    <h6 class="title">Toplam Sipariş</h6>
                                                </div>
                                                <div class="card-tools">
                                                    <em class="card-hint icon ni ni-cart text-primary"></em>
                                                </div>
                                            </div>
                                            <div class="card-amount">
                                                <span class="amount">{{ number_format($totals['order_count']) }}</span>
                                            </div>
                                            <div class="card-subtitle">Toplam sipariş sayısı</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-0">
                                                <div class="card-title">
                                                    <h6 class="title">Toplam Tutar</h6>
                                                </div>
                                                <div class="card-tools">
                                                    <em class="card-hint icon ni ni-coin text-primary"></em>
                                                </div>
                                            </div>
                                            <div class="card-amount">
                                                <span class="amount">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                            </div>
                                            <div class="card-subtitle">Toplam satış tutarı</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Frequency Categories Tabs -->
                        <div class="card-inner">
                            <ul class="nav nav-tabs">
                                <li class="nav-item">
                                    <a class="nav-link active" data-bs-toggle="tab" href="#tabAll">Tüm Müşteriler ({{ count($frequencyData) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabDaily">Günlük ({{ count($frequencyGroups['daily']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabWeekly">Haftalık ({{ count($frequencyGroups['weekly']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabMonthly">Aylık ({{ count($frequencyGroups['monthly']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabQuarterly">3 Aylık ({{ count($frequencyGroups['quarterly']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabSemiannual">6 Aylık ({{ count($frequencyGroups['semiannual']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabAnnual">Yıllık ({{ count($frequencyGroups['annual']) }})</a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" data-bs-toggle="tab" href="#tabIrregular">Düzensiz ({{ count($frequencyGroups['irregular']) }})</a>
                                </li>
                            </ul>
                            <div class="tab-content">
                                <div class="tab-pane active" id="tabAll">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyData])
                                </div>
                                <div class="tab-pane" id="tabDaily">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['daily']])
                                </div>
                                <div class="tab-pane" id="tabWeekly">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['weekly']])
                                </div>
                                <div class="tab-pane" id="tabMonthly">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['monthly']])
                                </div>
                                <div class="tab-pane" id="tabQuarterly">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['quarterly']])
                                </div>
                                <div class="tab-pane" id="tabSemiannual">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['semiannual']])
                                </div>
                                <div class="tab-pane" id="tabAnnual">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['annual']])
                                </div>
                                <div class="tab-pane" id="tabIrregular">
                                    @include('frontend.reports._frequency_table', ['customers' => $frequencyGroups['irregular']])
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Customer Frequency Distribution -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Müşteri Satınalma Sıklığı Dağılımı</h6>
                                            <p class="text-soft">Ortalama sipariş aralığına göre müşteri sayısı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="bar-chart" id="frequencyDistributionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Frequency vs. Revenue -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Satınalma Sıklığı ve Ciro İlişkisi</h6>
                                            <p class="text-soft">Satınalma sıklığı kategorilerine göre ciro dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="pie-chart" id="frequencyRevenueChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Frequency Distribution Chart
            var ctxFrequencyDistribution = document.getElementById('frequencyDistributionChart').getContext('2d');
            var frequencyDistributionChart = new Chart(ctxFrequencyDistribution, {
                type: 'bar',
                data: {
                    labels: ['Günlük', 'Haftalık', 'Aylık', '3 Aylık', '6 Aylık', 'Yıllık', 'Düzensiz'],
                    datasets: [{
                        label: 'Müşteri Sayısı',
                        data: [
                            {{ count($frequencyGroups['daily']) }},
                            {{ count($frequencyGroups['weekly']) }},
                            {{ count($frequencyGroups['monthly']) }},
                            {{ count($frequencyGroups['quarterly']) }},
                            {{ count($frequencyGroups['semiannual']) }},
                            {{ count($frequencyGroups['annual']) }},
                            {{ count($frequencyGroups['irregular']) }}
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff',
                            '#1c2b46',
                            '#8094ae'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                precision: 0
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + ' Müşteri';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Frequency vs. Revenue Chart
            var ctxFrequencyRevenue = document.getElementById('frequencyRevenueChart').getContext('2d');
            var frequencyRevenueChart = new Chart(ctxFrequencyRevenue, {
                type: 'pie',
                data: {
                    labels: ['Günlük', 'Haftalık', 'Aylık', '3 Aylık', '6 Aylık', 'Yıllık', 'Düzensiz'],
                    datasets: [{
                        data: [
                            {{ array_sum(array_column($frequencyGroups['daily'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['weekly'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['monthly'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['quarterly'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['semiannual'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['annual'], 'total_amount')) }},
                            {{ array_sum(array_column($frequencyGroups['irregular'], 'total_amount')) }}
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff',
                            '#1c2b46',
                            '#8094ae'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 10,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);

                                        // Add percentage
                                        const percentage = (context.parsed / {{ $totals['total_amount'] }} * 100).toFixed(2);
                                        label += ' (' + percentage + '%)';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection