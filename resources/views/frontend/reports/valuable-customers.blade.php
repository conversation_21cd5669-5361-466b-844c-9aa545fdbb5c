@extends('frontend.layouts.app')

@section('title', 'En Değerli Müşteriler Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">En Değerli Müşteriler Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $periodText }} En Değ<PERSON>li Müşteriler Analizi</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.valuable-customers', ['year' => $year, 'period' => $period, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.valuable-customers', ['year' => $year, 'period' => $period, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Pareto Analysis (80/20 Rule) -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Pareto Analizi (80/20 Kuralı)</h6>
                                            <p class="text-soft">Kümülatif gelir yüzdesi dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="line-chart" id="paretoChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Top Customers Pie Chart -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">En Değerli Müşteriler</h6>
                                            <p class="text-soft">Satış tutarına göre ilk 5 müşteri</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="pie-chart" id="topCustomersChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.valuable-customers') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" {{ $period == 'all' ? 'disabled' : '' }} onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="period">Dönem</label>
                                        <select class="form-select" id="period" name="period" onchange="this.form.submit()">
                                            <option value="yearly" {{ $period == 'yearly' ? 'selected' : '' }}>Yıllık</option>
                                            <option value="all" {{ $period == 'all' ? 'selected' : '' }}>Tüm Zamanlar</option>
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Sıra</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Müşteri Kodu</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Müşteri Adı</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Satış Temsilcisi</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Tutar</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam KDV</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Yüzdesi</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Kümülatif</span></div>
                                </div>

                                @foreach($customerData as $customer)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $customer['rank'] }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $customer['entity_code'] ?? '-' }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $customer['entity_name'] }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $customer['sales_rep_name'] ?? '-' }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($customer['order_count']) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($customer['total_amount'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($customer['total_vat'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($customer['percent_of_total'], 2) }}%</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($customer['cumulative_percent'], 2) }}%</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">100.00%</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">100.00%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Pareto Chart
            var ctxPareto = document.getElementById('paretoChart').getContext('2d');
            var paretoChart = new Chart(ctxPareto, {
                type: 'line',
                data: {
                    labels: [
                        @foreach($customerData as $customer)
                            '{{ $loop->index + 1 }}',
                        @endforeach
                    ],
                    datasets: [
                        {
                            type: 'bar',
                            label: 'Satış Tutarı',
                            data: [
                                @foreach($customerData as $customer)
                                        {{ $customer['total_amount'] }},
                                @endforeach
                            ],
                            backgroundColor: '#6576ff',
                            order: 2
                        },
                        {
                            type: 'line',
                            label: 'Kümülatif Yüzde',
                            data: [
                                @foreach($customerData as $customer)
                                        {{ $customer['cumulative_percent'] }},
                                @endforeach
                            ],
                            borderColor: '#e85347',
                            borderWidth: 2,
                            fill: false,
                            pointBorderColor: '#e85347',
                            pointBackgroundColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 4,
                            pointHoverRadius: 6,
                            order: 1,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        },
                        y1: {
                            position: 'right',
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        if (context.dataset.yAxisID === 'y1') {
                                            label += context.parsed.y.toFixed(2) + '%';
                                        } else {
                                            label += new Intl.NumberFormat('tr-TR', {
                                                style: 'currency',
                                                currency: 'TRY'
                                            }).format(context.parsed.y);
                                        }
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Top Customers Pie Chart
            var ctxTopCustomers = document.getElementById('topCustomersChart').getContext('2d');
            var topCustomersChart = new Chart(ctxTopCustomers, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach(array_slice($customerData, 0, 5) as $customer)
                            '{{ $customer['entity_name'] }}',
                        @endforeach
                            'Diğer Müşteriler'
                    ],
                    datasets: [{
                        data: [
                            @foreach(array_slice($customerData, 0, 5) as $customer)
                                    {{ $customer['total_amount'] }},
                            @endforeach
                                    {{ $totals['total_amount'] - array_sum(array_column(array_slice($customerData, 0, 5), 'total_amount')) }}
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff',
                            '#efefef'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);

                                        // Add percentage
                                        const percentage = (context.parsed / {{ $totals['total_amount'] }} * 100).toFixed(2);
                                        label += ' (' + percentage + '%)';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection