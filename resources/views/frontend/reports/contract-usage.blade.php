@extends('frontend.layouts.app')

@section('title', 'Sözleşme Kullanım Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Sözleşme Kullanım Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Sözleşme Kullanım Analiz Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.contract-usage', ['year' => $year, 'entity_id' => $entityId, 'sales_person_id' => $salesPersonId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.contract-usage', ['year' => $year, 'entity_id' => $entityId, 'sales_person_id' => $salesPersonId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.contract-usage') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select js-select2" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="sales_person_id">Satış Temsilcisi</label>
                                        <select class="form-select" id="sales_person_id" name="sales_person_id" onchange="this.form.submit()">
                                            <option value="">Tüm Satış Temsilcileri</option>
                                            @foreach($salesPeople as $salesPerson)
                                                <option value="{{ $salesPerson->id }}" {{ $salesPersonId == $salesPerson->id ? 'selected' : '' }}>{{ $salesPerson->first_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Summary Cards -->
                        <div class="card-inner">
                            <div class="row g-gs">
                                <div class="col-xxl-3 col-md-6">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Toplam Sözleşme Tutarı</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xxl-3 col-md-6">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Kullanılan Tutar</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['used_amount'], 2) }} ₺</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xxl-3 col-md-6">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Kalan Tutar</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['remaining_amount'], 2) }} ₺</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-xxl-3 col-md-6">
                                    <div class="card card-bordered">
                                        <div class="card-inner">
                                            <div class="card-title-group align-start mb-2">
                                                <div class="card-title">
                                                    <h6 class="title">Ortalama Kullanım Oranı</h6>
                                                </div>
                                            </div>
                                            <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                                <div class="nk-sale-data">
                                                    <span class="amount">{{ number_format($totals['average_usage'], 2) }}%</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Sözleşme No</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Müşteri</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Başlangıç</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Bitiş</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Tutar</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Kullanılan</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Kalan</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Kullanım Oranı</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Durum</span></div>
                                </div>

                                @foreach($contractData as $contract)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $contract->doc_no }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $contract->entity_name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ date('d.m.Y', strtotime($contract->contract_start_date)) }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($contract->total_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($contract->used_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($contract->remaining_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <div class="progress progress-md">
                                                <div class="progress-bar {{ $contract->usage_percentage > 80 ? 'bg-danger' : ($contract->usage_percentage > 60 ? 'bg-warning' : 'bg-success') }}"
                                                     data-progress="{{ $contract->usage_percentage }}"
                                                     style="width: {{ $contract->usage_percentage }}%;">
                                                    {{ number_format($contract->usage_percentage, 2) }}%
                                                </div>
                                            </div>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span class="badge badge-dot badge-{{ $contract->status_class }}">{{ $contract->status }}</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['used_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['remaining_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($totals['average_usage'], 2) }}%</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Contract Status -->
                        <div class="col-lg-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Sözleşme Durum Dağılımı</h6>
                                            <p class="text-soft">Sözleşmelerin durum dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="contract-status-chart" id="contractStatusChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Usage Distribution -->
                        <div class="col-lg-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Kullanım Dağılımı</h6>
                                            <p class="text-soft">Toplam tutar, kullanım ve kalan tutar dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="contract-usage-chart" id="contractUsageChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Calculate status distribution
            let statusCount = {
                'Aktif': 0,
                'Kritik': 0,
                'Sona Erdi': 0
            };

            @foreach($contractData as $contract)
                statusCount['{{ $contract->status }}']++;
            @endforeach

            // Contract Status Chart
            var ctxContractStatus = document.getElementById('contractStatusChart').getContext('2d');
            var contractStatusChart = new Chart(ctxContractStatus, {
                type: 'pie',
                data: {
                    labels: Object.keys(statusCount),
                    datasets: [{
                        data: Object.values(statusCount),
                        backgroundColor: [
                            '#05a45c', // Aktif (green)
                            '#f4bd0e', // Kritik (yellow)
                            '#e85347'  // Sona Erdi (red)
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += context.parsed + ' sözleşme';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Contract Usage Chart
            var ctxContractUsage = document.getElementById('contractUsageChart').getContext('2d');
            var contractUsageChart = new Chart(ctxContractUsage, {
                type: 'bar',
                data: {
                    labels: ['Kullanılan Tutar', 'Kalan Tutar'],
                    datasets: [{
                        label: 'Tutar (₺)',
                        data: [
                            {{ $totals['used_amount'] }},
                            {{ $totals['remaining_amount'] }}
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#e85347'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection