@extends('frontend.layouts.app')
@section('css')
    <style>
        /* public/css/ai-query.css */

        .ai-query-container {
            min-height: 400px;
        }

        .example-query {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .example-query:hover {
            background-color: #e9ecef !important;
        }

        #ai-response {
            min-height: 100px;
            max-height: 800px;
            overflow-y: auto;
        }

        .ai-response {
            font-family: 'Roboto', sans-serif;
        }

        .ai-response h1,
        .ai-response h2,
        .ai-response h3 {
            color: #2c3e50;
            margin-top: 1rem;
            margin-bottom: 0.75rem;
        }

        .ai-response table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }

        .ai-response table th {
            background-color: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            padding: 0.75rem;
            text-align: left;
        }

        .ai-response table td {
            padding: 0.75rem;
            border-top: 1px solid #dee2e6;
        }

        .ai-response .table-container {
            overflow-x: auto;
            margin-bottom: 1rem;
        }

        .ai-response .highlight {
            background-color: #fff3cd;
            padding: 0.2rem;
        }

        .ai-response .text-positive {
            color: #28a745;
        }

        .ai-response .text-negative {
            color: #dc3545;
        }

        .ai-response .summary-card {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 0.25rem;
            margin-bottom: 1rem;
        }

        .ai-response .data-row {
            display: flex;
            justify-content: space-between;
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }

        .ai-response .data-row:last-child {
            border-bottom: none;
        }</style>
    @endsection
@section('content')
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">AI Veri Analiz Asistanı</h4>
                    </div>

                    <div class="card-body">
                        <form id="ai-query-form" action="{{ route('frontend.reports.ai.query.process') }}" method="POST">
                            @csrf
                            <div class="form-group mb-4">
                                <label for="query" class="fw-bold">Sorunuzu Girin:</label>
                                <textarea class="form-control" id="query" name="query" rows="3" placeholder="Örneğin: Son 1 ayda en çok satılan 5 ürün nedir?" required></textarea>
                                <div class="mt-2">
                                    <small class="text-muted">Örnek sorular:</small>
                                    <div class="d-flex flex-wrap gap-2 mt-1">
                                        <span class="badge bg-light text-dark border example-query" style="cursor: pointer">Son 30 günde en çok sipariş edilen 10 ürün</span>
                                        <span class="badge bg-light text-dark border example-query" style="cursor: pointer">Bu ay en çok alım yapan 5 müşteri</span>
                                        <span class="badge bg-light text-dark border example-query" style="cursor: pointer">Aktif sözleşmelerin toplam ve kalan tutarları</span>
                                        <span class="badge bg-light text-dark border example-query" style="cursor: pointer">Son 3 aydaki satışların aylık dağılımı</span>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <button type="submit" class="btn btn-primary">Sorgula</button>
                                <button type="button" class="btn btn-outline-secondary" id="clear-results">Sonuçları Temizle</button>
                            </div>
                        </form>

                        <div class="mt-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h5 class="mb-0">Sorgu Sonucu</h5>
                                <div class="spinner-border spinner-border-sm text-primary d-none" id="loading-spinner" role="status">
                                    <span class="visually-hidden">Yükleniyor...</span>
                                </div>
                            </div>
                            <div id="ai-response" class="p-3 border rounded bg-light">
                                <p class="text-center text-muted">Sorgunuzu gönderdiğinizde sonuçlar burada gösterilecektir.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


@endsection

@section('js')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('ai-query-form');
            const responseArea = document.getElementById('ai-response');
            const spinner = document.getElementById('loading-spinner');
            const clearButton = document.getElementById('clear-results');
            const exampleQueries = document.querySelectorAll('.example-query');
            const queryInput = document.getElementById('query');

            // Örnek sorguları tıklandığında textarea'ya ekle
            exampleQueries.forEach(query => {
                query.addEventListener('click', function() {
                    queryInput.value = this.textContent;
                    // Textarea'ya odaklan
                    queryInput.focus();
                });
            });

            // Formu gönder
            form.addEventListener('submit', async function(e) {
                e.preventDefault();

                // Boş sorgu kontrolü
                if (!queryInput.value.trim()) {
                    responseArea.innerHTML = '<div class="alert alert-warning">Lütfen bir soru girin.</div>';
                    return;
                }

                // Yükleniyor göstergesini göster
                spinner.classList.remove('d-none');
                responseArea.innerHTML = '<p class="text-center">Sorgunuz işleniyor, lütfen bekleyin... (Veritabanı analizi yapılıyor)</p>';

                try {
                    const formData = new FormData(form);
                    const response = await fetch('/raporlar/ai', {
                        method: 'POST',
                        headers: {
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                            'Accept': 'application/json',
                            'X-Requested-With': 'XMLHttpRequest' // Ajax isteği olduğunu belirtmek için
                        },
                        body: formData
                    });

                    if (!response.ok) {
                        throw new Error('Sunucu hatası ' + response.status);
                    }

                    // İçerik türünü kontrol et
                    const contentType = response.headers.get('content-type');
                    let data;

                    if (contentType && contentType.indexOf('application/json') !== -1) {
                        // JSON yanıt
                        data = await response.json();
                    } else {
                        // HTML yanıt
                        const htmlContent = await response.text();
                        data = {
                            html_response: htmlContent,
                            from_cache: false
                        };
                    }

                    // AI yanıtını sayfaya yerleştir
                    responseArea.innerHTML = data.html_response;

                    // Yanıt içindeki tablolara Bootstrap sınıfları ekle
                    const tables = responseArea.querySelectorAll('table:not(.table)');
                    tables.forEach(table => {
                        table.classList.add('table', 'table-striped', 'table-bordered');

                        // Tablo çok genişse sarmalayıcı div ekle
                        if (table.offsetWidth > responseArea.offsetWidth) {
                            const wrapper = document.createElement('div');
                            wrapper.classList.add('table-responsive');
                            table.parentNode.insertBefore(wrapper, table);
                            wrapper.appendChild(table);
                        }
                    });

                    // Sayıları formatlı göster
                    const numericCells = responseArea.querySelectorAll('td:not(.formatted)');
                    numericCells.forEach(cell => {
                        const text = cell.textContent;
                        if (/^[0-9,.]+$/.test(text.trim())) {
                            const num = parseFloat(text.replace(/\./g, '').replace(',', '.'));
                            if (!isNaN(num)) {
                                cell.textContent = num.toLocaleString('tr-TR');
                                cell.classList.add('formatted');
                            }
                        }
                    });

                    // Önbellekten geliyorsa kullanıcıya bildir
                    if (data.from_cache) {
                        const cacheInfo = document.createElement('div');
                        cacheInfo.classList.add('text-muted', 'small', 'mt-2');
                        cacheInfo.innerHTML = '<i class="fas fa-database me-1"></i> Bu yanıt önbellekten alındı.';
                        responseArea.appendChild(cacheInfo);
                    }

                    // Yanıta scroll
                    responseArea.scrollIntoView({ behavior: 'smooth', block: 'start' });

                } catch (error) {
                    console.error('Hata:', error);
                    responseArea.innerHTML = '<div class="alert alert-danger">Sorgu işlenirken bir hata oluştu: ' + error.message + '<br>Lütfen daha sonra tekrar deneyin.</div>';
                } finally {
                    // Yükleniyor göstergesini gizle
                    spinner.classList.add('d-none');
                }
            });

            // Sonuçları temizle
            clearButton.addEventListener('click', function() {
                responseArea.innerHTML = '<p class="text-center text-muted">Sorgunuzu gönderdiğinizde sonuçlar burada gösterilecektir.</p>';
                queryInput.value = '';
                queryInput.focus();
            });

            // Enter tuşuna basıldığında formu gönder
            queryInput.addEventListener('keydown', function(e) {
                // Ctrl + Enter veya Cmd + Enter tuş kombinasyonu ile sorguyu gönder
                if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
                    form.dispatchEvent(new Event('submit'));
                }
            });
        });
    </script>
@endsection