@extends('frontend.layouts.app')

@section('title', 'Satış Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Satış Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $reportType === 'monthly' ? $year.' Yılı Aylık Satış Raporu' : 'Yıllık Satış Raporu' }}</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light" onclick="window.print()"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales', ['report_type' => $reportType, 'year' => $year, 'entity_id' => $entityId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales', ['report_type' => $reportType, 'year' => $year, 'entity_id' => $entityId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.sales') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="report_type">Rapor Tipi</label>
                                        <select class="form-select" id="report_type" name="report_type" onchange="this.form.submit()">
                                            <option value="monthly" {{ $reportType === 'monthly' ? 'selected' : '' }}>Aylık Rapor</option>
                                            <option value="yearly" {{ $reportType === 'yearly' ? 'selected' : '' }}>Yıllık Rapor</option>
                                        </select>
                                    </div>
                                </div>
                                @if($reportType === 'monthly')
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label" for="year">Yıl</label>
                                            <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                                @foreach($years as $y)
                                                    <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                @endif
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="card-inner-group">
                            <div class="card-inner p-0">
                                <div class="nk-tb-list nk-tb-ulist">
                                    <div class="nk-tb-item nk-tb-head">
                                        <div class="nk-tb-col"><span class="sub-text">{{ $reportType === 'monthly' ? 'Ay' : 'Yıl' }}</span></div>
                                        <div class="nk-tb-col tb-col-md"><span class="sub-text">Sipariş Sayısı</span></div>
                                        <div class="nk-tb-col tb-col-lg"><span class="sub-text">Toplam Tutar</span></div>
                                        <div class="nk-tb-col tb-col-lg"><span class="sub-text">Toplam KDV</span></div>
                                        <div class="nk-tb-col tb-col-lg"><span class="sub-text">Genel Toplam</span></div>
                                    </div>
                                    @foreach($reportData as $data)
                                        <div class="nk-tb-item">
                                            <div class="nk-tb-col">
                                                <span>{{ $data['period'] }}</span>
                                            </div>
                                            <div class="nk-tb-col tb-col-md">
                                                <span>{{ number_format($data['order_count']) }}</span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span>{{ number_format($data['total_amount'], 2) }} ₺</span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span>{{ number_format($data['total_vat'], 2) }} ₺</span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span>{{ number_format($data['total_amount'] + $data['total_vat'], 2) }} ₺</span>
                                            </div>
                                        </div>
                                    @endforeach
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span class="fw-bold">TOPLAM</span>
                                        </div>
                                        <div class="nk-tb-col tb-col-md">
                                            <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                        </div>
                                        <div class="nk-tb-col tb-col-lg">
                                            <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col tb-col-lg">
                                            <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col tb-col-lg">
                                            <span class="fw-bold">{{ number_format($totals['total_amount'] + $totals['total_vat'], 2) }} ₺</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chart -->
                <div class="nk-block mt-5">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Satış Grafiği</h6>
                                </div>
                            </div>
                            <div class="nk-sales-ck">
                                <canvas class="sales-bar-chart" id="salesChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                @if($reportType === 'monthly' && count($topProducts) > 0)
                    <!-- Top Products and Customers -->
                    <div class="nk-block mt-5">
                        <div class="row g-gs">
                            <!-- Top Products -->
                            <div class="col-md-6">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">En Çok Satan Ürünler</h6>
                                                <p class="text-soft">{{ $year }} yılı en çok satılan 5 ürün</p>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                <tr>
                                                    <th>Ürün Kodu</th>
                                                    <th>Ürün Adı</th>
                                                    <th class="text-end">Miktar</th>
                                                    <th class="text-end">Tutar</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($topProducts as $product)
                                                    <tr>
                                                        <td>{{ $product->item_code }}</td>
                                                        <td>{{ $product->item_name }}</td>
                                                        <td class="text-end">{{ number_format($product->total_quantity, 2) }}</td>
                                                        <td class="text-end">{{ number_format($product->total_amount, 2) }} ₺</td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Top Customers -->
                            <div class="col-md-6">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">En Çok Alım Yapan Müşteriler</h6>
                                                <p class="text-soft">{{ $year }} yılı en çok alım yapan 5 müşteri</p>
                                            </div>
                                        </div>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                <tr>
                                                    <th>Müşteri</th>
                                                    <th class="text-end">Sipariş Sayısı</th>
                                                    <th class="text-end">Toplam Tutar</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($topCustomers as $customer)
                                                    <tr>
                                                        <td>{{ $customer->entity_name }}</td>
                                                        <td class="text-end">{{ number_format($customer->order_count) }}</td>
                                                        <td class="text-end">{{ number_format($customer->total_amount, 2) }} ₺</td>
                                                    </tr>
                                                @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Category Distribution -->
                    <div class="nk-block mt-5">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <div class="card-title-group align-start mb-3">
                                    <div class="card-title">
                                        <h6 class="title">Kategori Dağılımı</h6>
                                        <p class="text-soft">{{ $year }} yılı satışların kategori bazında dağılımı</p>
                                    </div>
                                </div>
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <div class="nk-sale-data-group">
                                            <div class="nk-sale-data">
                                                <canvas id="categoryPieChart" style="height: 250px;"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="nk-sale-data-group">
                                            <div class="card-text">
                                                <div class="text-center">
                                                    <h5 class="title">Toplam Satış: {{ number_format($totals['total_amount'], 2) }} ₺</h5>
                                                </div>
                                                <div class="amount-list mt-4">
                                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <div class="rounded-circle" style="width: 12px; height: 12px; background-color: #798bff;"></div>
                                                            <span class="ms-2">Kategori 1</span>
                                                        </div>
                                                        <span class="text-success">{{ number_format(rand(15, 35)) }}%</span>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <div class="rounded-circle" style="width: 12px; height: 12px; background-color: #1ee0ac;"></div>
                                                            <span class="ms-2">Kategori 2</span>
                                                        </div>
                                                        <span class="text-success">{{ number_format(rand(15, 35)) }}%</span>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between mb-2">
                                                        <div class="d-flex align-items-center">
                                                            <div class="rounded-circle" style="width: 12px; height: 12px; background-color: #f4bd0e;"></div>
                                                            <span class="ms-2">Kategori 3</span>
                                                        </div>
                                                        <span class="text-success">{{ number_format(rand(10, 25)) }}%</span>
                                                    </div>
                                                    <div class="d-flex align-items-center justify-content-between">
                                                        <div class="d-flex align-items-center">
                                                            <div class="rounded-circle" style="width: 12px; height: 12px; background-color: #09c2de;"></div>
                                                            <span class="ms-2">Diğer</span>
                                                        </div>
                                                        <span class="text-success">{{ number_format(rand(5, 15)) }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Chart data - Sales Bar Chart
            var ctx = document.getElementById('salesChart').getContext('2d');
            var salesChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($reportData as $data)
                            '{{ $data['period'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($reportData as $data)
                                    {{ $data['total_amount'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(120, 113, 255, 0.7)',
                        borderColor: '#7871ff',
                        borderWidth: 1
                    }, {
                        label: 'KDV Tutarı',
                        data: [
                            @foreach($reportData as $data)
                                    {{ $data['total_vat'] }},
                            @endforeach
                        ],
                        backgroundColor: 'rgba(231, 82, 82, 0.7)',
                        borderColor: '#e75252',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('tr-TR') + ' ₺';
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            @if($reportType === 'monthly' && isset($topProducts) && count($topProducts) > 0)
            // Category Pie Chart
            var pieCtx = document.getElementById('categoryPieChart').getContext('2d');
            var categoryPieChart = new Chart(pieCtx, {
                type: 'pie',
                data: {
                    labels: ['Kategori 1', 'Kategori 2', 'Kategori 3', 'Diğer'],
                    datasets: [{
                        data: [
                            {{ rand(20, 35) }},
                            {{ rand(20, 35) }},
                            {{ rand(15, 25) }},
                            {{ rand(5, 15) }}
                        ],
                        backgroundColor: ['#798bff', '#1ee0ac', '#f4bd0e', '#09c2de'],
                        borderColor: '#fff',
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.parsed + '%';
                                }
                            }
                        }
                    }
                }
            });
            @endif

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection