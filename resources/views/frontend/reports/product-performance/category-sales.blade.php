@extends('frontend.layouts.app')

@section('title', 'Kategori Bazlı Satış Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Kategori Bazlı Satış Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} {{ isset($quarter) ? 'Q'.$quarter : '' }} Dönemi Kategori Bazlı Satış Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-performance.category-sales', ['year' => $year, 'quarter' => $quarter, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-performance.category-sales', ['year' => $year, 'quarter' => $quarter, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.product-performance.category-sales') }}" method="GET" class="row gy-3">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="quarter">Çeyrek Dönem</label>
                                        <select class="form-select" id="quarter" name="quarter">
                                            <option value="">Tüm Yıl</option>
                                            <option value="1" {{ isset($quarter) && $quarter == 1 ? 'selected' : '' }}>İlk Çeyrek (Ocak-Mart)</option>
                                            <option value="2" {{ isset($quarter) && $quarter == 2 ? 'selected' : '' }}>İkinci Çeyrek (Nisan-Haziran)</option>
                                            <option value="3" {{ isset($quarter) && $quarter == 3 ? 'selected' : '' }}>Üçüncü Çeyrek (Temmuz-Eylül)</option>
                                            <option value="4" {{ isset($quarter) && $quarter == 4 ? 'selected' : '' }}>Dördüncü Çeyrek (Ekim-Aralık)</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary w-100">Filtrele</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Kategori</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Ürün Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Miktarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">KDV Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Tutar</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Aksiyonlar</span></div>
                                </div>

                                @foreach($categorySales as $category)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $category->category_name ?? 'Kategorisiz' }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->product_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->order_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->total_quantity, 2) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->total_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->total_vat, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($category->total_amount + $category->total_vat, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <a href="{{ route('frontend.reports.product-performance.category-sales', ['year' => $year, 'quarter' => $quarter, 'category_id' => $category->category_id]) }}" class="btn btn-sm btn-outline-primary">Detay</a>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['product_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_quantity'], 2) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'] + $totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Quarterly Sales Chart -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Çeyrek Dönem Satış Trendi</h6>
                                            <p class="text-soft">{{ $year }} yılı çeyreklere göre satış trendi</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="quarterlyChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Category Breakdown Pie Chart -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Kategori Dağılımı</h6>
                                            <p class="text-soft">Satış tutarına göre kategori dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="categoryPieChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Breakdown for Selected Category -->
                @if(isset($selectedCategoryId) && $selectedCategoryId && !empty($monthlyBreakdown))
                    <div class="nk-block mt-5">
                        <div class="row g-gs">
                            <div class="col-lg-12">
                                <div class="card card-bordered">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">Kategori Aylık Satış Trendi</h6>
                                                <p class="text-soft">{{ $year }} yılı seçili kategori için aylık satış trendi</p>
                                            </div>
                                        </div>
                                        <div class="nk-sales-ck">
                                            <canvas class="sales-bar-chart" id="monthlyCategoryChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });

            // Quarterly Chart
            var ctxQuarterly = document.getElementById('quarterlyChart').getContext('2d');
            var quarterlyChart = new Chart(ctxQuarterly, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($quarterlyTrendData as $quarter)
                            '{{ $quarter["quarter"] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($quarterlyTrendData as $quarter)
                                    {{ $quarter["total_amount"] }},
                            @endforeach
                        ],
                        backgroundColor: '#6576ff',
                        borderWidth: 0,
                        borderRadius: 4
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Category Pie Chart
            var ctxCategoryPie = document.getElementById('categoryPieChart').getContext('2d');
            var categoryPieChart = new Chart(ctxCategoryPie, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach($categorySales->take(5) as $category)
                            '{{ $category->category_name ?? "Kategorisiz" }}',
                        @endforeach
                                @if(count($categorySales) > 5)
                            'Diğer'
                        @endif
                    ],
                    datasets: [{
                        data: [
                            @foreach($categorySales->take(5) as $category)
                                    {{ $category->total_amount }},
                            @endforeach
                                    @if(count($categorySales) > 5)
                                    {{ $categorySales->skip(5)->sum('total_amount') }}
                                    @endif
                        ],
                        backgroundColor: [
                            '#6576ff', '#05a45c', '#f4bd0e', '#e85347', '#816bff',
                            '#09c2de'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            @if(isset($selectedCategoryId) && $selectedCategoryId && !empty($monthlyBreakdown))
            // Monthly Category Chart
            var ctxMonthlyCategory = document.getElementById('monthlyCategoryChart').getContext('2d');
            var monthlyCategoryChart = new Chart(ctxMonthlyCategory, {
                type: 'line',
                data: {
                    labels: [
                        @foreach($monthlyBreakdown as $item)
                            '{{ $item["month"] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($monthlyBreakdown as $item)
                                    {{ $item["total_amount"] }},
                            @endforeach
                        ],
                        borderColor: '#6576ff',
                        backgroundColor: 'rgba(101, 118, 255, 0.1)',
                        borderWidth: 2,
                        fill: true,
                        tension: 0.3
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            @endif
        });
    </script>
@endsection