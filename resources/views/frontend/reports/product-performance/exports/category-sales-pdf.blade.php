<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .container {
            width: 100%;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        .header h1 {
            font-size: 18px;
            margin: 0;
            padding: 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .text-end {
            text-align: right;
        }
        .totals-row td {
            font-weight: bold;
        }
        .footer {
            text-align: center;
            font-size: 10px;
            margin-top: 20px;
            padding-top: 10px;
            border-top: 1px solid #ddd;
        }
    </style>
</head>
<body>
<div class="container">
    <div class="header">
        <h1>{{ $title }}</h1>
        <p>Oluşturulma Tarihi: {{ now()->format('d.m.Y H:i') }}</p>
    </div>

    <table>
        <thead>
        <tr>
            <th>Kategori</th>
            <th class="text-end">Ürün Sayısı</th>
            <th class="text-end">Sipariş Sayısı</th>
            <th class="text-end">Satış Miktarı</th>
            <th class="text-end">Satış Tutarı (₺)</th>
            <th class="text-end">KDV Tutarı (₺)</th>
            <th class="text-end">Toplam Tutar (₺)</th>
        </tr>
        </thead>
        <tbody>
        @foreach($reportData as $category)
            <tr>
                <td>{{ $category->category_name ?? 'Kategorisiz' }}</td>
                <td class="text-end">{{ number_format($category->product_count) }}</td>
                <td class="text-end">{{ number_format($category->order_count) }}</td>
                <td class="text-end">{{ number_format($category->total_quantity, 2) }}</td>
                <td class="text-end">{{ number_format($category->total_amount, 2) }} ₺</td>
                <td class="text-end">{{ number_format($category->total_vat, 2) }} ₺</td>
                <td class="text-end">{{ number_format($category->total_amount + $category->total_vat, 2) }} ₺</td>
            </tr>
        @endforeach
        <tr class="totals-row">
            <td>TOPLAM</td>
            <td class="text-end">{{ number_format($totals['product_count']) }}</td>
            <td class="text-end">{{ number_format($totals['order_count']) }}</td>
            <td class="text-end">{{ number_format($totals['total_quantity'], 2) }}</td>
            <td class="text-end">{{ number_format($totals['total_amount'], 2) }} ₺</td>
            <td class="text-end">{{ number_format($totals['total_vat'], 2) }} ₺</td>
            <td class="text-end">{{ number_format($totals['total_amount'] + $totals['total_vat'], 2) }} ₺</td>
        </tr>
        </tbody>
    </table>

    <div class="footer">
        <p>Bu rapor otomatik olarak oluşturulmuştur.</p>
    </div>
</div>
</body>
</html>