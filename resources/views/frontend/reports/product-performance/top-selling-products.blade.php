@extends('frontend.layouts.app')

@section('title', 'En Çok Satan Ürünler')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">En Çok Satan Ürünler</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ isset($month) ? $month . '/'. $year : $year }} Dönemi En Çok Satan Ürünler Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-performance.top-selling', ['year' => $year, 'month' => $month, 'category_id' => $categoryId, 'limit' => $limit, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-performance.top-selling', ['year' => $year, 'month' => $month, 'category_id' => $categoryId, 'limit' => $limit, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Product Details Section (only shown when a product is selected) -->
                @if(isset($selectedProductId) && $selectedProductId)
                    <div class="nk-block mt-5">
                        <div class="row g-gs">
                            <div class="col-lg-12">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">Aylık Satış Trendi</h6>
                                                <p class="text-soft">{{ $year }} yılı seçili ürün için aylık satış trendi</p>
                                            </div>
                                        </div>
                                        <div class="nk-sale-data">
                                            <div class="row g-4">
                                                <div class="col-md-8">
                                                    <div class="nk-sales-ck large">
                                                        <canvas class="sales-bar-chart" id="monthlySalesChart"></canvas>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="card">
                                                        <div class="nk-ecwg nk-ecwg3">
                                                            <div class="card-inner pb-0">
                                                                <div class="card-title-group">
                                                                    <div class="card-title">
                                                                        <h6 class="title">Aylık Satış Dağılımı</h6>
                                                                    </div>
                                                                </div>
                                                                <div class="data">
                                                                    <div class="data-group">
                                                                        <div class="nk-ecwg3-ck">
                                                                            <canvas class="pie-chart" id="monthlyDistributionChart"></canvas>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.product-performance.top-selling') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month">
                                            <option value="">Tüm Aylar</option>
                                            @for($m = 1; $m <= 12; $m++)
                                                <option value="{{ $m }}" {{ isset($month) && $month == $m ? 'selected' : '' }}>
                                                    {{ Carbon\Carbon::create()->month($m)->format('F') }}
                                                </option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="category_id">Kategori</label>
                                        <select class="form-select" id="category_id" name="category_id">
                                            <option value="">Tüm Kategoriler</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}" {{ $categoryId == $category->id ? 'selected' : '' }}>
                                                    {{ $category->description }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <label class="form-label" for="limit">Limit</label>
                                        <select class="form-select" id="limit" name="limit">
                                            <option value="10" {{ $limit == 10 ? 'selected' : '' }}>10</option>
                                            <option value="20" {{ $limit == 20 ? 'selected' : '' }}>20</option>
                                            <option value="50" {{ $limit == 50 ? 'selected' : '' }}>50</option>
                                            <option value="100" {{ $limit == 100 ? 'selected' : '' }}>100</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-1">
                                    <div class="form-group">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary w-100">Filtrele</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Ürün Kodu</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Ürün Adı</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Kategori</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Marka</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Miktarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Aksiyonlar</span></div>
                                </div>

                                @foreach($topSellingProducts as $product)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $product->item_code }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->item_name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->category_name ?? 'Kategorisiz' }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->brand_name ?? '-' }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->order_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->total_quantity, 2) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->total_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <a href="{{ route('frontend.reports.product-performance.top-selling', ['year' => $year, 'month' => $month, 'product_id' => $product->id]) }}" class="btn btn-sm btn-outline-primary">Detay</a>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_quantity'], 2) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>



            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });

            @if(isset($selectedProductId) && $selectedProductId && !empty($monthlyTrendData))
            // Monthly Sales Trend Chart
            var ctxMonthlySales = document.getElementById('monthlySalesChart').getContext('2d');
            var monthlySalesChart = new Chart(ctxMonthlySales, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($monthlyTrendData as $item)
                            '{{ $item['month'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($monthlyTrendData as $item)
                                    {{ $item['total_amount'] }},
                            @endforeach
                        ],
                        backgroundColor: '#6576ff',
                        borderWidth: 0,
                        borderRadius: 4
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        },
                        legend: {
                            display: false
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Monthly Distribution Pie Chart
            var ctxDistribution = document.getElementById('monthlyDistributionChart').getContext('2d');
            var monthlyDistributionChart = new Chart(ctxDistribution, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach($monthlyTrendData as $item)
                            '{{ $item['month'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        data: [
                            @foreach($monthlyTrendData as $item)
                                    {{ $item['total_amount'] }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff', '#05a45c', '#f4bd0e', '#e85347', '#816bff',
                            '#09c2de', '#6b79c8', '#ffa353', '#ff9ef5', '#3a8dfe'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            @endif
        });
    </script>
@endsection