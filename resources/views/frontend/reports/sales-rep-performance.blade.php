@extends('frontend.layouts.app')

@section('title', 'Satış Temsilcisi Performans Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Satış Temsilcisi Performans Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Satış Temsilcisi Performans Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales-rep-performance', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales-rep-performance', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.sales-rep-performance') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            @for($m = 1; $m <= 12; $m++)
                                                <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>
                                                    {{ date('F', mktime(0, 0, 0, $m, 1)) }}
                                                </option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="sales_person_id">Satış Temsilcisi</label>
                                        <select class="form-select" id="sales_person_id" name="sales_person_id" onchange="this.form.submit()">
                                            <option value="">Tüm Temsilciler</option>
                                            @foreach($salesReps as $rep)
                                                <option value="{{ $rep->id }}" {{ $salesRepId == $rep->id ? 'selected' : '' }}>
                                                    {{ $rep->first_name }} ({{ $rep->sales_person_code }})
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Satış Temsilcisi</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Temsilci Kodu</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Müşteri Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Satış</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam KDV</span></div>
                                </div>

                                @foreach($performanceData as $rep)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $rep->sales_rep_name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $rep->sales_person_code }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($rep->order_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($rep->customer_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($rep->total_sales, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($rep->total_vat, 2) }} ₺</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['customer_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_sales'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @if($salesRepId)
                    <!-- Charts -->
                    <div class="nk-block mt-5">
                        <div class="row g-gs">
                            <!-- Monthly Trend Chart -->
                            <div class="col-lg-7">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">Aylık Satış Trendi</h6>
                                                <p class="text-soft">{{ $year }} yılı aylık satış dağılımı</p>
                                            </div>
                                        </div>
                                        <div class="nk-sales-ck">
                                            <canvas class="sales-bar-chart" id="monthlySalesChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Top Customers -->
                            <div class="col-lg-5">
                                <div class="card card-bordered h-100">
                                    <div class="card-inner">
                                        <div class="card-title-group align-start mb-3">
                                            <div class="card-title">
                                                <h6 class="title">En İyi Müşteriler</h6>
                                                <p class="text-soft">Satış tutarına göre ilk 5 müşteri</p>
                                            </div>
                                        </div>
                                        <div class="nk-sales-ck">
                                            <canvas class="sales-bar-chart" id="topCustomersChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });

            @if($salesRepId && count($monthlyTrendData) > 0)
            // Monthly Trend Chart
            var ctxMonthly = document.getElementById('monthlySalesChart').getContext('2d');
            var monthlySalesChart = new Chart(ctxMonthly, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($monthlyTrendData as $data)
                            '{{ $data['month'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($monthlyTrendData as $data)
                                    {{ $data['total_sales'] }},
                            @endforeach
                        ],
                        backgroundColor: '#6576ff',
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
            @endif

            @if($salesRepId && count($topCustomers) > 0)
            // Top Customers Chart
            var ctxTopCustomers = document.getElementById('topCustomersChart').getContext('2d');
            var topCustomersChart = new Chart(ctxTopCustomers, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach($topCustomers as $customer)
                            '{{ $customer->entity_name }}',
                        @endforeach
                    ],
                    datasets: [{
                        data: [
                            @foreach($topCustomers as $customer)
                                    {{ $customer->total_amount }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });
            @endif
        });
    </script>
@endsection