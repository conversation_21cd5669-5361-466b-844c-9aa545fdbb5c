@extends('frontend.layouts.app')

@section('title', 'Ödeme Metodları Analizi')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Ödeme Metodları Analizi</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ isset($month) ? date('F', mktime(0, 0, 0, $month, 1)) : '' }} {{ $year }} Dönemi Ödeme Metodları Analiz Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.payment-methods', ['year' => $year, 'month' => $month, 'entity_id' => $entityId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.payment-methods', ['year' => $year, 'month' => $month, 'entity_id' => $entityId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.payment-methods') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            @foreach(range(1, 12) as $m)
                                                <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>{{ date('F', mktime(0, 0, 0, $m, 1)) }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select js-select2" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Ödeme Metodu</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">İşlem Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Tutar</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Ortalama Tutar</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Yüzde</span></div>
                                </div>

                                @foreach($paymentData as $payment)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $payment->payment_method }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($payment->transaction_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($payment->total_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($payment->average_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format(($payment->total_amount / $totals['total_amount']) * 100, 2) }}%</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['transaction_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['average_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">100.00%</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Payment Method Distribution -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Ödeme Metodları Dağılımı</h6>
                                            <p class="text-soft">{{ $year }} yılı ödeme metodları dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="payment-methods-chart" id="paymentMethodsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Trend -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Aylık Ödeme Trendi</h6>
                                            <p class="text-soft">{{ $year }} yılı aylık ödeme trendi</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="line-chart" id="monthlyTrendChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Payment Methods Distribution Chart
            var ctxPaymentMethods = document.getElementById('paymentMethodsChart').getContext('2d');
            var paymentMethodsChart = new Chart(ctxPaymentMethods, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach($paymentData as $payment)
                            '{{ $payment->payment_method }}',
                        @endforeach
                    ],
                    datasets: [{
                        data: [
                            @foreach($paymentData as $payment)
                                    {{ $payment->total_amount }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff', '#05a45c', '#f4bd0e', '#e85347', '#816bff',
                            '#aadbff', '#7de5aa', '#f9db7b', '#f4a097', '#c5b8ff'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Monthly Trend Chart
            var ctxMonthlyTrend = document.getElementById('monthlyTrendChart').getContext('2d');
            var monthlyTrendChart = new Chart(ctxMonthlyTrend, {
                type: 'line',
                data: {
                    labels: [
                        @foreach($monthlyTrendData as $item)
                            '{{ $item['month'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Ödeme Tutarı',
                        data: [
                            @foreach($monthlyTrendData as $item)
                                    {{ $item['total_amount'] }},
                            @endforeach
                        ],
                        borderColor: '#6576ff',
                        backgroundColor: 'rgba(101, 118, 255, 0.1)',
                        borderWidth: 2,
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection