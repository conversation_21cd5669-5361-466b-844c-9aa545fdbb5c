@extends('frontend.layouts.app')

@section('title', 'Yıllık Satış Performans Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Yıllık Satış Performans Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $startYear }} - {{ $endYear }} Yılları Arası Satış Performans Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.yearly-sales', ['start_year' => $startYear, 'end_year' => $endYear, 'entity_id' => $entityId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.yearly-sales', ['start_year' => $startYear, 'end_year' => $endYear, 'entity_id' => $entityId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.yearly-sales') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="start_year">Başlangıç Yılı</label>
                                        <select class="form-select" id="start_year" name="start_year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $startYear == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="end_year">Bitiş Yılı</label>
                                        <select class="form-select" id="end_year" name="end_year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $endYear == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Yıl</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">KDV Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Genel Toplam</span></div>
                                </div>

                                @foreach($reportData as $data)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $data['period'] }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['order_count']) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['total_amount'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['total_vat'], 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['total'], 2) }} ₺</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total'], 2) }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <div class="col-lg-8">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Yıllık Satış Grafiği</h6>
                                            <p class="text-soft">{{ $startYear }} - {{ $endYear }} yılları arası satış toplamları</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="yearlySalesChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-4">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Satış Oranları</h6>
                                            <p class="text-soft">Yıllara göre satış dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-doughnut-chart" id="salesDistributionChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Year-over-year Comparison -->
                <div class="nk-block mt-5">
                    <div class="card card-bordered h-100">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Yıllık Büyüme Analizi</h6>
                                    <p class="text-soft">Yıl bazında satış büyüme oranları</p>
                                </div>
                            </div>
                            <div class="row g-4">
                                <div class="col-md-12">
                                    <div class="nk-sales-ck">
                                        <canvas class="line-chart" id="growthChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Yearly Sales Chart
            var ctxYearlySales = document.getElementById('yearlySalesChart').getContext('2d');
            var yearlySalesChart = new Chart(ctxYearlySales, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($reportData as $data)
                            '{{ $data['period'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($reportData as $data)
                                    {{ $data['total'] }},
                            @endforeach
                        ],
                        backgroundColor: '#6576ff'
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Sales Distribution Chart
            var ctxSalesDistribution = document.getElementById('salesDistributionChart').getContext('2d');
            var salesDistributionChart = new Chart(ctxSalesDistribution, {
                type: 'doughnut',
                data: {
                    labels: [
                        @foreach($reportData as $data)
                            '{{ $data['period'] }}',
                        @endforeach
                    ],
                    datasets: [{
                        data: [
                            @foreach($reportData as $data)
                                    {{ $data['total'] }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff',
                            '#7de1f8',
                            '#f9db7b',
                            '#a1c4fd',
                            '#f68084',
                            '#96e6a1'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Calculate growth rates
            var years = [
                @foreach($reportData as $data)
                    '{{ $data['period'] }}',
                @endforeach
            ];

            var totals = [
                @foreach($reportData as $data)
                        {{ $data['total'] }},
                @endforeach
            ];

            var growthRates = [];

            for (var i = 1; i < totals.length; i++) {
                var previousYear = totals[i-1];
                var currentYear = totals[i];
                var growthRate = ((currentYear - previousYear) / previousYear) * 100;
                growthRates.push(growthRate.toFixed(2));
            }

            // Year-over-year Growth Chart
            var ctxGrowth = document.getElementById('growthChart').getContext('2d');
            var growthChart = new Chart(ctxGrowth, {
                type: 'line',
                data: {
                    labels: years.slice(1), // Remove first year as we don't have growth rate for it
                    datasets: [{
                        label: 'Yıllık Büyüme Oranı (%)',
                        data: growthRates,
                        fill: false,
                        borderColor: '#6576ff',
                        tension: 0.1,
                        pointBackgroundColor: '#6576ff',
                        pointBorderColor: '#fff',
                        pointRadius: 5,
                        pointHoverRadius: 7
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y + '%';
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection