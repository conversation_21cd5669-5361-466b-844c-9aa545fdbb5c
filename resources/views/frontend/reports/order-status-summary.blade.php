@extends('frontend.layouts.app')

@section('title', 'Sipariş Durumu Özeti')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Sipariş Durumu Özeti</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Sipariş Durumu Özet Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.order-status-summary', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'entity_id' => $entityId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.order-status-summary', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'entity_id' => $entityId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.order-status-summary') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            {{ setlocale(LC_TIME, 'tr_TR.UTF-8')}}
                                            @for($m = 1; $m <= 12; $m++)
                                                <option value="{{ $m }}" {{ $month == $m ? 'selected' : '' }}>{{ date('F', mktime(0, 0, 0, $m, 1)) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="sales_person_id">Satış Temsilcisi</label>
                                        <select class="form-select" id="sales_person_id" name="sales_person_id" onchange="this.form.submit()">
                                            <option value="">Tüm Temsilciler</option>
                                            @foreach($salesReps as $rep)
                                                <option value="{{ $rep->id }}" {{ $salesRepId == $rep->id ? 'selected' : '' }}>{{ $rep->first_name }} ({{ $rep->sales_person_code }})</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="entity_id">Müşteri</label>
                                        <select class="form-select" id="entity_id" name="entity_id" onchange="this.form.submit()">
                                            <option value="">Tüm Müşteriler</option>
                                            @foreach($entities as $entity)
                                                <option value="{{ $entity->id }}" {{ $entityId == $entity->id ? 'selected' : '' }}>{{ $entity->entity_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Dönem</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Bekleyen</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Onaylandı</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Kısmen Sevk</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Tamamen Sevk</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">İptal</span></div>
                                    <div class="nk-tb-col text-center"><span class="sub-text">Reddedildi</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Sipariş</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Toplam Tutar</span></div>
                                </div>

                                @foreach($monthlyData as $monthKey => $data)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $data['month_name'] }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][0] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][1] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][2] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][3] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][4] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-center">
                                            <span>{{ $data['statuses'][5] ?? 0 }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['total_orders']) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($data['total_amount'] + $data['total_vat'], 2) }} ₺</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[0]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[1]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[2]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[3]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[4]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-center">
                                        <span class="fw-bold">{{ number_format($statusCounts[5]) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totalAmounts['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totalAmounts['total_amount'] + $totalAmounts['total_vat'], 2) }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Status Distribution -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Sipariş Durumu Dağılımı</h6>
                                            <p class="text-soft">{{ $year }} yılı sipariş durumu dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="statusChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Trend -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Sipariş Durumu Oranları</h6>
                                            <p class="text-soft">{{ $year }} yılı sipariş durumu oranları</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="statusPieChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Status Distribution Chart
            var ctxStatus = document.getElementById('statusChart').getContext('2d');
            var statusChart = new Chart(ctxStatus, {
                type: 'bar',
                data: {
                    labels: [
                        'Bekleyen',
                        'Onaylandı',
                        'Kısmen Sevk',
                        'Tamamen Sevk',
                        'İptal',
                        'Reddedildi'
                    ],
                    datasets: [{
                        label: 'Sipariş Sayısı',
                        data: [
                            {{ $statusCounts[0] }},
                            {{ $statusCounts[1] }},
                            {{ $statusCounts[2] }},
                            {{ $statusCounts[3] }},
                            {{ $statusCounts[4] }},
                            {{ $statusCounts[5] }}
                        ],
                        backgroundColor: [
                            '#6576ff', // Bekleyen
                            '#1ee0ac', // Onaylandı
                            '#f4bd0e', // Kısmen Sevk
                            '#09c2de', // Tamamen Sevk
                            '#e85347', // İptal
                            '#816bff'  // Reddedildi
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Status Pie Chart
            var ctxPie = document.getElementById('statusPieChart').getContext('2d');
            var statusPieChart = new Chart(ctxPie, {
                type: 'pie',
                data: {
                    labels: [
                        'Bekleyen',
                        'Onaylandı',
                        'Kısmen Sevk',
                        'Tamamen Sevk',
                        'İptal',
                        'Reddedildi'
                    ],
                    datasets: [{
                        data: [
                            {{ $statusCounts[0] }},
                            {{ $statusCounts[1] }},
                            {{ $statusCounts[2] }},
                            {{ $statusCounts[3] }},
                            {{ $statusCounts[4] }},
                            {{ $statusCounts[5] }}
                        ],
                        backgroundColor: [
                            '#6576ff', // Bekleyen
                            '#1ee0ac', // Onaylandı
                            '#f4bd0e', // Kısmen Sevk
                            '#09c2de', // Tamamen Sevk
                            '#e85347', // İptal
                            '#816bff'  // Reddedildi
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection