<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 5px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-size: 10px;
        }
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        .number {
            text-align: right;
        }
        .footer {
            margin-top: 20px;
            font-size: 10px;
            text-align: center;
        }
    </style>
</head>
<body>
<div class="header">
    <h2>{{ $title }}</h2>
    <p>Oluşturulma Tarihi: {{ date('d.m.Y') }}</p>
</div>

<table>
    <thead>
    <tr>
        <th>Ü<PERSON><PERSON><PERSON></th>
        <th><PERSON><PERSON><PERSON><PERSON></th>
        <th><PERSON><PERSON><PERSON></th>
        <th><PERSON><PERSON></th>
        <th><PERSON><PERSON><PERSON><PERSON> Sayısı</th>
        <th>Satış Miktarı</th>
        <th>Satış Tutarı (₺)</th>
        <th>KDV Tutarı (₺)</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $product)
        <tr>
            <td>{{ $product->item_code }}</td>
            <td>{{ $product->item_name }}</td>
            <td>{{ $product->category_name ?? '-' }}</td>
            <td>{{ $product->brand_name ?? '-' }}</td>
            <td class="number">{{ number_format($product->order_count, 0, ',', '.') }}</td>
            <td class="number">{{ number_format($product->total_quantity, 2, ',', '.') }}</td>
            <td class="number">{{ number_format($product->total_amount, 2, ',', '.') }}</td>
            <td class="number">{{ number_format($product->total_vat, 2, ',', '.') }}</td>
        </tr>
    @endforeach
    <tr class="total-row">
        <td colspan="4">TOPLAM</td>
        <td class="number">{{ number_format($totals['order_count'], 0, ',', '.') }}</td>
        <td class="number">{{ number_format($totals['total_quantity'], 2, ',', '.') }}</td>
        <td class="number">{{ number_format($totals['total_amount'], 2, ',', '.') }}</td>
        <td class="number">{{ number_format($totals['total_vat'], 2, ',', '.') }}</td>
    </tr>
    </tbody>
</table>

<div class="footer">
    <p>Bu rapor otomatik olarak oluşturulmuştur.</p>
</div>
</body>
</html>