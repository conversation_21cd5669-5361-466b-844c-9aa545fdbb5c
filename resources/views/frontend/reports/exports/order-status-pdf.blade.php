<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0;
            padding: 0;
        }
        .header p {
            font-size: 12px;
            margin: 5px 0 0;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .footer {
            margin-top: 20px;
            font-size: 10px;
            text-align: center;
            color: #666;
        }
        .totals {
            font-weight: bold;
            background-color: #f8f8f8;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>{{ $title }}</h1>
    <p><PERSON><PERSON>şturulma tarihi: {{ now()->format('d.m.Y H:i') }}</p>
</div>

<table>
    <thead>
    <tr>
        <th>Dönem</th>
        <th class="text-center">Bekleyen</th>
        <th class="text-center">Onaylandı</th>
        <th class="text-center">Kısmen Sevk</th>
        <th class="text-center">Tamamen Sevk</th>
        <th class="text-center">İptal</th>
        <th class="text-center">Reddedildi</th>
        <th class="text-right">Toplam Sipariş</th>
        <th class="text-right">Toplam Tutar (₺)</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $monthKey => $data)
        <tr>
            <td>{{ $data['month_name'] }}</td>
            <td class="text-center">{{ $data['statuses'][0] ?? 0 }}</td>
            <td class="text-center">{{ $data['statuses'][1] ?? 0 }}</td>
            <td class="text-center">{{ $data['statuses'][2] ?? 0 }}</td>
            <td class="text-center">{{ $data['statuses'][3] ?? 0 }}</td>
            <td class="text-center">{{ $data['statuses'][4] ?? 0 }}</td>
            <td class="text-center">{{ $data['statuses'][5] ?? 0 }}</td>
            <td class="text-right">{{ number_format($data['total_orders']) }}</td>
            <td class="text-right">{{ number_format($data['total_amount'] + $data['total_vat'], 2) }} ₺</td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr class="totals">
        <td>TOPLAM</td>
        <td class="text-center">{{ number_format($totals[0] ?? 0) }}</td>
        <td class="text-center">{{ number_format($totals[1] ?? 0) }}</td>
        <td class="text-center">{{ number_format($totals[2] ?? 0) }}</td>
        <td class="text-center">{{ number_format($totals[3] ?? 0) }}</td>
        <td class="text-center">{{ number_format($totals[4] ?? 0) }}</td>
        <td class="text-center">{{ number_format($totals[5] ?? 0) }}</td>
        <td class="text-right">{{ number_format(array_sum($totals)) }}</td>
        <td class="text-right">{{ number_format($totals['total_amount'] + $totals['total_vat'], 2) }} ₺</td>
    </tr>
    </tfoot>
</table>

<div class="footer">
    <p>© {{ date('Y') }} B2B Portal - Bu rapor bilgilendirme amaçlıdır.</p>
</div>
</body>
</html>