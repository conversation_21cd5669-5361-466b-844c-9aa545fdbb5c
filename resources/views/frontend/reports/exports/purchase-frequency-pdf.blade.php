<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .page-header {
            margin-bottom: 20px;
            text-align: center;
        }
        h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .report-date {
            font-size: 12px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }
        .summary-section {
            margin-bottom: 20px;
        }
        .badge {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-size: 10px;
            color: white;
            font-weight: bold;
        }
        .badge-success {
            background-color: #28a745;
        }
        .badge-primary {
            background-color: #007bff;
        }
        .badge-info {
            background-color: #17a2b8;
        }
        .badge-warning {
            background-color: #ffc107;
            color: #333;
        }
        .badge-danger {
            background-color: #dc3545;
        }
        .badge-secondary {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
<div class="page-header">
    <h1>{{ $title }}</h1>
    <div class="company-info">Şirket B2B Portalı</div>
    <div class="report-date">Hazırlanma Tarihi: {{ date('d.m.Y H:i') }}</div>
</div>

<div class="summary-section">
    <h3>Özet Bilgiler</h3>
    <table>
        <tr>
            <th>Toplam Müşteri Sayısı</th>
            <th>Toplam Sipariş Sayısı</th>
            <th>Toplam Satış Tutarı</th>
        </tr>
        <tr>
            <td class="text-center">{{ number_format($totals['customer_count']) }}</td>
            <td class="text-center">{{ number_format($totals['order_count']) }}</td>
            <td class="text-right">{{ number_format($totals['total_amount'], 2) }} ₺</td>
        </tr>
    </table>
</div>

<h3>Müşteri Satınalma Sıklığı Analizi</h3>
<table>
    <thead>
    <tr>
        <th>Müşteri Kodu</th>
        <th>Müşteri Adı</th>
        <th>Satış Temsilcisi</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-right">Toplam Tutar</th>
        <th class="text-center">İlk Sipariş</th>
        <th class="text-center">Son Sipariş</th>
        <th class="text-center">Ort. Sipariş Aralığı</th>
        <th class="text-center">Aylık Sipariş</th>
        <th class="text-center">Son Siparişten Beri</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $customer)
        <tr>
            <td>{{ $customer['entity_code'] ?? '-' }}</td>
            <td>{{ $customer['entity_name'] }}</td>
            <td>{{ $customer['sales_rep_name'] ?? '-' }}</td>
            <td class="text-center">{{ number_format($customer['order_count']) }}</td>
            <td class="text-right">{{ number_format($customer['total_amount'], 2) }} ₺</td>
            <td class="text-center">{{ \Carbon\Carbon::parse($customer['first_order_date'])->format('d.m.Y') }}</td>
            <td class="text-center">{{ \Carbon\Carbon::parse($customer['last_order_date'])->format('d.m.Y') }}</td>
            <td class="text-center">
                        <span class="badge badge-{{ $customer['avg_days_between_orders'] <= 7 ? 'success' :
                            ($customer['avg_days_between_orders'] <= 30 ? 'primary' :
                            ($customer['avg_days_between_orders'] <= 90 ? 'info' :
                            ($customer['avg_days_between_orders'] <= 180 ? 'warning' :
                            ($customer['avg_days_between_orders'] <= 365 ? 'danger' : 'secondary')))) }}">
                            {{ $customer['avg_days_between_orders'] }} gün
                        </span>
            </td>
            <td class="text-center">{{ number_format($customer['orders_per_month'], 2) }}</td>
            <td class="text-center">
                        <span class="badge badge-{{ $customer['days_since_last_order'] <= 30 ? 'success' :
                            ($customer['days_since_last_order'] <= 90 ? 'info' :
                            ($customer['days_since_last_order'] <= 180 ? 'warning' : 'danger')) }}">
                            {{ $customer['days_since_last_order'] }} gün
                        </span>
            </td>
        </tr>
    @endforeach
    <tr class="totals-row">
        <td colspan="3">TOPLAM ({{ number_format($totals['customer_count']) }} Müşteri)</td>
        <td class="text-center">{{ number_format($totals['order_count']) }}</td>
        <td class="text-right">{{ number_format($totals['total_amount'], 2) }} ₺</td>
        <td colspan="5"></td>
    </tr>
    </tbody>
</table>

<div class="summary-section">
    <h3>Satınalma Sıklığı Kategorileri</h3>
    <p>
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #28a745; margin-right: 5px;"></span> Günlük (0-7 gün)
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #007bff; margin-left: 15px; margin-right: 5px;"></span> Haftalık (8-30 gün)
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #17a2b8; margin-left: 15px; margin-right: 5px;"></span> Aylık (31-90 gün)
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #ffc107; margin-left: 15px; margin-right: 5px;"></span> 3 Aylık (91-180 gün)
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #dc3545; margin-left: 15px; margin-right: 5px;"></span> 6 Aylık (181-365 gün)
        <span style="display: inline-block; width: 15px; height: 15px; background-color: #6c757d; margin-left: 15px; margin-right: 5px;"></span> Yıllık ve Düzensiz (365+ gün)
    </p>
</div>

<div style="font-size: 10px; text-align: center; margin-top: 30px;">
    <p>Bu rapor {{ date('d.m.Y H:i') }} tarihinde B2B Portalı tarafından otomatik olarak oluşturulmuştur.</p>
</div>
</body>
</html>