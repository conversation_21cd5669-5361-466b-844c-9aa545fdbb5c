<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .totals-row {
            font-weight: bold;
            background-color: #f3f3f3;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>{{ $title }}</h1>
    <p>Oluşturulma Tarihi: {{ date('d.m.Y H:i') }}</p>
</div>

<table>
    <thead>
    <tr>
        <th>Ödeme Metodu</th>
        <th class="text-right">İşlem Sayısı</th>
        <th class="text-right">Toplam Tutar (₺)</th>
        <th class="text-right">Ortalama Tutar (₺)</th>
        <th class="text-right">Yüzde (%)</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $payment)
        <tr>
            <td>{{ $payment->payment_method }}</td>
            <td class="text-right">{{ number_format($payment->transaction_count) }}</td>
            <td class="text-right">{{ number_format($payment->total_amount, 2) }} ₺</td>
            <td class="text-right">{{ number_format($payment->average_amount, 2) }} ₺</td>
            <td class="text-right">{{ number_format(($payment->total_amount / $totals['total_amount']) * 100, 2) }}%</td>
        </tr>
    @endforeach
    <tr class="totals-row">
        <td>TOPLAM</td>
        <td class="text-right">{{ number_format($totals['transaction_count']) }}</td>
        <td class="text-right">{{ number_format($totals['total_amount'], 2) }} ₺</td>
        <td class="text-right">{{ number_format($totals['average_amount'], 2) }} ₺</td>
        <td class="text-right">100.00%</td>
    </tr>
    </tbody>
</table>

<div class="footer">
    <p>Bu rapor, B2B portal sistemi tarafından otomatik olarak oluşturulmuştur.</p>
</div>
</body>
</html>