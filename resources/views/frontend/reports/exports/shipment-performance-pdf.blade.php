<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1 {
            font-size: 18px;
            margin: 0;
            padding: 0;
        }
        .header p {
            font-size: 12px;
            margin: 5px 0 0;
            color: #666;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .summary-card {
            border: 1px solid #ddd;
            padding: 10px;
            flex: 1;
            margin: 0 5px;
            text-align: center;
        }
        .summary-card h2 {
            font-size: 14px;
            margin: 0 0 5px;
        }
        .summary-card .amount {
            font-size: 20px;
            font-weight: bold;
        }
        .summary-card .percentage {
            font-size: 12px;
            color: #666;
        }
        .success {
            color: #1ee0ac;
        }
        .danger {
            color: #e85347;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .footer {
            margin-top: 20px;
            font-size: 10px;
            text-align: center;
            color: #666;
        }
        .totals {
            font-weight: bold;
            background-color: #f8f8f8;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            color: white;
        }
        .bg-success {
            background-color: #1ee0ac;
        }
        .bg-warning {
            background-color: #f4bd0e;
        }
        .bg-danger {
            background-color: #e85347;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>{{ $title }}</h1>
    <p>Oluşturulma tarihi: {{ now()->format('d.m.Y H:i') }}</p>
</div>

<div class="summary">
    <div class="summary-card">
        <h2>Toplam Sevkiyat</h2>
        <div class="amount">{{ number_format($totals['order_count']) }}</div>
    </div>
    <div class="summary-card">
        <h2>Zamanında Teslimat</h2>
        <div class="amount success">{{ number_format($totals['on_time_count']) }}</div>
        <div class="percentage success">{{ $totals['on_time_percentage'] }}%</div>
    </div>
    <div class="summary-card">
        <h2>Geç Teslimat</h2>
        <div class="amount danger">{{ number_format($totals['late_count']) }}</div>
        <div class="percentage danger">{{ $totals['late_percentage'] }}%</div>
    </div>
    <div class="summary-card">
        <h2>Ort. Teslimat Süresi</h2>
        <div class="amount">{{ number_format($totals['avg_delivery_days'], 1) }}</div>
        <div class="percentage">gün</div>
    </div>
</div>

<table>
    <thead>
    <tr>
        <th>Müşteri</th>
        <th>Taşıma Tipi</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-center">Ort. Teslim. (Gün)</th>
        <th class="text-center">Zamanında</th>
        <th class="text-center">Geç</th>
        <th class="text-right">Zamanında %</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $item)
        <tr>
            <td>{{ $item->entity_name }}</td>
            <td>{{ $item->transport_type ?? 'Belirtilmemiş' }}</td>
            <td class="text-center">{{ number_format($item->order_count) }}</td>
            <td class="text-center">{{ number_format($item->avg_delivery_days, 1) }}</td>
            <td class="text-center success">{{ number_format($item->on_time_count) }}</td>
            <td class="text-center danger">{{ number_format($item->late_count) }}</td>
            <td class="text-right">
                        <span class="badge {{ $item->on_time_percentage >= 90 ? 'bg-success' : ($item->on_time_percentage >= 75 ? 'bg-warning' : 'bg-danger') }}">
                            {{ $item->on_time_percentage }}%
                        </span>
            </td>
        </tr>
    @endforeach
    </tbody>
    <tfoot>
    <tr class="totals">
        <td>TOPLAM</td>
        <td></td>
        <td class="text-center">{{ number_format($totals['order_count']) }}</td>
        <td class="text-center">{{ number_format($totals['avg_delivery_days'], 1) }}</td>
        <td class="text-center success">{{ number_format($totals['on_time_count']) }}</td>
        <td class="text-center danger">{{ number_format($totals['late_count']) }}</td>
        <td class="text-right">
                    <span class="badge {{ $totals['on_time_percentage'] >= 90 ? 'bg-success' : ($totals['on_time_percentage'] >= 75 ? 'bg-warning' : 'bg-danger') }}">
                        {{ $totals['on_time_percentage'] }}%
                    </span>
        </td>
    </tr>
    </tfoot>
</table>

<div class="footer">
    <p>© {{ date('Y') }} B2B Portal - Bu rapor bilgilendirme amaçlıdır.</p>
</div>
</body>
</html>