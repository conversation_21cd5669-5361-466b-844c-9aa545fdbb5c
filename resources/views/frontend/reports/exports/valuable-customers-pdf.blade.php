<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.5;
            color: #333;
        }
        .page-header {
            margin-bottom: 20px;
            text-align: center;
        }
        h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .company-info {
            font-size: 14px;
            margin-bottom: 10px;
        }
        .report-date {
            font-size: 12px;
            margin-bottom: 20px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .totals-row {
            background-color: #f9f9f9;
            font-weight: bold;
        }
    </style>
</head>
<body>
<div class="page-header">
    <h1>{{ $title }}</h1>
    <div class="company-info">Şirket B2B Portalı</div>
    <div class="report-date">Hazırlanma Tarihi: {{ date('d.m.Y H:i') }}</div>
</div>

<table>
    <thead>
    <tr>
        <th>Sıra</th>
        <th>Müşteri Kodu</th>
        <th>Müşteri Adı</th>
        <th>Satış Temsilcisi</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-right">Toplam Tutar</th>
        <th class="text-right">Toplam KDV</th>
        <th class="text-right">Toplam Yüzdesi</th>
        <th class="text-right">Kümülatif</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $customer)
        <tr>
            <td>{{ $customer['rank'] }}</td>
            <td>{{ $customer['entity_code'] ?? '-' }}</td>
            <td>{{ $customer['entity_name'] }}</td>
            <td>{{ $customer['sales_rep_name'] ?? '-' }}</td>
            <td class="text-center">{{ number_format($customer['order_count']) }}</td>
            <td class="text-right">{{ number_format($customer['total_amount'], 2) }} ₺</td>
            <td class="text-right">{{ number_format($customer['total_vat'], 2) }} ₺</td>
            <td class="text-right">{{ number_format($customer['percent_of_total'], 2) }}%</td>
            <td class="text-right">{{ number_format($customer['cumulative_percent'], 2) }}%</td>
        </tr>
    @endforeach
    <tr class="totals-row">
        <td colspan="4">TOPLAM</td>
        <td class="text-center">{{ number_format($totals['order_count']) }}</td>
        <td class="text-right">{{ number_format($totals['total_amount'], 2) }} ₺</td>
        <td class="text-right">{{ number_format($totals['total_vat'], 2) }} ₺</td>
        <td class="text-right">100.00%</td>
        <td class="text-right">100.00%</td>
    </tr>
    </tbody>
</table>

<div style="font-size: 10px; text-align: center; margin-top: 30px;">
    <p>Bu rapor {{ date('d.m.Y H:i') }} tarihinde B2B Portalı tarafından otomatik olarak oluşturulmuştur.</p>
</div>
</body>
</html>