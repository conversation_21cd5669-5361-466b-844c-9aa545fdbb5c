<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: <PERSON><PERSON><PERSON><PERSON>, sans-serif;
            font-size: 10pt;
        }
        h1 {
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 5px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .summary-box {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
        }
        .badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 9pt;
            color: white;
        }
        .bg-success {
            background-color: #1ee0ac;
        }
        .bg-warning {
            background-color: #f4bd0e;
        }
        .bg-danger {
            background-color: #e85347;
        }
    </style>
</head>
<body>
<h1>{{ $title }}</h1>

<div class="summary-box">
    <h2>Özet Bilgiler</h2>
    <div class="summary-row">
        <span class="summary-label">Teklif Sayısı:</span>
        <span>{{ number_format($reportData['summary']['offer_count']) }}</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Teklif Tutarı:</span>
        <span>{{ number_format($reportData['summary']['offer_amount'], 2) }} ₺</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Sipariş Sayısı:</span>
        <span>{{ number_format($reportData['summary']['order_count']) }}</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Sipariş Tutarı:</span>
        <span>{{ number_format($reportData['summary']['order_amount'], 2) }} ₺</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Dönüşüm Oranı:</span>
        <span>{{ number_format($reportData['summary']['conversion_rate'], 2) }}%</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Değer Dönüşüm Oranı:</span>
        <span>{{ number_format($reportData['summary']['value_conversion_rate'], 2) }}%</span>
    </div>
</div>

<h2>Aylık Dönüşüm Verileri</h2>
<table>
    <thead>
    <tr>
        <th>Ay</th>
        <th class="text-center">Teklif Sayısı</th>
        <th class="text-right">Teklif Tutarı</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-right">Sipariş Tutarı</th>
        <th class="text-center">Dönüşüm Oranı</th>
        <th class="text-center">Değer Dönüşüm Oranı</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData['salesRepPerformance'] as $salesRep)
        <tr>
            <td>{{ $salesRep['name'] }}</td>
            <td class="text-center">{{ number_format($salesRep['offer_count']) }}</td>
            <td class="text-right">{{ number_format($salesRep['offer_amount'], 2) }} ₺</td>
            <td class="text-center">{{ number_format($salesRep['order_count']) }}</td>
            <td class="text-right">{{ number_format($salesRep['order_amount'], 2) }} ₺</td>
            <td class="text-center">
                        <span class="badge {{ $salesRep['conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($salesRep['conversion_rate'], 2) }}%
                        </span>
            </td>
            <td class="text-center">
                        <span class="badge {{ $salesRep['value_conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['value_conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($salesRep['value_conversion_rate'], 2) }}%
                        </span>
            </td>
        </tr>
    @endforeach
    </tbody>
</table>
@endif

<div style="text-align: center; font-size: 8pt; margin-top: 30px;">
    <p>Bu rapor {{ date('d.m.Y H:i') }} tarihinde oluşturulmuştur.</p>
</div>
</body>
</html>ariş Sayısı</th>
<th class="text-right">Sipariş Tutarı</th>
<th class="text-center">Dönüşüm Oranı</th>
<th class="text-center">Değer Dönüşüm Oranı</th>
</tr>
</thead>
<tbody>
@foreach($reportData['monthlyData'] as $data)
    <tr>
        <td>{{ $data['month'] }}</td>
        <td class="text-center">{{ number_format($data['offer_count']) }}</td>
        <td class="text-right">{{ number_format($data['offer_amount'], 2) }} ₺</td>
        <td class="text-center">{{ number_format($data['order_count']) }}</td>
        <td class="text-right">{{ number_format($data['order_amount'], 2) }} ₺</td>
        <td class="text-center">
                        <span class="badge {{ $data['conversion_rate'] >= 50 ? 'bg-success' : ($data['conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($data['conversion_rate'], 2) }}%
                        </span>
        </td>
        <td class="text-center">
                        <span class="badge {{ $data['value_conversion_rate'] >= 50 ? 'bg-success' : ($data['value_conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($data['value_conversion_rate'], 2) }}%
                        </span>
        </td>
    </tr>
@endforeach
</tbody>
</table>

@if(!empty($reportData['salesRepPerformance']))
    <h2>Satış Temsilcisi Performansı</h2>
    <table>
        <thead>
        <tr>
            <th>Satış Temsilcisi</th>
            <th class="text-center">Teklif Sayısı</th>
            <th class="text-right">Teklif Tutarı</th>
            <th class="text-center">Sipariş Sayısı</th>
            <th class="text-right">Sipariş Tutarı</th>
            <th class="text-center">Dönüşüm Oranı</th>
            <th class="text-center">Değer Dönüşüm Oranı</th>
        </tr>
        </thead>
            <tbody>
            @foreach($reportData['salesRepPerformance'] as $salesRep)
                <tr>
                    <td>{{ $salesRep['name'] }}</td>
                    <td class="text-center">{{ number_format($salesRep['offer_count']) }}</td>
                    <td class="text-right">{{ number_format($salesRep['offer_amount'], 2) }} ₺</td>
                    <td class="text-center">{{ number_format($salesRep['order_count']) }}</td>
                    <td class="text-right">{{ number_format($salesRep['order_amount'], 2) }} ₺</td>
                    <td class="text-center">
                        <span class="badge {{ $salesRep['conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($salesRep['conversion_rate'], 2) }}%
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="badge {{ $salesRep['value_conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['value_conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                            {{ number_format($salesRep['value_conversion_rate'], 2) }}%
                        </span>
                    </td>
                </tr>
            @endforeach
            </tbody>
    </table>
@endif

<div style="text-align: center; font-size: 8pt; margin-top: 30px;">
    <p>Bu rapor {{ date('d.m.Y H:i') }} tarihinde oluşturulmuştur.</p>
</div>
</body>
</html>