<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: 'Helvetica', 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        h1 {
            color: #2c3e50;
            font-size: 24px;
            margin-bottom: 10px;
        }
        .summary {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .summary-box {
            width: 23%;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
            text-align: center;
        }
        .summary-box h3 {
            margin-top: 0;
            font-size: 14px;
            color: #555;
        }
        .summary-box p {
            font-size: 18px;
            font-weight: bold;
            margin: 5px 0;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            font-size: 12px;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .text-right {
            text-align: right;
        }
        .text-center {
            text-align: center;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .totals-row {
            font-weight: bold;
            background-color: #f3f3f3;
        }
        .status-active {
            color: #05a45c;
        }
        .status-critical {
            color: #f4bd0e;
        }
        .status-ended {
            color: #e85347;
        }
        .progress-bar {
            background-color: #f0f0f0;
            height: 15px;
            width: 100%;
            border-radius: 10px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            text-align: center;
            font-size: 10px;
            line-height: 15px;
            color: white;
        }
        .progress-success {
            background-color: #05a45c;
        }
        .progress-warning {
            background-color: #f4bd0e;
        }
        .progress-danger {
            background-color: #e85347;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>{{ $title }}</h1>
    <p>Oluşturulma Tarihi: {{ date('d.m.Y H:i') }}</p>
</div>

<div class="summary" style="display: flex; flex-wrap: wrap;">
    <div class="summary-box" style="width: 22%; margin-right: 2%; margin-bottom: 20px;">
        <h3>Toplam Sözleşme Tutarı</h3>
        <p>{{ number_format($totals['total_amount'], 2) }} ₺</p>
    </div>
    <div class="summary-box" style="width: 22%; margin-right: 2%; margin-bottom: 20px;">
        <h3>Kullanılan Tutar</h3>
        <p>{{ number_format($totals['used_amount'], 2) }} ₺</p>
    </div>
    <div class="summary-box" style="width: 22%; margin-right: 2%; margin-bottom: 20px;">
        <h3>Kalan Tutar</h3>
        <p>{{ number_format($totals['remaining_amount'], 2) }} ₺</p>
    </div>
    <div class="summary-box" style="width: 22%; margin-bottom: 20px;">
        <h3>Ortalama Kullanım Oranı</h3>
        <p>{{ number_format($totals['average_usage'], 2) }}%</p>
    </div>
</div>

<table>
    <thead>
    <tr>
        <th>Sözleşme No</th>
        <th>Müşteri</th>
        <th>Başlangıç</th>
        <th>Bitiş</th>
        <th class="text-right">Toplam Tutar (₺)</th>
        <th class="text-right">Kullanılan (₺)</th>
        <th class="text-right">Kalan (₺)</th>
        <th class="text-center">Kullanım Oranı</th>
        <th class="text-center">Durum</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $contract)
        <tr>
            <td>{{ $contract->doc_no }}</td>
            <td>{{ $contract->entity_name }}</td>
            <td>{{ date('d.m.Y', strtotime($contract->contract_start_date)) }}</td>
            <td>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</td>
            <td class="text-right">{{ number_format($contract->total_amount, 2) }} ₺</td>
            <td class="text-right">{{ number_format($contract->used_amount, 2) }} ₺</td>
            <td class="text-right">{{ number_format($contract->remaining_amount, 2) }} ₺</td>
            <td class="text-center">
                <div class="progress-bar">
                    <div class="progress-fill {{ $contract->usage_percentage > 80 ? 'progress-danger' : ($contract->usage_percentage > 60 ? 'progress-warning' : 'progress-success') }}"
                         style="width: {{ $contract->usage_percentage }}%;">
                        {{ number_format($contract->usage_percentage, 2) }}%
                    </div>
                </div>
            </td>
            <td class="text-center">
                        <span class="status-{{ $contract->status_class == 'success' ? 'active' : ($contract->status_class == 'warning' ? 'critical' : 'ended') }}">
                            {{ $contract->status }}
                        </span>
            </td>
        </tr>
    @endforeach
    <tr class="totals-row">
        <td>TOPLAM</td>
        <td colspan="3"></td>
        <td class="text-right">{{ number_format($totals['total_amount'], 2) }} ₺</td>
        <td class="text-right">{{ number_format($totals['used_amount'], 2) }} ₺</td>
        <td class="text-right">{{ number_format($totals['remaining_amount'], 2) }} ₺</td>
        <td class="text-center">{{ number_format($totals['average_usage'], 2) }}%</td>
        <td></td>
    </tr>
    </tbody>
</table>

<div class="footer">
    <p>Bu rapor, B2B portal sistemi tarafından otomatik olarak oluşturulmuştur.</p>
</div>
</body>
</html>