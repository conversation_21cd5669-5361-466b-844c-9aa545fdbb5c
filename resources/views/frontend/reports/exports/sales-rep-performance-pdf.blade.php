<!DOCTYPE html>
<html lang="tr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            font-size: 22px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            color: #666;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .text-end {
            text-align: right;
        }
        .total-row {
            font-weight: bold;
            background-color: #f9f9f9;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
<div class="header">
    <h1>{{ $title }}</h1>
    <p>Oluşturulma Tarihi: {{ date('d.m.Y H:i') }}</p>
</div>

<table>
    <thead>
    <tr>
        <th>Satış Temsilcisi</th>
        <th>Temsilci Kodu</th>
        <th class="text-end">Sipariş Sayısı</th>
        <th class="text-end">Müşteri Sayısı</th>
        <th class="text-end">Toplam Satış (₺)</th>
        <th class="text-end">Toplam KDV (₺)</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData as $rep)
        <tr>
            <td>{{ $rep->sales_rep_name }}</td>
            <td>{{ $rep->sales_person_code }}</td>
            <td class="text-end">{{ number_format($rep->order_count) }}</td>
            <td class="text-end">{{ number_format($rep->customer_count) }}</td>
            <td class="text-end">{{ number_format($rep->total_sales, 2) }}</td>
            <td class="text-end">{{ number_format($rep->total_vat, 2) }}</td>
        </tr>
    @endforeach

    <tr class="total-row">
        <td colspan="2">TOPLAM</td>
        <td class="text-end">{{ number_format($totals['order_count']) }}</td>
        <td class="text-end">{{ number_format($totals['customer_count']) }}</td>
        <td class="text-end">{{ number_format($totals['total_sales'], 2) }}</td>
        <td class="text-end">{{ number_format($totals['total_vat'], 2) }}</td>
    </tr>
    </tbody>
</table>

<div class="footer">
    <p>Bu rapor otomatik olarak oluşturulmuştur. &copy; {{ date('Y') }} B2B Portal</p>
</div>
</body>
</html>