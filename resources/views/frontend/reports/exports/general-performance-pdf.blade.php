<!DOCTYPE html>
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>{{ $title }}</title>
    <style>
        body {
            font-family: <PERSON>ja<PERSON><PERSON>, sans-serif;
            font-size: 10pt;
        }
        h1 {
            font-size: 16pt;
            font-weight: bold;
            text-align: center;
            margin-bottom: 20px;
        }
        h2 {
            font-size: 14pt;
            font-weight: bold;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            padding: 5px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
        .text-right {
            text-align: right;
        }
        .summary-box {
            margin-bottom: 20px;
            padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .summary-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .summary-label {
            font-weight: bold;
        }
    </style>
</head>
<body>
<h1>{{ $title }}</h1>

<div class="summary-box">
    <h2>Özet Bilgiler</h2>
    <div class="summary-row">
        <span class="summary-label">Toplam Satış:</span>
        <span>{{ number_format($reportData['kpis']['total_sales'], 2) }} ₺</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Toplam Sipariş:</span>
        <span>{{ number_format($reportData['kpis']['total_orders']) }}</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Ortalama Sipariş Değeri:</span>
        <span>{{ number_format($reportData['kpis']['average_order_value'], 2) }} ₺</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Benzersiz Müşteri Sayısı:</span>
        <span>{{ number_format($reportData['kpis']['unique_customers']) }}</span>
    </div>
    <div class="summary-row">
        <span class="summary-label">Yıllık Büyüme:</span>
        <span>{{ number_format($reportData['kpis']['yoy_growth'], 2) }}%</span>
    </div>
</div>

<h2>En Çok Alım Yapan Müşteriler</h2>
<table>
    <thead>
    <tr>
        <th>Müşteri</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-right">Toplam Tutar</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData['topCustomers'] as $customer)
        <tr>
            <td>{{ $customer->entity_name }}</td>
            <td class="text-center">{{ number_format($customer->order_count) }}</td>
            <td class="text-right">{{ number_format($customer->total_amount, 2) }} ₺</td>
        </tr>
    @endforeach
    </tbody>
</table>

<h2>En Çok Satan Ürünler</h2>
<table>
    <thead>
    <tr>
        <th>Ürün Kodu</th>
        <th>Ürün Adı</th>
        <th class="text-center">Miktar</th>
        <th class="text-right">Toplam Tutar</th>
    </tr>
    </thead>
    <tbody>
    @foreach($reportData['topProducts'] as $product)
        <tr>
            <td>{{ $product->item_code }}</td>
            <td>{{ $product->item_name }}</td>
            <td class="text-center">{{ number_format($product->total_quantity, 2) }}</td>
            <td class="text-right">{{ number_format($product->total_amount, 2) }} ₺</td>
        </tr>
    @endforeach
    </tbody>
</table>

<h2>Aylık Satış Verileri</h2>
<table>
    <thead>
    <tr>
        <th>Ay</th>
        <th class="text-center">Sipariş Sayısı</th>
        <th class="text-right">Toplam Tutar</th>
    </tr>
    </thead>
    <tbody>
    @php
        $totalOrders = 0;
        $totalAmount = 0;
    @endphp
    @foreach($reportData['monthlySales'] as $monthlySale)
        @php
            $month = \Carbon\Carbon::create()->month($monthlySale->month)->format('F');
            $totalOrders += $monthlySale->order_count;
            $totalAmount += $monthlySale->total_amount;
        @endphp
        <tr>
            <td>{{ $month }}</td>
            <td class="text-center">{{ number_format($monthlySale->order_count) }}</td>
            <td class="text-right">{{ number_format($monthlySale->total_amount, 2) }} ₺</td>
        </tr>
    @endforeach
    <tr>
        <th>TOPLAM</th>
        <th class="text-center">{{ number_format($totalOrders) }}</th>
        <th class="text-right">{{ number_format($totalAmount, 2) }} ₺</th>
    </tr>
    </tbody>
</table>

<div style="text-align: center; font-size: 8pt; margin-top: 30px;">
    <p>Bu rapor {{ date('d.m.Y H:i') }} tarihinde oluşturulmuştur.</p>
</div>
</body>
</html>