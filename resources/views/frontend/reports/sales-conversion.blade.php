@extends('frontend.layouts.app')

@section('title', 'Satış Dönüşüm Oranı Raporu')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Satış Dönüşüm Oranı Raporu</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Satış Dönüşüm Metrikleri</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales-conversion', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.sales-conversion', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.sales-conversion') }}" method="GET" class="row gy-3">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            @for($i = 1; $i <= 12; $i++)
                                                <option value="{{ $i }}" {{ $month == $i ? 'selected' : '' }}>{{ date('F', mktime(0, 0, 0, $i, 1, 2000)) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="sales_person_id">Satış Temsilcisi</label>
                                        <select class="form-select" id="sales_person_id" name="sales_person_id" onchange="this.form.submit()">
                                            <option value="">Tüm Satış Temsilcileri</option>
                                            @foreach($salesReps as $salesRep)
                                                <option value="{{ $salesRep->id }}" {{ $salesRepId == $salesRep->id ? 'selected' : '' }}>{{ $salesRep->first_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="nk-block">
                    <div class="row g-gs">
                        <div class="col-xxl-6 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Satış Dönüşüm Oranı</h6>
                                        </div>
                                    </div>
                                    <div class="nk-ck-sm">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="circle bg-primary" data-progress="{{ $summary['conversion_rate'] }}" style="width: 80px; height: 80px;">
                                                    <div class="circle-progress-value text-center fs-3 fw-bold">{{ number_format($summary['conversion_rate'], 2) }}%</div>
                                                </div>
                                            </div>
                                            <div>
                                                <p class="mb-1">Teklifler: <strong>{{ number_format($summary['offer_count']) }}</strong></p>
                                                <p class="mb-0">Siparişler: <strong>{{ number_format($summary['order_count']) }}</strong></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-6 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Değer Dönüşüm Oranı</h6>
                                        </div>
                                    </div>
                                    <div class="nk-ck-sm">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="circle bg-danger" data-progress="{{ $summary['value_conversion_rate'] }}" style="width: 80px; height: 80px;">
                                                    <div class="circle-progress-value text-center fs-3 fw-bold">{{ number_format($summary['value_conversion_rate'], 2) }}%</div>
                                                </div>
                                            </div>
                                            <div>
                                                <p class="mb-1">Teklif Tutarı: <strong>{{ number_format($summary['offer_amount'], 2) }} ₺</strong></p>
                                                <p class="mb-0">Sipariş Tutarı: <strong>{{ number_format($summary['order_amount'], 2) }} ₺</strong></p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conversion Chart -->
                <div class="nk-block mt-4">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Aylık Dönüşüm Trendi</h6>
                                </div>
                            </div>
                            <div class="nk-sales-ck">
                                <canvas class="conversion-chart" id="conversionChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monthly Data Table -->
                <div class="nk-block mt-4">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Aylık Dönüşüm Detayları</h6>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                    <tr>
                                        <th>Ay</th>
                                        <th class="text-center">Teklif Sayısı</th>
                                        <th class="text-end">Teklif Tutarı</th>
                                        <th class="text-center">Sipariş Sayısı</th>
                                        <th class="text-end">Sipariş Tutarı</th>
                                        <th class="text-center">Dönüşüm Oranı</th>
                                        <th class="text-center">Değer Dönüşüm Oranı</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($monthlyData as $data)
                                        <tr>
                                            <td>{{ $data['month'] }}</td>
                                            <td class="text-center">{{ number_format($data['offer_count']) }}</td>
                                            <td class="text-end">{{ number_format($data['offer_amount'], 2) }} ₺</td>
                                            <td class="text-center">{{ number_format($data['order_count']) }}</td>
                                            <td class="text-end">{{ number_format($data['order_amount'], 2) }} ₺</td>
                                            <td class="text-center">
                                                    <span class="badge {{ $data['conversion_rate'] >= 50 ? 'bg-success' : ($data['conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                                                        {{ number_format($data['conversion_rate'], 2) }}%
                                                    </span>
                                            </td>
                                            <td class="text-center">
                                                    <span class="badge {{ $data['value_conversion_rate'] >= 50 ? 'bg-success' : ($data['value_conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                                                        {{ number_format($data['value_conversion_rate'], 2) }}%
                                                    </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Rep Performance Table -->
                @if(!$salesRepId && count($salesRepPerformance) > 0)
                    <div class="nk-block mt-4">
                        <div class="card card-bordered">
                            <div class="card-inner">
                                <div class="card-title-group align-start mb-3">
                                    <div class="card-title">
                                        <h6 class="title">Satış Temsilcisi Performansı</h6>
                                    </div>
                                </div>
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th>Satış Temsilcisi</th>
                                            <th class="text-center">Teklif Sayısı</th>
                                            <th class="text-end">Teklif Tutarı</th>
                                            <th class="text-center">Sipariş Sayısı</th>
                                            <th class="text-end">Sipariş Tutarı</th>
                                            <th class="text-center">Dönüşüm Oranı</th>
                                            <th class="text-center">Değer Dönüşüm Oranı</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($salesRepPerformance as $salesRep)
                                            <tr>
                                                <td>{{ $salesRep['name'] }}</td>
                                                <td class="text-center">{{ number_format($salesRep['offer_count']) }}</td>
                                                <td class="text-end">{{ number_format($salesRep['offer_amount'], 2) }} ₺</td>
                                                <td class="text-center">{{ number_format($salesRep['order_count']) }}</td>
                                                <td class="text-end">{{ number_format($salesRep['order_amount'], 2) }} ₺</td>
                                                <td class="text-center">
                                                    <span class="badge {{ $salesRep['conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                                                        {{ number_format($salesRep['conversion_rate'], 2) }}%
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge {{ $salesRep['value_conversion_rate'] >= 50 ? 'bg-success' : ($salesRep['value_conversion_rate'] >= 30 ? 'bg-warning' : 'bg-danger') }}">
                                                        {{ number_format($salesRep['value_conversion_rate'], 2) }}%
                                                    </span>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });

            // Circle progress
            $('.circle').each(function() {
                let value = $(this).data('progress');
                let color = $(this).hasClass('bg-primary') ? '#6576ff' : '#e85347';
                let radius = $(this).width() / 2;
                let circumference = 2 * Math.PI * (radius - 10);
                let dashoffset = circumference * (1 - value / 100);

                $(this).css({
                    'position': 'relative',
                    'border-radius': '50%',
                    'display': 'flex',
                    'justify-content': 'center',
                    'align-items': 'center'
                }).append('<svg width="100%" height="100%" viewBox="0 0 ' + (radius*2) + ' ' + (radius*2) + '" style="position: absolute; top: 0; left: 0; transform: rotate(-90deg);">' +
                    '<circle cx="' + radius + '" cy="' + radius + '" r="' + (radius - 10) + '" stroke-width="4" stroke="#f5f5f5" fill="none" />' +
                    '<circle cx="' + radius + '" cy="' + radius + '" r="' + (radius - 10) + '" stroke-width="8" stroke="' + color + '" fill="none" ' +
                    'style="stroke-dasharray: ' + circumference + '; stroke-dashoffset: ' + dashoffset + '" />' +
                    '</svg>');
            });

            // Conversion Chart
            var conversionCtx = document.getElementById('conversionChart').getContext('2d');
            var conversionData = @json($monthlyData);

            var months = conversionData.map(function(item) {
                return item.month;
            });

            var conversionRates = conversionData.map(function(item) {
                return item.conversion_rate;
            });

            var valueConversionRates = conversionData.map(function(item) {
                return item.value_conversion_rate;
            });

            var conversionChart = new Chart(conversionCtx, {
                type: 'line',
                data: {
                    labels: months,
                    datasets: [{
                        label: 'Dönüşüm Oranı (%)',
                        data: conversionRates,
                        backgroundColor: 'rgba(101, 118, 255, 0.2)',
                        borderColor: '#6576ff',
                        borderWidth: 2,
                        pointBackgroundColor: '#6576ff',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.3,
                        fill: true
                    }, {
                        label: 'Değer Dönüşüm Oranı (%)',
                        data: valueConversionRates,
                        backgroundColor: 'rgba(232, 83, 71, 0.2)',
                        borderColor: '#e85347',
                        borderWidth: 2,
                        pointBackgroundColor: '#e85347',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.3,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += context.parsed.y.toFixed(2) + '%';
                                    }
                                    return label;
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            }
                        }
                    }
                }
            });
        });
    </script>
@endsection