@extends('frontend.layouts.app')

@section('title', 'Ürün Satış Analizi')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Ürün Satış Analizi</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Ürün Satış Analiz Raporu</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-sales', ['year' => $year, 'category_id' => $categoryId, 'brand_id' => $brandId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.product-sales', ['year' => $year, 'category_id' => $categoryId, 'brand_id' => $brandId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.product-sales') }}" method="GET" class="row gy-3">
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="category_id">Kategori</label>
                                        <select class="form-select" id="category_id" name="category_id" onchange="this.form.submit()">
                                            <option value="">Tüm Kategoriler</option>
                                            @foreach($categories as $category)
                                                <option value="{{ $category->id }}" {{ $categoryId == $category->id ? 'selected' : '' }}>{{ $category->description }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-group">
                                        <label class="form-label" for="brand_id">Marka</label>
                                        <select class="form-select" id="brand_id" name="brand_id" onchange="this.form.submit()">
                                            <option value="">Tüm Markalar</option>
                                            @foreach($brands as $brand)
                                                <option value="{{ $brand->id }}" {{ $brandId == $brand->id ? 'selected' : '' }}>{{ $brand->description }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Data Table -->
                        <div class="card-inner p-0">
                            <div class="nk-tb-list nk-tb-ulist">
                                <div class="nk-tb-item nk-tb-head">
                                    <div class="nk-tb-col"><span class="sub-text">Ürün Kodu</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Ürün Adı</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Kategori</span></div>
                                    <div class="nk-tb-col"><span class="sub-text">Marka</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Sipariş Sayısı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Miktarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">Satış Tutarı</span></div>
                                    <div class="nk-tb-col text-end"><span class="sub-text">KDV Tutarı</span></div>
                                </div>

                                @foreach($productSales as $product)
                                    <div class="nk-tb-item">
                                        <div class="nk-tb-col">
                                            <span>{{ $product->item_code }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->item_name }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->category_name ?? '-' }}</span>
                                        </div>
                                        <div class="nk-tb-col">
                                            <span>{{ $product->brand_name ?? '-' }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->order_count) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->total_quantity, 2) }}</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->total_amount, 2) }} ₺</span>
                                        </div>
                                        <div class="nk-tb-col text-end">
                                            <span>{{ number_format($product->total_vat, 2) }} ₺</span>
                                        </div>
                                    </div>
                                @endforeach

                                <!-- Totals Row -->
                                <div class="nk-tb-item">
                                    <div class="nk-tb-col">
                                        <span class="fw-bold">TOPLAM</span>
                                    </div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col"></div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['order_count']) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_quantity'], 2) }}</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_amount'], 2) }} ₺</span>
                                    </div>
                                    <div class="nk-tb-col text-end">
                                        <span class="fw-bold">{{ number_format($totals['total_vat'], 2) }} ₺</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="nk-block mt-5">
                    <div class="row g-gs">
                        <!-- Category Distribution -->
                        <div class="col-lg-7">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">Kategori Dağılımı</h6>
                                            <p class="text-soft">{{ $year }} yılı kategori bazında satış dağılımı</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="categoryChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Top Products -->
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">En Çok Satan Ürünler</h6>
                                            <p class="text-soft">Satış tutarına göre ilk 5 ürün</p>
                                        </div>
                                    </div>
                                    <div class="nk-sales-ck">
                                        <canvas class="sales-bar-chart" id="topProductsChart"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Category Distribution Chart
            var ctxCategory = document.getElementById('categoryChart').getContext('2d');
            var categoryChart = new Chart(ctxCategory, {
                type: 'bar',
                data: {
                    labels: [
                        @foreach($categoryBreakdown as $category)
                            '{{ $category->category_name ?? "Kategori Yok" }}',
                        @endforeach
                    ],
                    datasets: [{
                        label: 'Satış Tutarı',
                        data: [
                            @foreach($categoryBreakdown as $category)
                                    {{ $category->total_amount }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff', '#7987ff', '#8c99ff', '#a0acff', '#b4beff',
                            '#c7d0ff', '#dbe2ff', '#eef3ff', '#f5f8ff', '#ffffff'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        }
                    },
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed.y);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Top Products Chart
            var ctxTopProducts = document.getElementById('topProductsChart').getContext('2d');
            var topProductsChart = new Chart(ctxTopProducts, {
                type: 'pie',
                data: {
                    labels: [
                        @foreach($productSales->take(5) as $product)
                            '{{ $product->item_name }}',
                        @endforeach
                    ],
                    datasets: [{
                        data: [
                            @foreach($productSales->take(5) as $product)
                                    {{ $product->total_amount }},
                            @endforeach
                        ],
                        backgroundColor: [
                            '#6576ff',
                            '#05a45c',
                            '#f4bd0e',
                            '#e85347',
                            '#816bff'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                boxWidth: 12,
                                padding: 20,
                                font: {
                                    size: 11
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed !== null) {
                                        label += new Intl.NumberFormat('tr-TR', {
                                            style: 'currency',
                                            currency: 'TRY'
                                        }).format(context.parsed);
                                    }
                                    return label;
                                }
                            }
                        }
                    }
                }
            });

            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });
        });
    </script>
@endsection