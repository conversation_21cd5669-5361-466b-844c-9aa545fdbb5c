@extends('frontend.layouts.app')

@section('title', 'Genel Performans Göstergeleri')

@section('content')
    <div class="container-fluid">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Genel Performans Göstergeleri</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ $year }} Yılı Performans Metrikleri</p>
                            </div>
                        </div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li>
                                            <a href="#" class="btn btn-white btn-outline-light btn-print"><em class="icon ni ni-printer"></em><span>Yazdır</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.general-performance', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'excel']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-download"></em><span>Excel</span></a>
                                        </li>
                                        <li>
                                            <a href="{{ route('frontend.reports.general-performance', ['year' => $year, 'month' => $month, 'sales_person_id' => $salesRepId, 'export' => 'pdf']) }}" class="btn btn-white btn-outline-light"><em class="icon ni ni-file-pdf"></em><span>PDF</span></a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner p-0 m-3">
                            <form action="{{ route('frontend.reports.general-performance') }}" method="GET" class="row gy-3">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="year">Yıl</label>
                                        <select class="form-select" id="year" name="year" onchange="this.form.submit()">
                                            @foreach($years as $y)
                                                <option value="{{ $y }}" {{ $year == $y ? 'selected' : '' }}>{{ $y }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="month">Ay</label>
                                        <select class="form-select" id="month" name="month" onchange="this.form.submit()">
                                            <option value="">Tüm Aylar</option>
                                            @for($i = 1; $i <= 12; $i++)
                                                <option value="{{ $i }}" {{ $month == $i ? 'selected' : '' }}>{{ date('F', mktime(0, 0, 0, $i, 1, 2000)) }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label class="form-label" for="sales_person_id">Satış Temsilcisi</label>
                                        <select class="form-select" id="sales_person_id" name="sales_person_id" onchange="this.form.submit()">
                                            <option value="">Tüm Satış Temsilcileri</option>
                                            @foreach($salesReps as $salesRep)
                                                <option value="{{ $salesRep->id }}" {{ $salesRepId == $salesRep->id ? 'selected' : '' }}>{{ $salesRep->first_name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- KPI Cards -->
                <div class="nk-block">
                    <div class="row g-gs">
                        <div class="col-xxl-3 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Toplam Satış</h6>
                                        </div>
                                    </div>
                                    <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                        <div class="nk-sale-data">
                                            <span class="amount">{{ number_format($kpis['total_sales'], 2) }} ₺</span>
                                            <span class="sub-title">
                                                <span class="change {{ $kpis['yoy_growth'] >= 0 ? 'up' : 'down' }}">
                                                    <em class="icon ni {{ $kpis['yoy_growth'] >= 0 ? 'ni-arrow-long-up' : 'ni-arrow-long-down' }}"></em>
                                                    {{ number_format(abs($kpis['yoy_growth']), 2) }}%
                                                </span>
                                                önceki yıla göre
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Toplam Sipariş</h6>
                                        </div>
                                    </div>
                                    <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                        <div class="nk-sale-data">
                                            <span class="amount">{{ number_format($kpis['total_orders']) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Ortalama Sipariş Değeri</h6>
                                        </div>
                                    </div>
                                    <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                        <div class="nk-sale-data">
                                            <span class="amount">{{ number_format($kpis['average_order_value'], 2) }} ₺</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xxl-3 col-md-6">
                            <div class="card h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-2">
                                        <div class="card-title">
                                            <h6 class="title">Benzersiz Müşteri Sayısı</h6>
                                        </div>
                                    </div>
                                    <div class="align-end flex-sm-wrap g-4 flex-md-nowrap">
                                        <div class="nk-sale-data">
                                            <span class="amount">{{ number_format($kpis['unique_customers']) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sales Chart -->
                <div class="nk-block mt-4">
                    <div class="card card-bordered">
                        <div class="card-inner">
                            <div class="card-title-group align-start mb-3">
                                <div class="card-title">
                                    <h6 class="title">Aylık Satış Trendi</h6>
                                </div>
                            </div>
                            <div class="nk-sales-ck">
                                <canvas class="sales-overview-chart" id="salesTrendChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tables -->
                <div class="nk-block mt-4">
                    <div class="row g-gs">
                        <!-- Top Customers -->
                        <div class="col-xxl-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">En Çok Alım Yapan Müşteriler</h6>
                                        </div>
                                    </div>
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th>Müşteri</th>
                                            <th class="text-center">Sipariş Sayısı</th>
                                            <th class="text-end">Toplam Tutar</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($topCustomers as $customer)
                                            <tr>
                                                <td>{{ $customer->entity_name }}</td>
                                                <td class="text-center">{{ number_format($customer->order_count) }}</td>
                                                <td class="text-end">{{ number_format($customer->total_amount, 2) }} ₺</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        <!-- Top Products -->
                        <div class="col-xxl-6">
                            <div class="card card-bordered h-100">
                                <div class="card-inner">
                                    <div class="card-title-group align-start mb-3">
                                        <div class="card-title">
                                            <h6 class="title">En Çok Satan Ürünler</h6>
                                        </div>
                                    </div>
                                    <table class="table table-striped">
                                        <thead>
                                        <tr>
                                            <th>Ürün Kodu</th>
                                            <th>Ürün Adı</th>
                                            <th class="text-center">Miktar</th>
                                            <th class="text-end">Toplam Tutar</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($topProducts as $product)
                                            <tr>
                                                <td>{{ $product->item_code }}</td>
                                                <td>{{ $product->item_name }}</td>
                                                <td class="text-center">{{ number_format($product->total_quantity, 2) }}</td>
                                                <td class="text-end">{{ number_format($product->total_amount, 2) }} ₺</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        $(document).ready(function() {
            // Print functionality
            $('.btn-print').on('click', function(e) {
                e.preventDefault();
                window.print();
            });

            // Sales Trend Chart
            var salesTrendCtx = document.getElementById('salesTrendChart').getContext('2d');
            var salesTrendChart = new Chart(salesTrendCtx, {
                type: 'bar',
                data: {
                    labels: {!! json_encode($monthLabels) !!},
                    datasets: [{
                        label: 'Satış Tutarı (₺)',
                        data: {!! json_encode($salesData) !!},
                        backgroundColor: '#6576ff',
                        borderWidth: 0,
                        yAxisID: 'y',
                        barPercentage: 0.5,
                        categoryPercentage: 0.7
                    }, {
                        label: 'Sipariş Sayısı',
                        data: {!! json_encode($orderCountData) !!},
                        backgroundColor: '#eb6459',
                        borderWidth: 0,
                        yAxisID: 'y1',
                        barPercentage: 0.5,
                        categoryPercentage: 0.7,
                        type: 'line',
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                        intersect: false,
                        mode: 'index',
                    },
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return new Intl.NumberFormat('tr-TR', {
                                        style: 'currency',
                                        currency: 'TRY',
                                        minimumFractionDigits: 0,
                                        maximumFractionDigits: 0
                                    }).format(value);
                                }
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            beginAtZero: true,
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.dataset.yAxisID === 'y') {
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('tr-TR', {
                                                style: 'currency',
                                                currency: 'TRY'
                                            }).format(context.parsed.y);
                                        }
                                    } else {
                                        if (context.parsed.y !== null) {
                                            label += new Intl.NumberFormat('tr-TR').format(context.parsed.y);
                                        }
                                    }
                                    return label;
                                }
                            }
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 15
                            }
                        }
                    }
                }
            });
        });
    </script>
@endsection