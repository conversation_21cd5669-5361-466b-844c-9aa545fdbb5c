<div class="modal-dialog" role="document">
    <div class="modal-content">
        <form action="{{ action('App\Http\Controllers\Frontend\ChequeRecordController@update', $cheque->id) }}" method="POST" id="cheque_record_form">
            @csrf
            @method('PUT')
        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                        aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Çek Kaydı Güncelle</h4>
        </div>

        <div class="modal-body">
            @if(!$can_update)
                <p class='text-center'><i class='fa fa-info-circle'></i> Sadece çek kaydını oluşturan kişi güncelleme yapabilir.</p>
            @endif
            @if($section == 'edit')
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">

                            <label for="customer_name">{{ __('contact.contact') }}:</label>
                            <input type="text" name="customer_name" id="customer_name" class="form-control" {{ $can_update ? '' : 'disabled' }} required placeholder="{{ __('Firma cari adı') }}" value="{{ $cheque->customer_name }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="form_bank_id">BANKA ADI:*</label>
                            <select name="bank_id" id="form_bank_id" class="form-select select2" {{ $can_update ? '' : 'disabled' }} required placeholder="{{ __('messages.all') }}">
                                @foreach($banks as $id => $name)
                                    <option value="{{ $id }}" {{ $cheque->bank_id == $id ? 'selected' : '' }}>{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            ```blade
                            <label for="amount">TUTAR:*</label>
                            <input type="text" name="amount" id="amount" class="form-control input_number" {{ $can_update ? '' : 'disabled' }} required placeholder="Çek tutarı" value="{{ $cheque->amount }}">
                            ```
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="currency_id">DOVİZ:*</label>
                            <select name="currency_id" id="currency_id" class="form-select select2" {{ $can_update ? '' : 'disabled' }} required placeholder="Seçiniz">
                                <option value="119" {{ $cheque->currency_id == 119 ? 'selected' : '' }}>TRY</option>
                                <option value="2" {{ $cheque->currency_id == 2 ? 'selected' : '' }}>USD</option>
                                <option value="38" {{ $cheque->currency_id == 38 ? 'selected' : '' }}>EUR</option>
                                <option value="19" {{ $cheque->currency_id == 19 ? 'selected' : '' }}>GDP</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="date">ÇEK TARİHİ:*</label>
                            <input type="text" name="date" id="date" class="form-control" {{ $can_update ? 'readonly' : 'disabled readonly' }} required placeholder="Gün-Ay-Yıl" value="{{ @format_date($cheque->date) }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="due_date">VADE TARİHİ:*</label>
                            <input type="text" name="due_date" id="due_date" class="form-control" {{ $can_update ? 'readonly' : 'disabled readonly' }} required placeholder="Gün-Ay-Yıl" value="{{ @format_date($cheque->due_date) }}">
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="contract_no">SÖZLEŞME NO:</label>
                            <input type="text" name="contract_no" id="contract_no" class="form-control" placeholder="İlgili sözleşme no" value="{{ $cheque->contract_no }}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">


                            <label for="documents">Çek & Sözleşme:</label>
                            <input type="file" name="documents[]" id="documents" multiple>

                        </div>
                    </div>

                </div>

                <div class="row">

                    <div class="clearfix"></div>
                    <div class="col-md-12">
                        <div class="form-group">

                            <label for="cheque_description">Açıklama/Notlar:</label>
                            <textarea name="description" id="cheque_description" class="form-control" rows="4" {{ $can_update ? '' : 'disabled' }}>{{ $cheque->description }}</textarea>
                        </div>
                    </div>
                </div>

                @if($cheque->media()->exists())
                    <hr>
                    <h3>Çek & Sözleşme Görüntüsü</h3>
                    @foreach($cheque->media as $media)
                        <p><a href='/uploads/media/{{ $media->file_name }}' target='_blank'>{{ $media->file_name }}</a>
                            @can('manage_modules')
                                <a href='#' class='btn btn-sm btn-danger btn-destroy-media'
                                   data-href="{{ action('App\Http\Controllers\Frontend\ChequeRecordController@index', ['media_id' => $media->id, 'action' => 'destroyMedia']) }}"
                                   role="button"><i class='fa fa-trash'></i></a>
                            @endcan
                        </p>
                    @endforeach
                @endif

            @endif

            @if($section == 'contract_no')
                <div class="col-md-12">
                    <div class="form-group">
                        <label for="contract_no">SÖZLEŞME NO:</label>
                        <input type="text" name="contract_no" id="contract_no" class="form-control" placeholder="İlgili sözleşme no" value="{{ $cheque->contract_no }}">
                    </div>
                </div>

                    <input type="hidden" name="only_contract_no" value="yes">
                    <input type="hidden" name="documents" value="">
            @endif

            @if($section == 'cargo')

                <div>
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" role="tablist">
                        <li role="presentation" class="active"><a href="#cargo" aria-controls="cargo" role="tab"
                                                                  data-toggle="tab">Kargo ile Teslim</a></li>
                        <li role="presentation"><a href="#not_cargo" aria-controls="not_cargo" role="tab"
                                                   data-toggle="tab">Elden Teslim</a></li>
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content">
                        <div role="tabpanel" class="tab-pane active" id="cargo" style='padding-top: 15px'>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">


                                        <label for="cargo_firm">KARGO FİRMASI ADI:</label>
                                        <input type="text" name="cargo_firm" id="cargo_firm" class="form-control" placeholder="Kargo firması adı" value="{{ $cheque->cargo_firm }}">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">


                                        <label for="cargo_tracking_number">KARGO TAKİP NO:</label>
                                        <input type="text" name="cargo_tracking_number" id="cargo_tracking_number" class="form-control" placeholder="Kargo takip no" value="{{ $cheque->cargo_tracking_number }}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div role="tabpanel" class="tab-pane" id="not_cargo" style='padding-top: 15px'>
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="form-group">


                                        <label for="person_delivered">TESLİM ALAN KİŞİ:</label>
                                        <input type="text" name="person_delivered" id="person_delivered" class="form-control" placeholder="Teslim alan kişi ad soyad" value="{{ $cheque->person_delivered }}">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">


                                        <label for="cargo_at">TESLİM TARİHİ:*</label>
                                        <input type="text" name="cargo_at" id="cargo_at" class="form-control" readonly required placeholder="Gün-Ay-Yıl ve Saat" value="{{ !empty($cheque->cargo_at) ? @format_datetime($cheque->cargo_at) : null }}">
                                    </div>
                                </div>


                                <input type="hidden" name="documents" value="">
                                <input type="hidden" name="cargo" value="yes">
                            </div>
                        </div>
                    </div>
                </div>

            @endif
        </div>

        <div class="modal-footer">
            <button type="submit" class="btn btn-primary ladda-button">@lang( 'messages.save' )</button>
            <button type="button" class="btn btn-default" data-dismiss="modal">@lang( 'messages.cancel' )</button>
        </div>
         </form>
    </div>
</div>
