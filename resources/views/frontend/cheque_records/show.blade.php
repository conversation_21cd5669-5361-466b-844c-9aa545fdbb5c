<div class="modal-dialog" role="document">
    <div class="modal-content">

        <div class="modal-header">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <h4 class="modal-title">Çek Detayı</h4>
        </div>
        <div class="modal-body">
            <table class="table">
                <tbody>
                <tr>
                    <td>CARİ ADI: </td>
                    <td>{{ $cheque->customer_name }}</td>
                </tr>
                <tr>
                    <td>SÖZLEŞME NO: </td>
                    <td>{{ $cheque->contract_no }}</td>
                </tr>
                <tr>
                    <td>BANKA ADI: </td>
                    <td>{{ $cheque->bank->name }}</td>
                </tr>
                <tr>
                    <td>TUTAR: </td>
                    <td>{{ $cheque->amount .' '.$cheque->currency_id }}</td>
                </tr>
                <tr>
                    <td>ÇEK TARİHİ: </td>
                    <td>{{ $cheque->date }}</td>
                </tr>
                <tr>
                    <td>VADE TARİHİ: </td>
                    <td>{{ $cheque->due_date }}</td>
                </tr>
                <tr>
                    <td>KARGO FİRMA: </td>
                    <td>{{ $cheque->cargo_firm }}</td>
                </tr>
                <tr>
                    <td>KARGO TAKİP NO: </td>
                    <td>{{ $cheque->cargo_tracking_number }}</td>
                </tr>
                <tr>
                    <td>ELDEN TESLİM ALAN KİŞİ: </td>
                    <td>{{ $cheque->person_delivered }}</td>
                </tr>
                <tr>
                    <td>GÖNDERİM TARİHİ: </td>
                    <td>{{ $cheque->cargo_at }}</td>
                </tr>
                <tr>
                    <td>TESLİM ALMA TARİHİ: </td>
                    <td>{{ $cheque->delivery_at	 }} - {{ ($cheque->delivery_user)->user_full_name }}</td>
                </tr>
                <tr>
                    <td>NOTLAR: </td>
                    <td>{{ $cheque->description }}</td>
                </tr>


                </tbody>
            </table>
            @if($cheque->media()->exists())

                <h4>Dosyalar</h4>
                <ol>
                    @foreach($cheque->media as $media)
                        <li><a href='/uploads/media/{{ $media->file_name }}' target='_blank'>{{ $media->file_name }}</a>
                            @can('shippingmanagement.edit')
                                <a href='#' class='btn btn-sm btn-danger btn-destroy-media' data-href="{{ action('App\Http\Controllers\Frontend\ChequeRecordController@index', ['media_id' => $media->id, 'action' => 'destroyMedia']) }}" role="button"> <i class='fa fa-trash'></i></a>
                            @endcan
                        </li>
                    @endforeach
                </ol>
            @endif
        </div>

    </div>
</div>
