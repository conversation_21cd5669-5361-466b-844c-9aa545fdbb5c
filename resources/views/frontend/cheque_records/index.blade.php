@extends('layouts.app')
@section('title', '<PERSON><PERSON>')

@section('content')

    <!-- Content Header (Page header) -->
    <section class="content-header">
        <h1><PERSON><PERSON></h1>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                @component('components.filters', ['title' => __('report.filters')])
                    <div class="col-sm-3">
                        <div class="form-group">
                            <label for="user_id">Kaydı Açan:</label>
                            <select name="user_id" id="user_id" class="form-select select2" placeholder="{{ __('messages.please_select') }}">
                                @if($is_admin)
                                    <option value="">{{ __('messages.please_select') }}</option>
                                @else
                                    <option value="{{ auth()->user()->id }}" selected>{{ auth()->user()->name }}</option>
                                @endif
                                @foreach($users as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>

                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="bank_id">Banka:</label>
                            <select name="bank_id" id="bank_id" class="form-select select2" required placeholder="{{ __('messages.all') }}">
                                <option value="">{{ __('messages.all') }}</option>
                                @foreach($banks as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="cargo_at">Kargo Durumu:</label>
                            <select name="cargo_at" id="cargo_at" class="form-select select2" required>
                                <option value="all">Tümü</option>
                                <option value="gone">Kargolandı</option>
                                <option value="waiting">Beklemede</option>
                            </select>
                        </div>
                    </div>

                    <div class="col-md-3">
                        <div class="form-group">
                            <label for="date_range">Çek Vade tarihi:</label>
                            <input type="text" name="date_range" id="date_range" class="form-control" placeholder="{{ __('lang_v1.select_a_date_range') }}" readonly>
                        </div>
                    </div>
                @endcomponent
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                @component('components.widget', ['class' => 'box-primary'])
                    @slot('tool')
                        <div class="box-tools">
                            <div class="btn-group" role="group" aria-label="...">
                                <button class="btn btn-xs btn-warning btn-modal" data-type="add"
                                        data-toggle="tooltip"
                                        data-href="{{action('App\Http\Controllers\Frontend\ChequeRecordController@create')}}"
                                        data-container="#modal_container" title="Yeni Çek Kaydı"><i
                                            class="fa fa-plus"></i> Yeni Çek Kaydı Ekle
                                </button>
                            </div>
                        </div>
                    @endslot
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover" id="cheque_records_table">
                            <thead>
                            <tr>
                                <th>#</th>
                                <th>Cari Adı</th>
                                <th>Kaydı Açan</th>
                                <th>Banka Adı</th>
                                <th>Tutar</th>
                                <th>Tarih</th>
                                <th>Vade Tarihi</th>
                                <th>Tanım</th>
                                <th>Sözleşme No</th>
                                <th>Kargo Durumu</th>
                                <th>İşlemler</th>
                            </tr>
                            </thead>
                        </table>
                    </div>
                @endcomponent
            </div>
        </div>
    </section>
    <div class="modal fade" id="modal_container" tabindex="-1" role="dialog"
         aria-labelledby="gridSystemModalLabel">
    </div>


@stop

@section('javascript')
    <script type="text/javascript">

        $(document).ready( function(){

            cheque_records_table = $('#cheque_records_table').DataTable({
                responsive: true,
                processing: true,
                serverSide: true,
                scrollY: "75vh",
                aaSorting: [[0, 'desc']],
                "ajax": {
                    "url": "{{ action('App\Http\Controllers\Frontend\ChequeRecordController@index') }}",
                    "data": function ( d ) {
                        d.cargo_at = $('#cargo_at').val();
                        d.user_id = $('#user_id').val();
                        d.bank_id = $('#bank_id').val();
                        d.start_date = $('input#date_range')
                            .data('daterangepicker')
                            .startDate.format('DD-MM-YYYY');
                        d.end_date = $('input#date_range')
                            .data('daterangepicker')
                            .endDate.format('DD-MM-YYYY');
                    }
                },

                "columns":[
                    { data: 'id', name: 'id' },
                    { data: 'customer_name', name: 'customer_name',orderable: true, searchable: true },
                    { data: 'user_id', name: 'user_id' },
                    { data: 'bank_id', name: 'bank_id' },
                    { data: 'amount', name: 'amount', orderable: false, searchable: false },
                    { data: 'date', name: 'date', orderable: false, searchable: false },
                    { data: 'due_date', name: 'due_date', orderable: false, searchable: false },
                    { data: 'description', name: 'description', orderable: false, searchable: false },
                    { data: 'contract_no', name: 'contract_no', orderable: false, searchable: false },
                    { data: 'cargo', name: 'cargo', orderable: false, searchable: false },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ]
            });
        });

        dateRangeSettings.startDate = moment().subtract(2, 'years');
        dateRangeSettings.endDate = moment().add(2, 'years');
        if ($('#date_range').length == 1) {
            $('#date_range').daterangepicker(
                dateRangeSettings,
                function (start, end) {
                    $('#date_range').val(
                        start.format(moment_date_format) + ' ~ ' + end.format(moment_date_format)
                    );
                    cheque_records_table.ajax.reload(null, false);
                }
            );
            $('#date_range').on('cancel.daterangepicker', function (ev, picker) {
                $('#product_sr_date_filter').val('');
                cheque_records_table.ajax.reload(null, false);
            });
        }

        $(document).on('change', '#user_id, #bank_id, #cargo_at, #date_range', function() {
            cheque_records_table.ajax.reload(null, false);
        });



        $('#modal_container').on('shown.bs.modal', function(e) {
            $('form#cheque_record_form .select2').select2({dropdownParent: $(this)});

            $('form#cheque_record_form #date, form#cheque_record_form #due_date, form#cheque_record_form #cargo_at').datepicker({
                format: 'dd-mm-yyyy',
                ignoreReadonly: true,
                language: 'tr',
                autoclose: true,
            });
            $('form#cheque_record_form #delivery_datetime').datetimepicker({
                format: moment_date_format + ' ' + moment_time_format,
                ignoreReadonly: true,
                locale: 'tr'
            });
        });

        var CSRF_TOKEN = document.querySelector('meta[name="csrf-token"]').getAttribute("content");
        $(document).on('submit', 'form#cheque_record_form', function (e) {
            e.preventDefault();
            var form = $("form#cheque_record_form")[0];
            var documents = form.documents;
            var data = new FormData(form);
            data.append('_token', CSRF_TOKEN);
            if (documents.length > 0) {
                data.append('documents', documents);
            }
            var url = $(this).attr("action");
            var method = $(this).attr("method");
            var ladda = Ladda.create(document.querySelector('.ladda-button'));
            ladda.start();
            $.ajax({
                method: method,
                url: url,
                data: data,
                dataType: "json",
                processData: false,
                contentType: false,
                success: function (result) {
                    ladda.stop();
                    if (result.success == true) {
                        $('div#modal_container').modal('hide');
                        toastr.success(result.msg);
                        cheque_records_table.ajax.reload(null, false);
                    } else {
                        toastr.error(result.msg);
                    }
                }
            });
        });


        $('#modal_container').on('shown.bs.modal', function (e) {
            if ($('form#offer_form .upload-zone').length) {
                NioApp.Dropzone('form#offer_form .upload-zone', {
                    url: "/images"
                });
            }
        });

        $(document).on('click', 'a.btn-delivered', function () {
            swal({
                title: 'Bu çek teslim alındı olarak kaydedilecek. Devam edilsin mi?',
                icon: 'warning',
                buttons: true,
                dangerMode: false,
            }).then(willDelete => {
                if (willDelete) {
                    var href = $(this).data('href');
                    var data = $(this).serialize();
                    $.ajax({
                        method: 'DELETE',
                        url: href,
                        dataType: 'json',
                        data: data,
                        success: function (result) {
                            if (result.success == true) {
                                toastr.success(result.msg);
                                cheque_records_table.ajax.reload(null, false);
                            } else {
                                toastr.error(result.msg);
                            }
                        },
                    });
                }
            });
        });


    </script>
@endsection
