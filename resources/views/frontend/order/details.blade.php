<div class="card">
    <div class="card-header border-bottom">{{ $order->doc_no }} {{ __('Numbered Order Detail') }}</div>
    <div class="card-inner">
        <table class="table table-tranx">
            <thead>
            <tr class="tb-tnx-head">
                <th>#</th>
                <th>{{ __('Date') }}</th>
                <th>{{ __('Product') }}</th>
                <th>{{ __('Quantity') }}</th>
                <th>{{ __('Unit Price') }}</th>
                <th>{{ __('Remaining Pallet') }}</th>
                <th>{{ __('Total') }}</th>
            </thead>
            <tbody>

            @foreach($order_details as $detail)

                <tr>
                    <td>{{ $detail->line_no }}</td>
                    <td>{{ date('d.m.Y', strtotime($detail->doc_date)) }}</td>
                    <td>{{ empty($detail->item_name) ? '-' : $detail->item_name }}</td>
                    <td>{{ $detail->qty > 0 ? number_format($detail->qty,2) . ' m²': '-' }}</td>
                    <td>@if($detail->qty>0){{ Number::currency($detail->unit_price,'try','tr') }}@else{{ '-' }}@endif</td>

                    <td>{{ $detail->zz_qty2 > 0 ? number_format(($detail->zz_qty2-$detail->qty_shipping),2). ' palet' : '-' }}</td>

                    <td>{{ Number::currency($detail->amt,'try','tr') }}</td>
                </tr>
            @endforeach
            </tbody>
            <tbody>
        </table>
    </div>
    <div class="card-footer border-top text-muted"></div>
</div>