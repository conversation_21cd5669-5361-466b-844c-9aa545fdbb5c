@extends('frontend.layouts.app')

@section('title')
    {{ __('Order Report') }}
@endsection
@section('css')

@endsection
@section('content')
    <div class="nk-content nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-body">
                <div class="nk-block-head">
                    <div class="nk-block-between-md g-4">
                        <div class="nk-block-head-content">
                            <h2 class="nk-block-title fw-normal">{{ __('Order Report') }}</h2>
                        </div>
                        <div class="nk-block-head-content">
                            <ul class="nk-block-tools g-3">
                                <li>
                                    <a href="{{ route('order.shipment_report')}}" class="btn btn-icon btn-dim btn-secondary d-md-none">
                                        <em class="icon ni ni-truck"></em></a>
                                    <a href="{{ route('order.shipment_report')}}" class="btn btn-dim btn-secondary d-none d-md-inline-flex"><em class="icon ni ni-truck"></em><span>{{ __('Order Shipment Report') }}</span>
                                    </a>
                                </li>
                                <li>
                                    <a href="/siparis/olustur" class="dropdown-toggle btn btn-primary"><em class="d-none d-sm-inline icon ni ni-plus-circle"></em><span><span class="d-none d-md-inline">{{ __('Order') }}</span> {{ __('Add') }}</span><em class="dd-indc icon ni ni-chevron-right"></em></a>
                                </li>
                            </ul>
                        </div>
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->
                <div class="nk-block nk-block-lg">
                    <div class="row g-gs">
                        <div class="col-lg-5">
                            <div class="card card-bordered h-100">
                                <div class="card-inner justify-center text-center h-100">
                                    <div class="nk-iv-wg5">
                                        <div class="nk-iv-wg5-head">
                                            <h5 class="nk-iv-wg5-title">{{ __('Contract Status') }}</h5>
                                        </div>
                                        <div class="nk-iv-wg5-ck">
                                            <input type="text" class="knob-half" value="{{ number_format(($active_contract_used_amount_sum/$active_contract_amt_sum*100),1) }}" data-fgColor="#33d895" data-bgColor="#d9e5f7" data-thickness=".06" data-width="300" data-height="155" data-displayInput="false">
                                            <div class="nk-iv-wg5-ck-result">
                                                <div class="text-lead">%{{ number_format(($active_contract_used_amount_sum/$active_contract_amt_sum*100),1) }}</div>
                                                <div class="text-sub">{{ __('Active contracts total amount spent') }}</div>
                                            </div>
                                            <div class="nk-iv-wg5-ck-minmax"><span>{{ Number::currency($active_contract_used_amount_sum, 'TRY', 'tr') }}</span><span>{{ Number::currency($active_contract_amt_sum, 'TRY', 'tr') }}</span></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!-- .col -->
                        <div class="col-lg-7">
                            <div class="card card-full card-bordered overflow-hidden">
                                <div class="nk-ecwg nk-ecwg4 h-100">
                                    <div class="card-inner flex-grow-1">
                                        <div class="card-title-group align-start mb-2">
                                            <div class="card-title">
                                                <h6 class="title">{{ __('Contract Distribution') }}</h6>
                                                <p>{{ __('Distribution of orders placed in the last 3 months according to contract types.') }}</p>
                                            </div>
                                        </div><!-- .card-title-group -->
                                        <div class="data-group">
                                            <div class="nk-ecwg4-ck">
                                                <canvas class="analytics-doughnut" id="LoanReportDoughnutData"></canvas>
                                            </div>
                                            <div class="flex-grow-1 ps-4">
                                                <ul class="nk-coin-ovwg6-legends gy-3">
                                                    <li><span class="dot dot-lg sq" data-bg="#D9E5F7"></span><span class="sub-text">{{ __('Total') }}</span> <span class="ms-auto lead-text">{{ $contract_orders->sum('total_orders') }}</span></li>
                                                    @foreach($contract_orders as $contract_order)
                                                        <li><span class="dot dot-lg sq" data-bg="{{ $colors[$loop->index] }}"></span><span class="sub-text">{{ $contract_order->doc_description }} (%{{ number_format($contract_order->percentage,1) }})</span> <span class="ms-auto lead-text">{{ $contract_order->total_orders }}</span></li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>
                                    </div><!-- .card-inner -->
                                </div>
                            </div><!-- .card -->
                        </div><!-- .col -->
                        <div class="col-12">
                            <div class="card card-bordered h-100">
                                <div class="card-inner border-bottom">
                                    <div class="card-title-group">
                                        <div class="card-title">
                                            <h6 class="title">{{ __('Transaction History') }}</h6>
                                        </div>
                                        <div class="card-tools">
                                            <a href="/siparis" class="link">{{ __('See All') }}</a>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-inner p-0">
                                    <table class="table table-tranx">
                                        <thead>
                                        <tr class="tb-tnx-head">
                                            <th class="tb-tnx-id"><span class="">#</span></th>
                                            <th class="tb-tnx-info">
                                                                <span class="tb-tnx-desc d-none d-sm-inline-block">
                                                                    <span>{{ __('Order No') }}</span>
                                                                </span>
                                                <span class="tb-tnx-date d-md-inline-block d-none">
                                                                    <span class="d-md-none">{{ __('Date') }}</span>
                                                                    <span class="d-none d-md-block">
                                                                        <span>{{ __('Date') }}</span>
                                                                    </span>
                                                                </span>
                                            </th>
                                            <th class="tb-tnx-amount is-alt">
                                                <span class="tb-tnx-total">{{ __('Amount') }}</span>
                                                <span class="tb-tnx-status d-none d-md-inline-block">{{ __('Status') }}</span>
                                            </th>
                                            <th class="tb-tnx-action">
                                                <span>&nbsp;</span>
                                            </th>
                                        </tr><!-- tb-tnx-item -->
                                        </thead>
                                        <tbody>
                                        @foreach($orders as $order)
                                            <tr class="tb-tnx-item">
                                                <td class="tb-tnx-id">
                                                    <a href="#" data-href="/siparis/{{ $order->id }}" data-container="#modal_container" class="btn-modal" ><span>{{ $order->id }}</span></a>
                                                </td>
                                                <td class="tb-tnx-info">
                                                    <div class="tb-tnx-desc">
                                                        <span class="title">{{ $order->doc_no }}</span>
                                                    </div>
                                                    <div class="tb-tnx-date">
                                                        <span class="date">{{ $order->doc_date->format('d.m.Y') }}</span>
                                                    </div>
                                                </td>
                                                <td class="tb-tnx-amount is-alt">
                                                    <div class="tb-tnx-total">
                                                        <span>{{ Number::currency(($order->amt+$order->amt_vat), $order->cur_code, app()->getLocale() ) }}</span>
                                                    </div>
                                                    <div class="tb-tnx-status">
                                                        <span class="badge badge-dot text-warning">{{ __('Completed') }}</span>
                                                    </div>
                                                </td>
                                                <td class="tb-tnx-action">
                                                    <div class="dropdown">
                                                        <a class="text-soft dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                        <div class="dropdown-menu dropdown-menu-end dropdown-menu-xs">
                                                            <ul class="link-list-plain">
                                                                <li><a href="#" data-href="/siparis/{{ encode_id($order->id) }}" data-container="#modal_container" class="btn-modal">{{ __('Details') }}</a></li>
                                                                <li><a href="#" data-href="/sozlesme/{{ encode_id($order->form_contract_m_id) }}?action=contract_details" data-container="#modal_container" class="btn-modal">{{ __('Contract') }}</a></li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr><!-- tb-tnx-item -->
                                        @endforeach

                                        </tbody>
                                    </table>
                                </div><!-- .card-inner -->
                            </div><!-- .card -->
                        </div>
                    </div><!-- .row -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>
@endsection

@section('js')
    <script>
        "use strict";

        !function (NioApp, $) {
            "use strict";

            var LoanReportDoughnutData = {
                labels: {!! $labels !!},
                dataUnit: '{{ __('Order') }}',
                legend: false,
                datasets: [{
                    borderColor: "#fff",
                    background: ['{!! implode ( "', '", $colors ) !!}'],
                    data: {{ $label_data }}
                }]
            };
            function analyticsDoughnut(selector, set_data) {
                var $selector = selector ? $(selector) : $('.analytics-doughnut');
                $selector.each(function () {
                    var $self = $(this),
                        _self_id = $self.attr('id'),
                        _get_data = typeof set_data === 'undefined' ? eval(_self_id) : set_data;
                    var selectCanvas = document.getElementById(_self_id).getContext("2d");
                    var chart_data = [];
                    for (var i = 0; i < _get_data.datasets.length; i++) {
                        chart_data.push({
                            backgroundColor: _get_data.datasets[i].background,
                            borderWidth: 2,
                            borderColor: _get_data.datasets[i].borderColor,
                            hoverBorderColor: _get_data.datasets[i].borderColor,
                            data: _get_data.datasets[i].data
                        });
                    }
                    var chart = new Chart(selectCanvas, {
                        type: 'doughnut',
                        data: {
                            labels: _get_data.labels,
                            datasets: chart_data
                        },
                        options: {
                            plugins: {
                                legend: {
                                    display: _get_data.legend ? _get_data.legend : false,
                                    rtl: NioApp.State.isRTL,
                                    labels: {
                                        boxWidth: 12,
                                        padding: 20,
                                        color: '#6783b8'
                                    }
                                },
                                tooltip: {
                                    enabled: true,
                                    rtl: NioApp.State.isRTL,
                                    callbacks: {
                                        label: function label(context) {
                                            return "".concat(context.parsed, " ").concat(_get_data.dataUnit);
                                        }
                                    },
                                    backgroundColor: '#fff',
                                    borderColor: '#eff6ff',
                                    borderWidth: 2,
                                    titleFont: {
                                        size: 13
                                    },
                                    titleColor: '#6783b8',
                                    titleMarginBottom: 6,
                                    bodyColor: '#9eaecf',
                                    bodyFont: {
                                        size: 12
                                    },
                                    bodySpacing: 4,
                                    padding: 10,
                                    footerMarginTop: 0,
                                    displayColors: false
                                }
                            },
                            rotation: -1.5,
                            cutoutPercentage: 70,
                            maintainAspectRatio: false
                        }
                    });
                });
            }
            // init chart
            NioApp.coms.docReady.push(function () {
                analyticsDoughnut();
            });
        }(NioApp, jQuery);
    </script>
@endsection