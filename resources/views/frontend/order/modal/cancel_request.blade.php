<div class="modal-dialog modal-lg" role="document">
    <form action="{{ url('siparis/'.$order->id) }}" method="post" id="cancel_request_form">
        <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
            <div class="modal-header bg-primary"><h5 class="modal-title text-white">{{ $order->doc_no }} {{ __('Cancellation Request') }}</h5></div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="cancel_reason">{{ __('Order') }}:</label>
                    <select name="order_m_id" class="form-select mb-3" disabled>
                        <option id="{{ $order->id }}">{{ $order->doc_no }}</option>
                    </select>
                    <label for="cancel_reason">{{ __('Related product') }}: *</label>
                    <select name="order_d_id" class="form-select mb-3">
                        @isset($order->order_d)
                            @foreach($order->order_d as $order_d)
                                <option value="{{ $order_d->id }}">{{ $order_d->product->item_name }}: {{ $order_d->qty }}</option>
                            @endforeach
                        @endisset
                    </select>
                    <label for="cancel_reason">{{ __('Pallet Quantity to Cancel') }}: *</label>
                    <input name="pallet_quantity" required class="form-control mb-3" type='number' step='any' max="{{ $order_d->qty }}" value='' placeholder='0.00' >
                    <label for="cancel_reason">{{ __('Cancellation Reason') }}: *</label>
                    <textarea class="form-control" name="cancel_reason" required id="cancel_reason"  rows="3"></textarea>
                </div>

                <button type="submit" class="btn btn-danger" id="cancel_request_form_submit">{{ __('Send Cancellation Request') }}</button>

                @csrf
                @method('put')
            </div>

        </div>
    </form>
</div>