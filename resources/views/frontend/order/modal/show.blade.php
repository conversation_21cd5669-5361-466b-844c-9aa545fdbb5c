<div class="modal-dialog modal-lg" role="document">
    <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
        <div class="modal-header bg-primary"><h5 class="modal-title text-white">{{ __('Order Detail') }} {{ $order->doc_no }}</h5></div>

        <table class="table table-tranx table-hover">
            <thead>
            <tr class="tb-tnx-head">
                <th>#</th>
                <th>{{ __('Date') }}</th>
                <th>{{ __('Product') }}</th>
                <th>{{ __('Quantity') }}</th>
                <th>{{ __('Unit Price') }}</th>
                <th>{{ __('Remaining Pallet') }}</th>
                <th>{{ __('Total') }}</th>
            </thead>
            <tbody>
            @foreach($orderDetails as $detail)
                <tr>
                    <td>{{ $detail->line_no }}</td>
                    <td>{{ date('d.m.Y', strtotime($detail->doc_date)) }}</td>
                    <td>{{ empty($detail->item_name) ? '-' : $detail->item_name }}</td>
                    <td>{{ $detail->qty > 0 ? number_format($detail->qty,2) : '-' }}</td>
                    <td>@if($detail->qty>0){{ Number::currency($detail->unit_price,'try','tr') }}@else{{ '-' }}@endif</td>
                    <td>{{ $detail->zz_qty2 > 0 ? number_format(($detail->zz_qty2-$detail->qty_shipping),2). ' palet' : '-' }}</td>
                    <td>{{ Number::currency($detail->amt+$detail->amt_vat,'try','tr') }}</td>
                </tr>
            @endforeach


            </tbody>
            <tbody>
        </table>


        <div class="modal-footer bg-light"><span class="sub-text">{{ __('Last update') }}: {{ date('d.m.Y') }}</span></div>
    </div>
</div>