@extends('frontend.layouts.app')

@section('title')
    {{ __('Production Query') }}
@endsection
@section('css')

@endsection
@section('content')
    <div class="page-content">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-inner">
                    <h4 class="card-title mb-1"><i class="ni ni-info-fill"></i> {{ __('Production Query') }}</h4>

                    <div class="form-group mt-4">
                        <div class="form-control-wrap">
                            <input type="text" class="form-control form-control-xl form-control-outlined" id="outlined-xl">
                            <label class="form-label-outlined" for="outlined-xl">{{ __('Enter query text') }}</label></div>
                    </div>
                    <div class="d-flex justify-content-between align-items-end">
                        <ol class="pt-2">
                            <li><em class="icon ni ni-dot"></em><span>{{ __('Enter Query') }}</span></li>
                            <li><em class="icon ni ni-dot"></em><span>{{ __('See Results') }}</span></li>
                            <li><em class="icon ni ni-dot"></em><span>{{ __('Get Production Information') }}</span></li>
                        </ol>
                        <span class="badge badge-dim badge-sm badge-pill bg-outline-info"></span></div>
                    <div class="collapse" id="collapseDes1" style="">
                        <div class="divider"></div>
                        <div class="rating-card-description"><h5 class="card-title">{{ __('Your previous queries') }}</h5>
                            <p class="text-muted">{{ __('Notes and information about your past queries. You can click on the relevant query to get detailed information.') }}</p>
                            <ul class="pt-2 gy-1">

                                <li><em class="icon ni ni-check-circle"></em><span>{{ __('Stock information dated 11/10/2024') }}</span></li>
                                <li class="text-warning"><em class="icon ni ni-clock text-warning"></em><span>{{ __('Stock information dated 10/10/2024') }}</span></li>
                                <li><em class="icon ni ni-check-circle"></em><span>{{ __('Stock information dated 1/10/2024') }}</span></li>
                                <li><em class="icon ni ni-check-circle"></em><span>{{ __('Stock information dated 22/05/2024') }}</span></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-footer rating-card-footer bg-light border-top d-flex align-center justify-content-between">
                    <a class="switch-text collapsed" data-bs-toggle="collapse" href="#collapseDes1"
                       aria-expanded="false">
                        <div class="link link-gray switch-text-normal"><span>{{ __('Hide') }}</span><em
                                    class="icon ni ni-upword-ios"></em></div>
                        <div class="link link-gray switch-text-collapsed"><span>{{ __('Your previous queries') }}</span><em
                                    class="icon ni ni-downward-ios"></em></div>
                    </a><a href="#" class="btn btn-primary">{{ __('Query') }}</a></div>
            </div>

        </div>

    </div>
    <!-- container-fluid -->
    </div>
@endsection

@section('js')
    <script src="/assets/js/libs/datatable-btns.js?ver=3.2.3"></script>
    <script>

        $(document).ready(function () {
            $(document).on('click', '.btn-modal', function (e) {
                e.preventDefault();
                var container = $(this).data('container');
                var url = $(this).data('href');

                $.ajax({
                    url: url,
                    dataType: 'html',
                    success: function (result) {
                        $(container)
                            .html(result)
                          //  .modal('show');

                        toastr.clear();
                        NioApp.Toast( result, 'success', {position: 'top-right'});
                    },
                });
            });
        });
    </script>
@endsection