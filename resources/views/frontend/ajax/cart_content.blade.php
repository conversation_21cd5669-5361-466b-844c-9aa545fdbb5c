@isset($cart)
<table class="datatable-init nk-tb-list nk-tb-ulist" data-auto-responsive="false">
    <thead>
    <tr class="nk-tb-item nk-tb-head">
        <th class="nk-tb-col"><span class="sub-text">STOK ADI</span></th>
        <th class="nk-tb-col tb-col-mb"><span class="sub-text">MİKTAR</span></th>
        <th class="nk-tb-col tb-col-mb"><span class="sub-text">PALET SAYISI</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">BİRİM FİYAT</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">TON FİYATI</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">TOPLAM</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">KDV</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">GENEL TOPLAM</span></th>
        <th class="nk-tb-col nk-tb-col-tools text-end">
        </th>
    </tr>
    </thead>
    <tbody>

    @foreach ($cart->cart_items as $cart_item)
@if($cart_item->product)
    <tr class="nk-tb-item @empty(!$cart_item->parent_id){{ 'bg-light bg-opacity-50' }}@endempty">
        <td class="nk-tb-col">
            <div class="user-card">
                <div class="user-info">
                    <span class="tb-lead" data-bs-toggle="tooltip" title="{{ $cart_item->product->item_name }}">
                        {{ $cart_item->product->item_name,1 }} <span class="dot dot-success d-md-none ms-1"></span>
                    </span>
                    @empty(!$cart_item->parent_id)<span>{{ limit_words($cart_item->parent_product->item_name,2) }}</span>@endempty
                </div>
            </div>
        </td>
        <td class="nk-tb-col tb-col-md">
            <span>{{ $cart_item->qty }} M<sup>2</sup></span>
        </td>
        <td class="nk-tb-col tb-col-md">
            {{ number_format($cart_item->quantity_pallet,2) }}
        </td>
        <td class="nk-tb-col tb-col-mb" data-order="">
            <span class="tb-amount">@if($cart_item->unit_price_tra>0){{ Number::currency($cart_item->unit_price_tra, 'TRY', 'tr') }}@else - @endif</span>
        </td>
        <td class="nk-tb-col tb-col-mb" data-order="">
            <span class="tb-amount">{{ Number::currency($cart_item->zz_ton_price, 'TRY', 'tr') }}</span>
        </td>
        <td class="nk-tb-col tb-col-md">
            <span>{{ Number::currency($cart_item->amt, 'TRY', 'tr') }}</span>
        </td>
        <td class="nk-tb-col tb-col-md">
            <span>{{ Number::currency($cart_item->amt_vat, 'TRY', 'tr') }}</span>
        </td>
        <td class="nk-tb-col tb-col-md">
            <span>{{ Number::currency($cart_item->amt + $cart_item->amt_vat, 'TRY', 'tr') }}</span>
        </td>

        <td class="nk-tb-col nk-tb-col-tools">
            <ul class="nk-tb-actions gx-1">

                <li>
                    @empty($cart_item->parent_id)
                    <a href="#" data-href="/delete-cart?item_id={{ $cart_item->id }}" data-container="#modal_container" class="btn btn-trigger btn-icon btn-modal" data-bs-toggle="tooltip" data-bs-placement="top" title="Sepetten sil">
                        <em class="icon ni ni-trash-alt"></em>
                    </a>
                    @else
                        <a href="#" class="btn btn-trigger btn-icon btn-modal" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $cart_item->parent_product->item_name }} sepetten silindiğinde silinir.">
                            <em class="icon ni ni-info-fill"></em>
                        </a>
                    @endempty
                </li>

            </ul>
        </td>
    </tr>
    @endif
    @endforeach



    </tbody>
    <tfoot>
    <tr class="nk-tb-item nk-tb-head">
        <th class="nk-tb-col"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-mb"><span class="sub-text">{{ $cart->qty }}</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">{{ $cart->quantity_pallet }}</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">{{ Number::currency($cart->amt, 'TRY', 'tr') }}</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">{{ Number::currency($cart->amt_vat, 'TRY', 'tr') }}</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">{{ Number::currency($cart->amt+$cart->amt_vat, 'TRY', 'tr') }}</span></th>
        <th class="nk-tb-col nk-tb-col-tools text-end">
        </th>
    </tr>
    </tfoot>
</table>

<div class="btn-group mt-3" id="cart_buttons">

    <a data-href="/delete-cart?id={{ $cart->id }}" data-container="#modal_container" class="btn-modal btn btn-block btn-outline-primary">
        <span>Sepeti Boşalt</span><em class="icon ni ni-trash-empty"></em>
    </a>
</div>
@else
<div class="alert alert-warning alert-icon"><em class="icon ni ni-alert-circle"></em><b>Sepetinizde ürün bulunmamaktadır.</div>

<table class="datatable-init nk-tb-list nk-tb-ulist" data-auto-responsive="false">
    <thead>
    <tr class="nk-tb-item nk-tb-head">
        <th class="nk-tb-col"><span class="sub-text">STOK ADI</span></th>
        <th class="nk-tb-col tb-col-mb"><span class="sub-text">MİKTAR</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">BİRİM FİYAT</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">TON FİYATI</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">TOPLAM</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">KDV</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">GENEL TOPLAM</span></th>
        <th class="nk-tb-col nk-tb-col-tools text-end">
        </th>
    </tr>
    </thead>
    <tbody>

    </tbody>
    <tfoot>
    <tr class="nk-tb-item nk-tb-head">
        <th class="nk-tb-col"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-mb"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text"></span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">₺0,00</span></th>
        <th class="nk-tb-col tb-col-md"><span class="sub-text">₺0,00</span></th>
        <th class="nk-tb-col tb-col-lg"><span class="sub-text">₺0,00</span></th>
        <th class="nk-tb-col nk-tb-col-tools text-end">
        </th>
    </tr>
    </tfoot>
</table>
@endif