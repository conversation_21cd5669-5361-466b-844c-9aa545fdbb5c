
        @if($action == 'contract_details')
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
            <div class="modal-header bg-primary"><h5 class="modal-title text-white"> Sözleşme Bilgileri</h5></div>

                    <table class="table">
                        <tbody>
                        <tr>
                            <td><b>Hareket Kodu</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
                        <tr>
                            <td><b><PERSON><PERSON></b></td>
                            <td>{{ $contract->entity->entity_name }}</td>
                        </tr>
                        <tr>
                            <td><b>Sözleşme Tarihi</b></td>
                            <td>{{ date('d.m.Y', strtotime($contract->doc_date)) }}</td>
                        </tr>
<tr>
                            <td><b>Sözleşme No</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
<tr>
                            <td><b>Başlangıç Tarihi</b></td>
                            <td>{{ date('d.m.Y', strtotime($contract->contract_start_date)) }}</td>
                        </tr>
<tr>
                            <td><b>Bitiş Tarihi</b></td>
                            <td>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }} ({{ $contract->contract_end_date->diffForHumans() }})</td>
                        </tr>
<tr>
                            <td><b>Satış Temsilcisi</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
<tr>
                            <td><b>Para Birimi</b></td>
                            <td>{{ $contract->currency->description }}</td>
                        </tr>
<tr>
                            <td><b>Sözleşme Tutarı</b></td>
                            <td>{{ number_format($contract->amt,2) }} {{ $contract->currency->cur_code }}</td>
                        </tr>
<tr>
                            <td><b>Kullanılan Tutar</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
<tr>
                            <td><b>Kalan Tutar</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
<tr>
                            <td><b>Son Sipariş Tarihi</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>
<tr>
                            <td><b>Ton Fiyatı</b></td>
                            <td>{!! nl2br($contract->note_large) !!}</td>
                        </tr>
<tr>
                            <td><b>Fiyat Listesi</b></td>
                            <td>{{ $contract->doc_no }}</td>
                        </tr>


                        </tbody>
                    </table>
                    <!-- DivTable.com -->


                </div>
            </div>

        @endif

        @if($action == 'quick')
            <div class="modal-dialog modal-xl" role="document">
                <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"
                                              aria-label="Close"> <em class="icon ni ni-cross"></em>
                    </a>
            <div class="modal-header"><h5 class="modal-title">{{ $contract->doc_no }} Sözleşme Detayı</h5></div>

            <div class="modal-body">
                <table class="datatable-init-export table table-striped table-hover">
                    <thead class="">
                    <tr>
                        <th>Tarih</th>
                        <th>Ürün</th>
                        <th>Miktar</th>
                        <th>Birim Fiyat</th>
                        <th>Toplam</th>
                    </thead>
                    <tbody>
                    @foreach($contractDetails as $detail)
                        <tr>
                            <td>{{ date('d.m.Y', strtotime($detail['doc_date'])) }}</td>
                            <td>{{ empty($detail['item_name']) ? '-' : $detail['item_name'] }}</td>
                            <td>{{ $detail['qty'] > 0 ? number_format($detail['qty'], 2) : '-' }}</td>
                            <td>@if($detail['qty']>0){{ number_format($detail['amt']/$detail['qty'],2) }}₺@else{{ '-' }}@endif</td>
                            <td>{{ number_format($detail['amt_tra'],2) }}₺</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>

                    <div class="modal-footer bg-light"><span
                                class="sub-text">Son güncelleme: {{ date('d.m.Y') }}</span></div>
                </div>
            </div>

        @endif

        @if($action == 'actions')
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"
                                                      aria-label="Close"> <em class="icon ni ni-cross"></em>
                            </a>
                    <div class="modal-header"><h5 class="modal-title">{{ $contract->doc_no }} Sözleşme İşlemleri</h5></div>
                    <div class="modal-body" style="min-height: 350px">
                @php
                    setlocale(LC_TIME, 'tr_TR.UTF-8');
                @endphp

                        <div class="simplebar-mask"><div class="simplebar-offset" ><div class="simplebar-content-wrapper" tabindex="0" role="region" aria-label="scrollable content" style="height: 100%; overflow: hidden scroll;"><div class="simplebar-content" style="padding: 0px;">

                                        <div class="card-inner">
                                            <div class="timeline">
                                                <ul class="timeline-list">
                                                    @foreach($contractDetails as $detail)
                                                        <li class="timeline-item">
                                                            @if($detail['qty']>0)
                                                                <div class="timeline-status bg-warning"></div>
                                                                <div class="timeline-date">{{ strftime('%e %B %Y', strtotime($detail['doc_date'])) }} </div>
                                                                <div class="timeline-data"><h6 class="timeline-title">{{ $detail['item_name'] }}</h6>
                                                                    <div class="timeline-des">
                                                                        <p>Tutar: {{ $detail['amt'] }} TL Miktar: {{ number_format($detail['qty'],2) }} adet</p>
                                                                        <span class="time">Birim fiyat: @if($detail['qty']>0){{ number_format($detail['amt']/$detail['qty'],2) }}₺@else{{ '-' }}@endif</span>
                                                                    </div>
                                                                </div>
                                                            @else
                                                                <div class="timeline-status bg-success"></div>
                                                                <div class="timeline-date">{{ strftime('%e %B', strtotime($detail['doc_date'])) }} <em class="icon ni ni-alarm-alt"></em></div>
                                                                <div class="timeline-data"><h6 class="timeline-title">İşlem</h6>
                                                                    <div class="timeline-des"><p>Tutar: {{ $detail['amt'] }}</p></div>
                                                                </div>
                                                            @endif
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>

                                    </div></div></div></div>


                    </div>
                            <div class="modal-footer bg-light"><span
                                        class="sub-text">Son güncelleme: {{ date('d.m.Y') }}</span></div>
                        </div>
                    </div>

        @endif




                        @if($action == 'update_end_date')
                            <div class="modal-dialog" role="document">
                                <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"
                                                              aria-label="Close"> <em class="icon ni ni-cross"></em>
                                    </a>
                    <div class="modal-header"><h5 class="modal-title">Sözleşme Bitiş Tarihi Güncelle</h5></div>
                    <div class="modal-body">
                        <p>{{ $contract->doc_no }} nolu sözleşme için güncel tarih: <b>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</b></p>

                        <form action="#" class="form-validate is-alter">
                            <div class="form-group">
                                <label class="form-label">Yeni Bitiş Tarihi</label>
                                <div class="form-control-wrap">
                                    <div class="form-icon form-icon-left">
                                        <em class="icon ni ni-calendar"></em>
                                    </div>
                                    <input type="text" name="contract_end_date" class="form-control date-picker" required data-date-format="dd-mm-yyyy">
                                </div>
                                <div class="form-note">Tarih formatı <code>gg-aa-yyyy</code></div>
                            </div>




                            <div class="form-group">
                                <label class="form-label" for="pay-amount">Not</label>
                                <div class="form-control-wrap">
                                    <input type="text" class="form-control" id="pay-amount">
                                </div>
                            </div>

                        </form>
                    </div>
                                    <div class="modal-footer bg-light">   <div class="form-group">
                                            <button type="submit" class="btn btn-lg btn-primary">Kaydet</button>
                                        </div></div>
                                </div>
                            </div>

        @endif
