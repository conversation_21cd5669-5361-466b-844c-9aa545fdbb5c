@extends('frontend.layouts.app')

@section('title')
  {{ __('Invoices') }}
@endsection

@section('content')

    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="components-preview mx-auto">
                    <div class="nk-block nk-block-lg">
                        <div class="nk-block-head">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">{{ __('Invoices') }}</h4>

                            </div>
                        </div>

                        <div class="card">
                            <div class="card-inner">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="entity_id" class="form-label">{{ __('Current') }}</label>
                                            <select name="entity_id" id="entity_id" class="form-select js-select2" data-search="on" style="width:100%">
                                                <option value="">{{ __('All') }}</option>
                                                @foreach($entities as $id => $name)
                                                    <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                            <div class="form-control-wrap">
                                                <div class="input-daterange date-picker-range input-group">
                                                    <input type="text" autocomplete="off" name="start_date" id="start_date" class="form-control"/>
                                                    <div class="input-group-addon">-</div>
                                                    <input type="text" autocomplete="off" name="end_date" id="end_date" class="form-control"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="status" id="status">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="active">{{ __('Active Invoices') }}</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card card-bordered card-preview">
                            <div class="card-body">
                                <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                                    <thead>
                                    <tr class="nk-tb-item nk-tb-head">
                                        <th class="nk-tb-col"><b>{{ __('Invoice No') }}</b></th>
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('Current Title') }}</b></th>
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('Invoice Date') }}</b></th>
                                        <th class="nk-tb-col tb-col-md"><b>{{ __('Goods Amount (VAT Excluded)') }}</b></th>
                                        <th class="nk-tb-col tb-col-md"><b>{{ __('VAT Included') }}</b></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Action') }}</span></th>
                                    </tr>
                                    </thead>

                                </table>
                            </div>

                        </div><!-- .card-preview -->
                    </div> <!-- nk-block -->
                </div><!-- .components-preview -->
            </div>
        </div>
    </div>

@endsection

@section('js')
    <script src="/assets/js/libs/datatable-btns.js?ver=3.2.3"></script>
    <script type="text/javascript">
        $(document).ready( function(){
            has_export = true;
            var export_title = $(this).data('export-title') ? $(this).data('export-title') : 'Export';
            var btn = has_export ? '<"dt-export-buttons d-flex align-center"<"dt-export-title d-none d-md-inline-block">B>' : '',
                btn_cls = has_export ? ' with-export' : '';
            var dom = '<"row justify-between g-2' + btn_cls + '"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2"' + btn + 'l>>>><"datatable-wrap my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';

            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\InvoiceController@index') }}',
                    data: function (d) {
                        d.entity_id = $('select#entity_id').val();
                        d.status = $('select#status').val();

                        d.start_date = $('input#start_date')
                            .val()
                            ;
                        d.end_date = $('input#end_date')
                            .val()
                            ;
                    },
                },

                dom: dom,

                buttons: ['copy', 'excel', 'colvis'],

                columnDefs: [ {
                    "targets": [1],
                    "orderable": true,
                    "searchable": true
                } ],
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },

                aaSorting: [[2, 'desc']],
                columns: [
                    { data: 'doc_no', name: 'doc_no', orderable: true, searchable: true },
                    { data: 'entity_name', name: 'entity_name', orderable: true, searchable: false },
                    { data: 'doc_date', name: 'doc_date', orderable: true, searchable: false },
                    { data: 'amt', name: 'amt' },
                    { data: 'amt_vat', name: 'amt_vat' },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ],createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                }
            });
            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#entity_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('select#status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );

        });
        $(document).ready(function() {
            $(document).on('click', '.btn-modal', function(e) {
                e.preventDefault();
                var container = $(this).data('container');
                var url = $(this).data('href');

                $.ajax({
                    url: url,
                    dataType: 'html',
                    success: function(result) {
                        $(container)
                            .html(result)
                            .modal('show');
                        NioApp.Picker.date('.date-picker');

                        NioApp.DataTable('.datatable-init-export', {
                            responsive: {
                                details: true
                            },
                            buttons: ['copy', 'excel', 'csv', 'pdf', 'colvis']
                        });

                    },
                });
            });
        });
    </script>




@endsection
