@extends('frontend.layouts.app')

@section('title')
    {{app_name()}}
@endsection

@section('content')
    <div class="nk-content nk-content-fluid">
        <div class="container-xl wide-xl">
            <div class="nk-content-body">

                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between">
                        <div class="nk-block-head-content"><h3 class="nk-block-title page-title">Tüm Duyurular</h3></div>
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle"><a href="#"
                                                                              class="btn btn-icon btn-trigger toggle-expand me-n1"
                                                                              data-target="pageMenu"><em
                                            class="icon ni ni-more-v"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        @can('create_announcement')
                                            <a href="#" data-target="addProduct" class="toggle btn btn-primary d-none d-md-inline-flex"><em class="icon ni ni-plus"></em><span>Duyuru Ekle</span></a>
                                            @endcan
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>


                    @if(!$errors->isEmpty())
                        <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                            @foreach($errors->all() as $error)
                                <strong>{{ __('Error') }}</strong> {{ $error }}
                            @endforeach
                        </div>
                    @endif

                @include('flash::message')

                <div class="nk-block">
                    <div class="nk-history">

                        @foreach($announcements as $announcement)
                        <div class="nk-history-item">
                            <div class="nk-history-symbol">
                                <div class="nk-history-symbol-dot"></div>
                            </div>
                            <div class="nk-history-content">
                                <div class="card">
                                    <div class="card-body">
                                        <div class="d-flex align-items-center justify-content-between mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="user-avatar xs text-primary bg-primary-dim">
                                                    <em class="icon ni ni-pen-fill"></em>
                                                </div>
                                                <h5 class="fs-14px fw-normal ms-2">{{ $announcement->title ?? 'B2B Duyurları' }}</h5>
                                            </div>
                                            <button class="btn btn-sm clipboard-init mx-n2" title="Hafızaya kopyala" data-clipboard-target="#post{{ $announcement->id }}" data-clip-success="<em class='icon ni ni-copy-fill'></em><span>Kopyalandı</span>" data-clip-text="<em class='icon ni ni-copy'></em><span>Kopyala</span>">
                                                <span class="clipboard-text align-center"><em class='icon ni ni-copy'></em><span>Kopyala</span></span>
                                            </button>


                                        </div>

                                        <p class="lead text-base" id="post{{ $announcement->id }}">
                                            {!! strip_tags($announcement->content,'<b><br>') !!}
                                         </p>

                                        @can('create_announcement')
                                            <div class="collapse" id="editAnnouncement{{ $announcement->id }}">
                                                <div class="card mb-3">
                                                    <form action="/duyuru-ekle" method="post" class="form-validate card-body">
                                                        @csrf
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <label for="start_date">Başlangıç tarihi</label>
                                                                <input name="start_date" type="text" class="form-control date-picker-alt" value="{{ $announcement->start_date }}" data-date-format="yyyy-mm-dd">
                                                            </div>
                                                            <div class="col-md-6">
                                                                <label for="end_date">Bitişi tarihi</label>
                                                                <input name="end_date" type="text" class="form-control date-picker-alt" value="{{ $announcement->start_date }}" autocomplete="off" data-date-format="yyyy-mm-dd">
                                                            </div>
                                                        </div>
                                                        <div class="col-12">
                                                            <label for="end_date">Duyuru başlığı</label>
                                                            <input name="title" type="text" class="form-control" value="{{ $announcement->title }}" autocomplete="off" >
                                                        </div>
                                                        <div class="col-12">
                                                            <label for="end_date">Duyuru metni *</label>
                                                            <div class="form-control-wrap">
                    <textarea name="content" id="content"  class="form-control" rows="5" required
                              placeholder="Duyuru metni">{{ $announcement->content }}</textarea>
                                                            </div>
                                                        </div>
                                                        <input type="hidden" name="id" value="{{ $announcement->id }}">
                                                       <div class="mt-2">
                                                           <button type="submit" class="btn btn-sm btn-primary"><em class='icon ni ni-save'></em> Kaydet</button>
                                                           <button class="btn btn-sm btn-danger" type="button" data-bs-toggle="collapse" data-bs-target="#editAnnouncement{{ $announcement->id }}" aria-expanded="false" aria-controls="editAnnouncement{{ $announcement->id }}">
                                                               <em class='icon ni ni-cross'></em>  Kapat
                                                           </button>
                                                       </div>
                                                    </form>
                                                </div>
                                            </div>
                                        @endcan
                                        <ul class="nk-history-meta">
                                            <li>{{ $announcement->created_at->format('d.m.Y H:i') }}</li>
                                            <li>{{ \Str::of(strip_tags($announcement->content))->wordCount() }} Kelime / {{ strlen($announcement->content) }} Karakter</li>

                                            <li class="end-0">  @can('create_announcement')
                                                    <button class="btn btn-sm btn-default" type="button" data-bs-toggle="collapse" data-bs-target="#editAnnouncement{{ $announcement->id }}" aria-expanded="false" aria-controls="editAnnouncement{{ $announcement->id }}">
                                                        <em class='icon ni ni-edit'></em>  Güncelle
                                                    </button>
                                                @endcan</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div><!-- .nk-history-item -->
                        @endforeach


                        
                    </div><!-- .nk-history -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>


    @can('create_announcement')
    <div class="nk-add-product toggle-slide toggle-slide-right" data-content="addProduct" data-toggle-screen="any" data-toggle-overlay="true" data-toggle-body="true" data-simplebar>
        <div class="nk-block-head">
            <div class="nk-block-head-content">
                <h5 class="nk-block-title">Yeni Duyuru</h5>
                <div class="nk-block-des">
                    <p>Duyuru için gerekli alanları girerek kaydet butonuna tıklayınız.</p>
                </div>
            </div>
        </div><!-- .nk-block-head -->
        <div class="nk-block">
            <form action="/duyuru-ekle" method="post" class="form-validate card-body">
            <div class="row g-3">
                        @csrf
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="start_date">Başlangıç tarihi</label>
                                <input name="start_date" type="text" class="form-control date-picker-alt" autocomplete="off" data-date-format="yyyy-mm-dd">
                            </div>
                            <div class="col-md-6">
                                <label for="end_date">Bitişi tarihi</label>
                                <input name="end_date" type="text" class="form-control date-picker-alt" autocomplete="off" data-date-format="yyyy-mm-dd">
                            </div>
                        </div>
                        <div class="col-12">
                            <label for="end_date">Duyuru başlığı</label>
                            <input name="title" type="text" class="form-control" autocomplete="off" >
                        </div>
                        <div class="col-12">
                            <label for="end_date">Duyuru metni *</label>
                            <div class="form-control-wrap">
                    <textarea name="content" id="content"  class="form-control" rows="5" required
                              placeholder="Duyuru metni"></textarea>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary"><em class="icon ni ni-plus"></em><span>Kaydet</span></button>
            </div>
            </form>
        </div><!-- .nk-block -->

    </div>
    @endcan
@endsection