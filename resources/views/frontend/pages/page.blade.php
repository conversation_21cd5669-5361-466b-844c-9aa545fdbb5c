@extends('frontend.layouts.app')

@section('title'){{ $page->title  ?? app_name() }} @endsection
@section('css')
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-lite.min.css">
@endsection
@section('content')
    <div class="nk-content-inner">
        @can('edit_pages')
            <p class="d-inline-flex gap-1">
                <button class="btn btn-primary" type="button" data-bs-toggle="collapse" data-bs-target="#pageForm" aria-expanded="false" aria-controls="pageForm">
                    Düzenle
                </button>
            </p>
            <div class="collapse" id="pageForm">
                <div class="card card-body">
                    <form action="{{ route('page_update', $page->slug) }}" method="post" enctype="multipart/form-data" >
                        @csrf


                        <div class="form-group mt-3">
                            <label for="title">Başlık</label>
                            <input type="text" class="form-control" name="title" id="title" value="{{ $page->title }}">
                        </div>

                <div class="form-group mt-3">
                    <label for="excerpt">Özet</label>
                    <textarea class="form-control" name="excerpt" id="excerpt" rows="3">{{ $page->content }}</textarea>
                </div>

                <div class="form-group mt-3">
                    <label for="body">Metin</label>
                    <textarea class="form-control summernote" name="body" id="body" rows="5">{{ $page->body }}</textarea>
                </div>

                        <div class="form-group mt-3">
                            <label for="status">Dosyalar</label>
                            <input type="file" class="form-control" name="media[]" id="media" multiple>
                        </div>


                <button type="submit" class="btn btn-primary mt-3">Güncelle</button>

                </form>

                    @if($page->media()->exists())
                    <h4>Dosyalar</h4>
                    <ol>
                        @foreach($page->media as $media)
                            <li><a href='/uploads/media/{{ $media->file_name }}' target='_blank'>{{ $media->file_name }}</a>
                                <a href="{{ route('page', ['slug' => $page->slug]) }}?media_id={{ $media->id }}" class='btn btn-sm btn-danger btn-destroy-media' role="button">
                                    <i class='fa fa-trash'></i>  Sil
                                </a>

                            </li>
                        @endforeach
                    </ol>
                        @endif
            </div>
            </div>
            <hr>
        @endcan
        <div class="nk-content-body">
            <div class="nk-content-wrap">
                <div class="nk-block-head nk-block-head-lg wide-sm">
                    <div class="nk-block-head-content">
                        <h2 class="nk-block-title fw-normal">{{ $page->title }}</h2>
                        <div class="nk-block-des">
                            <p class="lead">{{ $page->excerpt }}</p>
                            {!! $page->body !!}
                        </div>
                    </div>
                </div><!-- .nk-block-head -->
                <div class="nk-block">
                    @foreach($page->media as $media)
                    <embed src="{{ '/uploads/media/' . $media->id .'/' . $media->file_name }}" width="100%" height="640" type="{{ $media->mime_type }}">
                    @endforeach
                </div><!-- .nk-block -->
            </div>

        </div>
    </div>
@endsection

@section('js')
    @can('edit_pages')
    <script type="module" src="https://cdnjs.cloudflare.com/ajax/libs/summernote/0.8.20/summernote-lite.min.js"></script>
    <script type="module">
        $('#body').summernote({
            height: 120,
            toolbar: [
                ['style', ['style']],
                ['font', ['fontname', 'fontsize', 'bold', 'underline', 'clear']],
                ['color', ['color']],
                ['para', ['ul', 'ol', 'paragraph']],
                ['table', ['table']],
                ['view', ['codeview', 'undo', 'redo', 'help']],
            ],

        });
    </script>
@endcan
@endsection