@extends('frontend.layouts.app')

@section('title') {{app_name()}} @endsection

@section('content')
    <div class="content-page wide-sm m-auto">
        <div class="nk-content-inner">

            <div class="nk-content-body" data-select2-id="9">
                <div class="nk-content-wrap">
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-head-sub"><span>{{ __('Contact') }}</span></div>
                        <div class="nk-block-head-content"><h2 class="nk-block-title fw-normal">{{ __('Contact Form') }}</h2>
                            <div class="nk-block-des"><p>{{ __('Please fill out the contact form below in detail to reach us. Your message will be answered as soon as possible.') }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block mb-3">
                        <div class="card card-bordered">
                            <div class="card-inner">

                                @if(!$errors->isEmpty())
                                    <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                                        @foreach($errors->all() as $error)
                                            <strong>{{ __('Error') }}</strong> {{ $error }}<br>
                                        @endforeach
                                    </div>
                                @endif

                                @include('flash::message')




                                <form action="/iletisim" method="post" class="form-contact" data-select2-id="8">
                                    <div class="row g-4">
                                        <div class="col-md-6">
                                            <div class="custom-control custom-radio checked"><input type="radio"
                                                                                                    class="custom-control-input"
                                                                                                    name="type" id="type-general"
                                                                                                    checked=""><label
                                                        class="custom-control-label" for="type-general">{{ __('I am writing about a general topic') }}</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="custom-control custom-radio"><input type="radio"
                                                                                            class="custom-control-input" name="type"
                                                                                            id="type-problem"><label
                                                        class="custom-control-label" for="type-problem">{{ __('I have a problem and need help') }}</label></div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group"><label class="form-label"><span>{{ __('Category') }}</span></label>
                                                <div class="form-control-wrap"><select class="form-select js-select2" data-search="off" data-ui="lg">
                                                        <option value="general">{{ __('General') }}</option>
                                                        <option value="billing">{{ __('Payment') }}</option>
                                                        <option value="technical">{{ __('Technical issue') }}</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group"><label class="form-label"><span>{{ __('Your Email Address') }}</span></label>
                                                <div class="form-control-wrap">
                                                    <input type="email" class="form-control" name="email" value="{{ Auth::user()->email }}" placeholder="{{ __('Your email address') }}" required>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-group"><label class="form-label">{{ __('Write the subject of your message') }}</label>
                                                <div class="form-control-wrap"><input type="text" required name="subject"
                                                                                      class="form-control form-control-lg"
                                                                                      placeholder="{{ __('Write your problem') }}... "></div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="form-group"><label class="form-label"><span>{{ __('Provide detailed information') }} </span><em
                                                            class="icon ni ni-info text-gray"></em></label>

                                                <div class="form-control-wrap">
                                                    <div class="form-editor-custom"><textarea required name="message"
                                                                                              class="form-control form-control-lg no-resize"
                                                                                              placeholder="{{ __('Write your message') }}"></textarea>
                                                        {{--                                            <div class="form-editor-action"><a class="link collapsed"--}}
                                                        {{--                                                                               data-bs-toggle="collapse"--}}
                                                        {{--                                                                               href="#filedown"><em--}}
                                                        {{--                                                            class="icon ni ni-clip"></em> Dosya Ekle</a></div>--}}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {{--                            <div class="col-12">--}}
                                        {{--                                <div class="choose-file">--}}
                                        {{--                                    <div class="form-group collapse" id="filedown">--}}
                                        {{--                                        <div class="support-upload-box">--}}
                                        {{--                                            <div class="upload-zone upload-zone dz-clickable">--}}
                                        {{--                                                <div class="dz-message" data-dz-message=""><em--}}
                                        {{--                                                            class="icon ni ni-clip"></em><span class="dz-message-text">Dosyayı sürükle</span><span--}}
                                        {{--                                                            class="dz-message-or"> veya</span>--}}
                                        {{--                                                    <button class="btn btn-primary">Seç</button>--}}
                                        {{--                                                </div>--}}
                                        {{--                                            </div>--}}
                                        {{--                                        </div>--}}
                                        {{--                                    </div>--}}
                                        {{--                                </div>--}}
                                        {{--                            </div>--}}
                                        @csrf
                                        <div class="col-12"><button type="submit" class="btn btn-primary">{{ __('Send Email') }}</button></div>
                                    </div>
                                </form>


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection