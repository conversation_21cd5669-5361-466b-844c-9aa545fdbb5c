@extends('frontend.layouts.app')

@section('title') {{ __('Calendar') }} @endsection
@section('css')

    @endsection
@section('content')
   <div class="nk-content nk-content-fluid">
    <div class="container-xl wide-xl">
        <div class="nk-content-body">
            <div class="nk-block-head nk-block-head-sm">
                <div class="nk-block-between">
                    <div class="nk-block-head-content">
                        <h3 class="nk-block-title page-title">{{ __('Calendar') }}</h3>
                    </div><!-- .nk-block-head-content -->
                    <div class="nk-block-head-content d-flex">
                        <a class="link link-primary" data-bs-toggle="modal" href="#addEventPopup"><em class="icon ni ni-plus"></em> <span>{{ __('Add') }}</span></a>
                    </div><!-- .nk-block-head-content -->
                </div><!-- .nk-block-between -->
            </div><!-- .nk-block-head -->
            <div class="nk-block">
                <div class="card">
                    <div class="card-inner">
                        <div id="calendar" class="nk-calendar"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>

    <div class="modal fade" tabindex="-1" role="dialog" id="region">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <a href="#" class="close" data-bs-dismiss="modal"><em class="icon ni ni-cross-sm"></em></a>
                <div class="modal-body modal-body-md">
                    <h5 class="title mb-4">{{     __('Select Country') }}</h5>
                    <div class="nk-country-region">
                        <ul class="country-list text-center gy-2">
                            <li>
                                <a href="#" class="country-item">
                                    <img src="./images/flags/arg.png" alt="" class="country-flag">
                                    <span class="country-name">Türkiye</span>
                                </a>
                            </li>
                            <li>
                                <a href="#" class="country-item">
                                    <img src="./images/flags/aus.png" alt="" class="country-flag">
                                    <span class="country-name">Avustralya</span>
                                </a>
                            </li>

                        </ul>
                    </div>
                </div>
            </div><!-- .modal-content -->
        </div><!-- .modla-dialog -->
    </div><!-- .modal -->
    <div class="modal fade" id="addEventPopup">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">{{ __('Add') }}</h5>
                    <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                </div>
                <div class="modal-body">
                    <form action="#" id="addEventForm" class="form-validate is-alter">
                        <div class="row gx-4 gy-3">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="event-title">Kayıt Adı</label>
                                    <div class="form-control-wrap">
                                        <input type="text" class="form-control" id="event-title" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">Başlama Zamanı</label>
                                    <div class="row gx-2">
                                        <div class="w-55">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-calendar"></em>
                                                </div>
                                                <input type="text" id="event-start-date" class="form-control date-picker" data-date-format="yyyy-mm-dd" required>
                                            </div>
                                        </div>
                                        <div class="w-45">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-clock"></em>
                                                </div>
                                                <input type="text" id="event-start-time" data-time-format="HH:mm:ss" class="form-control time-picker">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">Bitiş Zamanı</label>
                                    <div class="row gx-2">
                                        <div class="w-55">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-calendar"></em>
                                                </div>
                                                <input type="text" id="event-end-date" class="form-control date-picker" data-date-format="yyyy-mm-dd">
                                            </div>
                                        </div>
                                        <div class="w-45">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-clock"></em>
                                                </div>
                                                <input type="text" id="event-end-time" data-time-format="HH:mm:ss" class="form-control time-picker">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="event-description">Tanım</label>
                                    <div class="form-control-wrap">
                                        <textarea class="form-control" id="event-description"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">Kategori</label>
                                    <div class="form-control-wrap">
                                        <select id="event-theme" class="select-calendar-theme form-control" data-search="on">
                                            <option value="event-primary">Sipariş</option>
                                            <option value="event-success">Sözleşme </option>
                                            <option value="event-info">Ödeme</option>
                                            <option value="event-warning">Teklif</option>
                                            <option value="event-danger">Toplantı</option>
                                            <option value="event-pink">Fatura</option>
                                            <option value="event-primary-dim">İrsaliye</option>
                                            <option value="event-success-dim">Cari Bilgisi</option>
                                            <option value="event-info-dim">Belge</option>
                                            <option value="event-warning-dim">Fiyat</option>
                                            <option value="event-danger-dim">Diğer</option>
                                            <option value="event-pink-dim">Stok kartı</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <ul class="d-flex justify-content-between gx-4 mt-1">
                                    <li>
                                        <button id="addEvent" type="submit" class="btn btn-primary">Ekle</button>
                                    </li>
                                    <li>
                                        <button id="resetEvent" data-bs-dismiss="modal" class="btn btn-danger btn-dim">İptal</button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="editEventPopup">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Kayıt Düzenle</h5>
                    <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                </div>
                <div class="modal-body">
                    <form action="#" id="editEventForm" class="form-validate is-alter">
                        <div class="row gx-4 gy-3">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="edit-event-title">Başlık</label>
                                    <div class="form-control-wrap">
                                        <input type="text" class="form-control" id="edit-event-title" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">Başlama Zamanı</label>
                                    <div class="row gx-2">
                                        <div class="w-55">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-calendar"></em>
                                                </div>
                                                <input type="text" id="edit-event-start-date" class="form-control date-picker" data-date-format="yyyy-mm-dd" required>
                                            </div>
                                        </div>
                                        <div class="w-45">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-clock"></em>
                                                </div>
                                                <input type="text" id="edit-event-start-time" data-time-format="HH:mm:ss" class="form-control time-picker">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="form-label">Bitiş Zamanı</label>
                                    <div class="row gx-2">
                                        <div class="w-55">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-calendar"></em>
                                                </div>
                                                <input type="text" id="edit-event-end-date" class="form-control date-picker" data-date-format="yyyy-mm-dd">
                                            </div>
                                        </div>
                                        <div class="w-45">
                                            <div class="form-control-wrap">
                                                <div class="form-icon form-icon-left">
                                                    <em class="icon ni ni-clock"></em>
                                                </div>
                                                <input type="text" id="edit-event-end-time" data-time-format="HH:mm:ss" class="form-control time-picker">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label" for="edit-event-description">Tanım</label>
                                    <div class="form-control-wrap">
                                        <textarea class="form-control" id="edit-event-description"></textarea>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="edit-event-id">
                            <div class="col-12">
                                <div class="form-group">
                                    <label class="form-label">Kategori</label>
                                    <div class="form-control-wrap">
                                        <select id="edit-event-theme" class="select-calendar-theme form-control" data-search="on">
                                            <option value="event-primary">Sipariş</option>
                                            <option value="event-success">Sözleşme </option>
                                            <option value="event-info">Ödeme</option>
                                            <option value="event-warning">Teklif</option>
                                            <option value="event-danger">Toplantı</option>
                                            <option value="event-pink">Fatura</option>
                                            <option value="event-primary-dim">İrsaliye</option>
                                            <option value="event-success-dim">Cari Bilgisi</option>
                                            <option value="event-info-dim">Belge</option>
                                            <option value="event-warning-dim">Fiyat</option>
                                            <option value="event-danger-dim">Diğer</option>
                                            <option value="event-pink-dim">Stok kartı</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <ul class="d-flex justify-content-between gx-4 mt-1 btn-actions">
                                    <li>
                                        <button id="updateEvent" type="submit" class="btn btn-primary">Güncelle</button>
                                    </li>
                                    <li>
                                        <button data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#deleteEventPopup" class="btn btn-danger btn-dim">Sil</button>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="previewEventPopup">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div id="preview-event-header" class="modal-header">
                    <h5 id="preview-event-title" class="modal-title">Başlık</h5>
                    <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <em class="icon ni ni-cross"></em>
                    </a>
                </div>
                <div class="modal-body">
                    <div class="row gy-3 py-1">
                        <div class="col-sm-6">
                            <h6 class="overline-title">Başlama Zamanı</h6>
                            <p id="preview-event-start"></p>
                        </div>
                        <div class="col-sm-6" id="preview-event-end-check">
                            <h6 class="overline-title">Bitiş Zamanı</h6>
                            <p id="preview-event-end"></p>
                        </div>
                        <div class="col-sm-10" id="preview-event-description-check">
                            <h6 class="overline-title">Tanım</h6>
                            <p id="preview-event-description"></p>
                        </div>
                    </div>
                    <ul class="d-flex justify-content-between gx-4 mt-3 btn-actions">
                        <li>
                            <button data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#editEventPopup" class="btn btn-primary">Düzenle</button>
                        </li>
                        <li>
                            <button data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#deleteEventPopup" class="btn btn-danger btn-dim">Sil</button>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="deleteEventPopup">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-body modal-body-lg text-center">
                    <div class="nk-modal py-4">
                        <em class="nk-modal-icon icon icon-circle icon-circle-xxl ni ni-cross bg-danger"></em>
                        <h4 class="nk-modal-title">Emin misiniz ?</h4>
                        <div class="nk-modal-text mt-n2">
                            <p class="text-soft">Bu kayıt kalıcı olarak silinecektir</p>
                        </div>
                        <ul class="d-flex justify-content-center gx-4 mt-4 btn-actions">
                            <li>
                                <button data-bs-dismiss="modal" id="deleteEvent" class="btn btn-success">Evet, sil</button>
                            </li>
                            <li>
                                <button data-bs-dismiss="modal" data-bs-toggle="modal" data-bs-target="#editEventPopup" class="btn btn-danger btn-dim">İptal</button>
                            </li>
                        </ul>
                    </div>
                </div><!-- .modal-body -->
            </div>
        </div>
    </div>

@endsection

@section('js')
    <script src="./assets/js/libs/fullcalendar.js"></script>
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@5.11.5/locales-all.min.js'></script>

<script>
    "use strict";

    !function (NioApp, $) {
        "use strict";

        // Variable
        var $win = $(window),
            $body = $('body'),
            breaks = NioApp.Break;
        NioApp.Calendar = function () {
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0');
            var yyyy = today.getFullYear();
            var tomorrow = new Date(today);
            tomorrow.setDate(today.getDate() + 1);
            var t_dd = String(tomorrow.getDate()).padStart(2, '0');
            var t_mm = String(tomorrow.getMonth() + 1).padStart(2, '0');
            var t_yyyy = tomorrow.getFullYear();
            var yesterday = new Date(today);
            yesterday.setDate(today.getDate() - 1);
            var y_dd = String(yesterday.getDate()).padStart(2, '0');
            var y_mm = String(yesterday.getMonth() + 1).padStart(2, '0');
            var y_yyyy = yesterday.getFullYear();
            var YM = yyyy + '-' + mm;
            var YESTERDAY = y_yyyy + '-' + y_mm + '-' + y_dd;
            var TODAY = yyyy + '-' + mm + '-' + dd;
            var TOMORROW = t_yyyy + '-' + t_mm + '-' + t_dd;
            var month = ["Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran", "Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık"];
            var calendarEl = document.getElementById('calendar');
            var eventsEl = document.getElementById('externalEvents');
            var removeEvent = document.getElementById('removeEvent');
            var addEventBtn = $('#addEvent');
            var addEventForm = $('#addEventForm');
            var addEventPopup = $('#addEventPopup');
            var updateEventBtn = $('#updateEvent');
            var editEventForm = $('#editEventForm');
            var editEventPopup = $('#editEventPopup');
            var previewEventPopup = $('#previewEventPopup');
            var deleteEventBtn = $('#deleteEvent');
            var mobileView = NioApp.Win.width < NioApp.Break.md ? true : false;
            var calendar = new FullCalendar.Calendar(calendarEl, {
                displayEventTime : false,

                timeZone: 'UTC',
                initialView: mobileView ? 'listWeek' : 'dayGridMonth',
                themeSystem: 'bootstrap5',
                headerToolbar: {
                    left: 'title prev,next',
                    center: null,
                    right: 'today dayGridMonth,timeGridWeek,timeGridDay,listWeek'
                },
                height: 800,
                contentHeight: 780,
                aspectRatio: 3,
                editable: true,
                droppable: true,
                views: {
                    dayGridMonth: {
                        dayMaxEventRows: 2
                    }
                },
                direction: NioApp.State.isRTL ? "rtl" : "ltr",
                nowIndicator: true,
                now: TODAY + 'T09:25:00',
                eventMouseEnter: function eventMouseEnter(info) {
                    var elm = info.el,
                        title = info.event._def.title,
                        content = info.event._def.extendedProps.description;
                    if (content) {
                        var fcPopover = new bootstrap.Popover(elm, {
                            template: '<div class="popover event-popover"><div class="popover-arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',
                            title: title,
                            content: content ? content : '',
                            placement: 'top'
                        });
                        fcPopover.show();
                    }
                },
                eventMouseLeave: function eventMouseLeave() {
                    removePopover();
                },
                eventDragStart: function eventDragStart() {
                    removePopover();
                },
                eventClick: function eventClick(info) {
                    // Get data
                    var title = info.event._def.title;
                    var description = info.event._def.extendedProps.description;

                    var start = info.event._instance.range.start;
                    var startDate = start.getFullYear() + '-' + String(start.getMonth() + 1).padStart(2, '0') + '-' + String(start.getDate()).padStart(2, '0');
                    var startTime = start.toUTCString().split(' ');
                    startTime = startTime[startTime.length - 2];
                    startTime = startTime == '00:00:00' ? '' : startTime;
                    var end = info.event._instance.range.end;
                    var endDate = end.getFullYear() + '-' + String(end.getMonth() + 1).padStart(2, '0') + '-' + String(end.getDate()).padStart(2, '0');
                    var endTime = end.toUTCString().split(' ');
                    endTime = endTime[endTime.length - 2];
                    endTime = endTime == '00:00:00' ? '' : endTime;
                    var className = info.event._def.ui.classNames[0].slice(3);
                    var eventId = info.event._def.publicId;
                    if (info.event.extendedProps.edit === false) {
                        // Hide the btn-actions div
                        jQuery('.btn-actions').attr('style','display:none !important');
                    } else {
                        // Otherwise, show the btn-actions div
                        $('.btn-actions').show();
                    }
                    //Set data in edit form
                    $('#edit-event-title').val(title);
                    $('#edit-event-start-date').val(startDate).datepicker('update');
                    $('#edit-event-end-date').val(endDate).datepicker('update');
                    $('#edit-event-start-time').val(startTime);
                    $('#edit-event-end-time').val(endTime);
                    $('#edit-event-description').val(description);
                    $('#edit-event-id').val(eventId);
                    $('#edit-event-theme').val(className);
                    $('#edit-event-theme').trigger('change.select2');
                    editEventForm.attr('data-id', eventId);

                    // Set data in preview
                    var previewStart = String(start.getDate()).padStart(2, '0') + ' ' + month[start.getMonth()] + ' ' + start.getFullYear() + (startTime ? ' - ' + to12(startTime) : '');
                    var previewEnd = String(end.getDate()).padStart(2, '0') + ' ' + month[end.getMonth()] + ' ' + end.getFullYear() + (endTime ? ' - ' + to12(endTime) : '');
                    $('#preview-event-title').text(title);
                    $('#preview-event-header').addClass('fc-' + className);
                    $('#preview-event-start').text(previewStart);
                    $('#preview-event-end').text(previewEnd);
                    $('#preview-event-description').text(description);
                    !description ? $('#preview-event-description-check').css('display', 'none') : null;
                    removePopover();
                    var fcMorePopover = document.querySelectorAll('.fc-more-popover');
                    fcMorePopover && fcMorePopover.forEach(function (elm) {
                        elm.remove();
                    });
                    previewEventPopup.modal('show');
                    // Check if the edit property of the clicked event is false
                },

                events: [
                        @foreach($events as $event)
                    {
                        id: '{{$event->id}}',
                        title: '{{$event->title}}',
                        start: '{{$event->start_date}}',
                        end: '{{$event->end_date}}',
                        className: 'fc-{{$event->event_theme}}',
                        description: '{{$event->description}}'
                    },
                        @endforeach
                        @foreach($orders as $order)
                    {
                        id: 'event-order-{{$order->id}}',
                        title: 'Yeni sipariş: {{$order->doc_no}}',
                        start: '{{$order->doc_date}}',
                        className: 'fc-event-primary',
                        description: '{{ Number::currency($order->amt,$order->currency->cur_code,app()->getLocale()) }} tutarlı, {{$order->doc_no}} sipariş numarası ile yeni siparişiniz oluşturuldu',
                            edit: false
                    },
                        @endforeach
                        @foreach($offers as $offer)
                    {
                        id: 'event-offer-{{$offer->id}}',
                        title: '{{$offer->doc_no}} Teklif',
                        start: '{{$offer->doc_date}}',
                        className: 'fc-event-pink',
                        description: 'Yeni teklif oluşturuldu',
                            edit: false
                    }, @endforeach
                        @foreach($shipments as $shipment)
                    {
                        id: 'event-offer-{{$shipment->id}}',
                        title: '{{$shipment->order_doc_no}} Sevk Edildi',
                        start: '{{$shipment->doc_date}}',
                        className: 'fc-event-primary-dim',
                        description: 'Yeni sevkiyat oluşturuldu',
                            edit: false
                    },
                        @endforeach
                        @foreach($contracts as $contract)
                    {
                        id: 'contract-end-{{$contract->id}}',
                        title: '{{$contract->doc_no . ' '. $contract->doc_description}} BİTİYOR',
                        start: '{{$contract->contract_end_date}}',

                        className: 'fc-event-warning',
                        description: '{{ 'Sözleşmenizin bitiş süresi yaklaşıyor. Sözleşmeniz ' . $contract->contract_end_date->diffForHumans() . ' '.$contract->contract_end_date->format('d.m.Y').' tarihinde sona erecektir.' }}',
                            edit: false
                    },
                    {
                        id: 'contract-start-{{$contract->id}}',
                        title: '{{$contract->doc_no . ' '. $contract->doc_description}}',
                        start: '{{$contract->contract_start_date}}',
                        className: 'fc-event-success',
                        description: { html: '<a href="/sozlesme/{{$contract->id}}">Detaylar için tıklayınız</a>' },
                        edit: false
                    },
                        @endforeach


                   ]
            });
            calendar.render();
            calendar.setOption('locale', 'tr');
            //Add event

            addEventBtn.on("click", function (e) {
                e.preventDefault();
                var eventTitle = $('#event-title').val();
                var eventStartDate = $('#event-start-date').val();
                var eventEndDate = $('#event-end-date').val();
                var eventStartTime = $('#event-start-time').val();
                var eventEndTime = $('#event-end-time').val();
                var eventDescription = $('#event-description').val();
                var eventTheme = $('#event-theme').val();
                var eventStartTimeCheck = eventStartTime ? 'T' + eventStartTime + 'Z' : '';
                var eventEndTimeCheck = eventEndTime ? 'T' + eventEndTime + 'Z' : '';
                calendar.addEvent({
                    id: 'added-event-id-' + Math.floor(Math.random() * 9999999),
                    title: eventTitle,
                    start: eventStartDate + eventStartTimeCheck,
                    end: eventEndDate + eventEndTimeCheck,
                    className: "fc-" + eventTheme,
                    description: eventDescription
                });
                addEventPopup.modal('hide');
            });
            updateEventBtn.on("click", function (e) {
                e.preventDefault();
                var eventTitle = $('#edit-event-title').val();
                var eventStartDate = $('#edit-event-start-date').val();
                var eventEndDate = $('#edit-event-end-date').val();
                var eventStartTime = $('#edit-event-start-time').val();
                var eventEndTime = $('#edit-event-end-time').val();
                var eventDescription = $('#edit-event-description').val();
                var eventTheme = $('#edit-event-theme').val();
                var eventId = $('#edit-event-id').val();
                var eventStartTimeCheck = eventStartTime ? 'T' + eventStartTime + 'Z' : '';
                var eventEndTimeCheck = eventEndTime ? 'T' + eventEndTime + 'Z' : '';
                var selectEvent = calendar.getEventById(editEventForm[0].dataset.id);
                selectEvent.remove();
                calendar.addEvent({
                    id: editEventForm[0].dataset.id,
                    title: eventTitle,
                    start: eventStartDate + eventStartTimeCheck,
                    end: eventEndDate + eventEndTimeCheck,
                    className: "fc-" + eventTheme,
                    description: eventDescription,
                    eventId: eventId
                });
                editEventPopup.modal('hide');
            });
            deleteEventBtn.on("click", function (e) {
                e.preventDefault();
                var selectEvent = calendar.getEventById(editEventForm[0].dataset.id);

                var eventId = $('#edit-event-id').val();
                $("#overlay").fadeIn(150);
                $.ajax({
                    url: '/takvim',
                    method: 'POST',
                    data: {
                        "_token": "{{ csrf_token() }}",
                        id: eventId,
                        action: 'delete'
                    },
                    success: function (response) {
                        $("#overlay").fadeOut(200);
                        if (response.success === true) {
                            toastr.clear();
                            NioApp.Toast(response.message, 'info', {position: 'top-right'});
                        } else {
                            toastr.clear();
                            NioApp.Toast(response.message, 'error', {position: 'top-center'});
                        }
                    },
                });

                selectEvent.remove();
            });
            function removePopover() {
                var fcPopover = document.querySelectorAll('.event-popover');
                fcPopover.forEach(function (elm) {
                    elm.remove();
                });
            }
            function to12(time) {
                time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];
                if (time.length > 1) {
                    time = time.slice(1);
                    time.pop();
                    time[5] = +time[0] < 12 ? ' AM' : ' PM'; // Set AM/PM
                    time[0] = +time[0] % 12 || 12;
                }
                time = time.join('');
                return time;
            }
            function customCalSelect(cat) {
                if (!cat.id) {
                    return cat.text;
                }
                var $cat = $('<span class="fc-' + cat.element.value + '"> <span class="dot"></span>' + cat.text + '</span>');
                return $cat;
            }
            ;
            function removePopover() {
                var fcPopover = document.querySelectorAll('.event-popover');
                fcPopover.forEach(function (elm) {
                    elm.remove();
                });
            }
            NioApp.Select2('.select-calendar-theme', {
                templateResult: customCalSelect
            });
            addEventPopup.on('hidden.bs.modal', function (e) {
                setTimeout(function () {
                    $('#addEventForm input,#addEventForm textarea').val('');
                    $('#event-theme').val('event-primary');
                    $('#event-theme').trigger('change.select2');
                }, 1000);
            });
            previewEventPopup.on('hidden.bs.modal', function (e) {
                $('#preview-event-header').removeClass().addClass('modal-header');
            });
        };
        NioApp.coms.docReady.push(NioApp.Calendar);
    }(NioApp, jQuery);


    $(document).on('click', '#addEvent', function (e) {
        var eventTitle = $('#event-title').val();
        var eventStartDate = $('#event-start-date').val();
        var eventEndDate = $('#event-end-date').val();
        var eventStartTime = $('#edit-event-start-time').val();
        var eventEndTime = $('#edit-event-end-time').val();
        var eventDescription = $('#event-description').val();
        var eventTheme = $('#event-theme').val();
        $("#overlay").fadeIn(150);
        $.ajax({
            url: '/takvim',
            method: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                title: eventTitle,
                description: eventDescription,
                start_date: eventStartDate + ' ' + eventStartTime,
                end_date: eventEndDate + ' ' + eventEndTime,
                event_theme: eventTheme,
                id: '',
            },
            success: function (response) {
                $("#overlay").fadeOut(200);
                if (response.success === true) {
                    NioApp.Toast(response.message, 'info', {position: 'top-right'});
                } else {
                    NioApp.Toast(response.message, 'error', {position: 'top-center'});
                }
            },
        });
    });

    $(document).on('click', '#updateEvent', function (e) {
        var eventTitle = $('#edit-event-title').val();
        var eventStartDate = $('#edit-event-start-date').val();
        var eventEndDate = $('#edit-event-end-date').val();
        var eventStartTime = $('#edit-event-start-time').val();
        var eventEndTime = $('#edit-event-end-time').val();
        var eventDescription = $('#edit-event-description').val();
        var eventTheme = $('#edit-event-theme').val();
        var eventId = $('#edit-event-id').val();
        $("#overlay").fadeIn(150);
        $.ajax({
            url: '/takvim',
            method: 'POST',
            data: {
                "_token": "{{ csrf_token() }}",
                title: eventTitle,
                description: eventDescription,
                start_date: eventStartDate + ' ' + eventStartTime,
                end_date: eventEndDate + ' ' + eventEndTime,
                event_theme: eventTheme,
                id: eventId,
            },
            success: function (response) {
                $("#overlay").fadeOut(200);
                if (response.success === true) {
                    NioApp.Toast(response.message, 'info', {position: 'top-right'});
                } else {
                    NioApp.Toast(response.message, 'error', {position: 'top-center'});
                }
            },
        });
    });
</script>

@endsection