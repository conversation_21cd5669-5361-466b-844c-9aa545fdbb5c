@extends('frontend.layouts.app')

@section('title')
{{ __('POS Transactions') }}
@endsection

@section('content')
    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="components-preview mx-auto">
                    <div class="nk-block nk-block-lg">
                        <div class="nk-block-head">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">{{ __('POS Transactions') }}</h4>

                            </div>
                        </div>

                        <div class="card">
                            <div class="card-inner">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="entity_id" class="form-label">{{ __('Current Account') }}</label>
                                            <select name="entity_id" id="entity_id" class="form-select js-select2" data-search="on" style="width:100%">
                                                <option value="">{{ __('All') }}</option>
                                                @foreach($entities as $id => $name)
                                                    <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                            <div class="form-control-wrap">
                                                <div class="input-daterange date-picker-range input-group">
                                                    <input type="text" autocomplete="off" name="start_date" id="start_date" class="form-control"/>
                                                    <div class="input-group-addon">-</div>
                                                    <input type="text" autocomplete="off" name="end_date" id="end_date" class="form-control"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="status" id="status">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="approved" selected>{{ __('Approved') }}</option>
                                                    <option value="refunded">{{ __('Refunded') }}</option>
                                                    <option value="declined">{{ __('Declined') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card card-bordered card-preview">
                            <div class="card-body">


                                <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                                    <thead>
                                    <tr class="nk-tb-item nk-tb-head">
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('BANK NAME') }}</b></th>
                                        <th class="nk-tb-col"><b>{{ __('COLLECTION DATE') }}</b></th>
                                        <th class="nk-tb-col"><b>{{ __('TIME') }}</b></th>
                                        <th class="nk-tb-col"><b>{{ __('DEALER') }}</b></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('CARD HOLDER') }}</span></th>
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('PROVISION NUMBER') }}</b></th>
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('WITHDRAWN AMOUNT') }}</b></th>
                                        <th class="nk-tb-col tb-col-sm"><b>{{ __('WITHDRAWN AMOUNT') }}</b></th>
                                        <th class="nk-tb-col tb-col-md"><b>{{ __('INSTALLMENT') }}</b></th>
                                        <th class="nk-tb-col tb-col-sm">{{ __('Action') }}</th>
                                    </tr>
                                    </thead>
                                </table>

                            </div>
                        </div><!-- .card-preview -->
                    </div> <!-- nk-block -->
                </div><!-- .components-preview -->
            </div>
        </div>
    </div>
    @endsection

@section('js')
    <script src="/assets/js/libs/datatable-btns.js?ver=3.2.3"></script>
    <script type="text/javascript">
        $(document).ready( function(){
            has_export = true;
            var export_title = $(this).data('export-title') ? $(this).data('export-title') : 'Export';
            var btn = has_export ? '<"dt-export-buttons d-flex align-center"<"dt-export-title d-none d-md-inline-block">B>' : '',
                btn_cls = has_export ? ' with-export' : '';
            var dom = '<"row justify-between g-2' + btn_cls + '"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2"' + btn + 'l>>>><"datatable-wrap my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';

            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\PaymentController@transactions') }}',
                    data: function (d) {
                        d.entity_id = $('select#entity_id').val();
                        d.status = $('select#status').val();
                        d.start_date = $('input#start_date')
                            .val();
                        d.end_date = $('input#end_date')
                            .val()
                        ;
                    },
                },
                dom: dom,
                buttons: ['copy', 'excel', 'colvis'],
                columnDefs: [ {
                    "targets": [1],
                    "orderable": true,
                    "searchable": true
                } ],
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                "lengthMenu": [
                    [10, 100, 1000, -1], // Bu seçenekler
                    [10, 100, 1000, "Tümü"] // Görüntülenecek metinler
                ],
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },
                aaSorting: [[1, 'desc']],
                columns: [
                    { data: 'pos_name', name: 'pos_name', orderable: true, searchable: true },
                    { data: 'created_at', name: 'created_at', orderable: true, searchable: false },
                    { data: 'created_at_hour', name: 'created_at_hour', orderable: false, searchable: false },
                    { data: 'entity_name', name: 'entity_name', orderable: false, searchable: false },
                    { data: 'card_holder', name: 'card_holder', orderable: false, searchable: false },
                    { data: 'provision_number', name: 'provision_number', orderable: false, searchable: false },
                    { data: 'amount', name: 'amount', orderable: true, searchable: false, visible: true },
                    { data: 'amount2', name: 'amount2', orderable: true, searchable: false, visible: false },
                    { data: 'installment_count', name: 'installment_count', orderable: true, searchable: false },
                    { data: 'action', name: 'pdf', orderable: false, searchable: false },
                ],createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                    $(row).css('background-color', data.status_color)

                }


            });


            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#entity_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('select#status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
        });
        $(document).ready(function() {
            $(document).on('click', '.btn-modal', function(e) {
                e.preventDefault();
                var container = $(this).data('container');
                var url = $(this).data('href');

                $.ajax({
                    url: url,
                    dataType: 'html',
                    success: function(result) {
                        $(container)
                            .html(result)
                            .modal('show');
                        NioApp.Picker.date('.date-picker');

                        NioApp.DataTable('.datatable-init-export', {
                            responsive: {
                                details: true
                            },
                            buttons: ['copy', 'excel', 'csv', 'pdf', 'colvis']
                        });

                    },
                });
            });
        });

        $(document).ready(function() {
            $(document).on('click', '.delete-payment-transaction', function(e) {
                e.preventDefault();
                var data_table = $('#data_table').DataTable();
                var id = $(this).data('id');

                Swal.fire({
                    title: 'Bu işlem silinecek. Devam edilsin mi?',
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    cancelButtonText: '<p style=> İptal </p>',
                    customClass: {
                        actions: 'my-actions',
                        cancelButton: 'order-1 right-gap',
                        confirmButton: 'order-2',
                        denyButton: 'order-3',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#overlay").fadeOut(200); $("#overlay").fadeOut(100);
                        $.ajax({
                            method: 'POST',
                            url: '/odeme-islemi-sil',
                            data: {
                                id: id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            success: function(result) {
                                $("#overlay").fadeOut(100);
                                if (result.success) {
                                    Swal.fire(result.message, '', 'success')
                                    data_table.ajax.reload(null, false);
                                } else {
                                    Swal.fire(result.message, '', 'error')
                                    data_table.ajax.reload(null, false);
                                }
                            }
                        });
                    } else if (result.isDenied) {
                        Swal.fire('Değişiklik uygulanamadı', '', 'info')
                    }
                })
            });
        });

        $(document).ready(function() {
            $(document).on('click', '.payment-refunded', function(e) {
                e.preventDefault();
                var data_table = $('#data_table').DataTable();
                var id = $(this).data('id');

                Swal.fire({
                    title: 'Bu işlem iade edildi olarak ayarlanacak. Devam edilsin mi?',
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    cancelButtonText: '<p style=> İptal </p>',
                    customClass: {
                        actions: 'my-actions',
                        cancelButton: 'order-1 right-gap',
                        confirmButton: 'order-2',
                        denyButton: 'order-3',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#overlay").fadeOut(200); $("#overlay").fadeOut(100);
                        $.ajax({
                            method: 'POST',
                            url: '/payment-refunded',
                            data: {
                                id: id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            success: function(result) {
                                $("#overlay").fadeOut(100);
                                if (result.success) {
                                    Swal.fire(result.message, '', 'success')
                                    data_table.ajax.reload(null, false);
                                } else {
                                    Swal.fire(result.message, '', 'error')
                                    data_table.ajax.reload(null, false);
                                }
                            }
                        });
                    } else if (result.isDenied) {
                        Swal.fire('Değişiklik uygulanamadı', '', 'info')
                    }
                })
            });
        });
    </script>
@endsection
