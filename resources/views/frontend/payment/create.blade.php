@extends('frontend.layouts.app')

@section('title')Ödemeler @endsection

@section('content')
    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between g-3">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">Ödeme İste</h3>
                            <div class="nk-block-des text-soft">
                                <p>Ödeme talep formu</p>
                            </div>
                        </div><!-- .nk-block-head-content -->
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li><a href="/odeme" class="btn btn-white btn-dim btn-outline-light"><em class="icon ni ni-arrow-left"></em><span>Geri</span></a></li>

                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner">
                                <div class="card-title-group">
                                    <div class="card-title">
                                        <h5 class="title">Ödeme formu</h5>
                                    </div>

                                </div><!-- .card-title-group -->
                            </div><!-- .card-inner -->
                            <div class="card-inner">

                                @if(!$errors->isEmpty())
                                    <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                                        @foreach($errors->all() as $error)
                                            <strong>{{ __('Error') }}</strong> {{ $error }}
                                        @endforeach
                                    </div>
                                @endif

                                <form action="{{ route('odeme.store') }}" method="POST">
                                    @csrf
                                    <div class="form-group">
                                        <label for="entity_id">İlgili Cari</label>
                                        {{ html()->select('entity_id', [])->class('form-select js-search-entity')->attribute('data-search', 'on') }}
                                    </div>
                                    <div class="form-group">
                                        <label for="order_id">İlgili Sipariş</label>
                                        {{ html()->select('order_id', [])->class('form-select js-search-order')->attribute('data-search', 'on') }}
                                    </div>

                                    <div class="form-group">
                                        <label for="amount">Toplam Tutar</label>
                                        <input type="number" min="0" step="any" class="form-control" id="amount" name="amount" required>
                                    </div>
                                    <div class="form-group">
                                        <label for="remaining_amount">Kalan Tutar</label>
                                        <input type="number" min="0" step="any" class="form-control" id="remaining_amount" name="remaining_amount" required>
                                    </div>
  <div class="form-group">
                                        <label for="remaining_amount">Son Ödeme Tarihi</label>
      <input name="last_payment_date" type="text" class="form-control date-picker">
                                    </div>

                                    <button type="submit" class="btn btn-primary">Gönder</button>
                                </form>
                            </div><!-- .card-inner -->

                        </div><!-- .card-inner-group -->
                    </div><!-- .card -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>
@endsection

@section('js')
<script>

    $("#order_id").select2({

        ajax: {
            url: "/order-search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: data.items,
                    pagination: {
                        more: (params.page * 10) < data.total_count
                    }
                };
            },
            cache: false
        },
        placeholder: 'Ara',
        minimumInputLength: 3,
    });


    $("#entity_id").select2({

        ajax: {
            url: "/entity-search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: data.items,
                    pagination: {
                        more: (params.page * 10) < data.total_count
                    }
                };
            },
            cache: false
        },
        placeholder: 'Ara',
        minimumInputLength: 3,
    });
</script>
@endsection