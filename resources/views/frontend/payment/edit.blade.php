@extends('frontend.layouts.app')

@section('title')
    {{ __('Edit Payment') }}
@endsection

@section('content')
    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between g-3">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">{{ __('Request Payment') }}</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ __('Payment request form') }}</p>
                            </div>
                        </div><!-- .nk-block-head-content -->
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-3">
                                        <li><a href="/odeme" class="btn btn-white btn-dim btn-outline-light"><em class="icon ni ni-arrow-left"></em><span>{{ __('Back') }}</span></a></li>

                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->

                <div class="nk-block">
                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner">
                                <div class="card-title-group">
                                    <div class="card-title">
                                        <h5 class="title">{{ __('Payment request form') }}</h5>
                                    </div>

                                </div><!-- .card-title-group -->
                            </div><!-- .card-inner -->
                            <div class="card-inner">

                                @if(!$errors->isEmpty())
                                    <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                                        @foreach($errors->all() as $error)
                                            <strong>{{ __('Error') }}</strong> {{ $error }}
                                        @endforeach
                                    </div>
                                @endif

                                <form action="{{ route('odeme.update', $payment->id) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <div class="form-group">
                                        <label for="entity_id">{{ __('Related Account') }}</label>
                                        {{ html()->select('entity_id', [],$payment->entity_id)->class('form-select js-search-entity mb-2')->attribute('data-search', 'on') }}
                                        @isset($payment->entity) <div class="text-muted mt-2">{{ __('SELECTED ACCOUNT') }}: {{ $payment->entity->entity_name }}</div>@endif
                                    </div>
                                    <div class="form-group">
                                        <label for="order_id">{{ __('Related Order') }}</label>
                                        {{ html()->select('order_id', [])->class('form-select js-search-order')->attribute('data-search', 'on') }}
                                        <span>@empty($payment->oder_id){{ __('No Related Order') }}@else{{ $payment->order->doc_no }}@endif</span>
                                    </div>

                                    <div class="form-group">
                                        <label for="amount">{{ __('Total Amount') }}</label>
                                        <input type="number" min="0" step="any"  type="text" class="form-control" id="amount" name="amount" value="{{ number_format($payment->amount,2) }}" required>
                                    </div>
                                    <div class="form-group">
                                        <label type="number" min="0" step="any" for="remaining_amount">{{ __('Remaining Amount') }}</label>
                                        <input type="text" class="form-control" id="remaining_amount" name="remaining_amount" value="{{ number_format($payment->remaining_amount,2) }}" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="remaining_amount">{{ __('Due Date') }}</label>
                                        <input name="last_payment_date" type="text" class="form-control date-picker" value="{{ $payment->last_payment_date }}">
                                    </div>
                                    <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                                </form>


                            </div><!-- .card-inner -->

                        </div><!-- .card-inner-group -->
                    </div><!-- .card -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>
@endsection

@section('js')
<script>

    $(document).ready(function() {
        // Initialize the date picker
        $('.date-picker').datepicker({
            format: 'dd-mm-yyyy'
        });

        // Set the selected date
        var selectedDate = '@empty(!$payment->last_payment_date){{ $payment->last_payment_date->format('d-m-Y') }}@endempty';
        $('.date-picker').datepicker('setDate', selectedDate);
    });



    $("#order_id").select2({

        ajax: {
            url: "/order-search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: data.items,
                    pagination: {
                        more: (params.page * 10) < data.total_count
                    }
                };
            },
            cache: false
        },
        placeholder: 'Ara',
        minimumInputLength: 3,
    });


    $("#entity_id").select2({

        ajax: {
            url: "/entity-search",
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    q: params.term,
                    page: params.page
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;

                return {
                    results: data.items,
                    pagination: {
                        more: (params.page * 10) < data.total_count
                    }
                };
            },
            cache: false
        },
        placeholder: 'Ara',
        minimumInputLength: 3,
    });
</script>
@endsection