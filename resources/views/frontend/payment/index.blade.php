@extends('frontend.layouts.app')

@section('title'){{ __('Payments') }} @endsection

@section('content')
    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="nk-block-head nk-block-head-sm">
                    <div class="nk-block-between g-3">
                        <div class="nk-block-head-content">
                            <h3 class="nk-block-title page-title">{{ __('Payment History') }}</h3>
                            <div class="nk-block-des text-soft">
                                <p>{{ clean_note_large($user->active_entity->entity_name) }} {{ __('payments') }}.</p>
                            </div>
                        </div><!-- .nk-block-head-content -->
                        <div class="nk-block-head-content">
                            <div class="toggle-wrap nk-block-tools-toggle">
                                <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                                <div class="toggle-expand-content" data-content="pageMenu">
                                    <ul class="nk-block-tools g-1">
                                        <li>
                                            <div class="drodown"><a href="#"
                                                                    class="dropdown-toggle btn btn-white btn-dim btn-outline-light"
                                                                    data-bs-toggle="dropdown" aria-expanded="false"><em
                                                            class="d-none d-sm-inline icon ni ni-filter-alt"></em><span>{{ __('Filter') }}</span><em
                                                            class="dd-indc icon ni ni-chevron-right"></em></a>
                                                <div class="dropdown-menu dropdown-menu-end" style="">
                                                    <ul class="link-list-opt no-bdr">
                                                        <li><a href="?filtre=devam-edenler"><span>{{ __('In Progress') }}</span></a></li>
                                                        <li><a href="?filtre=suresi-gecenler"><span>{{ __('Expired') }}</span></a></li>
                                                        <li><a href="?filtre=tamamlananlar"><span>{{ __('Completed') }}</span></a></li>
                                                        <li><a href="?filtre="><span>{{ __('All') }}</span></a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                        @can('view_pos_transactions')
                                            <li>
                                                <a href="/odeme/islemler" class="btn btn-info d-md-inline-flex"><em class="icon ni ni-list"></em><span>{{ __('POS Transactions') }}</span></a>
                                            </li>
                                        @endcan

                                        @can('may_request_payment')<li>
                                            <a href="#" data-target="addProduct" class="toggle btn btn-primary d-none d-md-inline-flex"><em class="icon ni ni-plus"></em><span>{{ __('Payment Request Form') }}</span></a></li>
                                        @endcan

                                        <li>
                                            <a href="{{ route('payment.make_payment') }}" class=" btn btn-primary d-none d-md-inline-flex"><em class="icon ni ni-cc-alt"></em><span>{{ __('Make Payment') }}</span></a>
                                        </li>

                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head-content -->
                    </div><!-- .nk-block-between -->
                </div><!-- .nk-block-head -->

                <div class="nk-block">

                    @if(!$errors->isEmpty())
                        <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                            @foreach($errors->all() as $error)
                                <strong>{{ __('Error') }}</strong> {{ $error }}
                            @endforeach
                        </div>
                    @endif
                    @include('flash::message')

                    <div class="card card-bordered card-stretch">
                        <div class="card-inner-group">
                            <div class="card-inner">
                                <div class="card-title-group">
                                    <div class="card-title">
                                        <h5 class="title">{{ __('All Your Payments') }}</h5>
                                    </div>
                                    <div class="card-tools me-n1">
                                        <ul class="btn-toolbar gx-1">

                                            <li class="btn-toolbar-sep"></li><!-- li -->
                                        </ul><!-- .btn-toolbar -->
                                    </div><!-- .card-tools -->
                                    <div class="card-search search-wrap" data-search="search">
                                        <div class="search-content">
                                            <a href="#" class="search-back btn btn-icon toggle-search" data-target="search"><em class="icon ni ni-arrow-left"></em></a>
                                            <input type="text" class="form-control border-transparent form-focus-none" placeholder="{{ __('Search in transactions') }}">
                                            <button class="search-submit btn btn-icon"><em class="icon ni ni-search"></em></button>
                                        </div>
                                    </div><!-- .card-search -->
                                </div><!-- .card-title-group -->
                            </div><!-- .card-inner -->

                            <div class="card-inner p-0">
                                <div class="nk-tb-list nk-tb-tnx">
                                    <div class="nk-tb-item nk-tb-head">
                                        <div class="nk-tb-col"><span>{{ __('Transaction Detail') }}</span></div>
                                        <div class="nk-tb-col tb-col-lg"><span>{{ __('Payment Method') }}</span></div>
                                        <div class="nk-tb-col text-end"><span>{{ __('Amount to Pay') }}</span></div>
                                        <div class="nk-tb-col tb-col-lg"><span>{{ __('Order') }}</span></div>
                                        <div class="nk-tb-col text-end tb-col-sm"><span>{{ __('Payment Date') }}</span></div>
                                        <div class="nk-tb-col nk-tb-col-status"><span class="sub-text d-none d-md-block">{{ __('Status') }}</span></div>
                                        <div class="nk-tb-col nk-tb-col-tools"></div>
                                    </div><!-- .nk-tb-item -->
                                    @foreach($payments as $payment)
                                        @php
                                            $paymentTransaction =  $payment->paymentTransactions()->latest()->first();


                                        @endphp
                                        <div class="nk-tb-item">
                                            <div class="nk-tb-col">
                                                <div class="nk-tnx-type">
                                                    @if($payment->remaining_amount == 0)
                                                        <div class="nk-tnx-type-icon bg-success-dim text-success">
                                                            <em class="icon ni ni-check"></em>
                                                        </div>
                                                    @else
                                                        <div class="nk-tnx-type-icon bg-warning-dim text-warning">
                                                            <em class="icon ni ni-arrow-up-right"></em>
                                                        </div>
                                                    @endif
                                                    <div class="nk-tnx-type-text">
                                                        <span class="tb-lead">{{ __('Due Date') }}</span>
                                                        <span class="tb-date">@empty($payment->last_payment_date){{ __('Indefinite') }}@else{{ $payment->last_payment_date->format('d.m.Y') .' ('. $payment->last_payment_date->diffForHumans().')' }}@endif</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span class="tb-lead-sub">{{ __('Bank') }}:@empty($paymentTransaction->pos_name){{ '-' }}@else{{ mb_strtoupper($paymentTransaction->pos_name) }}@endempty</span>
                                                <span class="tb-sub">{{ __('Provision Number') }}: {{ $paymentTransaction->provision_number ?? '-' }} </span><br>
                                            </div>

                                            <div class="nk-tb-col text-end">
                                                <span class="tb-amount">{{ Number::currency(($payment->amount) ?? 0, 'TRY', 'tr') }}</span>
                                                <span class="badge badge-dot bg-success">{{ __('Paid') }}: {{ Number::currency(($payment->amount-$payment->remaining_amount) ?? 0, 'TRY', 'tr') }}</span><br>
                                                <span class="text-warning">{{ __('Remaining') }}: {{ Number::currency(($payment->remaining_amount) ?? 0, 'TRY', 'tr') }}  </span>
                                            </div>
                                            <div class="nk-tb-col tb-col-lg">
                                                <span class="tb-lead-sub">{{ $payment->order->doc_no ?? '-' }}</span>
                                                <span class="tb-amount-sm">{{ limit_words($payment->entity->entity_name ?? '',3)   }}</span>
                                            </div>
                                            <div class="nk-tb-col text-end tb-col-sm">
                                                {{ $payment->updated_at->format('d.m.Y') }}
                                            </div>
                                            <div class="nk-tb-col nk-tb-col-status">
                                                <div class="dot dot-success d-md-none"></div>
                                                @if($payment->remaining_amount == 0.0000)
                                                    <span class="badge badge-sm badge-dim text-success bg-success-dim d-none d-md-inline-flex">{{ __('Completed') }}</span>

                                                @else

                                                    <span class="badge badge-sm text-warning badge-dim bg-outline-warning d-none d-md-inline-flex">{{ __('Progress') }}</span>
                                                @endif
                                            </div>
                                            <div class="nk-tb-col nk-tb-col-tools">
                                                <ul class="nk-tb-actions gx-2">
                                                    @if($payment->remaining_amount > 0)
                                                        <li class="nk-tb-action-hidden">
                                                            <a href="{{ route('payment.show', encode_id($payment->id)) }}" class="bg-white btn btn-sm btn-outline-light btn-icon btn-tooltip" title="{{ __('Make Payment') }}"><em class="icon ni ni-sign-try"></em></a>
                                                        </li>
                                                    @endif
                                                    <li>
                                                        <div class="dropdown">
                                                            <a href="#" class="dropdown-toggle bg-white btn btn-sm btn-outline-light btn-icon" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                            <div class="dropdown-menu dropdown-menu-end">
                                                                <ul class="link-list-opt">
                                                                    <li><a href="{{ route('odeme.edit', $payment->id) }}"><em class="icon ni ni-edit"></em><span>{{ __('Edit') }}</span></a></li>
                                                                    @if($payment->remaining_amount > 0)
                                                                        <li><a href="{{ route('payment.show', encode_id($payment->id)) }}"><em class="icon ni ni-sign-try"></em><span>{{ __('Make Payment') }}</span></a></li>
                                                                    @endif
                                                                    @if(auth()->user()->hasRole('super admin'))
                                                                        {{--                                                                    <li><a data-href="{{ action('App\Http\Controllers\Frontend\PaymentController@destroy', [$payment->id]) }}" class="delete-payment-request"><em class="icon ni ni-trash"></em><span>{{ __('Delete') }}</span></a></li>--}}

                                                                        <li><a data-id="{{ encode_id($payment->id) }}" class="delete-payment-request"><em class="icon ni ni-trash"></em><span>{{ __('Delete') }}</span></a></li>
                                                                    @endif
                                                                </ul>
                                                            </div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div><!-- .nk-tb-item -->
                                    @endforeach

                                </div><!-- .nk-tb-list -->
                            </div><!-- .card-inner -->
                            <div class="card-inner">
                                {{ $payments->appends(request()->query())->links() }}
                            </div><!-- .card-inner -->
                        </div><!-- .card-inner-group -->
                    </div><!-- .card -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>

    @can('may_request_payment')
        <div class="nk-add-product toggle-slide toggle-slide-right" data-content="addProduct" data-toggle-screen="any" data-toggle-overlay="true" data-toggle-body="true" data-simplebar>
            <div class="nk-block-head">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ __('New Payment Request') }}</h5>
                    <p>{{ __('Payment request form') }}</p>
                </div>
            </div><!-- .nk-block-head -->
            <div class="nk-block">
                <form action="{{ route('odeme.store') }}" method="POST" class="form-validate">
                    @csrf
                    @if($user->hasRole('super admin'))
                        <div class="form-group">
                            <label for="entity_id">{{ __('Related Current Account') }}</label>
                            {{ html()->select('entity_id', old('entity_id', []))->class('form-select js-search-entity')->attribute('data-search', 'on') }}
                        </div>
                    @endif

                    <div class="form-group">
                        <label for="entity_id">{{ __('Mobile Phone') }}</label>
                        {{ html()->text('phone_number')->class('form-control')->placeholder(__('Enter mobile phone number'))->value(old('phone_number')) }}
                    </div>
                    <div class="form-group">
                        <label for="order_id">{{ __('Related Order') }}</label>
                        {{ html()->select('order_id', old('order_id'), [])->class('form-select js-search-order')->attribute('data-search', 'on') }}
                    </div>

                    <div class="form-group">
                        <label for="amount">{{ __('Total Amount') }}</label>
                        <input type="number" min="0" step="any" value="{{ old('amount') }}" class="form-control" id="amount" name="amount" required>
                    </div>
                    <div class="form-group">
                        <label for="remaining_amount">{{ __('Remaining Amount') }}</label>
                        <input type="number" min="0" step="any" class="form-control" value="{{ old('remaining_amount') }}" placeholder="0.00" id="remaining_amount" name="remaining_amount">
                    </div>

                    <div class="form-group">
                        <label for="last_payment_date">{{ __('Due Date') }}</label>
                        <input name="last_payment_date" value="{{ old('_last_payment_date') }}" type="text" class="form-control date-picker" required>
                    </div>
                    <button type="submit" class="btn btn-primary"><em class="icon ni ni-save"></em> <span>{{ __('Save') }}</span></button>
                </form>
            </div><!-- .nk-block -->
        </div>
    @endcan
@endsection

@section('js')
    <script>

        $("#order_id").select2({

            ajax: {
                url: "/order-search",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 10) < data.total_count
                        }
                    };
                },
                cache: false
            },
            placeholder: 'Ara',
            minimumInputLength: 3,
        });


        $("#entity_id").select2({

            ajax: {
                url: "/entity-search",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 10) < data.total_count
                        }
                    };
                },
                cache: false
            },
            placeholder: 'Ara',
            minimumInputLength: 3,
        });


        $(document).ready(function() {
            $(document).on('click', '.delete-payment-request', function(e) {
                e.preventDefault();

                var id = $(this).data('id');

                Swal.fire({
                    title: 'Bu ödeme talebi silinecek. Devam edilsin mi?',
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    cancelButtonText: '<p style=> İptal </p>',
                    customClass: {
                        actions: 'my-actions',
                        cancelButton: 'order-1 right-gap',
                        confirmButton: 'order-2',
                        denyButton: 'order-3',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#overlay").fadeOut(200); $("#overlay").fadeOut(100);
                        $.ajax({
                            method: 'POST',
                            url: '/odeme-sil',
                            data: {
                                id: id,
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            success: function(result) {
                                $("#overlay").fadeOut(100);
                                if (result.success) {
                                    Swal.fire(result.message, '', 'success')
                                    data_table.ajax.reload(null, false);
                                } else {
                                    Swal.fire(result.message, '', 'error')
                                    data_table.ajax.reload(null, false);
                                }
                            }
                        });
                    } else if (result.isDenied) {
                        Swal.fire('Değişiklik uygulanamadı', '', 'info')
                    }
                })
            });
        });
    </script>
@endsection