<div class="modal-dialog" role="document">
    <div class="modal-content">
        <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
        <div class="modal-header bg-primary"><h5 class="modal-title text-white"> {{ __('Add Address') }}</h5></div>
        <form action="{{ action('App\Http\Controllers\Frontend\AddressController@store') }}" method="post" id="address_form">
            @csrf

            <div class="modal-body">

                <div class="form-group">
                    <label for="title" class="form-label">{{ __('Address Title') }}*</label>
                    <input type="text" name="title" id="title" class="form-control" required placeholder="{{ __('Enter a title for the address') }}">
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="country_id" class="form-label">{{ __('Country') }}*</label>
                            <select name="country_id" id="country_id" class="form-select js-select2" data-search="on" style="width:100%" required>
                                <option value="">{{ __('All') }}</option>
                                @foreach($countries as $id => $name)
                                    <option value="{{ $id }}">{{ $name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="city_id" class="form-label">{{ __('City') }}*</label>
                            <select name="city_id" id="city_id" class="form-select js-select2" data-search="on" style="width:100%" required>
                                <option value="">{{ __('All') }}</option>
                                <!-- Şehir seçenekleri JavaScript ile doldurulacak -->
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="town_id" class="form-label">{{ __('District') }}</label>
                            <select name="town_id" id="town_id" class="form-select js-select2" data-search="on" style="width:100%">
                                <option value="">{{ __('All') }}</option>
                                <!-- İlçe seçenekleri JavaScript ile doldurulacak -->
                            </select>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="address1" class="form-label">{{ __('Address Line 1') }} *</label>
                    <input type="text" name="address1" id="address1" class="form-control" required placeholder="{{ __('Address') }}">
                </div>

                <div class="form-group">
                    <label for="address2" class="form-label">{{ __('Address Line 2') }}</label>
                    <input type="text" name="address2" id="address2" class="form-control" placeholder="{{ __('Address Line 2') }}">
                </div>

                <div class="form-group">
                    <label for="address3" class="form-label">{{ __('Address Line 3') }}</label>
                    <input type="text" name="address3" id="address3" class="form-control" placeholder="{{ __('Address Line 3') }}">
                </div>
            </div>

            <div class="modal-footer">
                <button type="submit" class="btn btn-primary">{{ __('Save') }}</button>
                <button type="button" class="btn btn-default" data-bs-dismiss="modal">{{ __('Cancel') }}</button>
            </div>
        </form>
    </div>
</div>