<div class="modal-dialog" role="document">
    <div class="modal-content">
        <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
        <div class="modal-header bg-primary"><h5 class="modal-title text-white"> {{ __('Edit Address') }}</h5></div>
        <div class="modal-body">
            <div class="form-group">
                <label class="form-label" for="title">{{ __('Title') }}</label>
                <div class="form-control-wrap">
                    <input type="text" class="form-control form-control-lg" id="title" name="title" value="{{ $address->title }}" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="full-name">{{ __('Address') }}</label>
                    <div class="form-control-wrap">
                        <div class="form-icon form-icon-left"><em class="icon ni ni-map-pin"></em></div>
                        <input type="text" class="form-control form-control-lg" value="{{ $address->address }}" readonly>
                    </div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="full-name">{{ __('Country') }}</label>
                    <div class="form-group">
                        <label for="country_id" class="form-label">{{ __('Country') }}</label>
                        <select name="country_id" id="country_id" class="form-select js-select2" data-search="on" style="width:100%">
                            <option value="">{{ __('All') }}</option>
                            @foreach($countries as $id => $name)
                                <option value="{{ $id }}">{{ $name }}</option>
                            @endforeach
                        </select>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>