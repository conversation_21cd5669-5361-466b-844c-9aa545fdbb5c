<div class="modal-dialog modal" role="document">
    <form action="{{ action('App\Http\Controllers\Frontend\EntityController@change_entity_update') }}" method="post" id="change_active_entity_form">

        <div class="modal-content">
            <a href="#" class="close" data-bs-dismiss="modal"
               aria-label="Close"> <em class="icon ni ni-cross"></em>
            </a>
            <div class="modal-header"><h5 class="modal-title">{{ __('Change Active Account') }}</h5>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">

                            @empty($entities)
                                <label for="active_entity_id" class="form-label">{{ __('Search company') }}</label>
                                {{ html()->select('active_entity_id', [])->class('form-select js-search-entity')->attribute('data-search', 'on') }}
                            @else
                                <label for="active_entity_id" class="form-label">{{ __('Select company') }}</label>
                                <select name="active_entity_id" id="active_entity_id" class="form-select js-select2" data-search="on">
                                    <option></option>
                                    @foreach($entities as $entity)
                                        <option value="{{ $entity->id }}">{{ limit_words($entity->entity_name,6) }}</option>
                                    @endforeach
                                </select>
                            @endempty

                            <span class="sub-text">{{ __('If you have multiple companies, you can change your active account to process.') }}</span>
                        </div>
                    </div>
                </div>

            </div>
            <div class="modal-footer bg-light">
                <div class="form-group">
                    <button type="submit" class="btn btn-lg btn-primary">{{ __('Save') }}</button>
                </div>
            </div>
        </div>
    @csrf
     </form>
</div>


