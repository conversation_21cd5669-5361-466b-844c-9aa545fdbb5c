@extends('frontend.layouts.app')

@section('title')
{{ __('Contract') }}
@endsection

@section('content')
    <div class="nk-content nk-content-fluid">
        <div class="container-xl wide-lg">
            <div class="nk-content-body">
                <div class="nk-block-head">
                    <div class="nk-block-head-sub"><span>{{ __('Contracts') }}</span></div>
                    <div class="nk-block-between-md g-4">
                        <div class="nk-block-head-content">
                            <h2 class="nk-block-title fw-normal">{{ __('Contract Details') }}</h2>
                            <div class="nk-block-des">
                                <p>{{ $contract->doc_no }}</p>
                            </div>
                        </div>
                        <div class="nk-block-head nk-block-head-sm nk-block-between">
                            <div class="btn-group">
                                <button type="button" class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown"><span>{{ __('Contract List') }}</span> <em class="icon ni ni-chevron-down"></em></button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <ul class="link-list-opt no-bdr">
                                        <li><a href="#"><span>{{ __('Check Contract') }}</span></a></li>
                                        <li><a href="#"><span>{{ __('DBS Contract') }}</span></a></li>
                                        <li><a href="#"></em><span>{{ __('Cash Contract') }}</span></a></li>
                                        <li><a href="#"></em><span>{{ __('Credit Card Contract') }}</span></a></li>
                                    </ul>
                                </div>
                            </div>
                        </div><!-- .nk-block-head -->
                    </div>
                </div><!-- .nk-block-head -->
                <div class="nk-block">
                    <div class="card card-bordered">
                        <div class="card-aside-wrap">
                            <div class="card-content">
                                <div class="card-inner">
                                    <div class="nk-block">
                                        <div class="nk-block-head nk-block-head-sm nk-block-between">
                                            <h5 class="title">{{ __('Contract Detail') }}</h5>
                                        </div><!-- .nk-block-head -->
                                        <div class="row gy-5">
                                            <div class="col-md-3 col-lg-2 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Total Amount') }}</span>
                                                    <span> {{ Number::currency($contract->amt, $contract->currency->cur_code, 'tr') }} </span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-lg-2 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Ton Price') }}</span>
                                                    <span> {{ Number::currency($contract->zz_ton_price, $contract->currency->cur_code, 'tr') }} </span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-lg-2  col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Connection Type') }}</span>
                                                    <span>{{ $contract->doc_description }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-lg-2  col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Maturity') }}</span>
                                                    <span>{{ $contract->due_day }} {{ __('days') }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-lg-2  col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Start Date') }}</span>
                                                    <span>{{ date('d.m.Y', strtotime($contract->contract_start_date)) }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-lg-2  col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('End Date') }}</span>
                                                    <span>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div><!-- .nk-block -->
                                    <div class="nk-divider divider md"></div>
                                    <div class="nk-block">
                                        <div class="nk-block-head nk-block-head-sm nk-block-between">
                                            <h5 class="title">{{ __('Contract Orders') }}</h5>
                                        </div><!-- .nk-block-head -->
                                        <div class="row gy-5">
                                            <div class="col-md-3 col-sm-4 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Total Order Amount') }}</span>
                                                    <span>{{ Number::currency($contract_order_total, $contract->currency->cur_code, 'tr') }} </span>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-4 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Total Orders') }}</span>
                                                    <span class="amount text-primary">{{ $contract_order_count }}</span>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-4 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Closed Orders') }}</span>
                                                    <span class="amount text-success">3</span>
                                                </div>
                                            </div>
                                            <div class="col-md-2 col-sm-4 col-12">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Open Orders') }}</span>
                                                    <span class="amount text-warning">1</span>
                                                </div>
                                            </div>
                                            <div class="col-md-3 col-sm-4 col-6">
                                                <div class="profile-stats">
                                                    <span class="profile-ud-label">{{ __('Last Order Date') }}</span>
                                                    @empty($last_order_date)-@else <span>{{ $last_order_date->diffForHumans() .' (' .date('d.m.Y', strtotime($last_order_date)).')' }}@endempty</span>
                                                </div>
                                            </div>

                                        </div>
                                    </div><!-- .nk-block -->
                                    <div class="nk-divider divider md"></div>
                                    <div class="nk-block">
                                        <div class="nk-block-head nk-block-head-sm nk-block-between">
                                            <h5 class="title">{{ __('Usage Status') }}</h5>
                                        </div><!-- .nk-block-head -->
                                        <div class="row gy-5">
                                            <div class="col-12">
                                                <div class="profile-stats">
                                                    <div class="progress progress-lg">
                                                        <div class="progress-bar progress-bar-striped bg-success" data-progress="{{ $used_percent }}">
                                                            {{ $used_percent }}%</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div><!-- .nk-block -->
                                    <div class="nk-divider divider md"></div>
                                    <div class="nk-block">
                                        <div class="nk-block-head nk-block-head-sm nk-block-between">
                                            <h5 class="title"><code>{{ __('Note') }}</code></h5>
                                        </div><!-- .nk-block-head -->
                                        <div class="row gy-5">
                                            <div class="col-12">
                                                <dt>{{ $contract->entity->entity_name .' '. $contract->note_large }}</dt>
                                            </div>
                                        </div>
                                    </div><!-- .nk-block -->
                                </div><!-- .card-inner -->

                                @php($amt_tra = 0)
                                @isset($details)
                                    <table class="table">
                                        <thead>
                                        <tr>
                                            <th>{{ __('Transaction Date') }}</th>
                                            <th>{{ __('Transaction Amount') }}</th>
                                            <th>{{ __('Transaction Quantity') }}</th>
                                            <th>{{ __('Source') }}</th>
                                            <th>{{ __('Input/Output') }}</th>
                                            <th>{{ __('Plus/Minus') }}</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($details as $detail)
                                            <tr>
                                                <td>{{ date('d.m.Y', strtotime($detail['doc_date'])) }}</td>
                                                <td>{{ Number::currency($detail['amt_tra'], 'try', 'tr') }}</td>
                                                <td>{{ $detail['qty'] }}</td>
                                                <td> {{ $detail['source_app'] }}</td>
                                                <td> {{ $detail['input_output'] }}</td>
                                                <td> {{ $detail['plus_minus'] }}</td>
                                            </tr>
                                            {{ $amt_tra += $detail['amt_tra'] }}
                                        @endforeach
                                        </tbody>
                                        <tfoot>
                                        <tr>
                                            <th>-</th>
                                            <th>{{ $amt_tra }}</th>
                                            <th>{{ __('Transaction Quantity') }}</th>
                                            <th>-</th>
                                            <th>-</th>
                                            <th>-</th>
                                        </tr>
                                        </tfoot>
                                    </table>
                                @endisset
                            </div><!-- .card-content -->
                        </div><!-- .card-aside-wrap -->
                    </div><!-- .card -->
                </div><!-- .nk-block -->
            </div>
        </div>
    </div>
@endsection

@section('js')
@endsection
