<div class="modal-dialog modal-lg" role="document">
    <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"
                                  aria-label="Close"> <em class="icon ni ni-cross"></em>
        </a>
        <div class="modal-header"><h5 class="modal-title">{{ $contract->doc_no }} Sözleşme İşlemleri</h5></div>
        <div class="modal-body" style="min-height: 350px">
            @php
                setlocale(LC_TIME, 'tr_TR.UTF-8');
            @endphp

            <div class="simplebar-mask">
                <div class="simplebar-offset">
                    <div class="simplebar-content-wrapper" tabindex="0" role="region"
                         aria-label="scrollable content" style="height: 100%; overflow: hidden scroll;">
                        <div class="simplebar-content" style="padding: 0px;">

                            <div class="card-inner">
                                <div class="timeline">
                                    <ul class="timeline-list">
                                        @foreach($contractDetails as $detail)
                                            <li class="timeline-item">
                                                @if($detail['qty']>0)
                                                    <div class="timeline-status bg-warning"></div>
                                                    <div class="timeline-date">{{ strftime('%e %B %Y', strtotime($detail['doc_date'])) }} </div>
                                                    <div class="timeline-data"><h6
                                                                class="timeline-title">{{ $detail['item_name'] }}</h6>
                                                        <div class="timeline-des">
                                                            <p>Tutar: {{ $detail['amt'] }} TL
                                                                Miktar: {{ number_format($detail['qty'],2) }} adet</p>
                                                            <span class="time">Birim fiyat: @if($detail['qty']>0)
                                                                    {{ number_format($detail['amt']/$detail['qty'],2) }}
                                                                    ₺
                                                                @else
                                                                    {{ '-' }}
                                                                @endif</span>
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="timeline-status bg-success"></div>
                                                    <div class="timeline-date">{{ strftime('%e %B', strtotime($detail['doc_date'])) }}
                                                        <em class="icon ni ni-alarm-alt"></em></div>
                                                    <div class="timeline-data"><h6 class="timeline-title">İşlem</h6>
                                                        <div class="timeline-des"><p>Tutar: {{ $detail['amt'] }}</p>
                                                        </div>
                                                    </div>
                                                @endif
                                            </li>
                                        @endforeach
                                    </ul>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>


        </div>
        <div class="modal-footer bg-light"><span
                    class="sub-text">Son güncelleme: {{ date('d.m.Y') }}</span></div>
    </div>
</div>