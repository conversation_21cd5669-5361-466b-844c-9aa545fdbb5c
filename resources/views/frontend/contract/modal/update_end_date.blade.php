<div class="modal-dialog" role="document">
    <div class="modal-content"><a href="#" class="close" data-bs-dismiss="modal"
                                  aria-label="Close"> <em class="icon ni ni-cross"></em>
        </a>
        <form action="/sozlesme/{{ encode_id($contract->id) }}" class="form-validate is-alter" id="update_contract_end_date_form" method="post">
            <div class="modal-header"><h5 class="modal-title">Sözleşme Bitiş Tarihi Güncelle</h5></div>
        <div class="modal-body">
            <p>{{ $contract->doc_no }} nolu sözleşme için güncel tarih:
                <b>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}</b></p>
                <div class="form-group">
                    <label class="form-label"><PERSON><PERSON> Bitiş <PERSON></label>
                    <div class="form-control-wrap">
                        <div class="form-icon form-icon-left">
                            <em class="icon ni ni-calendar"></em>
                        </div>
                        <input type="date" name="contract_end_date" class="form-control date-picker" required
                               data-date-format="dd-mm-yyyy">
                    </div>
                    <div class="form-note">Tarih formatı <code>gg-aa-yyyy</code></div>
                </div>
                <div class="form-group">
                    <label class="form-label" for="pay-amount">Not</label>
                    <div class="form-control-wrap">
                        <input type="text" class="form-control" id="pay-amount">
                    </div>
                </div>
@csrf
@method('PUT')
<input type="hidden" name="contract_id" value="{{ $contract->id }}">
        </div>
        <div class="modal-footer bg-light">
            <div class="form-group">
                <button type="submit" class="btn btn-lg btn-primary">Kaydet</button>
            </div>
        </div>
        </form>
    </div>
</div>