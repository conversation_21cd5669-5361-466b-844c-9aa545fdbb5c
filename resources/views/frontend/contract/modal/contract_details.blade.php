<div class="modal-dialog" role="document">
    <div class="modal-content">
        <a href="#" class="close" data-bs-dismiss="modal" aria-label="Close"> <em class="icon ni ni-cross"></em></a>
        <div class="modal-header bg-primary"><h5 class="modal-title text-white"> Sözleşme Bilgileri</h5></div>

        <table class="table">
            <tbody>
            <tr>
                <td><b>Sözleşme No</b></td>
                <td>{{ $contract->doc_no }}</td>
            </tr>
            <tr>
                <td><b><PERSON><PERSON></b></td>
                <td>{{ $contract->entity->entity_name }}</td>
            </tr>

            <tr>
                <td><b><PERSON><PERSON><PERSON><PERSON><PERSON> Tarihi</b></td>
                <td>{{ date('d.m.Y', strtotime($contract->contract_start_date)) }}</td>
            </tr>
            <tr>
                <td><b><PERSON><PERSON>ş Tarihi</b></td>
                <td>{{ date('d.m.Y', strtotime($contract->contract_end_date)) }}
                    ({{ $contract->contract_end_date->diffForHumans() }})
                </td>
            </tr>
            <tr>
                <td><b>Satış Temsilcisi</b></td>
                <td>@if(!empty($contract->sales_person)){{ $contract->sales_person->first_name }}@else{{ '-' }}@endif</td>
            </tr>
            <tr>
                <td><b>Para Birimi</b></td>
                <td>{{ $contract->currency->description }}</td>
            </tr>
            <tr>
                <td><b>Sözleşme Tutarı</b></td>
                <td>{{ Number::currency($contract->amt,'TRY','tr') }} </td>
            </tr>
            <tr>
                <td><b>Kullanılan Tutar</b></td>
                <td>{{ Number::currency($contract->used_amount,'try','tr') }}</td>
            </tr>
            <tr>
                <td><b>Kalan Tutar</b></td>
                <td>{{ Number::currency(($contract->remaining_amount),'try','tr') }}</td>
            </tr>

            <tr>
                <td><b>Ton Fiyatı</b></td>
                <td>{{ Number::currency($contract->zz_ton_price,'try','tr') }}</td>
            </tr>


            </tbody>
        </table>
        <!-- DivTable.com -->


    </div>
</div>