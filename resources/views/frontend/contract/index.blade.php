@extends('frontend.layouts.app')

@section('title')
    {{ __('Contracts') }}
@endsection

@section('content')

    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="components-preview mx-auto">
                    <div class="nk-block nk-block-lg">
                        <div class="nk-block-head">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">{{ __('Contracts') }}</h4>
                                <div class="nk-block-des">
                                    <p>{{ __('You can access contract information and details through this screen.') }}</p>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-inner">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="entity_id" class="form-label">{{ __('Current') }}</label>
                                            <select name="entity_id" id="entity_id" @if(count($entities) == 1){{ 'disabled' }}@endif class="form-select js-select2" data-search="on" style="width:100%">
                                                <option value="">{{ __('All') }}</option>
                                                @foreach($entities as $id => $name)
                                                    <option value="{{ $id }}" {{ auth()->user()->active_entity_id == $id ? 'selected' : '' }}>{{ $name }}</option>
                                                @endforeach
                                            </select>

                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                            <div class="form-control-wrap">
                                                <div class="input-daterange date-picker-range input-group">
                                                    <input type="text" autocomplete="off" name="start_date" id="start_date" class="form-control"/>
                                                    <div class="input-group-addon">-</div>
                                                    <input type="text" autocomplete="off" name="end_date" id="end_date" class="form-control"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="ispassive" id="ispassive">
                                                    <option value="0" selected>{{ __('Active Contracts') }}</option>
                                                    <option value="1">{{ __('Expired Contracts') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Connection Type') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="doc_tra_id" id="doc_tra_id">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="2919">{{ __('Cash Contract') }}</option>
                                                    <option value="2920">{{ __('DBS Contract') }}</option>
                                                    <option value="2918">{{ __('Credit Card Contract') }}</option>
                                                    <option value="2916">{{ __('Check Contract') }}</option>

                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card card-bordered card-preview">
                            <div class="card-body">
                                <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                                    <thead>
                                    <tr class="nk-tb-item nk-tb-head">
                                        <th class="nk-tb-col">
                                            #
                                        </th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Connection No') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Current Title') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Connection Type') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Ton Price') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Maturity') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Start Date') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('End Date') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Connection Amount') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Shipment Amount') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Remaining Amount') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Action') }}</span></th>
                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div><!-- .card-preview -->
                    </div> <!-- nk-block -->
                </div><!-- .components-preview -->
            </div>
        </div>
    </div>

@endsection

@section('js')

    <script type="text/javascript">
        $(document).ready( function(){
            var dom = '<"row justify-between g-2 has_export"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2" l>>>><"my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';
            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\ContractController@index') }}',
                    data: function (d) {
                        d.entity_id = $('select#entity_id').val();
                        d.ispassive = $('select#ispassive').val();
                        d.doc_tra_id = $('select#doc_tra_id').val();

                        d.start_date = $('input#start_date')
                            .val()
                            ;
                        d.end_date = $('input#end_date')
                            .val()
                            ;
                    },
                },

                columnDefs: [ {
                    "targets": [1],
                    "orderable": true,
                    "searchable": true
                } ],
                dom: dom,
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },

                aaSorting: [[0, 'desc']],
                columns: [
                    { data: 'id', name: 'id' },
                    { data: 'doc_no', name: 'doc_no', orderable: false, searchable: false },
                    { data: 'entity_name', name: 'entity_name', orderable: false, searchable: false },
                    { data: 'doc_description', name: 'doc_description', orderable: false, searchable: true },
                    { data: 'zz_ton_price', name: 'zz_ton_price', orderable: true, searchable: false },
                    { data: 'due_day', name: 'due_day', orderable: true, searchable: false },
                    { data: 'contract_start_date', name: 'contract_start_date', orderable: false, searchable: false },
                    { data: 'contract_end_date', name: 'contract_end_date', orderable: false, searchable: false },
                    { data: 'amt', name: 'amt' },
                    { data: 'shipping_amount', name: 'shipping_amount', orderable: true, searchable: false  },
                    { data: 'remaining_amount', name: 'remaining_amount', orderable: true, searchable: false  },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ],createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                    $(row).css('background-color', data.status_color)
                }
            });
            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#entity_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );   $('select#doc_tra_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('select#ispassive').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
        });

        $(document).on('click', '.btn-modal', function(e) {
            e.preventDefault();
            var container = $(this).data('container');
            var url = $(this).data('href');

            $.ajax({
                url: url,
                dataType: 'html',
                success: function(result) {
                    $(container)
                        .html(result)
                        .modal('show');
                NioApp.DataTable('.datatable-init', {
                    responsive: {
                        details: false
                    },
                    columnDefs: [
                        { responsivePriority: 1, targets: 0 },
                        { responsivePriority: 2, targets: -1 }
                    ],
                    order: [[0, 'desc']],
                    lengthMenu: [10, 20, 30, 50, 100],
                    pageLength: 10
                });


                },
            });
        });

        $('#modal_container').on('shown.bs.modal', function (e) {
            var orders_datatable = $('#modal_container #orders_datatable');

            NioApp.DataTable(orders_datatable, {
                layout: {
                    topStart: {
                        buttons: ['copy', 'csv', 'excel', 'pdf', 'print']
                    }
                }
            });
            })

        $(document).on('submit', "form#update_contract_end_date_form", function (e) {
            e.preventDefault();
            var form = $(this);
            var data_table = $('#data_table').DataTable();
            var data = form.serialize();
            data = data + '&action=update_contract_end_date';
            $("#overlay").fadeIn(150);
            $.ajax({
                method: 'POST',
                url: $(this).attr('action'),
                dataType: 'json',
                data: data,
                success: function (response) {
                    $("#overlay").fadeOut(200);
                    if (response.success === true) {
                        $('div#modal_container').modal('hide');
                        toastr.clear();
                        NioApp.Toast(response.message, 'info', {position: 'top-center'});
                        data_table.ajax.reload(null, false);

                    } else {
                        $("#overlay").fadeOut(100);
                        toastr.clear();
                        NioApp.Toast(response.message, 'error', {position: 'top-center'});
                    }
                },
            });
        });


    </script>


@can('active_passive_contract')
    <script>
        $(document).ready(function() {
            $(document).on('click', '.active-passive', function(e) {
                e.preventDefault();
                var id = $(this).data('id');
                var url = '/sozlesme/'+id;
                var data_table = $('#data_table').DataTable();

                Swal.fire({
                    title: 'Sözleşme durumu güncellenecek. Devam edilsin mi?',
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    cancelButtonText: '<p style=> İptal </p>',
                    customClass: {
                        actions: 'my-actions',
                        cancelButton: 'order-1 right-gap',
                        confirmButton: 'order-2',
                        denyButton: 'order-3',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        $("#overlay").fadeOut(200); $("#overlay").fadeOut(100);
                        $.ajax({
                            method: 'PUT',
                            url: url,
                            data: {
                                id: id,
                                action: 'active_passive_contract',
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            success: function(result) {
                                $("#overlay").fadeOut(100);
                                if (result.success) {
                                    Swal.fire(result.message, '', 'success')
                                    data_table.ajax.reload(null, false);
                                } else {
                                    Swal.fire(result.message, '', 'error')
                                    data_table.ajax.reload(null, false);
                                }
                            }
                        });
                    } else if (result.isDenied) {
                        Swal.fire('Değişiklik uygulanamadı', '', 'info')
                    }
                })
            });
        });
    </script>
    @endcan
@endsection
