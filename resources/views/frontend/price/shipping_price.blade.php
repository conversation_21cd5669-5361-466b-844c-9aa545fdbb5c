@extends('frontend.layouts.app')

@section('title')
    {{ __('Shipping Price') }}
@endsection

@section('content')

    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="components-preview mx-auto">
                    <div class="nk-block nk-block-lg">
                        <div class="nk-block-head">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">{{ __('Shipping Information') }}</h4>

                            </div>
                        </div>

                        <div class="card card-bordered card-preview">
                            <div class="card-body">

                                <div class="card-inner" id="payment-types">
                                    <ul class="nav nav-tabs mt-n3" role="tablist">
                                        <li class="nav-item active" role="presentation" id="tab-eft"><a
                                                    class="nav-link"
                                                    data-bs-toggle="tab"
                                                    href="#tabItem6"
                                                    aria-selected="false"
                                                    tabindex="-1" role="tab"><em
                                                        class="icon ni ni-money"></em><span>{{ __('Shipping Prices') }}</span></a>
                                        </li>
                                        <li class="nav-item " role="presentation" id="tab-cc"><a
                                                    class="nav-link"
                                                    data-bs-toggle="tab"
                                                    href="#tabItem5"
                                                    aria-selected="true" role="tab"><em
                                                        class="icon ni ni-truck"></em><span>{{ __('Shipping Companies') }}</span></a>
                                        </li>


                                    </ul>
                                    <div class="tab-content">

                                        <div class="tab-pane " id="tabItem5" role="tabpanel">
                                            <div class="row g-gs mb-3">



                                            </div>
                                        </div>

                                        <div class="tab-paneactive" id="tabItem6" role="tabpanel">


                                            <table class="nk-tb-list nk-tb-ulist datatable-init">
                                                <thead>
                                                <tr class="nk-tb-item nk-tb-head">
                                                    <th class="nk-tb-col">#</th>
                                                    <th class="nk-tb-col"><span class="sub-text">{{ __('Destination') }}</span></th>
                                                    <th class="nk-tb-col"><span class="sub-text">{{ __('Price') }}</span></th>
                                                    <th class="nk-tb-col"><span class="sub-text">{{ __('VAT') }}</span></th>

                                                </tr>
                                                </thead>
                                                <tbody>
                                                @foreach($prices as $price)
                                                    <tr class="nk-tb-item nk-tb-head">
                                                        <th class="nk-tb-col">{{ $price->line_no }}</th>
                                                        <th class="nk-tb-col"><span class="sub-text">{{ $price->priceRule->rule_name }}</span></th>
                                                        <th class="nk-tb-col"><span class="sub-text">{{ Number::currency($price->unit_price_tra,'TRY','tr') }}</span></th>
                                                        <th class="nk-tb-col"><span class="sub-text">{{ Number::currency($price->unit_price_tra * 0.20,'TRY','tr')}}</span></th>
                                                        @endforeach
                                                    </tr>
                                                </tbody>
                                            </table>

                                        </div>


                                    </div>
                                </div>

                            </div>
                        </div><!-- .card-preview -->
                    </div> <!-- nk-block -->
                </div><!-- .components-preview -->
            </div>
        </div>
    </div>

@endsection

@section('js')

    <script type="text/javascript">




    </script>

@endsection
