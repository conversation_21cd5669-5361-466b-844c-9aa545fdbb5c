@extends('frontend.layouts.app')

@section('title')
    {{ __('Prices') }}
@endsection

@section('content')

    <div class="container">
        <div class="nk-content-inner">
            <div class="nk-content-body">
                <div class="components-preview mx-auto">
                    <div class="nk-block nk-block-lg">
                        <div class="nk-block-head">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">{{ __('Price Information') }}</h4>

                            </div>
                        </div>

                        <div class="card">
                            <div class="card-inner">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="contract_id" class="form-label">{{ __('Contract') }}</label>
                                            <select name="contract_id" id="contract_id" class="form-select js-select2" data-search="on" style="width:100%">
                                                <option value="0">{{ __('Purchase without contract') }}</option>
                                                @foreach($contracts as $id => $name)
                                                    <option value="{{ $id }}">{{ $name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group"><label class="form-label">{{ __('Date') }}</label>
                                            <div class="form-control-wrap">
                                                <div class="input-daterange date-picker-range input-group">
                                                    <input type="text" autocomplete="off" name="start_date"
                                                           id="start_date" class="form-control"/>
                                                    <div class="input-group-addon">-</div>
                                                    <input type="text" autocomplete="off" name="end_date" id="end_date"
                                                           class="form-control"/></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label class="form-label">{{ __('Status') }}</label>
                                            <div class="form-control-wrap">
                                                <select class="form-control" name="status" id="status">
                                                    <option value="">{{ __('All') }}</option>
                                                    <option value="active">{{ __('Active Prices') }}</option>
                                                    <option value="passive">{{ __('Ended Passive') }}</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>

                        <div class="card card-bordered card-preview">
                            <div class="card-body">
                                <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                                    <thead>
                                    <tr class="nk-tb-item nk-tb-head">
                                        <th class="nk-tb-col">#</th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Stock Code') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Stock Name') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Unit Price') }}</span></th>
                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Unit') }}</span></th>

                                        <th class="nk-tb-col"><span class="sub-text">{{ __('Ton Price') }}</span></th>

                                    </tr>
                                    </thead>
                                </table>
                            </div>
                        </div><!-- .card-preview -->
                    </div> <!-- nk-block -->
                </div><!-- .components-preview -->
            </div>
        </div>
    </div>

@endsection

@section('js')

    <script type="text/javascript">
        $(document).ready(function () {
            var dom = '<"row justify-between g-2 has_export"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2" l>>>><"my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';
            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ action('App\Http\Controllers\Frontend\PriceController@index') }}',
                    data: function (d) {
                        d.contract_id = $('select#contract_id').val();
                        d.status = $('select#status').val();
                        d.start_date = $('input#start_date').val();
                        d.end_date = $('input#end_date').val();
                    },
                },

                dom: dom,
                createdRow: (row, data, dataIndex, cells) => {
                    $(row).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },
                aaSorting: [[1, 'asc']],
                columns: [
                    {data: 'id', name: 'id', orderable: false, searchable: false},
                    {data: 'item_code', name: 'item_code', orderable: true, searchable: true},
                    {data: 'product_name', name: 'product_name', orderable: false, searchable: false},
                    {data: 'unit_price', name: 'unit_price', orderable: false, searchable: true},
                    {data: 'unit_name', name: 'unit_name', orderable: true, searchable: false},
                    {data: 'zz_ton_price', name: 'zz_ton_price', orderable: false, searchable: false},
                ], createdRow: (row, data, dataIndex, cells) => {
                    $(row).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                }
            });
            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});
            $('select#contract_id').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('select#status').on(
                'change',
                function () {
                    data_table.ajax.reload(null, false);
                }
            );

            $('input#start_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );
            $('input#end_date').on('change', function () {
                    data_table.ajax.reload(null, false);
                }
            );

        });



    </script>

@endsection
