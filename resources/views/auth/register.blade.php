@extends('frontend.layouts.auth')

@section('title')  @lang('Register') - {{app_name()}} @endsection

@section('content')

    <div class="nk-split-content nk-block-area nk-block-area-column nk-auth-container bg-white">
        <div class="absolute-top-right d-lg-none p-3 p-sm-5">
            <a href="#" class="toggle btn-white btn btn-icon btn-light" data-target="athPromo"><em class="icon ni ni-info"></em></a>
        </div>
        <div class="nk-block nk-block-middle nk-auth-body">
            <div class="brand-logo pb-5">
                <a href="/" class="logo-link">
                    <img class="logo-light logo-img logo-img-lg" src="/images/logo.png" srcset="/images/logo2x.png 2x" alt="logo">
                    <img class="logo-dark logo-img logo-img-lg" src="/images/logo-dark.png" srcset="/images/logo-dark2x.png 2x" alt="logo-dark">
                </a>
            </div>
            <div class="nk-block-head">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ __('Register') }}</h5>
                    <div class="nk-block-des">
                        <p>{{ __('Create New Portal Account') }}</p>
                    </div>
                </div>
            </div><!-- .nk-block-head -->

            @if(!$errors->isEmpty())
                <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                    @foreach($errors->all() as $error)
                        <strong>{{ __('Error') }}</strong> {{ $error }}
                    @endforeach
                </div>
            @endif

            <form action="{{ route('register') }}" method="post" class="form-validate is-alter">
                @csrf

                <div class="row mb-3">
                    <div class="col-md-6">
                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="first_name">{{ __('First Name') }}</label>
                    </div>
                    <div class="form-control-wrap">
                        <input name="first_name" type="text" class="form-control form-control-lg" required value="{{ old('first_name') }}" id="first_name" placeholder="{{ __('First Name') }}">
                    </div>
                </div>
                    </div>
                    <div class="col-md-6">
                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="last_name">{{ __('Last Name') }}</label>
                    </div>
                    <div class="form-control-wrap">
                        <input name="last_name" type="text" class="form-control form-control-lg" required value="{{ old('last_name') }}" id="last_name" placeholder="{{ __('Last Name') }}">
                    </div>
                </div>
                </div>
                </div>

                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="email">{{ __('Email') }}</label>
                    </div>
                    <div class="form-control-wrap">
                        <input name="email" type="email" class="form-control form-control-lg" required value="{{ old('email') }}" id="email" placeholder="{{ __('Email') }}">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="password">{{ __('Password') }}</label>
                    </div>
                    <div class="form-control-wrap">
                        <input name="password" type="password" class="form-control form-control-lg" required value="{{ old('password') }}" id="password" placeholder="{{ __('Password') }}">
                    </div>
                </div>

                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="password_confirmation">{{ __('Confirm Password') }}</label>
                    </div>
                    <div class="form-control-wrap">
                        <input name="password_confirmation" type="password" class="form-control form-control-lg" required value="{{ old('password_confirmation') }}" id="password_confirmation" placeholder="{{ __('Confirm Password') }}">
                    </div>
                </div>

                <div class="form-group"><div class="custom-control custom-control-xs custom-checkbox"><input type="checkbox" class="custom-control-input" id="checkbox"><label class="custom-control-label" for="checkbox">Site <a tabindex="-1" href="/kullanim-kosullari">Kullanım Koşullarını</a> kabul ediyorum.</a></label></div></div>

                <div class="form-group">
                    <button type="submit" class="btn btn-lg btn-primary btn-block">{{ __('Register') }}</button>
                </div>
            </form><!-- form -->
            <div class="form-note-s2 pt-4"> {{ __('Already have an account?') }} <a href="/login"><strong>{{ __('Sign in') }}</strong></a></div>
            <div class="text-center pt-4 pb-3"><h6 class="overline-title overline-title-sap"><span>{{ __('OR') }}</span></h6></div>
            <ul class="nav justify-center gx-8"><li class="nav-item"><a class="link link-primary fw-normal py-2 px-3" href="#">Facebook</a></li><li class="nav-item"><a class="link link-primary fw-normal py-2 px-3" href="#">Google</a></li></ul>


                </div><!-- .nk-block -->
                @include('auth.footer')
        </div><!-- .nk-split-content -->

        @endsection


