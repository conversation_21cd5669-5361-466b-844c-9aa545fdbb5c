@extends('frontend.layouts.auth')

@section('title')
    @lang('Login') - {{app_name()}}
@endsection

@section('content')

    <div class="page-single">
        <div class="container-fluid">
            <div class="row justify-content-center p-5">
                <div class="col-sm-12">
                    <div class="card authentication-card border border-warning border-2 rounded-5 bg-transparent py-5 float-end" style="min-width: 300px">
                        <div class="p-5 text-center">
                            <img class="mb-2 text-white" src="/images/logo-minerra-white.png">
                            <p class="fs-13 mb-1 text-white"></p>

                        </div>
                        <div class="card-body border-top-0 p-4">


                            @if(!$errors->isEmpty())
                                <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                                    @foreach($errors->all() as $error)
                                        <strong>{{ __('Error') }}</strong> {{ $error }}
                                    @endforeach
                                </div>
                            @endif

                            <form action="{{ route('login') }}" method="post" class="form-validate is-alter">
                                <div class="form-group">

                                    <div class="form-control-wrap">
                                        <input name="email" type="text" class="form-control form-control-lg" required id="email-address"
                                               placeholder="{{ __('Enter your email address or username') }}">
                                    </div>
                                </div><!-- .form-group -->

                                <div class="form-group">
                                    <div class="form-label-group">
                                        <a class="link link-primary link-sm" tabindex="-1"
                                           href="{{ route('password.request') }}">   {{ __('Forgot your password?') }}</a>
                                    </div>
                                    <div class="form-control-wrap">
                                        <a tabindex="-1" href="#" class="form-icon form-icon-right passcode-switch lg"
                                           data-target="password">
                                            <em class="passcode-icon icon-show icon ni ni-eye"></em>
                                            <em class="passcode-icon icon-hide icon ni ni-eye-off"></em>
                                        </a>
                                        <input type="password" name="password" class="form-control form-control-lg" required
                                               id="password" placeholder="{{ __('Enter your passcode') }}">
                                    </div>
                                </div><!-- .form-group -->

                                <div class="form-group">

                                    <div class="form-control-wrap">
                                        <div class="custom-control custom-checkbox checked"><input type="checkbox" class="custom-control-input" checked="" id="customCheck2"><label class="custom-control-label text-white" for="customCheck2">Bu bilgisayarda beni hatırla</label></div>
                                    </div>
                                </div><!-- .form-group -->

                                <div class="form-group">
                                    <button type="submit" class="btn btn-primary border-warning border-2 btn-block">{{ __('Sign in') }}</button>
                                </div>
                                @csrf
                            </form><!-- form -->

                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
