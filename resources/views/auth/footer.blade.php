<div class="nk-block nk-auth-footer">
    <div class="nk-block-between">
        <ul class="nav nav-sm">
            <li class="nav-item">
                <a class="link link-primary fw-normal py-2 px-3" href="/kullanim-kosullari">{{ __('Terms & Condition') }}</a>
            </li>

            <li class="nav-item">
                <a class="link link-primary fw-normal py-2 px-3" href="/yardim">{{ __('Help') }}</a>
            </li>
            <li class="nav-item dropup">
                <a class="dropdown-toggle dropdown-indicator has-indicator link link-primary fw-normal py-2 px-3" data-bs-toggle="dropdown" data-offset="0,10"><small>@if ( Config::get('app.locale') == 'en')
                            {{ 'English' }}
                        @elseif ( Config::get('app.locale') == 'tr' )
                            {{ 'Türkçe' }}
                        @endif</small></a>
                <div class="dropdown-menu dropdown-menu-sm dropdown-menu-end">
                    <ul class="language-list">
                        @foreach(config('app.available_locales') as $locale_code => $locale_name) <li>
                            <a href="{{route('language.switch', $locale_code)}}" class="language-item">
                                <img src="/images/flags/{{ $locale_code }}.png" alt="" class="language-flag">
                                <span class="language-name">{{ $locale_name }}</span>
                            </a>
                        </li>
                        @endforeach
                    </ul>
                </div>
            </li>
        </ul><!-- .nav -->
    </div>
    <div class="mt-3">
        <p>&copy; {{ date('Y') }} Akdağ Yalıtım A.Ş. {{ __('All rights reserved.') }}</p>
    </div>
</div><!-- .nk-block -->