@extends('frontend.layouts.auth')

@section('title')  @lang('Login') - {{app_name()}} @endsection

@section('content')

    <div class="nk-split-content nk-block-area nk-block-area-column nk-auth-container bg-white">

        <div class="nk-block nk-block-middle nk-auth-body">
            <div class="brand-logo pb-5">
                <a href="/" class="logo-link">
                    <img class="logo-light logo-img logo-img-lg" src="/images/logo.png" srcset="/images/logo2x.png 2x" alt="logo">
                    <img class="logo-dark logo-img logo-img-lg" src="/images/logo-dark.png" srcset="/images/logo-dark2x.png 2x" alt="logo-dark">
                </a>
            </div>

            <div class="nk-block-head">
                <div class="nk-block-head-content">
                    <h5 class="nk-block-title">{{ __('Reset password') }}</h5>
                    <div class="nk-block-des">
                        <p>            {{ __('Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one.') }}
                        </p>
                    </div>
                </div>
            </div><!-- .nk-block-head -->
            @if(!$errors->isEmpty())
                <div class="alert alert-danger alert-icon"><em class="icon ni ni-cross-circle"></em>
                    @foreach($errors->all() as $error)
                        <strong>{{ __('Error') }}</strong> {{ $error }}
                    @endforeach
                </div>
            @endif
            <x-auth-session-status class="mb-4" :status="session('status')" />

            <form method="POST" action="{{ route('password.email') }}">
                <div class="form-group">
                    <div class="form-label-group">
                        <label class="form-label text-primary" for="default-01">{{ __('Email') }}</label>
                        <a class="link link-primary link-sm" href="/yardim">{{ __('Need Help?') }}</a>
                    </div>
                    <div class="form-control-wrap">
                        <input type="email" class="form-control form-control-lg" name="email" id="default-01" placeholder="{{ __('Enter your email address') }}">
                    </div>
                </div>
                <div class="form-group">
                    <button class="btn btn-lg btn-primary btn-block"> {{ __('Email Password Reset Link') }}</button>
                </div>
                @csrf
            </form><!-- form -->
            <div class="form-note-s2 pt-5">
                <a href="/login"><strong>{{ __('Return to login') }}</strong></a>
            </div>
        </div>
        @include('auth.footer')
    </div>



        @endsection

