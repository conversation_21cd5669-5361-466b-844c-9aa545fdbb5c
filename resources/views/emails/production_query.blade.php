<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title>Si<PERSON><PERSON>ş İptal Talebi</title>
    <style>
        body, table td {
            font-size: 14px
        }

        .body, body {
            background-color: #f6f6f6
        }

        .container, .content {
            display: block;
            max-width: 580px;
            padding: 10px
        }

        body, h1, h2, h3, h4 {
            line-height: 1.4;
            font-family: sans-serif
        }

        body, h1, h2, h3, h4, ol, p, table td, ul {
            font-family: sans-serif
        }

        .btn a, .btn table td {
            background-color: #fff
        }

        .btn, .btn a, .content, .wrapper {
            box-sizing: border-box
        }

        .btn a, h1 {
            text-transform: capitalize
        }

        .align-center, .btn table td, .footer, h1 {
            text-align: center
        }

        .clear, .footer {
            clear: both
        }

        .btn a, .powered-by a {
            text-decoration: none
        }

        img {
            border: none;
            -ms-interpolation-mode: bicubic;
            max-width: 100%
        }

        body {
            -webkit-font-smoothing: antialiased;
            margin: 0;
            padding: 0;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%
        }

        table {
            border-collapse: separate;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            width: 100%
        }

        table td {
            vertical-align: top
        }

        .body {
            width: 100%
        }

        .container {
            margin: 0 auto !important;
            width: 580px
        }

        .btn, .footer, .main {
            width: 100%
        }

        .content {
            margin: 0 auto
        }

        .main {
            background: #fff;
            border-radius: 3px
        }

        .wrapper {
            padding: 20px
        }

        .content-block {
            padding-bottom: 10px;
            padding-top: 10px
        }

        .footer {
            margin-top: 10px
        }

        .footer a, .footer p, .footer span, .footer td {
            color: #999;
            font-size: 12px;
            text-align: center
        }

        h1, h2, h3, h4 {
            color: #000;
            font-weight: 400;
            margin: 0 0 30px
        }

        .btn a, a {
            color: #3498db
        }

        h1 {
            font-size: 35px;
            font-weight: 300
        }

        .btn a, ol, p, ul {
            font-size: 14px
        }

        ol, p, ul {
            font-weight: 400;
            margin: 0 0 15px
        }

        ol li, p li, ul li {
            list-style-position: inside;
            margin-left: 5px
        }

        a {
            text-decoration: underline
        }

        .btn > tbody > tr > td {
            padding-bottom: 15px
        }

        .btn table {
            width: auto
        }

        .btn table td {
            border-radius: 5px
        }

        .btn a {
            border: 1px solid #3498db;
            border-radius: 5px;
            cursor: pointer;
            display: inline-block;
            font-weight: 700;
            margin: 0;
            padding: 12px 25px
        }

        .btn-primary a, .btn-primary table td {
            background-color: #3498db
        }

        .btn-primary a {
            border-color: #3498db;
            color: #fff
        }

        .last, .mb0 {
            margin-bottom: 0
        }

        .first, .mt0 {
            margin-top: 0
        }

        .align-right {
            text-align: right
        }

        .align-left {
            text-align: left
        }

        .preheader {
            color: transparent;
            display: none;
            height: 0;
            max-height: 0;
            max-width: 0;
            opacity: 0;
            overflow: hidden;
            mso-hide: all;
            visibility: hidden;
            width: 0
        }

        hr {
            border: 0;
            border-bottom: 1px solid #f6f6f6;
            Margin: 20px 0
        }

        @media only screen and (max-width: 620px) {
            table[class=body] h1 {
                font-size: 28px !important;
                margin-bottom: 10px !important
            }

            table[class=body] a, table[class=body] ol, table[class=body] p, table[class=body] span, table[class=body] td, table[class=body] ul {
                font-size: 16px !important
            }

            table[class=body] .article, table[class=body] .wrapper {
                padding: 10px !important
            }

            table[class=body] .content {
                padding: 0 !important
            }

            table[class=body] .container {
                padding: 0 !important;
                width: 100% !important
            }

            table[class=body] .main {
                border-left-width: 0 !important;
                border-radius: 0 !important;
                border-right-width: 0 !important
            }

            table[class=body] .btn a, table[class=body] .btn table {
                width: 100% !important
            }

            table[class=body] .img-responsive {
                height: auto !important;
                max-width: 100% !important;
                width: auto !important
            }
        }

        @media all {
            .btn-primary a:hover, .btn-primary table td:hover {
                background-color: #34495e !important
            }

            .ExternalClass {
                width: 100%
            }

            .ExternalClass, .ExternalClass div, .ExternalClass font, .ExternalClass p, .ExternalClass span, .ExternalClass td {
                line-height: 100%
            }

            .apple-link a {
                color: inherit !important;
                font-family: inherit !important;
                font-size: inherit !important;
                font-weight: inherit !important;
                line-height: inherit !important;
                text-decoration: none !important
            }

            .btn-primary a:hover {
                border-color: #34495e !important
            }
        }

    </style>
</head>
<body class="">
<table border="0" cellpadding="0" cellspacing="0" class="body">
    <tr>
        <td>&nbsp;</td>
        <td class="container">
            <div class="content">
                <div style="text-align: center; height: 65px">
                    <img src="data:image/png;base64,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" alt="Minerra Logo" width="150" /></div>
                <!-- START CENTERED WHITE CONTAINER -->
                <span class="preheader"></span>
                <table class="main">

                    <!-- START MAIN CONTENT AREA -->
                    <tr>
                        <td class="wrapper">

                            <table border="0" cellpadding="0" cellspacing="0" style="padding-top:50px ">
                                <tr>
                                    <td>
                                        <p>Merhaba,</p>

                                        <p>{{ $user->name }} tarafından üretim sorgusu oluşturuldu.</p>

                                     <b>Yoğunluk:</b> {{ $input['select_density'] }}<br>
                                     <b>Kalınlık:</b> {{ $input['select_depth'] }}<br>
                                     <b>En x Boy:</b> {{ $input['select_wh'] }}<br>

                                        <br>
                                        <em>Sorgu sahibi email adresi aşağıdaki şekildedir: {{ $user->email }}</em>
                                        <br>
                                        <p>İyi çalışmalar dileriz.</p>

                                        <p>Akdağ Taşyünü B2B Portal
                                        </p>
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>

                    <!-- END MAIN CONTENT AREA -->
                </table>

                <!-- START FOOTER -->
                <div class="footer">

                </div>
                <!-- END FOOTER -->

                <!-- END CENTERED WHITE CONTAINER -->
            </div>
        </td>
        <td>&nbsp;</td>
    </tr>
</table>


</body>
</html>
