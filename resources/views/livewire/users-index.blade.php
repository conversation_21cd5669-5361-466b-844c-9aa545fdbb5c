<div>
    <div class="row mt-4">
        <div class="col">
            <input type="text" class="form-control my-2" placeholder=" Search" wire:model.live="searchTerm" />

            <div class="table-responsive">
                <table class="table table-hover table-responsive-sm" wire:loading.class="table-secondary">
                    <thead>
                        <tr>
                            <th>Firma</th>
                            <th>{{ __('labels.backend.users.fields.name') }}</th>
                            <th>{{ __('labels.backend.users.fields.email') }}</th>
                            <th>{{ __('labels.backend.users.fields.status') }}</th>
                            <th>{{ __('labels.backend.users.fields.roles') }}</th>
                            <th>{{ __('labels.backend.users.fields.permissions') }}</th>
                            <th>{{ __('labels.backend.users.fields.social') }}</th>

                            <th class="text-end">{{ __('labels.backend.action') }}</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($users as $user)
                        <tr>
                            <td>
                                <ul class="list-unstyled">

                                    @foreach ($user->entities as $entity)
                                        <li>
                                           {{ limit_words($entity->entity_name,3) }}
                                        </li>
                                    @endforeach
                                </ul>
                            </td>
                            <td>
                                <strong>
                                    <a href="{{route('backend.users.show', $user->id)}}">
                                        {{ $user->name }}
                                    </a>
                                </strong>
                            </td>

                            <td>{{ $user->email }}</td>
                            <td>
                                {!! $user->status_label !!}
                                {!! $user->confirmed_label !!}
                            </td>
                            <td>
                                @if($user->roles()->count() > 0)
                                <ul class="fa-ul">
                                    @foreach ($user->roles()->get() as $role)
                                    <li><span class="fa-li"><i class="fas fa-check-square"></i></span> {{ ucwords($role->name) }}</li>
                                    @endforeach
                                </ul>
                                @else
                                Bayi
                                @endif
                            </td>
                            <td>
                                @if($user->getAllPermissions()->count() > 0)
                                <ul>
                                    @foreach ($user->getDirectPermissions() as $permission)
                                    <li>{{ $permission->name }}</li>
                                    @endforeach
                                </ul>
                                @endif
                            </td>
                            <td>
                                <ul class="list-unstyled">
                                    @foreach ($user->providers as $provider)
                                    <li>
                                        <i class="fab fa-{{ $provider->provider }}"></i> {{ label_case($provider->provider) }}
                                    </li>
                                    @endforeach
                                </ul>
                            </td>

                            <td class="text-end">
                                <a href="{{route('backend.users.show', $user)}}" class="btn btn-success btn-sm mt-1" data-toggle="tooltip" title="{{__('labels.backend.show')}}"><i class="ni ni-eye"></i></a>
                                @can('edit_users')
                                <a href="{{route('backend.users.entities', $user)}}" class="btn btn-secondary btn-sm mt-1" data-toggle="tooltip" title="Yetkilisi olduğu firmalar"><i class="ni ni-security"></i></a>
                                    <a href="{{route('backend.users.edit', $user)}}" class="btn btn-primary btn-sm mt-1" data-toggle="tooltip" title="{{__('labels.backend.edit')}}"><i class="ni ni-pen-alt-fill"></i></a>
                                <a href="{{route('backend.users.changePassword', $user)}}" class="btn btn-info btn-sm mt-1" data-toggle="tooltip" title="{{__('labels.backend.changePassword')}}"><i class="ni ni-security"></i></a>
                                @if ($user->status != 2)
                                <a href="{{route('backend.users.block', $user)}}" class="btn btn-danger btn-sm mt-1" data-method="PATCH" data-token="{{csrf_token()}}" data-toggle="tooltip" title="{{__('labels.backend.block')}}" data-confirm="Emin misiniz?"><i class="ni ni-na"></i></a>
                                @endif
                                @if ($user->status == 2)
                                <a href="{{route('backend.users.unblock', $user)}}" class="btn btn-info btn-sm mt-1" data-method="PATCH" data-token="{{csrf_token()}}" data-toggle="tooltip" title="{{__('labels.backend.unblock')}}" data-confirm="Emin misiniz?"><i class="fas fa-check"></i></a>
                                @endif
                                <a href="{{route('backend.users.destroy', $user)}}" class="btn btn-danger btn-sm mt-1" data-method="DELETE" data-token="{{csrf_token()}}" data-toggle="tooltip" title="{{__('labels.backend.delete')}}" data-confirm="Emin misiniz?"><i class="ni ni-trash"></i></a>
                                @if ($user->email_verified_at == null)
                                <a href="{{route('backend.users.emailConfirmationResend', $user->id)}}" class="btn btn-primary btn-sm mt-1" data-toggle="tooltip" title="Onay Maili Gönder"><i class="ni ni-mail"></i></a>
                                @endif
                                @endcan
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-7">
            <div class="float-left">
                {!! $users->total() !!} {{ __('labels.backend.total') }}
            </div>
        </div>
        <div class="col-5">
            <div class="float-end">
                {!! $users->links() !!}
            </div>
        </div>
    </div>
</div>