@extends('frontend.layouts.app')

@section('title')
    Yönlendiriliyor
@endsection
@section('content')

    <div class="nk-block">
        <div class="card card-bordered">
            <div class="card-inner card-inner-lg">
                <div class="nk-kyc-app p-sm-2 text-center">
                    <form method="{{ $formData['method'] }}" action="{{ $formData['gateway'] }}"  class="redirect-form" role="form">
                        @foreach($formData['inputs'] as $key => $value)
                            <input type="hidden" name="{{ $key }}" value="{{ $value }}">
                        @endforeach
                        <div class="nk-kyc-app-icon">
                            <div class="spinner-border text-primary" role="status"><span class="visually-hidden">{{ __('Loading...') }}</span></div>
                        </div>
                        <div class="nk-kyc-app-text mx-auto">
                            <div class="text-center">{{ __('Redirecting...') }}</div>
                        </div>
                        <div class="nk-kyc-app-action">
                            <button type="submit" class="btn btn-lg btn-block btn-success">{{ __('Submit') }}</button>
                        </div>


                    </form>


                </div>
            </div>
        </div>

    </div><!-- nk-block -->



<script>
    // Formu JS ile otomatik submit ederek kullaniciyi banka gatewayine yonlendiriyoruz.
    let redirectForm = document.querySelector('form.redirect-form');
    if (redirectForm) {
        redirectForm.submit();
    }
</script>
@endsection