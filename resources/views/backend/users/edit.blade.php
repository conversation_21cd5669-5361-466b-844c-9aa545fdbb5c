@extends('backend.layouts.app')

@section('title') {{ $user->name }} - <PERSON><PERSON><PERSON>le @endsection

@section('content')
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Kullanıcı Düzenle</h3>
                <div class="nk-block-des text-soft">
                    <p>{{ $user->name }} kullanıcısının bilgilerini düzenleyin.</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li><a href="{{ route('backend.users.index') }}" class="btn btn-outline-light bg-white d-none d-sm-inline-flex"><em class="icon ni ni-arrow-left"></em><span>Geri Dön</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-bordered">
            <div class="card-inner">
                <form action="{{ route('backend.users.update', $user->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <div class="row g-4">
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="first_name">Ad <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <input type="text" class="form-control @error('first_name') is-invalid @enderror" id="first_name" name="first_name" value="{{ old('first_name', $user->first_name) }}" required>
                                    @error('first_name')
                                    <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="last_name">Soyad <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <input type="text" class="form-control @error('last_name') is-invalid @enderror" id="last_name" name="last_name" value="{{ old('last_name', $user->last_name) }}" required>
                                    @error('last_name')
                                    <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="email">E-posta <span class="text-danger">*</span></label>
                                <div class="form-control-wrap">
                                    <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                    @error('email')
                                    <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="mobile">Telefon</label>
                                <div class="form-control-wrap">
                                    <input type="text" class="form-control @error('mobile') is-invalid @enderror" id="mobile" name="mobile" value="{{ old('mobile', $user->mobile) }}">
                                    @error('mobile')
                                    <span class="invalid-feedback" role="alert">
                                    <strong>{{ $message }}</strong>
                                </span>
                                    @enderror
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="status">Durum</label>
                                <div class="form-control-wrap">
                                    <select class="form-select" id="status" name="status">
                                        <option value="1" {{ old('status', $user->status) == '1' ? 'selected' : '' }}>Aktif</option>
                                        <option value="0" {{ old('status', $user->status) == '0' ? 'selected' : '' }}>Pasif</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="col-lg-6">
                            <div class="form-group">
                                <label class="form-label" for="avatar">Profil Resmi</label>
                                @if($user->avatar && $user->avatar != 'img/default-avatar.jpg')
                                    <div class="form-control-wrap mb-3">
                                        <img src="{{ asset($user->avatar) }}" alt="{{ $user->name }}" class="user-avatar xl">
                                    </div>
                                @endif
                                <div class="form-control-wrap">
                                    <div class="form-file">
                                        <input type="file" class="form-file-input @error('avatar') is-invalid @enderror" id="avatar" name="avatar">
                                        <label class="form-file-label" for="avatar">Dosya Seç</label>
                                        @error('avatar')
                                        <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <label class="form-label">Rol</label>
                                <div class="form-control-wrap">
                                    <div class="row g-3">
                                        <select name="role" id="role" class="form-control @error('role') is-invalid @enderror" required>
                                            <option value="">Rol Seçin</option>
                                            @foreach($roles as $role)
                                                <option value="{{ $role->id }}" @if(isset($userRole) && $userRole == $role->id) selected @endif>
                                                    {{ $role->name }}
                                                </option>
                                            @endforeach
                                        </select>
                                        @error('role')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-12">
                            <div class="form-group">
                                <div class="mb-3" id="entities-container">
                                    <label for="entities" class="form-label">Firmalar</label>
                                    <select name="entities[]" id="entities" class="form-control select2 @error('entities') is-invalid @enderror" multiple>
                                        @foreach($user->entities as $entity)
                                            <option value="{{ $entity->id }}" selected>{{ $entity->entity_name }}</option>
                                        @endforeach
                                    </select>
                                    @error('entities')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Not: "Bayi" rolü için firma seçimi zorunludur.</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">Güncelle</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@push('after-scripts')
    @push('after-scripts')
        <script>
            $(document).ready(function() {
                // Initialize Select2 for entities dropdown
                $('#entities').select2({
                    ajax: {
                        url: "{{ route('backend.api.entities') }}",
                        dataType: 'json',
                        delay: 250,
                        data: function(params) {
                            return {
                                q: params.term, // search term
                                page: params.page
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;

                            return {
                                results: data.items,
                                pagination: {
                                    more: (params.page * 30) < data.total_count
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Firma seçin',
                    minimumInputLength: 0,
                    templateResult: formatCompany,
                    templateSelection: formatCompanySelection
                });

                // Format company item in dropdown
                function formatCompany(company) {
                    if (company.loading) {
                        return company.text;
                    }

                    // If company is already selected or is an option from preloaded data
                    if (typeof company.entity_name === 'undefined') {
                        return company.text;
                    }

                    var $container = $(
                        "<div class='select2-result clearfix'>" +
                        "<div class='select2-result__title'>" + company.entity_name + "</div>" +
                        "<div class='select2-result__meta'>" + company.entity_code + "</div>" +
                        "</div>"
                    );

                    return $container;
                }

                // Format selected company in dropdown
                function formatCompanySelection(company) {
                    return company.entity_name || company.text;
                }

                // Password strength meter
                $('#password').on('keyup', function() {
                    var password = $(this).val();
                    var strength = 0;

                    if (password.length > 7) strength += 1;
                    if (password.match(/[a-z]+/)) strength += 1;
                    if (password.match(/[A-Z]+/)) strength += 1;
                    if (password.match(/[0-9]+/)) strength += 1;
                    if (password.match(/[$@#&!]+/)) strength += 1;

                    var strengthBar = $('.password-strength');

                    switch (strength) {
                        case 0:
                            strengthBar.css('width', '0%').removeClass().addClass('password-strength bg-danger');
                            break;
                        case 1:
                            strengthBar.css('width', '20%').removeClass().addClass('password-strength bg-danger');
                            break;
                        case 2:
                            strengthBar.css('width', '40%').removeClass().addClass('password-strength bg-warning');
                            break;
                        case 3:
                            strengthBar.css('width', '60%').removeClass().addClass('password-strength bg-warning');
                            break;
                        case 4:
                            strengthBar.css('width', '80%').removeClass().addClass('password-strength bg-success');
                            break;
                        case 5:
                            strengthBar.css('width', '100%').removeClass().addClass('password-strength bg-success');
                            break;
                    }
                });

                // Avatar preview
                $('#avatar').on('change', function() {
                    var file = this.files[0];
                    if (file) {
                        var reader = new FileReader();
                        reader.onload = function(e) {
                            $('.avatar-preview').attr('src', e.target.result);
                        }
                        reader.readAsDataURL(file);
                    }
                });

                // Password confirmation validation
                $('#password_confirmation').on('keyup', function() {
                    var password = $('#password').val();
                    var passwordConfirm = $(this).val();

                    if (password !== passwordConfirm) {
                        $(this).addClass('is-invalid');
                        $('.password-match').html('<span class="text-danger">Şifreler eşleşmiyor!</span>');
                    } else {
                        $(this).removeClass('is-invalid');
                        $('.password-match').html('<span class="text-success">Şifreler eşleşiyor.</span>');
                    }
                });

                // Form validation
                $('#user-form').on('submit', function(e) {
                    var password = $('#password').val();
                    var passwordConfirm = $('#password_confirmation').val();

                    if (password !== passwordConfirm) {
                        e.preventDefault();
                        showNotification('Şifreler eşleşmiyor!', 'error');
                        return false;
                    }

                    return true;
                });

                // Success and error messages
                function showNotification(message, type) {
                    NioApp.Toast(message, type, {
                        position: 'top-right'
                    });
                }
            });

            document.addEventListener('DOMContentLoaded', function() {
                const roleSelect = document.getElementById('role');
                const entitiesContainer = document.getElementById('entities-container');
                const entitiesSelect = document.getElementById('entities');
                const entitiesLabel = document.querySelector('label[for="entities"]');

                // Initial check on page load
                toggleEntitiesRequirement();

                // Add event listener for role change
                roleSelect.addEventListener('change', toggleEntitiesRequirement);

                function toggleEntitiesRequirement() {
                    // Get the selected role text
                    const selectedOption = roleSelect.options[roleSelect.selectedIndex];
                    const roleName = selectedOption ? selectedOption.text.toLowerCase() : '';

                    if (roleName === 'bayi') {
                        // Show entities selection and make it required
                        entitiesContainer.style.display = 'block';
                        entitiesSelect.setAttribute('required', 'required');

                        // Add asterisk to label if not already present
                        if (!entitiesLabel.innerHTML.includes('*')) {
                            entitiesLabel.innerHTML += ' <span class="text-danger">*</span>';
                        }

                        // Update the validation message
                        entitiesSelect.setAttribute('data-msg-required', 'Bayi rolü için en az bir firma seçilmelidir.');
                    } else {
                        // For other roles, make it optional but still visible
                        entitiesContainer.style.display = 'block';
                        entitiesSelect.removeAttribute('required');

                        // Remove asterisk from label if present
                        entitiesLabel.innerHTML = entitiesLabel.innerHTML.replace(' <span class="text-danger">*</span>', '');
                    }
                }
            });

            $(document).ready(function() {
                // Initialize Select2 for entities dropdown
                $('#entities').select2({
                    ajax: {
                        url: "{{ route('backend.api.entities') }}",
                        dataType: 'json',
                        delay: 250,
                        data: function(params) {
                            return {
                                q: params.term, // search term
                                page: params.page
                            };
                        },
                        processResults: function(data, params) {
                            params.page = params.page || 1;

                            return {
                                results: data.items,
                                pagination: {
                                    more: (params.page * 30) < data.total_count
                                }
                            };
                        },
                        cache: true
                    },
                    placeholder: 'Firma seçin',
                    minimumInputLength: 0,
                    templateResult: formatCompany,
                    templateSelection: formatCompanySelection
                }).on('change', function() {
                    // Seçim yapıldığında liste yenilensin
                    $(this).trigger('select2:select');
                });

                // Format company item in dropdown
                function formatCompany(company) {
                    if (company.loading) {
                        return company.text;
                    }

                    // If company is already selected or is an option from preloaded data
                    if (typeof company.entity_name === 'undefined') {
                        return company.text;
                    }

                    var $container = $(
                        "<div class='select2-result clearfix'>" +
                        "<div class='select2-result__title'>" + company.entity_name + "</div>" +
                        "<div class='select2-result__meta'>" + company.entity_code + "</div>" +
                        "</div>"
                    );

                    return $container;
                }

                // Format selected company in dropdown
                function formatCompanySelection(company) {
                    return company.entity_name || company.text;
                }
            });
        </script>
    @endpush
@endpush