@extends('backend.layouts.app')

@section('title')
    Kullanıcının Yetkilisi Olduğu Cariler
@endsection

@section('content')
    <div class="card">
        <div class="card-body">
            <x-backend.section-header>
                <i class="icon ni ni-users"></i> <PERSON>llan<PERSON><PERSON><PERSON> <small class="text-muted">Car<PERSON>i</small>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>

            <div class="row mt-4 mb-4">
                <div class="col">

                    <div class="nk-block">
                        <div class="nk-tb-list is-separate mb-3">

                            <table class="nk-tb-list nk-tb-ulist" id="data_table" style="width:100%">
                                <thead>
                                <tr class="nk-tb-item nk-tb-head">
                                    <th class="nk-tb-col"><span class="sub-text">Cari</span></th>
                                    <th class="nk-tb-col"><span class="sub-text"><PERSON><PERSON></span></th>
                                    <th class="nk-tb-col"><span class="sub-text">Yetki Tarihi</span></th>
                                    <th class="nk-tb-col"><span class="sub-text">Durum</span></th>
                                    <th class="nk-tb-col"><span class="sub-text">İşlem</span></th>
                                </tr>
                                </thead>
                            </table>


                        </div><!-- .nk-tb-list -->

                    </div><!-- .nk-block -->


                    <hr>
                    <div class="card">
                        <div class="card-inner">
                            <form >
                                @csrf
                                <div class="rating-card-description mt 3">
                                    <h5 class="card-title">Cari ekle</h5>
                                    <div class="form-group">
                                        <div class="form-control-wrap">
                                            <select name="entities[]" class="form-select js-search-entity"
                                                    data-search="on" multiple></select>
                                        </div>
                                    </div>
                                    <input type="hidden" name="user_id" value="{{ $id }}">
                                    <div class="input-group-append">
                                        <button type="button" class="btn btn-outline-primary btn-dim btn-submit">Kaydet</button>
                                    </div>
                                </div>

                            </form>
                        </div>


                    </div>
                    <!--/.col-->
            </div>
            <!--/.row-->
        </div>

        <div class="card-footer">
            <x-backend.section-footer>
                @lang('Updated'): {{$user->updated_at->diffForHumans()}},
                @lang('Created at'): {{$user->created_at->isoFormat('LLLL')}}
            </x-backend.section-footer>
        </div>
    </div>
@endsection

@section('scripts')

    <script>
        $(document).ready( function(){
            var dom = '<"row justify-between g-2 has_export"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2" l>>>><"my-3"t><"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>';
            var data_table = $('#data_table').DataTable({
                processing: true,
                serverSide: true,
                responsive: false,
                ajax: {
                    url: '{{ url()->current() }}',
                    data: function (d) {
                        d.start_date = $('input#start_date').val();
                        d.end_date = $('input#end_date').val();
                    },
                },

                dom: dom,
                createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                },
                language: {
                    search: "",
                    searchPlaceholder: "Arama yap",
                    lengthMenu: "<span class='d-none d-sm-inline-block'>Göster</span><div class='form-control-select'> _MENU_ </div>",
                    info: "_START_ -_END_ toplam _TOTAL_",
                    infoEmpty: "0",
                    infoFiltered: "( Toplam _MAX_  )",
                    paginate: {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    }
                },
                aaSorting: [[2, 'desc']],
                columns: [
                    { data: 'entity_name', name: 'entity_name', orderable: true, searchable: true },
                    { data: 'pivot_created_by', name: 'pivot_created_by', orderable: false, searchable: false },
                    { data: 'pivot_created_at', name: 'pivot_created_at', orderable: false, searchable: false },
                    { data: 'pivot_status', name: 'pivot_status', orderable: true, searchable: true },
                    { data: 'action', name: 'action', orderable: false, searchable: false },
                ],createdRow: (row, data, dataIndex, cells) => {
                    $( row ).find('td').addClass('nk-tb-col');
                    $(row).addClass("nk-tb-item");
                }
            });
            $('body').tooltip({selector: '[data-bs-toggle="tooltip"]'});

            $('input#start_date').on('change', function () {data_table.ajax.reload(null, false);});
            $('input#end_date').on('change', function () {data_table.ajax.reload(null, false);});

            $(document).on('click', '.remove-entity', function(e) {
                e.preventDefault();
                var url = $(this).data('href');
                var warning = $(this).data('warning');

                Swal.fire({
                    title: warning,
                    showDenyButton: true,
                    showCancelButton: true,
                    confirmButtonText: 'Evet',
                    denyButtonText: 'Hayır',
                    customClass: {
                        actions: 'my-actions',
                        cancelButton: 'order-1 right-gap',
                        confirmButton: 'order-2',
                        denyButton: 'order-3',
                    },
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            method: 'POST',
                            url: url,
                            data: {
                                entity_id: $(this).data('id'),
                                user_id: $(this).data('user_id'),
                                status: $(this).data('status'),
                                _token: $('meta[name="csrf-token"]').attr('content')
                            },
                            dataType: 'json',
                            success: function(result) {
                                if (result.success) {
                                    Swal.fire(result.message, '', 'success')
                                    data_table.ajax.reload(null, false);
                                } else {
                                    Swal.fire(result.message, '', 'error')
                                    data_table.ajax.reload(null, false);
                                }
                            }
                        });
                    } else if (result.isDenied) {
                        Swal.fire('Değişiklik uygulanamadı', '', 'info')
                    }
                })

            });

            $(document).on('click', '.custom-control-input', function(e) {
                $.ajax({
                    method: 'POST',
                    url: '{{ route('entity_user_update_status') }}',
                    data: {
                        entity_id: $(this).data('id'),
                        user_id: $(this).data('user_id'),
                        status: $(this).data('status'),
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    dataType: 'json',
                    success: function(result) {
                        if (result.success) {
                            NioApp.Toast(result.message, 'info', {position: 'top-right'});
                            data_table.ajax.reload(null, false);
                        } else {
                            NioApp.Toast(result.message, 'error', {position: 'top-right'});
                        }
                    }
                });
            });

            $(document).on('click', '.btn-submit', function(e) {
                $.ajax({
                    method: 'POST',
                    url: '{{ route('entity_user_update_status') }}',
                    data: {
                        entities: $('select[name="entities[]"]').val(),
                        user_id: $('input[name="user_id"]').val(),
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    dataType: 'json',
                    success: function(result) {
                        if (result.success) {
                            NioApp.Toast(result.message, 'info', {position: 'top-right'});
                            data_table.ajax.reload(null, false);
                        } else {
                            NioApp.Toast(result.message, 'error', {position: 'top-right'});
                        }
                    }
                });
            });
        });

        $(".js-search-entity").select2({
            ajax: {
                url: "/entity-search",
                dataType: 'json',
                delay: 250,
                data: function (params) {
                    return {
                        q: params.term,
                        page: params.page
                    };
                },
                processResults: function (data, params) {
                    params.page = params.page || 1;

                    return {
                        results: data.items,
                        pagination: {
                            more: (params.page * 30) < data.total_count
                        }
                    };
                },
                cache: false
            },
            placeholder: 'Ara',
            minimumInputLength: 3,
        }).val({{ $user_entity_ids }}).trigger('change'); // ajax olduğu için değerler yüklendikten sonra seçili hale getiriyoruz


    </script>
@endsection