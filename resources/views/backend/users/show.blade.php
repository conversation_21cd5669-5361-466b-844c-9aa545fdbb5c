@extends('backend.layouts.app')

@section('title') {{ $user->name }} - Detay @endsection

@section('content')
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Kullanıcı Detayları</h3>
                <div class="nk-block-des text-soft">
                    <p>{{ $user->name }} kullanıcısının detaylı bilgileri.</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li><a href="{{ route('backend.users.index') }}" class="btn btn-outline-light bg-white d-none d-sm-inline-flex"><em class="icon ni ni-arrow-left"></em><span>Geri Dön</span></a></li>
                            <li><a href="{{ route('backend.users.edit', $user->id) }}" class="btn btn-primary"><em class="icon ni ni-edit"></em><span>Düzenle</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-bordered">
            <div class="card-aside-wrap">
                <div class="card-inner card-inner-lg">
                    <div class="nk-block-head nk-block-head-lg">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">Kişisel Bilgiler</h4>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="nk-data data-list">
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Ad Soyad</span>
                                    <span class="data-value">{{ $user->name }}</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">E-posta</span>
                                    <span class="data-value">{{ $user->email }}</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Telefon</span>
                                    <span class="data-value">{{ $user->mobile ?? 'Belirtilmemiş' }}</span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Durum</span>
                                    <span class="data-value">
                                    @if($user->status == 1)
                                            <span class="badge bg-success">Aktif</span>
                                        @else
                                            <span class="badge bg-danger">Pasif</span>
                                        @endif
                                </span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Son Giriş</span>
                                    <span class="data-value">
                                    @if($user->last_login)
                                            {{ Carbon\Carbon::parse($user->last_login)->format('d.m.Y H:i') }}
                                        @else
                                            Henüz giriş yapmadı
                                        @endif
                                </span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Kayıt Tarihi</span>
                                    <span class="data-value">{{ $user->created_at->format('d.m.Y H:i') }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block-head nk-block-head-lg mt-4">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">Roller ve İzinler</h4>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="nk-data data-list">
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Roller</span>
                                    <span class="data-value">
                                    @forelse($user->roles as $role)
                                            <span class="badge bg-primary">{{ $role->name }}</span>
                                        @empty
                                            <span class="text-muted">Rol atanmamış</span>
                                        @endforelse
                                </span>
                                </div>
                            </div>
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">İzinler</span>
                                    <span class="data-value">
                                    @php
                                        $permissions = $user->getAllPermissions();
                                    @endphp
                                        @forelse($permissions as $permission)
                                            <span class="badge bg-info">{{ $permission->name }}</span>
                                        @empty
                                            <span class="text-muted">İzin atanmamış</span>
                                        @endforelse
                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block-head nk-block-head-lg mt-4">
                        <div class="nk-block-between">
                            <div class="nk-block-head-content">
                                <h4 class="nk-block-title">Firmalar</h4>
                            </div>
                        </div>
                    </div>
                    <div class="nk-block">
                        <div class="nk-data data-list">
                            <div class="data-item">
                                <div class="data-col">
                                    <span class="data-label">Yetkili Olduğu Firmalar</span>
                                    <span class="data-value">
                                    @forelse($user->entities as $company)
                                            <span class="badge bg-success">{{ $company->entity_name }}</span>
                                        @empty
                                            <span class="text-muted">Firma atanmamış</span>
                                        @endforelse
                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-aside card-aside-left user-aside toggle-slide toggle-slide-left toggle-break-lg" data-toggle-body="true" data-content="userAside" data-toggle-screen="lg" data-toggle-overlay="true">
                    <div class="card-inner-group">
                        <div class="card-inner">
                            <div class="user-card">
                                <div class="user-avatar bg-primary">
                                    @if($user->avatar && $user->avatar != 'img/default-avatar.jpg')
                                        <img src="{{ asset($user->avatar) }}" alt="{{ $user->name }}">
                                    @else
                                        <span>{{ substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1) }}</span>
                                    @endif
                                </div>
                                <div class="user-info">
                                    <span class="lead-text">{{ $user->name }}</span>
                                    <span class="sub-text">{{ $user->email }}</span>
                                </div>
                            </div>
                        </div>
                        <div class="card-inner p-0">
                            <ul class="link-list-menu">
                                <li><a class="active" href="{{ route('backend.users.show', $user->id) }}"><em class="icon ni ni-user-fill"></em><span>Kişisel Bilgiler</span></a></li>
                                <li><a href="{{ route('backend.users.edit', $user->id) }}"><em class="icon ni ni-edit"></em><span>Düzenle</span></a></li>
                                <li><a href="{{ route('backend.users.password', $user->id) }}"><em class="icon ni ni-lock"></em><span>Şifre Belirle</span></a></li>
                                @if($user->status == 1)
                                    <li><a href="{{ route('backend.users.status', ['id' => $user->id, 'status' => 0]) }}"><em class="icon ni ni-na"></em><span>Blokla</span></a></li>
                                @else
                                    <li><a href="{{ route('backend.users.status', ['id' => $user->id, 'status' => 1]) }}"><em class="icon ni ni-check-circle"></em><span>Aktifleştir</span></a></li>
                                @endif
                                <li><a href="{{ route('backend.users.send-password', $user->id) }}"><em class="icon ni ni-mail"></em><span>Şifre Gönder</span></a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection