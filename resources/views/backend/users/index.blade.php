@extends('backend.layouts.app')

@section('title') Kullanıcı Yönetimi @endsection

@section('content')
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Kullanıcı Yönetimi</h3>
                <div class="nk-block-des text-soft">
                    <p><PERSON>stem<PERSON><PERSON> tüm kullanıcıları yönetin.</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li><a href="{{ route('backend.users.create') }}" class="btn btn-primary"><em class="icon ni ni-plus"></em><span>Yeni Kullanıcı</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-bordered card-stretch">
            <div class="card-inner-group">
                <div class="card-inner position-relative card-tools-toggle">
                    <div class="card-title-group">
                        <div class="card-tools">
                            <div class="form-inline flex-nowrap gx-3">
                                <div class="form-wrap w-150px">
                                    <select class="form-select js-select2" name="status" data-search="off" data-placeholder="Durumu">
                                        <option>Seçiniz</option>
                                        <option value="3" @if($status == '3') selected @endif>Tümü</option>
                                        <option value="1" @if($status == '1') selected @endif>Aktif</option>
                                        <option value="0" @if($status == '0') selected @endif>Pasif</option>
                                        <option value="2" @if($status == '2') selected @endif>Bloklanan</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-inner p-3">
                    <div class="nk-block-between-md g-3">
                        <div class=" table-responsive">
                    <table class="datatable-init nk-tb-list nk-tb-ulist" data-auto-responsive="false">
                        <thead>
                        <tr class="nk-tb-item nk-tb-head">
                            <th class="nk-tb-col"><span class="sub-text">Kullanıcı</span></th>
                            <th class="nk-tb-col tb-col-mb"><span class="sub-text">Firmalar</span></th>
                            <th class="nk-tb-col tb-col-md"><span class="sub-text">Telefon</span></th>
                            <th class="nk-tb-col tb-col-lg">İşlemler</th>
                            <th class="nk-tb-col tb-col-lg"><span class="sub-text">Rol</span></th>
                            <th class="nk-tb-col tb-col-md text-end"><span class="sub-text">Durum</span></th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($users as $user)
                            <tr class="nk-tb-item">
                                <td class="nk-tb-col">
                                    <div class="user-card">
                                        <div class="user-avatar bg-primary">
                                            @if($user->avatar)
                                                <img src="{{ asset($user->avatar) }}" alt="">
                                            @else
                                                <span>{{ substr($user->first_name, 0, 1) . substr($user->last_name, 0, 1) }}</span>
                                            @endif
                                        </div>
                                        <div class="user-info">
                                            <span class="tb-lead">{{ $user->name }} <span class="dot dot-success d-md-none ms-1"></span></span>
                                            <span>{{ $user->email }}</span>
                                        </div>
                                    </div>
                                </td>
                                <td class="nk-tb-col tb-col-mb">
                                    @foreach($user->entities as $entity)
                                        <span class="badge bg-primary">{{ $entity->entity_name }}</span>
                                    @endforeach
                                </td>
                                <td class="nk-tb-col tb-col-md">
                                    <span>{{ $user->mobile }}</span>
                                </td>


                                <td class="nk-tb-col tb-col-lg" style="width: 150px">
                                    <ul class="nk-tb-actions gx-1">
                                        <li>
                                            <div class="drodown">
                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <ul class="link-list-opt no-bdr">
                                                        <li><a href="{{ route('backend.users.edit', $user->id) }}"><em class="icon ni ni-edit"></em><span>Düzenle</span></a></li>
                                                        <li><a href="{{ route('backend.users.show', $user->id) }}"><em class="icon ni ni-eye"></em><span>Görüntüle</span></a></li>
                                                        <li><a href="{{ route('backend.users.password', $user->id) }}"><em class="icon ni ni-lock"></em><span>Şifre Belirle</span></a></li>
                                                        <li class="divider"></li>
                                                        @if($user->status == 1)
                                                            <li><a href="{{ route('backend.users.status', $user->id) }}" class="user-status-change" data-id="{{ $user->id }}" data-status="0"><em class="icon ni ni-na"></em><span>Blokla</span></a></li>
                                                        @else
                                                            <li><a href="{{ route('backend.users.status', $user->id) }}" class="user-status-change" data-id="{{ $user->id }}" data-status="1"><em class="icon ni ni-check-circle"></em><span>Aktifleştir</span></a></li>
                                                        @endif
                                                        <li><a href="{{ route('backend.users.send-password', $user->id) }}"><em class="icon ni ni-mail"></em><span>Şifre Gönder</span></a></li>
                                                        <li class="divider"></li>
                                                        <li>
                                                            <a href="#" class="text-danger" onclick="event.preventDefault(); if(confirm('Bu kullanıcıyı silmek istediğinize emin misiniz?')){ document.getElementById('delete-user-{{ $user->id }}').submit(); }">
                                                                <em class="icon ni ni-trash"></em><span>Sil</span>
                                                            </a>
                                                            <form id="delete-user-{{ $user->id }}" action="{{ route('backend.users.destroy', $user->id) }}" method="POST" style="display: none;">
                                                                @csrf
                                                                @method('DELETE')
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </td>
                                <td class="nk-tb-col tb-col-lg">
                                    @foreach($user->roles as $role)
                                        <span class="badge bg-info">{{ $role->name }}</span>
                                    @endforeach
                                </td>
                                <td class="nk-tb-col tb-col-md">
                                    @if($user->status == 1)
                                        <span class="badge bg-success">Aktif</span>
                                    @else
                                        <span class="badge bg-danger">Pasif</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('after-scripts')
    <script>
        // public/js/backend/users.js

        $(document).ready(function() {

                $('select[name="status"]').on('change', function() {
                    var selectedStatus = $(this).val();

                    // Reload the page with the selected status as a query parameter
                    window.location.href = '?status=' + selectedStatus;
                });



            // DataTables Initialization
            var userTable = $('.datatable-init').DataTable({
                responsive: true,
                "bDestroy": true, // Add this line here
                language: {
                    "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "sInfoEmpty": "Kayıt yok",
                    "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "sInfoPostFix": "",
                    "sInfoThousands": ".",
                    "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                    "sLoadingRecords": "Yükleniyor...",
                    "sProcessing": "İşleniyor...",
                    "sSearch": "Ara:",
                    "sZeroRecords": "Eşleşen kayıt bulunamadı",
                    "oPaginate": {
                        "sFirst": "İlk",
                        "sLast": "Son",
                        "sNext": "Sonraki",
                        "sPrevious": "Önceki"
                    },
                    "oAria": {
                        "sSortAscending": ": artan sütun sıralamasını aktifleştir",
                        "sSortDescending": ": azalan sütun sıralamasını aktifleştir"
                    },
                    "select": {
                        "rows": {
                            "_": "%d kayıt seçildi",
                            "0": "",
                            "1": "1 kayıt seçildi"
                        }
                    }
                },
                dom: '<"row justify-between g-2"<"col-7 col-sm-4 text-start"f><"col-5 col-sm-8 text-end"<"datatable-filter"<"d-flex justify-content-end g-2"lB>>>>t<"row align-items-center"<"col-7 col-sm-12 col-md-9"p><"col-5 col-sm-12 col-md-3 text-start text-md-end"i>>',
                buttons: [
                    { extend: 'copy', className: 'btn-sm' },
                    { extend: 'csv', className: 'btn-sm' },
                    { extend: 'excel', className: 'btn-sm' },
                    { extend: 'pdf', className: 'btn-sm' },
                    { extend: 'print', className: 'btn-sm' }
                ],
                lengthMenu: [10, 25, 50, 75, 100],
                pageLength: 25
            });

            // Status filter (dropdown)
            $('select[data-search="off"]').on('change', function() {
                var status = $(this).val();
                userTable.column(5).search(status).draw();
            });

            // General ajax setup
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                }
            });

            // Success and error messages
            function showNotification(message, type) {
                NioApp.Toast(message, type, {
                    position: 'top-right'
                });
            }

            // Handle status change
            $(document).on('click', '.user-status-change', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                var userId = $(this).data('id');
                var status = $(this).data('status');

                $.ajax({
                    url: url,
                    type: 'POST', // POST metodu kullanıyoruz
                    data: {
                        status: status,
                        _token: $('meta[name="csrf-token"]').attr('content') // CSRF token ekliyoruz
                    },
                    success: function(response) {
                        if (response.success) {
                            showNotification(response.message, 'success');
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            showNotification(response.message, 'error');
                        }
                    },
                    error: function(xhr) {
                        showNotification('İşlem sırasında bir hata oluştu!', 'error');
                    }
                });
            });

            // Send password mail
            $(document).on('click', '.send-password', function(e) {
                e.preventDefault();
                var url = $(this).attr('href');
                var userId = $(this).data('id');

                if (confirm('Kullanıcıya şifre sıfırlama e-postası göndermek istediğinize emin misiniz?')) {
                    $.ajax({
                        url: url,
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                showNotification(response.message, 'success');
                            } else {
                                showNotification(response.message, 'error');
                            }
                        },
                        error: function(xhr) {
                            showNotification('İşlem sırasında bir hata oluştu!', 'error');
                        }
                    });
                }
            });
        });


        // public/js/backend/user-form.js

        $(document).ready(function() {
            // Initialize Select2 for entities dropdown
            $('#entities').select2({
                ajax: {
                    url: route('backend.api.entities'),
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term, // search term
                            page: params.page
                        };
                    },
                    processResults: function(data, params) {
                        params.page = params.page || 1;

                        return {
                            results: data.items,
                            pagination: {
                                more: (params.page * 30) < data.total_count
                            }
                        };
                    },
                    cache: true
                },
                placeholder: 'Firma seçin',
                minimumInputLength: 0,
                templateResult: formatCompany,
                templateSelection: formatCompanySelection
            });

            // Format company item in dropdown
            function formatCompany(company) {
                if (company.loading) {
                    return company.text;
                }

                var $container = $(
                    "<div class='select2-result clearfix'>" +
                    "<div class='select2-result__title'>" + company.entity_name + "</div>" +
                    "<div class='select2-result__meta'>" + company.entity_name + "</div>" +
                    "</div>"
                );

                return $container;
            }

            // Format selected company in dropdown
            function formatCompanySelection(company) {
                return company.entity_name || company.text;
            }

            // Password strength meter
            $('#password').on('keyup', function() {
                var password = $(this).val();
                var strength = 0;

                if (password.length > 7) strength += 1;
                if (password.match(/[a-z]+/)) strength += 1;
                if (password.match(/[A-Z]+/)) strength += 1;
                if (password.match(/[0-9]+/)) strength += 1;
                if (password.match(/[$@#&!]+/)) strength += 1;

                var strengthBar = $('.password-strength');

                switch (strength) {
                    case 0:
                        strengthBar.css('width', '0%').removeClass().addClass('password-strength bg-danger');
                        break;
                    case 1:
                        strengthBar.css('width', '20%').removeClass().addClass('password-strength bg-danger');
                        break;
                    case 2:
                        strengthBar.css('width', '40%').removeClass().addClass('password-strength bg-warning');
                        break;
                    case 3:
                        strengthBar.css('width', '60%').removeClass().addClass('password-strength bg-warning');
                        break;
                    case 4:
                        strengthBar.css('width', '80%').removeClass().addClass('password-strength bg-success');
                        break;
                    case 5:
                        strengthBar.css('width', '100%').removeClass().addClass('password-strength bg-success');
                        break;
                }
            });

            // Avatar preview
            $('#avatar').on('change', function() {
                var file = this.files[0];
                if (file) {
                    var reader = new FileReader();
                    reader.onload = function(e) {
                        $('.avatar-preview').attr('src', e.target.result);
                    }
                    reader.readAsDataURL(file);
                }
            });

            // Password confirmation validation
            $('#password_confirmation').on('keyup', function() {
                var password = $('#password').val();
                var passwordConfirm = $(this).val();

                if (password !== passwordConfirm) {
                    $(this).addClass('is-invalid');
                    $('.password-match').html('<span class="text-danger">Şifreler eşleşmiyor!</span>');
                } else {
                    $(this).removeClass('is-invalid');
                    $('.password-match').html('<span class="text-success">Şifreler eşleşiyor.</span>');
                }
            });

            // Form validation
            $('#user-form').on('submit', function(e) {
                var password = $('#password').val();
                var passwordConfirm = $('#password_confirmation').val();

                if (password !== passwordConfirm) {
                    e.preventDefault();
                    showNotification('Şifreler eşleşmiyor!', 'error');
                    return false;
                }

                return true;
            });

            // Success and error messages
            function showNotification(message, type) {
                NioApp.Toast(message, type, {
                    position: 'top-right'
                });
            }
        });


    </script>
@endpush