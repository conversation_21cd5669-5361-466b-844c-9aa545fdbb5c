<!-- resources/views/backend/roles/index.blade.php -->
@extends('backend.layouts.app')

@section('title') Rol ve İzin Yönetimi @endsection

@section('content')
    <div class="nk-block-head nk-block-head-sm">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">Rol ve İzin Yönetimi</h3>
                <div class="nk-block-des text-soft">
                    <p>Sistemdeki rolleri ve izinleri yönetin.</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="pageMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="pageMenu">
                        <ul class="nk-block-tools g-3">
                            <li><a href="{{ route('backend.roles.create') }}" class="btn btn-primary"><em class="icon ni ni-plus"></em><span>Yeni Rol</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-bordered card-stretch">
            <div class="card-inner-group">
                <div class="card-inner p-0">
                    <div class="nk-tb-list nk-tb-ulist">
                        <div class="nk-tb-item nk-tb-head">
                            <div class="nk-tb-col"><span class="sub-text">Rol Adı</span></div>
                            <div class="nk-tb-col tb-col-lg"><span class="sub-text">İzinler</span></div>
                            <div class="nk-tb-col tb-col-md"><span class="sub-text">Kullanıcı Sayısı</span></div>
                            <div class="nk-tb-col nk-tb-col-tools text-end">İşlemler</div>
                        </div>
                        @foreach($roles as $role)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="tb-lead">{{ $role->name }}</span>
                                </div>
                                <div class="nk-tb-col tb-col-lg">
                                    @foreach($role->permissions as $permission)
                                        <span class="badge bg-info">{{ __('permission.' . $permission->name) }}</span>
                                    @endforeach
                                </div>
                                <div class="nk-tb-col tb-col-md">
                                    <span class="tb-status text-blue">{{ $role->users->count() }}</span>
                                </div>
                                <div class="nk-tb-col nk-tb-col-tools">
                                    <ul class="nk-tb-actions gx-1">
                                        <li>
                                            <div class="drodown">
                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <ul class="link-list-opt no-bdr">
                                                        <li><a href="{{ route('backend.roles.edit', $role->id) }}"><em class="icon ni ni-edit"></em><span>Düzenle</span></a></li>
                                                        <li>
                                                            <a href="#" class="text-danger" onclick="event.preventDefault(); if(confirm('Bu rolü silmek istediğinize emin misiniz?')){ document.getElementById('delete-role-{{ $role->id }}').submit(); }">
                                                                <em class="icon ni ni-trash"></em><span>Sil</span>
                                                            </a>
                                                            <form id="delete-role-{{ $role->id }}" action="{{ route('backend.roles.destroy', $role->id) }}" method="POST" style="display: none;">
                                                                @csrf
                                                                @method('DELETE')
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block-head nk-block-head-sm mt-5">
        <div class="nk-block-between">
            <div class="nk-block-head-content">
                <h3 class="nk-block-title page-title">İzinler</h3>
                <div class="nk-block-des text-soft">
                    <p>Sistemdeki mevcut izinler.</p>
                </div>
            </div>
            <div class="nk-block-head-content">
                <div class="toggle-wrap nk-block-tools-toggle">
                    <a href="#" class="btn btn-icon btn-trigger toggle-expand me-n1" data-target="permissionMenu"><em class="icon ni ni-menu-alt-r"></em></a>
                    <div class="toggle-expand-content" data-content="permissionMenu">
                        <ul class="nk-block-tools g-3">
                            <li><a href="{{ route('backend.permissions.create') }}" class="btn btn-primary"><em class="icon ni ni-plus"></em><span>Yeni İzin</span></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="nk-block">
        <div class="card card-bordered card-stretch">
            <div class="card-inner-group">
                <div class="card-inner p-0">
                    <div class="nk-tb-list nk-tb-ulist">
                        <div class="nk-tb-item nk-tb-head">
                            <div class="nk-tb-col"><span class="sub-text">İzin Adı</span></div>
                            <div class="nk-tb-col tb-col-md"><span class="sub-text">Rol Sayısı</span></div>
                            <div class="nk-tb-col nk-tb-col-tools text-end">İşlemler</div>
                        </div>
                        @foreach($permissions as $permission)
                            <div class="nk-tb-item">
                                <div class="nk-tb-col">
                                    <span class="tb-lead">{{ __('permission.' . $permission->name) }}</span>
                                </div>
                                <div class="nk-tb-col tb-col-md">
                                    <span class="tb-status text-blue">{{ $permission->roles->count() }}</span>
                                </div>
                                <div class="nk-tb-col nk-tb-col-tools">
                                    <ul class="nk-tb-actions gx-1">
                                        <li>
                                            <div class="drodown">
                                                <a href="#" class="dropdown-toggle btn btn-icon btn-trigger" data-bs-toggle="dropdown"><em class="icon ni ni-more-h"></em></a>
                                                <div class="dropdown-menu dropdown-menu-end">
                                                    <ul class="link-list-opt no-bdr">
                                                        <li><a href="{{ route('backend.permissions.edit', $permission->id) }}"><em class="icon ni ni-edit"></em><span>Düzenle</span></a></li>
                                                        <li>
                                                            <a href="#" class="text-danger" onclick="event.preventDefault(); if(confirm('Bu izni silmek istediğinize emin misiniz?')){ document.getElementById('delete-permission-{{ $permission->id }}').submit(); }">
                                                                <em class="icon ni ni-trash"></em><span>Sil</span>
                                                            </a>
                                                            <form id="delete-permission-{{ $permission->id }}" action="{{ route('backend.permissions.destroy', $permission->id) }}" method="POST" style="display: none;">
                                                                @csrf
                                                                @method('DELETE')
                                                            </form>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('after-scripts')
    <script>
        // public/js/backend/roles.js

        $(document).ready(function() {
            // Check/uncheck all permissions
            $('#check-all-permissions').on('change', function() {
                var isChecked = $(this).prop('checked');
                $('.permission-checkbox').prop('checked', isChecked);
            });

            // Group permissions by module
            $('.module-check').on('change', function() {
                var moduleId = $(this).data('module');
                var isChecked = $(this).prop('checked');

                $('.permission-checkbox[data-module="' + moduleId + '"]').prop('checked', isChecked);
            });

            // Individual permission check
            $('.permission-checkbox').on('change', function() {
                var moduleId = $(this).data('module');
                var allChecked = true;

                $('.permission-checkbox[data-module="' + moduleId + '"]').each(function() {
                    if (!$(this).prop('checked')) {
                        allChecked = false;
                        return false;
                    }
                });

                $('.module-check[data-module="' + moduleId + '"]').prop('checked', allChecked);

                // Check if all permissions are checked
                var allModulesChecked = true;
                $('.permission-checkbox').each(function() {
                    if (!$(this).prop('checked')) {
                        allModulesChecked = false;
                        return false;
                    }
                });

                $('#check-all-permissions').prop('checked', allModulesChecked);
            });

            // Show selected permissions in table
            $('.datatable-init-role-permissions').DataTable({
                responsive: true,
                language: {
                    "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "sInfoEmpty": "Kayıt yok",
                    "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "sInfoPostFix": "",
                    "sInfoThousands": ".",
                    "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                    "sLoadingRecords": "Yükleniyor...",
                    "sProcessing": "İşleniyor...",
                    "sSearch": "Ara:",
                    "sZeroRecords": "Eşleşen kayıt bulunamadı",
                    "oPaginate": {
                        "sFirst": "İlk",
                        "sLast": "Son",
                        "sNext": "Sonraki",
                        "sPrevious": "Önceki"
                    }
                },
                pageLength: 10
            });
        });
    </script>
@endsection