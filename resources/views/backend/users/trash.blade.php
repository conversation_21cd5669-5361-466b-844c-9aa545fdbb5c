@extends ('backend.layouts.app')

@section('title') {{ __($module_action) }} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> @endsection

@section('breadcrumbs')
<x-backend-breadcrumbs>
    <x-backend-breadcrumb-item type="active" icon='{{ $module_icon }}'><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></x-backend-breadcrumb-item>
</x-backend-breadcrumbs>
@endsection

@section('content')
<div class="card">
    <div class="card-body">

        <x-backend.section-header>
            <i class="{{ $module_icon }}"></i> <PERSON><PERSON><PERSON><PERSON><PERSON>lar <small class="text-muted">{{ __($module_action) }}</small>

            <x-slot name="subtitle">
                @lang(":module_name Management Dashboard", ['module_name'=>Str::title($users)])
            </x-slot>
            <x-slot name="toolbar">
                <x-backend.buttons.return-back />
                <a href='{{ route("backend.$users.index") }}' class="btn btn-secondary" data-toggle="tooltip" title="{{ ucwords($users) }} List"><i class="fas fa-list"></i> List</a>
            </x-slot>
        </x-backend.section-header>

        <hr>

        <div class="row mt-4">
            <div class="col">
                <table id="datatable" class="table table-hover table-responsive-sm">
                    <thead>
                        <tr>
                            <th>
                                #
                            </th>
                            <th>
                                Ad
                            </th>
                            <th>
                                Güncellenme
                            </th>
                            <th>
                                Oluşturulma
                            </th>
                            <th class="text-end">
                                İşlem
                            </th>
                        </tr>
                    </thead>

                    <tbody>
                        @foreach($$users as$user)
                        <tr>
                            <td>
                                {{$user->id }}
                            </td>
                            <td>
                                {{$user->name }}
                            </td>
                            <td>
                                {{$user->updated_at->diffForHumans() }}
                            </td>
                            <td>
                                {{$user->created_by }}
                            </td>
                            <td class="text-end">
                                <a href="{{route("backend.$users.restore",$user)}}" class="btn btn-danger btn-sm mt-1" data-method="PATCH" data-token="{{csrf_token()}}"><i class="fas fa-undo" data-toggle="tooltip" title="{{__('labels.backend.restore')}}" data-confirm="{{ __('Are you sure?') }}"></i></a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="card-footer">
        <div class="row">
            <div class="col-7">
                <div class="float-left">
                    Total {{ $$users->total() }} {{ ucwords($users) }}
                </div>
            </div>
            <div class="col-5">
                <div class="float-end">
                    {!! $$users->render() !!}
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
@section ('after-scripts-end')

@endsection