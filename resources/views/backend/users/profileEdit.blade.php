@extends('backend.layouts.app')

@section('title') {{ __($module_action) }} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> @endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item route='{{route("backend.$users.index")}}' icon='{{ $module_icon }}'>
            Kullanıcılar
        </x-backend-breadcrumb-item>
        <x-backend-breadcrumb-item route='{{route("backend.$users.show", $user->id)}}' icon='{{ $module_icon }}'>
            {{ $user->name }}
        </x-backend-breadcrumb-item>

        <x-backend-breadcrumb-item type="active">{{ __($module_action) }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <x-backend.layouts.edit :data="$user">
        <x-backend.section-header>
            <i class="{{ $module_icon }}"></i> {{ __('Profile') }} <small class="text-muted">{{ __($module_action) }}</small>

            <x-slot name="subtitle">
                Kullanıcı Profil Bilgileri
            </x-slot>
            <x-slot name="toolbar">
                <x-backend.buttons.return-back />
            </x-slot>
        </x-backend.section-header>

        <hr>

        <div class="row mt-4">
            <div class="col">
                {{ html()->modelForm($userprofile, 'PATCH', route('backend.users.profileUpdate', $user->id))->class('form-horizontal')->attributes(['enctype'=>"multipart/form-data"])->open() }}
                <div class="form-group row">
                    {{ html()->label(__('labels.backend.users.fields.avatar'))->class('col-md-2 form-control-label')->for('name') }}

                    <div class="col-md-5">
                        <img src="{{asset($user->avatar)}}" class="user-profile-image img-fluid img-thumbnail" style="max-height:200px; max-width:200px;" />
                    </div>
                    <div class="col-md-5">
                        <input id="file-multiple-input" name="avatar" multiple="" type="file">
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">

                            {{ html()->label(__('First Name'), 'first_name')->class('form-label') }} {!! fielf_required("required") !!}
                            {{ html()->text('first_name')->placeholder(__('First name'))->class('form-control')->attributes(["required"]) }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">

                            {{ html()->label(__('Last Name'), 'last_name')->class('form-label') }} {!! fielf_required("required") !!}
                            {{ html()->text('last_name')->placeholder(__('Last name'))->class('form-control')->attributes(['required']) }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">
                            {{ html()->label('Email', 'email')->class('form-label') }} {!! fielf_required("required") !!}
                            {{ html()->email('email')->placeholder('email')->class('form-control')->attributes(["required"]) }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">

                            {{ html()->label(__('Mobile'), 'mobile')->class('form-label') }}
                            {{ html()->text('mobile')->placeholder(__('Mobile'))->class('form-control') }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">
                            {{ html()->label(__('Gender'), 'gender')->class('form-label') }}
                            {{ html()->select('gender', [
                                'kadın' => 'Kadın',
                                'erkek' => 'Erkek',
                                'diger' => 'Diğer',
                            ])->placeholder(__('Gender'))->class('form-select') }}
                        </div>
                    </div>

                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">
                            {{ html()->label(__('Date of Birth'), 'date_of_birth')->class('form-label') }}
                            {{ html()->date('date_of_birth')->placeholder(__('Date of Birth'))->class('form-control') }}
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">
                            {{ html()->label(__('Address'), 'address')->class('form-label') }}
                            {{ html()->textarea('address')->placeholder(__('Address'))->class('form-control') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-6 mb-3">
                        <div class="form-group">
                            {{ html()->label(__('Bio'), 'bio')->class('form-label') }}
                            {{ html()->textarea('bio')->placeholder(__('Bio'))->class('form-control') }}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12 mb-3">
                        <div class="form-group">
                            {{ html()->label(__('Website'), 'url_website')->class('form-label') }}
                            {{ html()->text('url_website')->placeholder(__('Website'))->class('form-control') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 mb-3">
                        <div class="form-group">
                            {{ html()->label('Facebook', 'url_facebook')->class('form-label') }}
                            {{ html()->text('url_facebook')->placeholder('Facbook')->class('form-control') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 mb-3">
                        <div class="form-group">
                            {{ html()->label('Instagram', 'url_instagram')->class('form-label') }}
                            {{ html()->text('url_instagram')->placeholder('Instagram')->class('form-control') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 mb-3">
                        <div class="form-group">

                            {{ html()->label('Twitter', 'url_twitter')->class('form-label') }}
                            {{ html()->text('url_twitter')->placeholder('Twitter')->class('form-control') }}
                        </div>
                    </div>
                    <div class="col-12 col-sm-3 mb-3">
                        <div class="form-group">

                            {{ html()->label('Linkedin', 'url_linkedin')->class('form-label') }}
                            {{ html()->text('url_linkedin')->placeholder('Linkedin')->class('form-control') }}
                        </div>
                    </div>
                </div>
                <div class="row mt-4">
                    <div class="col-6">
                        <x-backend.buttons.save />
                    </div>
                    <div class="col-6 text-end">
                        <x-backend.buttons.cancel />
                    </div>
                </div>
                {{ html()->closeModelForm() }}
            </div>
        </div>
    </x-backend.layouts.edit>
@endsection