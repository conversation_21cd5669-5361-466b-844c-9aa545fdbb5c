@extends('backend.layouts.app')

@section('title')
    {{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> {{ $title }}
             <x-slot name="subtitle">
                    {{ $sub_title ?? ''}} düzenle
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>


            <div class="row mt-4">
                <div class="col">
                    <table class="table datatable-init" id="order_quantity">
                        <thead>
                        <tr>
                            <th><PERSON><PERSON><PERSON> Adı</th>
                            <th>İş<PERSON></th>
                        </tr>
                        </thead>
                        <tbody>

                        @foreach($artisans as $artisan)
                        <tr>
                            <td>
                                {{ $artisan['name'] }}
                            </td>
                            <td>
                                <li class="preview-item" >
                                    <button class="btn btn-primary d-none refresh_loading" type="button" disabled>
                                            <span class="spinner-border spinner-border-sm" role="status"
                                                  aria-hidden="true"></span>
                                        <span class="visually-hidden">{{ __('Loading...') }}</span>
                                    </button>
                                    <a data-href="{{ action('App\Http\Controllers\Frontend\AjaxController@run_artisan', ['command' => $artisan['command']]) }}"
                                       data-bs-toggle="tooltip" title=" {{ $artisan['name'] }}"
                                       class="btn btn-outline-primary btn-dim run-artisan refresh"><em
                                                class="icon ni ni-reload "></em>
                                    </a>
                                    <button class="btn btn-outline-primary btn-dim d-none refreshed" type="button" disabled>
                                        <em class="icon ni ni-check"></em>
                                    </button>
                                </li>
                            </td>
                        </tr>
                        @endforeach



                        </tbody>
                    </table>

                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        var refreshButtons = $('.refresh');
        var refreshLoadingButtons = $('.refresh_loading');
        var refreshedButtons = $('.refreshed');

        $('.run-artisan').click(function () {
            var refresh = $(this).closest('tr').find('.refresh');
            var refresh_loading = $(this).closest('tr').find('.refresh_loading');
            var refreshed = $(this).closest('tr').find('.refreshed');

            refresh.addClass('d-none');
            refresh_loading.removeClass('d-none');
            refresh_loading.addClass('d-inline-block');

            var url = $(this).data('href');
            $.ajax({
                url: url,
                method: 'GET',
                success: function (response) {
                    NioApp.Toast(response.message, 'info', {position: 'top-center'});
                    refresh_loading.addClass('d-none');
                    refreshed.removeClass('d-none');
                    refreshed.addClass('d-inline-block');
                }
            });
        });


    </script>
@endsection