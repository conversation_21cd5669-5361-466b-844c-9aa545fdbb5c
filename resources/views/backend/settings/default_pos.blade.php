@extends('backend.layouts.app')

@section('title')
    {{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> {{ $title }}
             <x-slot name="subtitle">
                    {{ $title }} düzenle
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>

            <div class="row mt-4">
                <div class="col">
                    <h2 class="h5">POS Aç/Kapa</h2>

                    <table class="table" id="set_pos">
                        <thead>
                        <tr>
                            <th>Banka</th>
                            <th>İşlem</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($banks as $bankKey => $bankConfig)
                            @php
                                $is_active_pos = \App\Models\Setting::where('name', $bankKey)->value('val');
                            @endphp
                            <tr>
                                <td>
                                    {{ $bankConfig['name'] }}
                                </td>
                                <td>
                                    <div class="custom-control custom-switch @if($is_active_pos === '1'){{ 'checked' }}@endif"><input type="checkbox" class="custom-control-input" @if($is_active_pos === '1'){{ 'checked=""' }}@endif data-id="{{ $bankKey }}" data-status="{{ $is_active_pos == 1 ? 0 : 1 }}" id="pos-{{ $bankKey }}"><label class="custom-control-label" for="pos-{{ $bankKey }}"></label></div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>


                    <h2 class="mt-3 h5">Varsayılan POS</h2>
                    <form method="post" action="{{ route('backend.settings.store') }}" class="form-horizontal" role="form">
                        {!! csrf_field() !!}
                        {{ html()->hidden('section')->value($section) }}

                        <select class="form-select" data-search="on" name="default_pos" required>
                            <option value="" {{ $default_pos == '' ? 'selected' : '' }}>Banka Seçiniz</option>
                            @foreach($banks as $bankKey => $bankConfig)
                                <option value="{{ $bankKey }}" {{ $default_pos == $bankKey ? 'selected' : '' }}>{{ $bankConfig['name'] }}</option>
                            @endforeach
                        </select>
                        <div class="row m-b-md mt-3">
                            <div class="col-md-12">
                                <button class="btn-primary btn">
                                    <i class='fas fa-save'></i> @lang('Save')
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection
@section('scripts')
    <script>

        $(document).on('click', '.custom-control-input', function(e) {
            var checkbox = $(this);
            $.ajax({
                method: 'POST',
                url: '{{ route('backend.settings.store', ['section' => 'other_settings']) }}',
                data: {
                    setting: checkbox.data('id'),
                    setting_value: checkbox.data('status'),
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(result) {
                    if (result.success) {
                        NioApp.Toast(result.message, 'info', {position: 'top-right'});
                        checkbox.data('status', result.value ? 1 : 0);

                    } else {

checkbox.prop('checked', true).closest('.custom-control').addClass('checked');

                        NioApp.Toast(result.message, 'error', {position: 'top-right'});
                    }
                }
            });
        });
    </script>
@endsection