@extends('backend.layouts.app')

@section('title')
    {{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> {{ $title }}
             <x-slot name="subtitle">
                    {{ $sub_title ?? ''}} düzenle
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>


            <div class="row mt-4">
                <div class="col">
                    <table class="table datatable-init" id="order_quantity">
                        <thead>
                        <tr>
                            <th>Cari</th>
                            <th>İşlem</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($entities as $entity)
                        <tr>
                            <td>
                                {{ $entity->entity_name }}
                            </td>
                            <td>
                                @if($entity->order_quantity)
                                    <div class="custom-control custom-switch checked"><input type="checkbox" class="custom-control-input" checked="" data-id="{{ $entity->id }}" data-status="1" id="entity-{{ $entity->id }}"><label class="custom-control-label" for="entity-{{ $entity->id }}"></label></div>
                                @else
                                    <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" data-id="{{ $entity->id }}" data-status="0" id="entity-{{ $entity->id }}"><label class="custom-control-label" for="entity-{{ $entity->id }}"></label></div>
                                @endif

                            </td>
                        </tr>
                        @endforeach
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>

        $(document).on('click', '.custom-control-input', function(e) {
            $.ajax({
                method: 'POST',
                url: '{{ route('entity_update_status') }}',
                data: {
                    entity_id: $(this).data('id'),
                    order_quantity: $(this).data('order_quantity'),
                    action: 'order_quantity',
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(result) {
                    if (result.success) {
                        NioApp.Toast(result.message, 'info', {position: 'top-right'});
                        data_table.ajax.reload(null, false);
                    } else {
                        NioApp.Toast(result.message, 'error', {position: 'top-right'});
                    }
                }
            });
        });
    </script>
@endsection