@extends('backend.layouts.app')

@section('title')
 <PERSON><PERSON><PERSON> - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> <PERSON><PERSON><PERSON>
             <x-slot name="subtitle">

                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>


            <div class="row mt-4">
                <div class="col">
                    <table class="table">
                        <thead>
                        <tr>
                            <th><PERSON><PERSON><PERSON>kla<PERSON></th>
                            <th>Durum</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>
                                <PERSON><PERSON> Fiyat Listesi ile Sipariş
                            </td>
                            <td>
                                @if($general_price_list === 'on')
                                    <div class="custom-control custom-switch checked"><input type="checkbox" class="custom-control-input" checked="" data-id="general_price_list" data-status="off" id="general_price_list"><label class="custom-control-label" for="general_price_list"></label></div>
                                @else
                                    <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" data-id="general_price_list" data-status="on" id="general_price_list"><label class="custom-control-label" for="general_price_list"></label></div>
                                @endif
                            </td>
                        </tr>

                        <tr>
                            <td>
                                Sözleşme ile Sipariş
                            </td>
                            <td>
                                @if($order_with_contract === 'on')
                                    <div class="custom-control custom-switch checked"><input type="checkbox" class="custom-control-input" checked="" data-id="order_with_contract" data-status="off" id="order_with_contract"><label class="custom-control-label" for="order_with_contract"></label></div>
                                @else
                                    <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" data-id="order_with_contract" data-status="on" id="order_with_contract"><label class="custom-control-label" for="order_with_contract"></label></div>
                                @endif
                            </td>
                        </tr>

                        <tr>
                            <td>
                                Sadece Depo Teslim Siparişi Al
                            </td>
                            <td>
                                @if($warehouse_delivery_only === 'on')
                                    <div class="custom-control custom-switch checked"><input type="checkbox" class="custom-control-input" checked="" data-id="warehouse_delivery_only" data-status="off" id="warehouse_delivery_only"><label class="custom-control-label" for="warehouse_delivery_only"></label></div>
                                @else
                                    <div class="custom-control custom-switch"><input type="checkbox" class="custom-control-input" data-id="warehouse_delivery_only" data-status="on" id="warehouse_delivery_only"><label class="custom-control-label" for="warehouse_delivery_only"></label></div>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td>
                                Şifre Değiştirme Aralığı
                            </td>
                            <td>
                                <select name="password_change_frequency" id="password_change_frequency" class="form-select">
                                    <option value="1" @if($password_change_frequency == '1'){{ 'selected' }}@endif>1 Ay</option>
                                    <option value="2" @if($password_change_frequency == '2'){{ 'selected' }}@endif>2 Ay</option>
                                    <option value="3" @if($password_change_frequency == '3'){{ 'selected' }}@endif>3 Ay</option>
                                    <option value="4" @if($password_change_frequency == '4'){{ 'selected' }}@endif>4 Ay</option>
                                    <option value="5" @if($password_change_frequency == '5'){{ 'selected' }}@endif>5 Ay</option>
                                    <option value="6" @if($password_change_frequency == '6'){{ 'selected' }}@endif>6 Ay</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>
                               POS ile İstenecek Minimum Ödeme Tutarı
                            </td>
                            <td>
                                <div class="input-group mb-3">
                                <input type="number" min="0" step="any" name="min_payment_amount" id="min_payment_amount" class="form-control" placeholder="Tutar giriniz" value="{{ $min_payment_amount }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-primary" type="button" id="save_amount" onclick="saveMinPaymentAmount()">Tutarı Kaydet</button>
                                    </div>
                                </div>

                            </td>
                        </tr>
                        </tbody>
                    </table>

                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>

        $(document).on('click', '.custom-control-input', function(e) {
            $.ajax({
                method: 'POST',
                url: '{{ route('backend.settings.store', ['section' => 'other_settings']) }}',
                data: {
                    setting: $(this).data('id'),
                    setting_value: $(this).data('status'),
                    action: 'other_settings',
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(result) {
                    if (result.success) {
                        NioApp.Toast(result.message, 'info', {position: 'top-right'});
                    } else {
                        NioApp.Toast(result.message, 'error', {position: 'top-right'});
                    }
                }
            });
        });

        $(document).ready(function() {

            $('#password_change_frequency').change(function(){
                $.ajax({
                    method: 'POST',
                    url: '{{ route('backend.settings.store', ['section' => 'other_settings']) }}',
                    data: {
                        setting: 'password_change_frequency',
                        setting_value: $(this).val(),
                        action: 'other_settings',
                        _token: $('meta[name="csrf-token"]').attr('content')
                    },
                    dataType: 'json',
                    success: function(result) {
                        if (result.success) {
                            NioApp.Toast(result.message, 'info', {position: 'top-right'});
                        } else {
                            NioApp.Toast(result.message, 'error', {position: 'top-right'});
                        }
                    }
                });
            });
        });


        function saveMinPaymentAmount() {
            $.ajax({
                method: 'POST',
                url: '{{ route('backend.settings.store', ['section' => 'other_settings']) }}',
                data: {
                    setting: 'min_payment_amount',
                    setting_value: $('#min_payment_amount').val(),
                    action: 'other_settings',
                    _token: $('meta[name="csrf-token"]').attr('content')
                },
                dataType: 'json',
                success: function(result) {
                    $("#overlay").fadeOut(200);
                    if (result.success) {
                        NioApp.Toast(result.message, 'info', {position: 'top-right'});
                    } else {
                        NioApp.Toast(result.message, 'error', {position: 'top-right'});
                    }
                }
            });
        }

    </script>
@endsection