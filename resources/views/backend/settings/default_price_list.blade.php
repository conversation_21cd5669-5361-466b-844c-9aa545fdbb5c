@extends('backend.layouts.app')

@section('title')
    {{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fa fas fa-cog'>{{ __('Settings') }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">

            <x-backend.section-header>
                <i class="fa fas fa-cog"></i> {{ $title }}
             <x-slot name="subtitle">
                    {{ $title }} düzenle
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>


            <div class="row mt-4">
                <div class="col">

                    <form method="post" action="{{ route('backend.settings.store') }}" class="form-horizontal" role="form">
                        {!! csrf_field() !!}
                        {{ html()->hidden('section')->value($section) }}

                        {{ html()->select('default_price_list', $price_lists, $default_price_list_id)->class('form-select js-select2')->attribute('data-search', 'on') }}

                        <div class="row m-b-md mt-2">
                            <div class="col-md-12">
                                <button class="btn-primary btn">
                                    <i class='fas fa-save'></i> @lang('Save')
                                </button>
                            </div>
                        </div>

                    </form>
                </div>
            </div>
        </div>
        <div class="card-footer">
            <div class="row">

            </div>
        </div>
    </div>
@endsection