@extends('backend.layouts.app')

@section('title')
{{ $title }} - {{ __('Settings') }}
@endsection

@section('breadcrumbs')
    <x-backend-breadcrumbs>
        <x-backend-breadcrumb-item type="active" icon='fas fa-window-close'>{{ $title }}</x-backend-breadcrumb-item>
    </x-backend-breadcrumbs>
@endsection

@section('content')
    <div class="card">
        <div class="card-body">
            <x-backend.section-header>
                <i class="fas fa-window-close"></i> {{ $title }}
                <x-slot name="subtitle">
                    Popup gösterilmeyecek carileri seçin
                </x-slot>
                <x-slot name="toolbar">
                    <x-backend.buttons.return-back/>
                </x-slot>
            </x-backend.section-header>

            <form action="{{ route('backend.settings.store', ['section' => 'popup-ayarlari']) }}" method="POST">
                @csrf
                <input type="hidden" name="section" value="popup-ayarlari">
                
                <div class="row mt-4">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="excluded_entities">Popup Gösterilmeyecek Cariler</label>
                            <select name="excluded_entities[]" id="excluded_entities" class="form-control" multiple="multiple">
                                @foreach($selected_entities as $entity)
                                    <option value="{{ $entity->id }}" selected>
                                        {{ $entity->entity_name }} ({{ $entity->entity_code }})
                                    </option>
                                @endforeach
                            </select>
                            <small class="form-text text-muted">
                                Cari ara ve seçim yap. Bu carilerin yetkilileri oturum açtığında popup gözükmeyecektir.
                            </small>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save"></i> Kaydet
                        </button>
                    </div>
                </div>
            </form>

            @if($selected_entities->count() > 0)
            <div class="row mt-4">
                <div class="col-md-12">
                    <h5>Şu Anda Popup Gösterilmeyen Cariler:</h5>
                    <div class="list-group">
                        @foreach($selected_entities as $entity)
                            <div class="list-group-item">
                                <strong>{{ $entity->entity_name }}</strong> ({{ $entity->entity_code }})
                                <span class="badge badge-secondary">ID: {{ $entity->id }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>
    </div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $('#excluded_entities').select2({
        placeholder: 'Cari ara ve seçim yap...',
        allowClear: true,
        width: '100%',
        ajax: {
            url: '{{ route('backend.settings.search-entities') }}',
            dataType: 'json',
            delay: 250,
            data: function (params) {
                return {
                    search: params.term,
                    page: params.page || 1
                };
            },
            processResults: function (data, params) {
                params.page = params.page || 1;
                return {
                    results: data.results,
                    pagination: {
                        more: data.pagination.more
                    }
                };
            },
            cache: true
        },
        escapeMarkup: function (markup) { 
            return markup; 
        },
        minimumInputLength: 1,
        language: {
            inputTooShort: function () {
                return 'En az 1 karakter girin';
            },
            searching: function () {
                return 'Aranıyor...';
            },
            noResults: function () {
                return 'Sonuç bulunamadı';
            },
            loadingMore: function () {
                return 'Daha fazla sonuç yükleniyor...';
            }
        }
    });
});
</script>
@endsection