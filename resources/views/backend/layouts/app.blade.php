<!DOCTYPE html>
<html lang="tr" class="js">

<head>

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <link rel="icon" type="image/png" href="{{asset('img/favicon.png')}}">
    <link rel="apple-touch-icon" sizes="76x76" href="{{asset('img/favicon.png')}}">
    <meta name="keyword" content="{{ setting('meta_keyword') }}">
    <meta name="description" content="{{ setting('meta_description') }}">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link rel="shortcut icon" href="{{url('images/favicon.png')}}">
    <link rel="icon" type="image/ico" href="{{url('images/favicon.png')}}"/>
    <title>@yield('title') | {{ config('app.name') }}</title>
    <!-- StyleSheets  -->
    <link rel="stylesheet" href="/assets/css/main.css?ver=3.2.3">

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    @stack('after-styles')

    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />


    @livewireStyles
</head>

<body class="nk-body bg-lighter npc-default has-sidebar ">
<div class="nk-app-root">
    <!-- main @s -->
    <div class="nk-main ">
        <!-- sidebar @s -->
        @include('backend.includes.sidebar')
        <!-- sidebar @e -->
        <!-- wrap @s -->
        <div class="nk-wrap ">
            <!-- main header @s -->

            @include('frontend.includes.header')
            <!-- main header @e -->
            <!-- content @s -->
            <div class="nk-content ">
                <div class="container-fluid">
                    <div class="nk-content-inner">
                        <div class="nk-content-body">
                            @include('flash::message')

                            <!-- Errors block -->
                            @include('backend.includes.errors')
                            <!-- / Errors block -->

                            <!-- Main content block -->
                            @yield('content')
                            <!-- / Main content block -->
                        </div>
                    </div>
                </div>
            </div>
            <!-- content @e -->
            <!-- footer @s -->
            @include('frontend.includes.footer')

            <!-- footer @e -->
        </div>
        <!-- wrap @e -->
    </div>
    <!-- main @e -->
</div>
<!-- app-root @e -->
<!-- select region modal -->
<!-- .modal -->
<!-- JavaScript -->

<script src="/assets/js/backend.js"></script>
<script src="/assets/js/bundle.js?ver=3.2.3"></script>
<script src="/assets/js/scripts.js?ver=3.2.3"></script>


<!-- Scripts -->
@livewireScripts

@stack('after-scripts')
<!-- / Scripts -->
@yield('scripts')
<script>

    $(document).on('click', '.btn-modal', function (e) {
        e.preventDefault();
        var container = $(this).data('container');
        $("#overlay").fadeIn(150);
        $.ajax({
            url: $(this).data('href'),
            dataType: 'html',
            success: function (result) {
                $("#overlay").fadeOut(200);
                $(container)
                    .html(result)
                    .modal('show');
                $('[data-bs-toggle="tooltip"]').tooltip()
                // NioApp.DataTable('.datatable-init', {
                //     responsive: {
                //         details: false
                //     },
                //
                // });
            },
        });
    });



    $(document).on('submit', 'form#change_active_entity_form', function (e) {
        e.preventDefault();
        var form = $("form#change_active_entity_form")[0];
        var data = new FormData(form);
        var url = $(this).attr("action");
        var method = $(this).attr("method");
        $.ajax({
            method: method,
            url: url,
            data: data,
            dataType: "json",
            processData: false,
            contentType: false,
            success: function (result) {
                if (result.success == true) {
                    $('div#modal_container').modal('hide');
                    toastr.clear();
                    NioApp.Toast(result.message, 'info', {position: 'top-center'});
                    $('#active_entity').html(result.entity_name);
                    if (result.redirect) {
                        setTimeout(function () {
                            window.location.href = result.redirect;
                        }, 2000);
                    }
                } else {
                    toastr.clear();
                    NioApp.Toast(result.msg, 'error', {position: 'top-center'});
                }
            }
        });
    });


    @if (\Session::has('error'))
    $(document).ready(function () {
        NioApp.Toast({!! \Session::get('error') !!}, 'warning', {position: 'top-center'});
    });
    @endif

    @if (\Session::has('success'))
    $(document).ready(function () {
        NioApp.Toast({!! \Session::get('success') !!}, 'info', {position: 'top-center'});
    });
    @endif

</script>
</body>

</html>
